chcp 65002

set tool_home=%~dp0
set root=%TOOL_HOME%..\
cd %root%

:: 判断config.env文件是否存在
if not exist "local/config.env" (
    echo config.env not exist, please check!!!
    exit
)

:: 循环将配置中的参数放到环境变量中
FOR /F "eol=# tokens=*" %%i IN (%tool_home%config.env) DO SET %%i

:: 判断环境变量是否存在 platform_version
if not defined platform_version (
    echo platform_version not defined, please check config.env
    exit
)
:: 判断环境变量是否存在 jcsj_version
if not defined jcsj_version (
    echo jcsj_version not defined, please check config.env
    exit
)

:: 拉取平台包
IF NOT EXIST "%root%packages/platform/" MD "%root%packages/platform/"
cd %root%packages/platform/

if not exist %platform_version%.zip (
    curl https://pkg.mingyuanyun.com/packages/platform/core-full/%platform_version%/%platform_version%.zip -o   %root%packages/platform/%platform_version%.temp
    :: 下载成功将下载临时文件*.temp改为zip格式
    if %errorlevel% equ 0 (
        ren %platform_version%.temp  %platform_version%.zip
        echo download %platform_version%.zip success.
    ) else (
        echo download %platform_version%.zip failure.
        exit
    )
)
::先删除
rd /s/q %platform_version%\
rd /s/q _metadata\
rd /s/q data\
rd /s/q wwwroot\
::解压
mkdir %platform_version%
cd %platform_version%
tar -xzf ../%platform_version%.zip
:: 拷贝平台元数据 sql wwwroot
xcopy %root%packages\platform\%platform_version%\src\_metadata %root%packages\platform\_metadata\ /E /Y
xcopy %root%packages\platform\%platform_version%\data %root%packages\platform\data\sql\ /E /Y
xcopy %root%packages\platform\%platform_version%\src\wwwroot %root%packages\platform\wwwroot\ /E /Y


::拉基础数据包
IF NOT EXIST "%root%packages/jcsj" MD "%root%packages/jcsj"
cd %root%packages/jcsj

echo downloading jcsj_java %jcsj_version%
if not exist %jcsj_version%.zip (
    curl https://pkg.mingyuanyun.com/packages/jcsj_java/full/%jcsj_version%/%jcsj_version%.zip -o   %root%packages/jcsj/%jcsj_version%.temp
    :: 下载成功将下载临时文件*.temp改为zip格式
    if %errorlevel% equ 0 (
        ren %jcsj_version%.temp  %jcsj_version%.zip
        echo download jcsj_java.%jcsj_version%.zip success.
    ) else (
        echo download jcsj_java.%jcsj_version%.zip failure.
        exit
    )
)

rd /s/q %jcsj_version%
rd /s/q data
::解压
mkdir %jcsj_version%
mkdir data

cd %jcsj_version%
tar -xzf ../%jcsj_version%.zip
:: 拷贝平台元数据 sql wwwroot

cd ..
xcopy %jcsj_version%\data data /D /E /Q

cd %root%
echo excute pull over!!!

cd %root%

echo excute pull over!!!

pause