
use `nacos`;

INSERT INTO tenant_info(kp, tenant_id, tenant_name, tenant_desc, create_source, gmt_create, gmt_modified)
VALUES('1', 'skyline', 'skyline', 'skyline', 'nacos', 1689234171131, 1689234171131);

INSERT  INTO config_info(data_id,group_id,content,`md5`,gmt_create,gmt_modified,src_user,src_ip,app_name,tenant_id,c_desc,c_use,effect,`type`,c_schema,encrypted_data_key) VALUES
('common.yml','DEFAULT_GROUP','skyline:\n  business:\n    mode: \'Saas\' #新商务公有云环境：SaaS，新商务私有化环境：Private_SaaS，老商务私有化环境：OP\n  dataSource:\n    host: dbhost\n    port: \'3306\'\n    userName: root\n    password: Mysoft95938\n    dbType: \'MySql\' # 默认值：MySql\n  cache:\n    redisType: 0     #redis类型 0 默认单节点，1哨兵，2 集群模式\n    redisServerIps: redis:6379     #redis地址\n  rabbitmq:\n    userName: root\n    host: rabbitmq\n    port: 5672\n    password: root\n  # storage:  #对象存储信息\n  #   type: \'minio\'   #对象存储类型(alioss:阿里云OSS,华为云OBS:obs,Minio:minio,腾讯云Cos:cos)\n  #   endpoint: \'http://***********:9000\'  #对象存储访问地址\n  #   accessKey: \'mysoft\'   #对象存储的AK\n  #   accessSecret: \'Mysoft@95938\'\n  site:\n    moqlService:\n      Enable: true\n      ServiceAddress: http://moql:9080\n    tenant:\n      tenantApiAddresses: http://tenantservice:7000     #多租户管理系统地址\n      tenantApiSignId: erp     #多租户管理系统API校验参数\n      tenantApiSignKey: 123456     #多租户管理系统API校验参数\n    document:\n      url: http://mydoc:9070     #文档服务外网地址\n      insideUrl: http://mydoc:9070     #文档服务内网地址\n      containerUrl: http://mydoc:9070     #文档服务容器地址\n    jkgl:\n      jkglUrl:       #接口管家站点地址\n      fdccloudUrl:       #云助手地址\n      jkglCloudUrl:      #云管家站点地址\n    dashboard:\n      dashboardSecret: YC2UFKz7      #数见密钥\n      dashboardUrl:       #数见地址（经营分析平台）\n    reportService:\n      reportServiceUri: https://rpt-test.mingyuanyun.com/     #报表服务地址\n      reportServiceInsideUri: https://rpt-test.mingyuanyun.com/     #报表服务内网地址\n      reportServiceContainerUri: https://rpt-test.mingyuanyun.com/     #报表服务容器地址\n      reportManageUri:      #报表后台管理地址      \n    dataCenter:\n      datacenterUrl:       #数据服务中心外网地址\n      datacenterInsideUrl:       #数据服务中心内网地址\n    bpm:\n      url: \'http://***********:9080\'      #流程外网地址\n      insideUrl: \'http://***********:9080\'     #流程内网地址\n      containerUrl:       #流程容器地址\n      appSecret: Mysoft95938     #安全密钥，平台级认证\n    ipaas:\n      url:       #集成开放平台的地址\n      appKey:      #应用标识\n      appSecret:      #应用密钥  \n    environment:\n      type: \'ERP_SAAS\'                            # ERP_OP/ERP_SAAS/Industrial_Construction/Smart_Park\n      businessGroup: \'xc\'                     # xc代表信创专业版\n    portal:                                                 #超级工作台\n      domainBackstage: \'https://tj-manager.xcylt.mingyuanyun.com:9443\'           #管理后台-域名\n      domainEnterprise: \'https://tj-work.xcylt.mingyuanyun.com:9443\'         #企业平台-域名  ','3f591c810a11ab7a47ea8bf60c16a8ad','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('bpm.yml','DEFAULT_GROUP','skyline:\n  cache:\n    defaultDb: 4     #redis数据库\n  dataSource:\n    database: bpm_app_saas\n  site:\n    bpmEngine:\n      # url: bpm-engine.{client-domain}     #流程中心引擎外网地址\n      url: https://bpm-engine.xcylt.mingyuanyun.com:9443     #流程中心引擎外网地址\n      insideUrl: http://bpm-engine.lcpt-test.svc     #流程中心引擎内网地址\n      containerUrl: http://bpm-engine.lcpt-test.svc     #流程中心引擎容器地址\n  bpm:\n    components:\n      jobConf:\n        storageType: Redis     #hangfire存储类型（Db、Redis）\n        redisDb: 4    #hangfire 使用的redis-db，默认流程的数据库号 4\n        workerCount: 4     #工作线程数\n      messageEventConf:\n        maxThreads: 20     #最大线程数，默认 20\n        multiCpuCores: 20     #服务器CPU核心倍数，默认 20\n    sysConfig:\n      enableMultiTenancy: true     #启用saas模式，默认为 true\n      enableFastTracker: true     #启用天眼配置，默认为 true\n      enableMipAutoRegister: true     #是否开启 mip 连接器自动注册。默认为false\n      enableFastRouteTracker: false     #是否开启天眼链路追踪。默认为false\n','f82c2fa8054cf09503c1dce14a6c196d','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('datacenter.yml','DEFAULT_GROUP','skyline:\n','2437e387aefc881c0ec560b462b5b983','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('jkgl.yml','DEFAULT_GROUP','skyline:\n  deployMode: \'Saas\'    # 部署模式（私有化：Privatize；Saas化：Saas）\n  dataSource: 	# 数据库配置\n    databaseName: \'mysoft_jkgj\'   # 数据库名称\n  cache:  #缓存配置节点\n    database: \'0\'   # Redis数据库 默认0  \n  tracing:  #链路追踪\n    fastType: \'Console\'   # 日志模式（天眼：Fast；控制台：Console）\n    fastUrl: \'1\'    # 天眼日志上报地址 \n','dc487ca85bc086b0547d252b5a043b3b','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('mdc-tenant-init.yml','DEFAULT_GROUP','skyline:\n  dataSource:\n    host:           #mdc模板库地址\n    port:            #mdc模板库端口\n    userName:    #mdc模板库用户名\n    password:         #mdc模板库用户密码\n    dbType: \'MySQL\'         #mdc模板库类型\n  templateDb:\n    mdc:\n      dbname: \'mdc_template\'       #mdc模板库\n  tenantDb:\n    connection:\n      host:            #mdc租户库地址\n      port:            #mdc租户库端口号\n      username:    #mdc租户库用户名\n      password:         #mdc租户库密码\n      dbtype: \'MySQL\'         #mdc租户库数据库类型\n    dbname:\n      prefix: \'mdc\'       #mdc租户数据库前缀\n  EnableODS: false  #是否开启ods层\n','029308bf5a90d4ad59c1250e50ee23df','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('mydoc.yml','DEFAULT_GROUP','skyline:\n  #saasMySoftFastLogInfo:\n  #  productName: \'wdfw\'     #天眼日志产品标识\n  cache:\n    cachePrefixKey: \'\'    # 缓存前缀\n    database: \'2\'      #redis数据库  \n  dataSource:\n    logDbType: \'Fast\'         # 日志采集类型（SqlServer, MySql, Fast:天眼日志采集)\n    databaseName: \'modeling_platform\'     #文档服务数据库名称\n  storage:  #对象存储信息\n    type: \'single\'   #对象存储类型(alioss:阿里云OSS,华为云OBS:obs,Minio:minio,腾讯云Cos:cos)\n  document:\n    deploy: \'single\'      #部署模式(single:单站点部署,ha:高可用模式)\n    fileUpLoadPath: \'fileUpLoadPath/uploaddoc\'       #文档存储路径，只有使用单节点存储才需要\n    tempPath: \'mysoft-upfiles\'    #临时文件存储路径，只有使用单节点存储才需要\n    tempFileCleanupCycle: 7      #临时文件清理周期\n    bucketName: \'jmpt-doc\'      #存储空间名，只有使用对象存储时候才配置\n    tempBucketName: \'jmpt-doc-temp\'      #临时文件存储空间名，只有使用对象存储时候才配置\n    uploadMode: \'regular\'   #上传模式：兼容模式(regular)，保持现有上传逻辑不变；直连模式(direct)，直连已支持的第三方存储进行上传操作\n    temporaryUploadUrlExpireSeconds: 3600      #直连模式上传有效时间','ec89f8e267bbaecb03d5164f952f639b','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('site-common.yml','DEFAULT_GROUP','skyline:\n  metadataService:\n    workmode: Product      #工作模式，Product 或者Customize\n    enable: false      #是否开启元数据服务\n    url: http://metadataservice:9012\n  global:\n    customerGUID: AAAAAAAA-BBBB-CCCC-DDDD-000000001112     #客户id\n    customerName: 安装盘第一轮测试     #客户名称\n    gatewayUrl: \'http://gateway:9013\' #租户路由网关地址\n    serviceMode: \'Customize\' #服务模式Customize:二开，Product：产品   \n    EnableTestProject: false      #是否支持测试项目  \n  dataSource:\n    databaseName: modeling_platform\n    readonlyUserName: dbreader #数据库只读账号\n    readonlyPassword: Mysoft@95938 #数据库只读账号密码\n    logDbType: 2 #日志库类型（0:,1:,2:天眼日志采集,3:mysql)\n  cache:\n    cachePrefixKey: #redis缓存前缀\n    database: 0 #redis数据库\n  rabbitmq:\n    virtualHost: \'/\'  #rabbitmq虚拟空间名\n  site:\n    apaas:\n      url: http://modelingplatform:9300\n      key: modeling_platform\n      name: 天际建模平台\n      insideUrl: http://modelingplatform:9300\n      containerUrl: http://modelingplatform:9300  \n    bpm:\n      appCode: \'erp\' #流程调用appCode\n  scheduling:\n    containerUrl: http://javaschedule:8099 #调度服务容器地址\n    #ip: #调度服务ip地址\n  saasMySoftFastLogInfo:\n    apiUrl: https://fast.mypaas.com #天眼接口地址\n    accessKey: LTAI4G6Jxun6xBtKtHU4E4a10 #天眼accessKey\n    accessKeySecret: qGzxku2KblJiNtoUKPW9aQOoLEH9QL4a10 #天眼accessKeySecret\n    productName: #天眼日志产品名称\n    envCode: #天眼环境标识（prod：生产，beta:预发，test：测试，dev：开发）\n  tracing:\n    fastType: #天眼FAST模式(Report模式:Report,Forward模式:Forward)\n    fastUrl: #天眼链路地址\ncoreConfig:\n  softDog:\n    dogType: DeveloperDog #狗类型\n    dogServer: #狗服务地址\nmodelingConfig:\n  manager:\n    workMode: Product #服务模式\nappSettings:\n  ERPLicenseProductVersion: cyerp #License版本\nfastTracker:\n  enable: true #是否开启天眼采集\n  carrierHeader:\n    trackerName: fast-tracker #天眼采集名称\n  serviceName: jmpt-multi #服务名称\n  collectLayer:\n    HTTP:\n      collectQueryString: false','7b9b50d39cf85dad3aabb5044b5c6f6c','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('skyline-ipaas.yml','DEFAULT_GROUP','skyline:\n  dataSource:\n    databaseName: \'ipaas\' #数据库名称\n  rabbitmq:\n    virtualHost: \'vhost_tj_ipaas\'	    #虚拟机地址\n  mipclient:                #集成平台客户端\n    mip_gateway_connectiontimeout: \'60\'    #连接超时时间\n    mip_gateway_retrytimespan: \'1\'            #重试时间间隔\n    mip_gateway_retrycount: \'3\'                  #重试次数\n    mip_exceptionrequestcount: \'5\'            #异常请求个数\n    mip_gateway_fusetime: \'5\'                      #熔断持续时间\n  ipaas:\n    envSetting:\n      licenceKey: hdQyCxKxQDRu5Gh6rWItM7iPYRgwD+NAwraIResD7XXX1oMMlQfomPIFfVLTC+d0SOezDa4UeEzfO9GWon9MijjvQocayUd4XK9NX2wrOzuYPbS6x0uqAmAF2jvfNVvxN2pr22tqKmqBzVkhNJorXaq9Z+PY8rvaE65Ryuu/sNXMs7VpxT0SZ//cuca5G0TGLkA4c24SSN4NDPfZQOV2BuDFI14wLGVqfyWU+/jkbH1SPv+qj/KRSxHco7VyiaJxCPFgXAbw6KW/3q4BPd+DT5gkSbDuyz+s+G3NPoTEhmV1AHMoXQAkJxKzHfhxzW+cUiqQlZs8F2wmc1OhSq+auhOtNFWkkHAqDEvP7waDWmdpBJBfiA+tuQ==     #license 授权信息\n      eventBusType: RabbitMQ     #消息队列类型\n      publicNetworkAddress: ipass-erp-saastest.mingyuanyun.com     #集成开放平台外网地址','90c6cb4b30b27ee9161d54be536f925d','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('tenant-config-service.yml','DEFAULT_GROUP','skyline:   \n  tenant:\n    dbName: tenantconfigservice #租户管理服务数据库名称\n  cache:\n    database: 0     #redis数据库\ntenant:\n  platform:\n    targetedNoticeAddress: # 定向通知地址\n      tenantCreated: # 租户创建\n        # 初始化租户数据：【新SaaS（投资管理等）、云ERP SaaS（房开公有云已上线元数据服务）、云定制】 - [erp主站内网地址]/pub/Mysoft.PubPlatform.Integration.AppServices.ILicenseAuthPublicService/ClearLicense\n        - \'http://modelingplatform:9300/pub/Mysoft.PubPlatform.Integration.AppServices.ILicenseAuthPublicService/ClearLicense\' \n        # 【新SaaS（投资管理等）】- [erp主站内网地址]/pub/Mysoft.PubPlatform.ProjectOverview.PublicServices.ICloudPullDataPublicService/PullOpData    \n        - \'\' \n        # 【新SaaS（投资管理等）】- [erp主站内网地址]/pub/Mysoft.PubPlatform.Organization.PublicServices.IOrganizationPublicService/SyncAllBasicData\n        - \'\' \n        # 【云ERP SaaS（房开公有云已上线元数据服务）】 - [erp主站内网地址]/pub/Mysoft.PubPlatform.Organization.PublicServices.IFunctionPointPublicService/Initmipbasedataintegrate\n        - \'\' \n        # 【云定制】 - [集成基础数据内网地址]/api/Notify/tenantCreate\n        - \'\' \n      tenantExtended: \n        # 租户开通定制能力通知 -[erp主站内网地址]/api/Tenant/Init（OP商务和私有化SaaS，不需要初始化租户索引）\n        - \'http://metadataservice.ysjfw-prod.svc/api/Tenant/Init\'\n','733349e361817b11eb8eab4baa31fdff','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('tenant-init.yml','DEFAULT_GROUP','skyline:   \n  templateDb:\n    modelingPlatform:\n      dbname: modeling_platform_template      #平台模板库\n    mdc:\n      dbname: mdc_template      #mdc模板库\n    bpm:\n      dbname: bpm_app_saas      #bpm模板库\n  tenantDb:\n    connection:\n      host: \'dbhost\'             #租户库地址 \n      port: \'3306\'             #租户库端口号 \n      username: \'root\'     #租户库用户名\n      password: \'\'     #租户库密码\n      dbType: \'mysql\'           #租户库类型，默认值mysql \n    dbname:\n      prefix: erp      #租户数据库前缀','b55009279e8d8b2c6dccd1c6a1e63488','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('upgrade-service.yml','DEFAULT_GROUP','skyline:\r\n  cache:\r\n    database: 0      #redis数据库\r\n    cacheMode: 1      #缓存模式 0 Memory 1 Redis\r\n  lockOptions:\r\n    lockMode: 1      #线程池最小线程数\r\n    tryCount: 10      #重试次数\r\n    expire: 30      #锁过期时间（秒）\r\n  storage:\r\n    bucketName:       #存储空间名称\r\n  rdc:\r\n    reportSvc:\r\n      expire: 30      #请求超时时间（秒）\r\n      url: https://devcloud.mypaas.com      #执行结果上报地址\r\n    packageDownloadSvc:\r\n      expire: 30      #请求超时时间（秒）\r\n      url: https://pkg.mingyuanyun.com      #应用包下载地址\r\n  environment:\r\n    env: test     #环境标识码：prod：生产，beta:预发，test：测试，dev：开发\r\n    envName: fkerpsaas-test     #环境名称\r\n    envId: fkerpsaas-test     #环境ID\r\n    customerId: 3a05713a-f2be-9d62-2c6d-9c3b5c6ad324     #客户ID\r\n    hostAddress: http://azgxfw-app.jmpt-azgxfw-test.svc     #环境host地址\r\n','5ae4fc66f013fcac9002adc7143f7706','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('License','DEFAULT_GROUP','<license>\n  <license2>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</license2>\n  <licenseDog>PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjxEb2dDZXJ0aWZpY2F0ZSB4bWxuczp4c2Q9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hIiB4bWxuczp4c2k9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hLWluc3RhbmNlIj4NCiAgICA8RGF0YT4NCiAgICAgICAgPERvZ1R5cGU+UHJvZHVjdERvZzwvRG9nVHlwZT4NCiAgICAgICAgPERvZ0lkPjE4Nzk1NDM0NjI8L0RvZ0lkPg0KICAgICAgICA8RG9nSWQyIC8+DQogICAgICAgIDxFeHBpcmVzPjIwMjQtMDItMDE8L0V4cGlyZXM+DQogICAgICAgIDxFeHBpcmVzMiAvPg0KICAgICAgICA8UHVycG9zZSAvPg0KICAgICAgICA8UHVycG9zZTI+5Y+M5py654Ot5aSHPC9QdXJwb3NlMj4NCiAgICAgICAgPEN1c3RvbWVySWQ+MTlDN0FFREMtQTI1OS00NjkxLTgwRDYtRUJFRDlFODY1NERGPC9DdXN0b21lcklkPg0KICAgICAgICA8Q3VzdG9tZXJOYW1lPjIwMjLkv6HliJvorqTor4E8L0N1c3RvbWVyTmFtZT4NCiAgICAgICAgPElzc3VlRGF0ZT4yMDIzLTAyLTA2VDE1OjIyOjAzLjc4NzQ2NDkrMDg6MDA8L0lzc3VlRGF0ZT4NCiAgICA8L0RhdGE+DQogICAgPFNpZ25hdHVyZT5oaTRjSDdDTUZDRWF4djlQcVA2c1dVbW9nWmswQitRb2s3MThBUFRrSG1XdDZTNEtvVmVyQithVGEycFdKK25rQlhxc3RRWGdjVXlRcE55bzJBNC80M2FlOXFuVVJtelNuSDFHaFpqVExWSnZ2ZFMwRnlsMWs2YmhRcmloOEE5dEUvRUNxM1orZEUrbzBqeGgzNlduc0IyVmVocjRWckVpVzZ4SEdMNnlMYzZMbjBnbHdtYUY5RzQzSVhYa2tqcVRFSCs2RHptMGpQdVVaUE5SU0Rtc0lTdVFRVit3d09zTUF5SWhlVTErZExocmVFTUxVVFM1YWtaTmtMSFR1YTJPMXB5Z1RyVWRHVzNVSGlJbzBwUjNtbWU0dEFVVS9SMDhGbW1paURnQjBFczl0VVRTcEVuWGFobU1VRXBPdlE5YXU2NnlLam5UTjFtNWk1STRQekRzWGc9PTwvU2lnbmF0dXJlPg0KICAgIDxDZXJ0TmFtZT5NeW9zZnQtTmV3RG9nU2lnbi1DRVJUPC9DZXJ0TmFtZT4NCiAgICA8U2VxR3VpZD43MzQ2MTY1Yi03NzA3LTRlOTQtODczZS1mMjkwNjdjY2Q0MTc8L1NlcUd1aWQ+DQo8L0RvZ0NlcnRpZmljYXRlPg==</licenseDog>\n</license>','957a9864290385e388c6e13f1e47e4f7','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'text',NULL,''),
('jschedule-service.yml','DEFAULT_GROUP','xxl:\n  job:\n    accessToken:\n    admin:\n      addresses: http://xxljobadmin:8082/xxl-job-admin # xxl-job客户端地址，根据实际情况修改\n      username: admin # xxl-job客户端登陆名，根据实际情况修改\n      password: 123456 # xxl-job客户端登陆密码，根据实际情况修改\n    executor:\n      address:\n      ip: javaschedule # 调度服务提供xxl-job客户端调度的IP，容器云部署使用内网域名\n      port: 9999 # 调度服务提供xxl-job客户端调度的端口，docker部署时，需要向外暴露端口\n      logpath: log/xxl-job/jobhandler\n      logretentiondays: 30\n \nlocal:\n  job:\n    logCleanCycle: 129600 # 任务执行日志清理周期，单位：分钟\n    asyncTimeoutCycle: 1440 # 异步任务超时周期，单位：分钟\n','07f500473ae58b9d1b8e1b9cd09a50aa','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('metadata-service.yml','DEFAULT_GROUP','skyline:\n    mongodb:\n        connectionString: mongodb://***********:27018/?replicaSet=rs0\n        databaseName: xc-dm\n    metadata:\n        localMetadataRoot: /metadata/tenant\n    storage:\n        bucketName: xc-dm\n    features:\n        threadPoolMinThreads: 0','d868145ecef6d0f71693d81cdeac010b','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('service-common.yml','DEFAULT_GROUP','spring:\n  rabbitmq: \n    listener:\n      simple:\n        concurrency: 1      # 消费者数\n        max-concurrency: 3  # 最大消费者数\n  redis: \n    lettuce:\n      pool:\n        max-active: 3       # 连接池最大连接数（使用负值表示没有限制）\n        max-wait: -1        # 连接池最大阻塞等待时间（使用负值表示没有限制）\n        max-idle: 3         # 连接池中的最大空闲连接\n        min-idle: 1         # 连接池中的最小空闲连接\nmysoft:\n  sdk:\n    context:                # 通用线程池\n      thread-pool:\n        asyncPool:\n          corePoolSize: 3 \n          maxPoolSize: 6\n          queue: 1000\n          keepAliveSeconds: 60 \n        default-pool:\n          corePoolSize: 3 \n          maxPoolSize: 6 \n          queue: 1000 \n          keepAliveSeconds: 60 \n    datasource:\n      dynamic:\n        hikari:\n          max-pool-size: 200\n          min-idle: 20      # 默认最新空闲数\n    redisson: \n      threads: 3\n      nettyThreads: 3\n    rabbitmq: \n      coreThreadNum: 3\n  metadata:\n    mode: site #元数据访问模式(site/local/server)，默认ERP站点模式\n','5c3a9f3054f86dcef7fc1f105064c600','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,''),
('upgrade-java.yml','DEFAULT_GROUP','skyline:\r\n  cache:\r\n    cacheMode: 0     # 缓存模式 0 Memory 1 Redis\r\n  lockOptions:\r\n    lockMode: 0      # 0内存，1redis\r\n    tryCount: 10     # 重试次数\r\n    expire: 30       # 锁过期时间（秒）\r\n  storage:\r\n    bucketName:  \'jmpt-gxfw\'  # 桶名称，不能使用大写字线和下划线\r\n  rdc:\r\n    reportSvc:\r\n      expire: 30                              #请求超时时间（秒）\r\n      url: \'https://devcloud.mypaas.com\'      # 执行结果上报地址\r\n  environment:\r\n    env: \'test\'                  # 环境标识 prod：生产，beta:预发，test：测试，dev：开发\r\n    envName: \'信创-专业版演练第二轮20230628-idc\'          # 容器云环境名\r\n    envId: \'xc-protesttwo20230628-idc\'              # 容器云环境编码\r\n    customerId: \'129ae3db-c617-453e-a0eb-1ff4f6e986cf\'    # 容器云客户ID\r\n    hostAddress: \'http://upgrade-java.upgrade-java-test.svc\' # java更新服务访问地址 envcode 对应容器云 MKS_ClientEnvTag\r\n','8f68553fc9c120b151dec4d0644cb4c9','2023-07-17 15:28:57','2023-07-17 15:28:57',NULL,'**********','','skyline',NULL,NULL,NULL,'yaml',NULL,'');



