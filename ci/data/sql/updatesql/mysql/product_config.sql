
CREATE TABLE IF NOT EXISTS `product_config` (
  `id` VARCHAR(63) NOT NULL COMMENT '配置ID',
  `app_code` VARCHAR(63) DEFAULT NULL COMMENT '应用code',
  `app_key` VARCHAR(127) DEFAULT NULL COMMENT '应用key',
  `url` VARCHAR(511) DEFAULT NULL COMMENT '应用服务外网地址',
  `inside_url` VARCHAR(511) DEFAULT NULL COMMENT '应用服务内网地址',
  `container_url` VARCHAR(511) DEFAULT NULL COMMENT '应用服务服务地址',
  `creator` VARCHAR(127) DEFAULT NULL COMMENT '创建人',
  `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` VARCHAR(127) DEFAULT NULL COMMENT '最后修改人',
  `modified_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=COMPACT;


INSERT  INTO `product_config`(`id`,`app_code`,`app_key`,`url`,`inside_url`,`container_url`,`creator`,`created_time`,`modifier`,`modified_time`)
select '9d2215e5-83e0-4c6c-ac1d-c4d12d57e590','0301','jcsj_java','http://jcsj:8300','http://jcsj:8300','http://jcsj:8300',NULL,'2023-06-09 18:30:47',NULL,'2023-06-19 16:35:14'
from dual where not EXISTS (select * from product_config where id='9d2215e5-83e0-4c6c-ac1d-c4d12d57e590');

INSERT  INTO `product_config`(`id`,`app_code`,`app_key`,`url`,`inside_url`,`container_url`,`creator`,`created_time`,`modifier`,`modified_time`)
select  'd287d981-eded-4ab6-929f-67b3121e1886','4200','gptbuilder','http://javaservice:8080','http://javaservice:8080','http://javaservice:8080',NULL,'2023-06-09 19:05:48',NULL,'2023-06-19 16:35:00'
from dual where not EXISTS (select * from product_config where id='d287d981-eded-4ab6-929f-67b3121e1886');

INSERT  INTO `product_config`(`id`,`app_code`,`app_key`,`url`,`inside_url`,`container_url`,`creator`,`created_time`,`modifier`,`modified_time`)
select 'f97ff2f7-4ed2-4d9d-90ca-2fd41b615b0d','0000','platform',NULL,NULL,NULL,NULL,'2023-06-09 18:03:17',NULL,'2023-06-09 18:03:17'
from dual where not EXISTS (select * from product_config where id='f97ff2f7-4ed2-4d9d-90ca-2fd41b615b0d');

