<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-07-04T09:33:30.7659815+08:00" modifiedBy="彭宗一" modifiedOn="2025-06-18T11:47:50.51107+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dc9bc9-4fb3-422d-8a48-1a1550235cc2" functionGuid="08dc8b75-8664-4aac-8fa3-89702bd0b9ac" application="4200" componentName="生成多样性" componentId="GenerateDiversity" componentType="0" type="3" kind="General" openness="none" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <layout><![CDATA[<div class="generate-diversity">
  <template v-if="configData.config">
     <div class="diversity_row" v-if="showThinking">
      <div class="miniux-form-label-title diversity_title" label-width="100px">深度思考</div>
      <div class="diversity_control">
        <hc-radio-group v-model="enableThinking" @change="onThinkingChange">
          <hc-radio :label="0" :disabled="disabled" key="enableThinking_0">否</hc-radio>
          <hc-radio :label="1" :disabled="disabled" key="enableThinking_1">是</hc-radio>
        </hc-radio-group>
      </div>
    </div>
    <div class="diversity_row" v-if="showThinking && enableThinking">
      <div class="miniux-form-label-title diversity_title" label-width="100px">
        思考长度
        <hc-popover :min-width="38" :max-width="228" position="bottom">
          <template #content>
            <span v-html="thinking_budget_tips"></span>
          </template>
          <span class="mp-icon-help el-tooltip miniux-form-float-icon"></span>
        </hc-popover>
      </div>
      <div class="diversity_control" :class="{'disabled': disabled}">
        <!-- <hc-spinner
          :min="0"
          :rounding="0"
          :decimal-places="0"
          :disabled="disabled"
          :value="ExecutionSetting.thinking_budget"
          @change="(v) => onChange({data: v.value}, 'thinking_budget')"
        ></hc-spinner> -->
        <slider-component
          :max-value="38912"
          :min-value="0"
          :rounding="0"
          :slider="ExecutionSetting.thinking_budget"
          @change="(v) => onChange(v, 'thinking_budget')"
          @custom="onCustom('thinking_budget')"
        ></slider-component>
      </div>
    </div>
    <div class="diversity_row" v-if="isShow">
      <div class="miniux-form-label-title diversity_title" label-width="100px">生成多样性</div>
      <div class="diversity_control">
        <hc-radio-group v-model="GenerationDiversity" @change="onSelectChange">
          <hc-radio v-for="(item, key) in diversityOptions" :label="item.value" :disabled="disabled" :key="'option_'+ key">{{item.label}}</hc-radio>
        </hc-radio-group>
      </div>
    </div>
    <div class="diversity_row" v-for="(config, key) in configData.config" :key="'config_'+key">
      <div class="miniux-form-label-title diversity_title" label-width="100px">
        {{config.name || key}}
        <hc-popover :min-width="38" :max-width="228" position="bottom">
          <template #content>
            <span v-html="config.tips"></span>
          </template>
          <span v-if="config.tips" class="mp-icon-help el-tooltip miniux-form-float-icon"></span>
        </hc-popover>
      </div>
      <div class="diversity_control" :class="{'disabled': disabled}">
        <slider-component :max-value="config.maxValue" :min-value="config.minValue" :rounding="config.rounding" :slider="ExecutionSetting[key]" @change="(v) => onChange(v, key)" @custom="onCustom(key)"></slider-component>
      </div>
    </div>
  </template>
</div>]]></layout>
            <script><![CDATA[module.exports = {
  data: function () {
      return {
        model: '',
        options: [],
        GenerationDiversity: 'precise',
        ExecutionSetting: {},
        enableThinking: 0,
        thinking_budget_tips:  `启用深度思考，模型有时会输出冗长的推理过程，需要较长时间才能进行回复内容的生成，且会消耗较多 Token。
        <br/>为了解决这一问题，您可以设置【思考长度】参数来约束推理过程的最大长度。

        <br/>① 如果模型思考过程生成的 Token 数超过思考长度阈值，推理内容会进行截断并立刻开始生成最终回复内容。
        <br/>② 不同任务对深度思考的需求差异显著。例如，简单数学题可能仅需少量步骤，而复杂编程或逻辑问题需要长链条推理。通过调整思考的最大长度，模型可适配任务复杂度，例如在简单问题中缩短推理以提升效率，在难题中延长推理以提高准确性
        `,
        innnerServiceTypeEnum: null,
        innnerSupportDeepThink: null,

      }
  },
  computed: {
    diversityOptions: function () {
      return [
        {label: '精确模式', value: 'precise'},
        {label: '平衡模式', value: 'balance'},
        {label: '创意模式', value: 'creative'},
        {label: '自定义', value: 'custom'}
      ]
    },
    configData: function() {
      if (!this.model) return {}
      var model = this.model
      var item = this.options.find(function(value) {
        return value.value === model
      })
      if (!item || !item.extData || !item.extData.ExecutionSetting) return {}
      try {
        var data = $_.parseJSON(item.extData.ExecutionSetting || '{}') || {}
      } catch (err) {
        data = {}
      }
      return data
    },
    isShow: function() {
      var show = false
      try {
        var config = Object.keys(this.configData.config || {})
        if (!config.length || (config.length === 1 && config.includes('max_tokens'))) {
          show = false
        } else {
          show = true
        }
      } catch(err) {}
      return show
    },
    disabled: function() {
      return $page.getParams().mode === '3'
    },
    showThinking: function() {
      const ServiceTypeEnum = typeof this.innnerServiceTypeEnum === 'number' ? this.innnerServiceTypeEnum : $form.getData('ServiceTypeEnum')
      const SupportDeepThink = typeof this.innnerSupportDeepThink === 'number' ? this.innnerSupportDeepThink : $form.getData('SupportDeepThink')

      return ServiceTypeEnum == 0 && (SupportDeepThink == 1 || SupportDeepThink == '1')
    }
  },
  watch: {
    GenerationDiversity: function(v) {
      this.$emit('change', {GenerationDiversity: v})
    },
    ExecutionSetting: {
      deep: true,
      handler(v) {
        this.$emit('change', {ExecutionSetting: $_.toJSON(this.ExecutionSetting)})
      }
    }
  },
  methods: {
    setOptions: function(options) {
      var data = options
      if (typeof data === 'string') {
        try {
          data = $_.parseJSON($_.parseJSON(data))
        } catch (err) {}
      }
      this.options = data
    },
    modelChange: function(model) {
      this.model = model
      this.GenerationDiversity = 'precise'
      try {
        var precise = this.configData.generationDiversity.precise || {}
        if (precise) {
          this.ExecutionSetting = $_.cloneDeep(precise)
        }
      } catch (err) {
        this.ExecutionSetting = {}
      }
      
      try {
        var maxTokens = this.configData.config.max_tokens
        if (maxTokens) {
          this.ExecutionSetting.max_tokens = maxTokens.defaultValue
        }
      } catch (err) {}

      this.innnerServiceTypeEnum = $form.getData('ServiceTypeEnum')
      this.innnerSupportDeepThink = $form.getData('SupportDeepThink')

      // 重置是否开启生度思考
      this.enableThinking = 0
      this.ExecutionSetting.enable_thinking = 0
      this.ExecutionSetting.thinking_budget = 0
      
    },
    setData: function(v) {
      this.model = v.model
      this.GenerationDiversity = v.GenerationDiversity || ''
      this.innnerServiceTypeEnum = $form.getData('ServiceTypeEnum')
      this.innnerSupportDeepThink = $form.getData('SupportDeepThink')
      try {
        this.ExecutionSetting = $_.parseJSON(v.ExecutionSetting || '{}')
        this.enableThinking = this.ExecutionSetting.enable_thinking || 0

      } catch (err) {
        this.ExecutionSetting = {}
       
      }
    },
    onThinkingChange: function(val) {
      this.enableThinking = val.value
      this.onChange({data: val.value}, 'enable_thinking')
    },
    onSelectChange: function() {
      var data = {}
      try {
        var diversity = this.configData.generationDiversity[this.GenerationDiversity]
        if (diversity) {
          data = $_.cloneDeep(diversity) || {}
        }
      } catch(err) {
        data = {}
      }
      this.ExecutionSetting = {...this.ExecutionSetting, ...data}
    },
    onChange: function(v, key) {
      var data = $_.cloneDeep(this.ExecutionSetting)
      data[key] = Number(v.data || 0)
      this.ExecutionSetting = data
    },
    onCustom: function(key) {
      if (key !== 'max_tokens' && key !== 'thinking_budget' && key !== 'enable_thinking') {
        this.GenerationDiversity = 'custom'
      }
    }
  }
}]]></script>
            <style>
                <code><![CDATA[.generate-diversity {
  .diversity_row {
    height: 38px;
  }
  .diversity_title {
    width: 100px;
    padding-left: 10px;
    box-sizing: border-box;
    float: left;
  }
  .diversity_control {
    width: calc(100% - 100px);
    padding-left: 16px;
    box-sizing: border-box;
    float: left;
    &.disabled {
      pointer-events: none;
    }
  }
  .mp-icon-help {
    color: #999;
    font-size: 14px;
    cursor: pointer;
  }
  .hc-radio,
  .hc-radio-group {
    display: inline-block !important;
  }
}]]></code>
                <runtimeCode><![CDATA[.modeling-business-component--generatediversity .generate-diversity .diversity_row {
  height: 38px;
}
.modeling-business-component--generatediversity .generate-diversity .diversity_title {
  width: 100px;
  padding-left: 10px;
  box-sizing: border-box;
  float: left;
}
.modeling-business-component--generatediversity .generate-diversity .diversity_control {
  width: calc(100% - 100px);
  padding-left: 16px;
  box-sizing: border-box;
  float: left;
}
.modeling-business-component--generatediversity .generate-diversity .diversity_control.disabled {
  pointer-events: none;
}
.modeling-business-component--generatediversity .generate-diversity .mp-icon-help {
  color: #999;
  font-size: 14px;
  cursor: pointer;
}
.modeling-business-component--generatediversity .generate-diversity .hc-radio,
.modeling-business-component--generatediversity .generate-diversity .hc-radio-group {
  display: inline-block !important;
}
]]></runtimeCode>
            </style>
        </content>
        <config>
            <property><![CDATA[[]]]></property>
            <event><![CDATA[[{
  name: 'change',
  label: '数据改变',
  description: '数据改变',
  params:[]
}]]]></event>
            <interface><![CDATA[[{
  name: 'setOptions',
  label: '设置模型设置数据',
  description: '设置模型设置数据',
  params:[],
  return: ''
},{
  name: 'modelChange',
  label: '切换模型设置',
  description: '切换模型设置',
  params:[],
  return: ''
},{
  name: 'setData',
  label: '设置数据',
  description: '设置数据',
  params:[],
  return: ''
}]]]></interface>
            <slot><![CDATA[[{
  name: 'demoSlot', //插槽名称
  label: 'demo插槽', // 插槽标题
  description: 'demo使用插槽', // 插槽描述
  //插槽属性
  props: [
    {
      name: 'formData',
      label: '扩展数据',
      type: 'Object' // 参数类型：String | Number | Object | Array
    }
  ]
}]]]></slot>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents>
        <dependentComponent componentGuid="08dc9b09-cd86-4d92-8727-0118c9f649a0" dependentId="slider-component" />
    </dependentComponents>
    <apis />
    <flows />
    <langs />
    <subControls />
</businessComponent>