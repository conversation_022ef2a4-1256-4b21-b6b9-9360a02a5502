<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-10T17:35:31.7101327+08:00" modifiedBy="夏娜" modifiedOn="2024-05-13T16:45:35.7674277+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dc70d4-8934-4f28-80a8-ee7517807677" functionGuid="08dc64ca-b4b8-4c56-8225-302339e29baf" application="4200" componentName="输出参数" componentId="SkillOutput" componentType="1" type="2" kind="General" openness="none" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <script><![CDATA[module.exports = {
  props:{
    editMode: Boolean
  },
  methods: {
    getData: function () {
      var gridData = this.$grid.getData()
      return gridData
    }
  }
}]]></script>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'editMode',
  label: '编辑状态',
  designer: {
    component: 'Radio',
    props: {
      data: [{
        text: '编辑',
        value: '2'
      }, {
        text: '查看',
        value: '3'
      }],
      enableRule: true
    }
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'gridData',
  label: '列表数据',
  description: '列表数据',
  params:[{
    name: 'gridData',
    label: '表单数据',
    type: 'Array'
  }]
}]]]></event>
            <interface><![CDATA[[{
  name: 'getData',
  label: '获取列表数据',
  description: '获取列表数据',
  params:[],
  return: 'Array'
}]]]></interface>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents />
    <apis />
    <flows />
    <langs />
    <control createdOn="0001-01-01T00:00:00+08:00" modifiedOn="0001-01-01T00:00:00+08:00" metadataStatus="Product" id="SkillOutput_appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dc70d4-8936-4e72-80e5-2efc2368f717" templateId="BusinessComponent.AppGrid" reserved="0" autoHeight="0" />
    <subControls />
</businessComponent>