<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-10T17:04:11.9802616+08:00" modifiedBy="夏娜" modifiedOn="2024-05-23T15:28:33.3543098+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dc70d0-28cc-4f3f-8292-83fe158ea7ab" functionGuid="08dc64ca-b4b8-4c56-8225-302339e29baf" application="4200" componentName="技能基本信息" componentId="SkillInfo" componentType="3" type="2" kind="General" openness="none" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <script><![CDATA[module.exports = {
  props:{
    editMode: Boolean
  },
  methods: {
    getData: function () {
      var formData = this.$form.getData()
      var gridData = this.$form.getProp('subGrid_gpt_SkillQuestions_SkillGUID', 'grid').getData()
      formData.questions = $_.map(gridData, function(item, key) {
        return {question: item.question, sort: key+1}
      })
      return formData
    },
    setData: function (data) {
      this.$form.setData({
        SkillName: data.name,
        SkillCode: data.code,
        ModelInstanceCode: data.modelInstanceCode,
        OpenDialogWindow: data.openDialogWindow,
        Describe: data.description
      })
      var grid = this.$form.getProp('subGrid_gpt_SkillQuestions_SkillGUID', 'grid')
      grid.setData({data: data.questions || []})
    },
    validate: function() {
      return this.$form.validate()
    }
  }
}]]></script>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'editMode',
  label: '编辑状态',
  designer: {
    component: 'Radio',
    props: {
      data: [{
        text: '编辑',
        value: '2'
      }, {
        text: '查看',
        value: '3'
      }],
      enableRule: true
    }
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'formData',
  label: '获取表单数据',
  description: '获取表单数据',
  params:[{
    name: 'formData',
    label: '表单数据',
    type: 'Object'
  }]
}]]]></event>
            <interface><![CDATA[[{
  name: 'getData',
  label: '获取表单数据',
  description: '获取表单数据',
  params:[],
  return: 'Object'
},{
  name: 'validate',
  label: '表单数据校验',
  description: '表单数据校验',
  params:[],
  return: 'Boolean'
},{
  name: 'setData',
  label: '设置表单数据',
  description: '设置表单数据',
  params:[],
  return: 'Object'
}]]]></interface>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents />
    <apis />
    <flows />
    <langs />
    <control createdOn="0001-01-01T00:00:00+08:00" modifiedOn="0001-01-01T00:00:00+08:00" metadataStatus="Product" id="SkillInfo_appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dc70d0-28ce-454e-8549-ee988abe564f" templateId="BusinessComponent.AppForm" reserved="0" autoHeight="0" />
    <subControls>
        <subControl metadataversion="3.0" createdOn="0001-01-01T00:00:00" modifiedOn="0001-01-01T00:00:00" metadataStatus="Product" id="appGrid0" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dc7af3-c3be-4b80-82a1-4af21e6c2b43" templateId="BusinessComponent.Relevant.AppGrid" reserved="0" autoHeight="0" />
    </subControls>
</businessComponent>