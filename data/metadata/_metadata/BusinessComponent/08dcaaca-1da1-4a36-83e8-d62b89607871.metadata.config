<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-07-23T11:47:03.7028327+08:00" modifiedBy="夏娜" modifiedOn="2025-01-14T14:38:55.6871557+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dcaaca-1da1-4a36-83e8-d62b89607871" functionGuid="08dc64ca-a030-45c5-89a5-9c708ee220b0" application="4200" componentName="使用方式示例" componentId="ExapleCode" componentType="0" type="3" kind="General" openness="currentApplication" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <layout><![CDATA[<div class="exaple-code">
  <span class="icon mp-icon-copy" title="复制" @click="handleCopy"></span>
  <pre>
    <code>
      {{url}}
    </code>
  </pre>
</div>]]></layout>
            <script><![CDATA[module.exports = {
  data: function () {
    return {
      value: '',
      engineUrl: '',
      baseInfo: ''
    }
  },
  computed: {
    url: function () {
      if (!this.value || !this.engineUrl) {
        return ''
      }
      let url = this.replaceAll(this.value, '{{baseInfo}}', this.baseInfo)
      url = this.replaceAll(url, '{{baseUrl}}', this.engineUrl)
      return url
    }
  },
  created: function() {
    var $this = this
    $api.application.applicationUrlParam().then((res) => {
      if (res.engineUrl) {
        $this.engineUrl = res.engineUrl
        $form.setData('BaseUrl', res.engineUrl)
      }
      if (res.encryptData) {
        $this.baseInfo = res.encryptData
        $form.setData('BaseInfo', res.encryptData)
      }
      $form.resetChangeState()
    })
  },
  methods: {
    setValue: function (v) {
      this.value = v
    },
    handleCopy: function () {
      $util.clipboard.set(this.url).then(function() {
        $notify.success('复制成功！')
      });
    },
    replaceAll: function (str, find, replace) {  
      return str.replace(new RegExp(find, 'g'), replace);  
    }
  }
}]]></script>
            <style>
                <code><![CDATA[.exaple-code {
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid #DDD;
  position: relative;
  pre {
    white-space: pre-line;
    line-height: 20px;
  }
  .icon {
    position: absolute;
    right: 8px;
    top: 6px;
    cursor: pointer;
    &:hover {
      color: $theme.primaryColor;
    }
  }
}]]></code>
                <runtimeCode><![CDATA[.modeling-business-component--exaplecode .exaple-code {
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid #DDD;
  position: relative;
}
.modeling-business-component--exaplecode .exaple-code pre {
  white-space: pre-line;
  line-height: 20px;
}
.modeling-business-component--exaplecode .exaple-code .icon {
  position: absolute;
  right: 8px;
  top: 6px;
  cursor: pointer;
}
.modeling-business-component--exaplecode .exaple-code .icon:hover {
  color: $theme.primaryColor;
}
]]></runtimeCode>
            </style>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'editMode',
  label: '编辑状态',
  designer: {
    component: 'Radio',
    props: {
      data: [{
        text: '编辑',
        value: '2'
      }, {
        text: '查看',
        value: '3'
      }],
      enableRule: true
    }
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'formSubmit',
  label: '表单数据提交',
  description: '业务组件表单数据提交',
  params:[{
    name: 'formData',
    label: '表单数据',
    type: 'Object'
  }]
}]]]></event>
            <interface><![CDATA[[{
  name: 'setValue',
  label: '设置值',
  description: '设置值',
  params:[],
  return: ''
}]]]></interface>
            <slot><![CDATA[[{
  name: 'demoSlot', //插槽名称
  label: 'demo插槽', // 插槽标题
  description: 'demo使用插槽', // 插槽描述
  //插槽属性
  props: [
    {
      name: 'formData',
      label: '扩展数据',
      type: 'Object' // 参数类型：String | Number | Object | Array
    }
  ]
}]]]></slot>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents />
    <apis>
        <api functionCode="42000101" service="application" action="applicationUrlParam" type="0" />
    </apis>
    <flows />
    <langs />
    <subControls />
</businessComponent>