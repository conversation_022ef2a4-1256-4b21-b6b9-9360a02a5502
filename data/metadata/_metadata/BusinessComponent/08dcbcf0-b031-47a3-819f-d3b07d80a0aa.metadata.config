<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-08-15T14:08:31.397752+08:00" modifiedBy="夏娜" modifiedOn="2024-08-30T15:55:45.397832+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dcbcf0-b031-47a3-819f-d3b07d80a0aa" functionGuid="08dcafc1-1efb-4e88-8461-60f0d014ac14" application="4200" componentName="进度条组件" componentId="ProgressBarComponent" componentType="0" type="3" kind="General" openness="none" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <layout><![CDATA[<div class="progress">
  <span class="label" :title="label">{{label}}</span>
  <div class="progress-inner">
    <div class="progress-bar" :style="barStyle"></div>
  </div>
  <span class="value">{{num}}</span>
</div>]]></layout>
            <script><![CDATA[module.exports = {
  props:{
    rate: Number,
    label: String,
    bgColor: String,
    num: Number
  },
  data: function () {
    return {}
  },
  computed: {
    barStyle: function () {
      return `width: ${this.rate || 0}%;background: ${this.bgColor || '#266EFF'};`
    }
  },
  methods: {}
}]]></script>
            <style>
                <code><![CDATA[.progress {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  float: left;
  width: 100%;
  .label {
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #666666;
    display: inline-block;
    white-space: nowrap;
    float: left;
    height: 22px;
  }
  .value {
    width: 48px;
    color: #333333;
    display: inline-block;
    float: left;
  }
  + .progress {
    margin-top: 12px;
  }
}
.progress-inner {
  float: left;
  width: calc(100% - 210px);
  height: 6px;
  background: #f2f4f7;
  position: relative;
  border-radius: 3px;
  display: inline-block;
  margin: 8px 8px 8px 12px;
}
.progress-bar {
  height: 6px;
  border-radius: 3px;
  position: absolute;
  top: 0;
  left: 0;
}
]]></code>
                <runtimeCode><![CDATA[.modeling-business-component--progressbarcomponent .progress {
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  float: left;
  width: 100%;
}
.modeling-business-component--progressbarcomponent .progress .label {
  width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666666;
  display: inline-block;
  white-space: nowrap;
  float: left;
  height: 22px;
}
.modeling-business-component--progressbarcomponent .progress .value {
  width: 48px;
  color: #333333;
  display: inline-block;
  float: left;
}
.modeling-business-component--progressbarcomponent .progress + .progress {
  margin-top: 12px;
}
.modeling-business-component--progressbarcomponent .progress-inner {
  float: left;
  width: calc(100% - 210px);
  height: 6px;
  background: #f2f4f7;
  position: relative;
  border-radius: 3px;
  display: inline-block;
  margin: 8px 8px 8px 12px;
}
.modeling-business-component--progressbarcomponent .progress-bar {
  height: 6px;
  border-radius: 3px;
  position: absolute;
  top: 0;
  left: 0;
}
]]></runtimeCode>
            </style>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'editMode',
  label: '编辑状态',
  designer: {
    component: 'Radio',
    props: {
      data: [{
        text: '编辑',
        value: '2'
      }, {
        text: '查看',
        value: '3'
      }],
      enableRule: true
    }
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'formSubmit',
  label: '表单数据提交',
  description: '业务组件表单数据提交',
  params:[{
    name: 'formData',
    label: '表单数据',
    type: 'Object'
  }]
}]]]></event>
            <interface><![CDATA[[{
  name: 'handleFormSubmit',
  label: '提交数据',
  description: '表单提交数据',
  params:[],
  return: ''
}]]]></interface>
            <slot><![CDATA[[{
  name: 'demoSlot', //插槽名称
  label: 'demo插槽', // 插槽标题
  description: 'demo使用插槽', // 插槽描述
  //插槽属性
  props: [
    {
      name: 'formData',
      label: '扩展数据',
      type: 'Object' // 参数类型：String | Number | Object | Array
    }
  ]
}]]]></slot>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents />
    <apis />
    <flows />
    <langs />
    <subControls />
</businessComponent>