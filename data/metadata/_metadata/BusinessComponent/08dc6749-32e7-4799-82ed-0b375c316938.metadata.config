<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-04-28T14:05:26.4079165+08:00" modifiedBy="彭宗一" modifiedOn="2025-06-30T11:45:34.2233999+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dc6749-32e7-4799-82ed-0b375c316938" functionGuid="08dc64ca-b4b8-4c56-8225-302339e29baf" application="4200" componentName="提示词编辑器" componentId="PromptEditor" componentType="0" type="3" kind="General" openness="currentApplication" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <layout><![CDATA[<div :class="className">
  <div class="pd-input-textarea" :class="{ 'is-focus': this.editorFocus }">
  <div class="pd-input-textarea__header" v-if="allowEdit || hasTitle">
    <div class="editor_title" v-if="hasTitle">{{ title }}</div>
    <div v-if="allowEdit && allowDel" class="pd-input-textarea__header-btn" @click="handleDel">
      <span class="mp-icon-trash-3 grey" title="删除"></span>
    </div>
    <div class="pd-input-textarea__header-btn" @click="handleCopy">
      <span class="mp-icon-copy grey" title="复制"></span>
    </div>
    <div v-if="allowEdit" class="pd-input-textarea__header-btn splice" @click="handleAdd">
      <span class="mp-icon-plus-2"></span>变量
    </div>
    <div v-if="allowEdit" class="pd-input-textarea__header-btn" @click="handleChange">
      <span class="svg-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M8.00001 7.9C11.8 8.29981 14.4001 5.70016 14.1 2" stroke="#266EFF" stroke-width="1.2" stroke-linecap="round"/>
          <path d="M9.26676 5.19995L6.6001 7.86662L9.26676 10.5333" stroke="#266EFF" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M14 9.6V12.8C14 13.4627 13.4627 14 12.8 14H3.2C2.53726 14 2 13.4627 2 12.8V3.2C2 2.53726 2.53726 2 3.2 2H6.4" stroke="#266EFF" stroke-width="1.2" stroke-linecap="round"/>
        </svg>
      </span>
      模板
    </div>
    <div class="pd-input-textarea__header-btn disabled_hover splice" v-if="allowEdit && enableSample">
      <hc-switch v-model="Mode" size="mini"></hc-switch>少样本模式
    </div>
  </div>
    <div ref="editor" class="pd-input-textarea__body" :style="{minHeight: `${minHeight}px`}"></div>
  </div>
  <hc-dialog v-model="visible" title="插入变量" size="small" :toolbar="toolbar">
    <prompt-editor-select-field v-if="visible" :grid-data="treeData" @select="onSelect"></prompt-editor-select-field>
  </hc-dialog>
</div>]]></layout>
            <script><![CDATA[module.exports = {
  name: 'PromptEditor',
  props: {
    title: String,
    form: Object,
    enableSample: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请输入提示词'
    },
    allowDel: Boolean,
    minHeight: {
      type: Number,
      default: 240
    },
    maxHeight: {
      type: Number,
      default: 400
    },
  },
  data() {
    return {
      moduleLoader: null,
      visible: false,
      content: '',
      editorFocus: false,
      selectRow: null,
      inputParams: [],
      systemParam: [],
      Mode: false,
      addFieldToCurrent: null,
      setContent: null,
      editor: null,
      refreshPlaceholders: null,
      createMarkdownEditor: () => {}
    }
  },
  watch: {
    Mode: function(v) {
      this.$emit('mode', {value: v})
    },
    editorSourceMap: function(v) {
      if (this.refreshPlaceholders) {
        this.refreshPlaceholders(this.editorSourceMap, true)
      }
    },
    refreshPlaceholders: {
      handler(val) {
        if (val) {
          this.inputParams = this.grid.getData()
          this.refreshPlaceholders(this.editorSourceMap, true)
        }
      }
    }
  },
  mounted: function () {
    if (this.enableSample) {
      this.Mode = $form.getData('Mode') === 1 ? true : false
    }
    this.$nextTick(() => {
      // 更新设计器的字段
      this.inputParams = this.grid.getData()
    })
  },
  computed: {
    grid: function () {
      if (!$form) {
        return this.form.getProp('subGrid_gpt_PromptParam_PromptGUID', 'grid')
      }
      return $form.getProp('subGrid_gpt_PromptParam_PromptGUID', 'grid')
    },
    inputData: function () {
      var data = []
      this.inputParams.forEach(function (item) {
        if (!item.ParamName || !item.ParamCode) {
          return
        }
        data.push({
          name: item.ParamName,
          code: item.ParamCode,
          text: item.ParamName,
          value: item.ParamCode
        })
      })
      return data
    },
    editorSourceMap: function() {
      var data = {}
      var arr = this.systemParam.concat(this.inputData)
      arr.forEach(function(item) {
        if (item.code && (item.name || item.text)) {
          data[`$${item.value}`] = {
            id: item.value,
            text: item.name || item.text,
            name: item.name || item.text,
            label: item.name || item.text,
            code: item.code,
            value: item.value,
            title: item.name || item.text,
            type: 'variable'
          }
          data[item.value] = data[`$${item.value}`]
        }
      })
      return data
    },
    toolbar: function() {
      var $this = this
      return [
        {
          label: '取消',
          name: 'cancel'
        },
        {
          label: '确认',
          name: 'save',
          type: 'primary',
          click: function () {
            $this.handleClick()
            $this.visible = false
          }
        }
      ]
    },
    params: function () {
      return $_.values(this.editorSourceMap)
    },
    treeData: function () {
      var params = [
        { id: 'template0', text: '参数', pid: '' }
      ]
      this.inputData.forEach(function(item) {
        item.pid = item.pid || 'template0'
        params.push(item)
      })
      return params
    },
    allowEdit: function () {
      var params = $page.getParams()
      return params.mode === '1' || params.mode === '2'
    },
    className: function () {
      if (!this.allowEdit) {
        return 'textarea_disabled'
      }
      return ''
    },
    hasTitle: function () {
      return this.enableSample && this.Mode || !this.enableSample
    }
  },
  methods: {
    init() {
        try {
          if (this.editor || this.moduleLoader ) {
          } else {
            const src = `/gptbuilder/designer/assets/markdownEditor.js?v=${new Date().getTime()}`
            this.moduleLoader = import(src);
            this.$refs.editor.innerHTML = ''
            this.moduleLoader.then((res) => {
              const { createMarkdownEditor = () => {} } = res || {}
              this.createMarkdownEditor = createMarkdownEditor
              this.createEditor()
              this.$nextTick(() => {
                this.setContent(this.content)
              })
            })
          }
        } catch (e) {
          console.log(e)
        }
    },
    createEditor() {
      const { markdownEditorView, setContent, addFieldToCurrent, refreshPlaceholders } = this.createMarkdownEditor(this.$refs.editor, this.content, {
        sourceMap: this.editorSourceMap,
        placeholder: this.placeholder,
        disabled: !this.allowEdit,
        minHeight: `${this.minHeight}px`,
        maxHeight: `${this.maxHeight}px`,
        update: (v) => {
          this.update(v)
        }
      })
      this.editor = markdownEditorView
      this.setContent = setContent
      this.addFieldToCurrent = addFieldToCurrent
      this.refreshPlaceholders = refreshPlaceholders
    },
    // removeDeletedFields() {
    //   // 在同步完字段列表的时候 查询当前内容中是否有已经被删除的内容，删除变更的字段
    //   if (!this.editor) return

    //   const doc = this.editor.state.doc
    //   const changes = []

    //   for (let i = 0; i < doc.lines; i++) {
    //     const line = doc.line(i + 1)
    //     const lineText = line.text

    //     const regex = /\{\{\$(\w+)\}\}/g
    //     let match
    //     while ((match = regex.exec(lineText)) !== null) {
    //       const fieldName = match[1]
    //       if (!this.editorSourceMap[`$${fieldName}`]) {
    //         changes.push({
    //           from: line.from + match.index,
    //           to: line.from + match.index + match[0].length,
    //         })
    //       }
    //     }
    //   }

    //   if (changes.length > 0) {
    //     this.editor.dispatch({
    //       changes,
    //     })
    //   }
    // },
    update(value) {
      this.content = value
      this.$emit('change', {value: value})
    },
    handleClick() {
      var item = this.selectRow
      if (!item) return
      this.$nextTick(() => {
        this.addFieldToCurrent(item.value)
      })
    },
    getContent(text) {
      var regex = /{{\$([^{}]+)}}/g
      var content = text.replace(/[\n\r\t\f\v\u2028\u2029]/g, '')
      var tokens = []

      let match

      while ((match = regex.exec(content)) !== null) {
        var [, fieldText] = match
        tokens.push({
          code: fieldText,
        })
      }
      return tokens
    },
    handleAdd() {
      this.inputParams = this.grid.getData()
      this.selectRow = null
      this.visible = true

    },
    onSelect(row) {
      if (row.id === "template0") {
        this.selectRow = null
        return
      }
      this.selectRow = row
    },
    getData() {
      return this.content
    },
    setData(value, prohibitDelay) {
      if (prohibitDelay) {
        this.inputParams = this.grid.getData()
        this.content = value
        this.init()
        return
      }
      var $this = this
      $_.delay(function() {
        $this.inputParams = $this.grid.getData()
        $this.content = value
        $this.init()
      }, 100)
       
    },
    handleChange() {
      var $this = this
      var params = $page.getParams()
      $page.dialog({
        url: "/std/42001202/08dc8e73-b5ba-4e57-828c-c6c2bedc7bbf",
        title: "引入模板",
        height: 600,
        width: 960,
        buttons: [],
        parameters: {
          spaceGUID: params.SpaceGUID || ''
        },
        onclose: function ($e) {
            if ($e.data && $e.data.type === 'submit') {
            var data = $e.data.prompt
            var content = data && data.PromptTemplate ? data.PromptTemplate : ''
            $this.$emit('change', {value: content})
            $this.update(content)
            $this.setData(content)
            if ($this.setContent) {
              $this.setContent(content)
            } else {
              $this.setData(content)
            }
          }
        },
        showFooter: false
      })
    },
    validate() {
      this.inputParams = this.grid.getData()
      var params = this.getContent(this.content)
      var $this = this
      var isError = false
      if (params.length) {
        isError = params.some(function(item) {
          return !$this.editorSourceMap[item.code]
        })
      }
      return !isError
    },
    setMode(val) {
      this.Mode = val
    },
    handleDel() {
      this.$emit('delete')
    },
    handleCopy() {
      try {
        var content = this.content
        $util.clipboard.set(content).then(function() {
          $notify.success('复制成功！')
        })
      } catch (err) {}
    }
  }
}
]]></script>
            <style>
                <code><![CDATA[@color-primary: #266EFF;
.editor_title {
  font-size: 14px;
  font-weight: 500;
  float: left;
  display: inline-block;
}
.pd-input-textarea {
  position: relative;
  font-size: 12px;
  border: 1px solid #ddd;
  transition: border-color .2s;
  border-radius: 4px;

  &.is-focus {
    border-color: @color-primary;
  }

  &__popover {
    padding: 8px;
    margin-right: 15px;
  }

  &__empty,
  &__items {
    width: 342px;
  }

  &__empty {
    color: #d3d8e9;
    padding: 4px;
  }

  &__bar {
    position: absolute;
    right: 8px;
    bottom: 5px;
    line-height: 1.5;
    color: @color-primary;
    cursor: pointer;
    user-select: none;
    font-weight: 400;
    font-size: 12px;
    padding-left: 14px;

    span {
      position: relative;
      top: 1px;
    }

    &--empty {
      color: #b9caff;
    }
    .hc-icon-plus {
      font-size: 12px;
      font-weight: bold;
    }
  }

  &__header {
    height: 34px;
    width: 100%;
    border-bottom: 1px solid #DDDDDD;
    color: #333333;
    font-size: 14px;
    line-height: 22px;
    padding: 6px 16px;
    box-sizing: border-box;
    background: #fbfbfb;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;

    span {
      color: @color-primary;
      display: inline-block;
      width: 20px;
    }
    &.splice {
      border-left: 1px solid #DDDDDD;
      padding-left: 10px;
    }
  }
  &__header-btn {
    cursor: pointer;
    float: right;
    margin-left: 12px;
    &:hover {
      color: @color-primary;
    }
    &.del:hover {
      color: #FF4C4C;
    }
    &.disabled_hover:hover {
      color: #333;
    }
    .hc-switch {
      margin-right: 4px;
    }
    &.splice {
      border-right: 1px solid #DDDDDD;
      padding-right: 12px;
    }
    .svg-icon {
      display: inline-block;
      width: 22px;
      height: 20px;
      line-height: 28px;
      float: left;
    }
    .grey {
      color: #999 !important;
    }
  }


  .field-editor {
    &--p {
      line-height: 24px;
      color: #222;
      field:first-child {
        margin-left: 0;
      }
    }

    &--field {
      border-radius: 2px;
      line-height: 20px;
      color: #333;
      background: #EAEAEA;
      background-clip: padding-box;
      font-weight: 400;
      font-size: 12px;
      padding: 0 2px 0 8px;
      display: inline-block;
      user-select: none;
      height: 20px;

      &.ProseMirror-selectednode {
        border-color: rgba(76, 153, 254, .4);
      }
    }
    &_close {
      display: inline-block;
      width: 16px;
      line-height: 20px;
      font-size: 16px;
      margin-left: 2px;
      color: #999999;
      cursor: pointer;
      float: right;
      &:hover {
        color: @color-primary;
      }
    }
  }
  .pd-input-textarea .field-editor--field {
    border-radius: 4px;
    border: 1px solid rgba(76, 153, 254, .05);
    line-height: 22px;
    color: #266eff;
    background: rgba(76, 153, 254, .05);
    background-clip: padding-box;
    font-weight: 400;
    font-size: 12px;
    margin: 0 2px;
    cursor: pointer;
    display: inline-block;
    padding: 0 4px;
    -webkit-user-select: none;
    user-select: none;
  }
}

.textarea_disabled {
  background: #f5f5f5;
  .field-editor_close {
    display: none;
  }
  .field-editor--field {
    padding-right: 8px;
    pointer-events: none;
  }
  .field-editor--p {
    color: #999;
  }
}

.ProseMirror {
  resize: none;
  padding: 6px 12px 26px;
  line-height: 24px;
  color: #222;
  overflow: auto;

  &-focused {
    outline: 0;

    .ProseMirror-placeholder {
      display: none;
    }

    .ProseMirror-gap-field-cursor {
      display: block;
    }
  }

  &-placeholder {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    pointer-events: none;
    height: 0;
  }

  &-gap-field-cursor {
    display: none;
    pointer-events: none;
    position: absolute;
  }

  &-gap-field-cursor:after {
    content: "";
    display: block;
    position: absolute;
    top: 4px;
    width: 7px;
    height: 14px;
    left: -8px;
    border-left: 1px solid black;
    animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
  }

  @keyframes ProseMirror-cursor-blink {
    to {
      visibility: hidden;
    }
  }
}

.select-field_tip {
  width: 100%;
  padding: 12px 20px 8px 20px;
  border-radius: 3px 3px 0 0;
  border: 1px solid #EAEAEA;
  background: #FFF;
  display: inline-block;
  border-bottom: 0;
  margin-bottom: -6px;
  box-sizing: border-box;
  span {
    float: left;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: #999999;
    text-overflow: ellipsis;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  >span + span {
    margin-left: 4px;
  }
  .select-field_icon {
    font-size: 14px;
  }
}]]></code>
                <runtimeCode><![CDATA[.modeling-business-component--prompteditor .editor_title {
  font-size: 14px;
  font-weight: 500;
  float: left;
  display: inline-block;
}
.modeling-business-component--prompteditor .pd-input-textarea {
  position: relative;
  font-size: 12px;
  border: 1px solid #ddd;
  transition: border-color 0.2s;
  border-radius: 4px;
}
.modeling-business-component--prompteditor .pd-input-textarea.is-focus {
  border-color: #266EFF;
}
.modeling-business-component--prompteditor .pd-input-textarea__popover {
  padding: 8px;
  margin-right: 15px;
}
.modeling-business-component--prompteditor .pd-input-textarea__empty,
.modeling-business-component--prompteditor .pd-input-textarea__items {
  width: 342px;
}
.modeling-business-component--prompteditor .pd-input-textarea__empty {
  color: #d3d8e9;
  padding: 4px;
}
.modeling-business-component--prompteditor .pd-input-textarea__bar {
  position: absolute;
  right: 8px;
  bottom: 5px;
  line-height: 1.5;
  color: #266EFF;
  cursor: pointer;
  user-select: none;
  font-weight: 400;
  font-size: 12px;
  padding-left: 14px;
}
.modeling-business-component--prompteditor .pd-input-textarea__bar span {
  position: relative;
  top: 1px;
}
.modeling-business-component--prompteditor .pd-input-textarea__bar--empty {
  color: #b9caff;
}
.modeling-business-component--prompteditor .pd-input-textarea__bar .hc-icon-plus {
  font-size: 12px;
  font-weight: bold;
}
.modeling-business-component--prompteditor .pd-input-textarea__header {
  height: 34px;
  width: 100%;
  border-bottom: 1px solid #DDDDDD;
  color: #333333;
  font-size: 14px;
  line-height: 22px;
  padding: 6px 16px;
  box-sizing: border-box;
  background: #fbfbfb;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header span {
  color: #266EFF;
  display: inline-block;
  width: 20px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header.splice {
  border-left: 1px solid #DDDDDD;
  padding-left: 10px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn {
  cursor: pointer;
  float: right;
  margin-left: 12px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn:hover {
  color: #266EFF;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn.del:hover {
  color: #FF4C4C;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn.disabled_hover:hover {
  color: #333;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn .hc-switch {
  margin-right: 4px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn.splice {
  border-right: 1px solid #DDDDDD;
  padding-right: 12px;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn .svg-icon {
  display: inline-block;
  width: 22px;
  height: 20px;
  line-height: 28px;
  float: left;
}
.modeling-business-component--prompteditor .pd-input-textarea__header-btn .grey {
  color: #999 !important;
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor--p {
  line-height: 24px;
  color: #222;
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor--p field:first-child {
  margin-left: 0;
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor--field {
  border-radius: 2px;
  line-height: 20px;
  color: #333;
  background: #EAEAEA;
  background-clip: padding-box;
  font-weight: 400;
  font-size: 12px;
  padding: 0 2px 0 8px;
  display: inline-block;
  user-select: none;
  height: 20px;
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor--field.ProseMirror-selectednode {
  border-color: rgba(76, 153, 254, 0.4);
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor_close {
  display: inline-block;
  width: 16px;
  line-height: 20px;
  font-size: 16px;
  margin-left: 2px;
  color: #999999;
  cursor: pointer;
  float: right;
}
.modeling-business-component--prompteditor .pd-input-textarea .field-editor_close:hover {
  color: #266EFF;
}
.modeling-business-component--prompteditor .pd-input-textarea .pd-input-textarea .field-editor--field {
  border-radius: 4px;
  border: 1px solid rgba(76, 153, 254, 0.05);
  line-height: 22px;
  color: #266eff;
  background: rgba(76, 153, 254, 0.05);
  background-clip: padding-box;
  font-weight: 400;
  font-size: 12px;
  margin: 0 2px;
  cursor: pointer;
  display: inline-block;
  padding: 0 4px;
  -webkit-user-select: none;
  user-select: none;
}
.modeling-business-component--prompteditor .textarea_disabled {
  background: #f5f5f5;
}
.modeling-business-component--prompteditor .textarea_disabled .field-editor_close {
  display: none;
}
.modeling-business-component--prompteditor .textarea_disabled .field-editor--field {
  padding-right: 8px;
  pointer-events: none;
}
.modeling-business-component--prompteditor .textarea_disabled .field-editor--p {
  color: #999;
}
.modeling-business-component--prompteditor .ProseMirror {
  resize: none;
  padding: 6px 12px 26px;
  line-height: 24px;
  color: #222;
  overflow: auto;
}
.modeling-business-component--prompteditor .ProseMirror-focused {
  outline: 0;
}
.modeling-business-component--prompteditor .ProseMirror-focused .ProseMirror-placeholder {
  display: none;
}
.modeling-business-component--prompteditor .ProseMirror-focused .ProseMirror-gap-field-cursor {
  display: block;
}
.modeling-business-component--prompteditor .ProseMirror-placeholder {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  pointer-events: none;
  height: 0;
}
.modeling-business-component--prompteditor .ProseMirror-gap-field-cursor {
  display: none;
  pointer-events: none;
  position: absolute;
}
.modeling-business-component--prompteditor .ProseMirror-gap-field-cursor:after {
  content: "";
  display: block;
  position: absolute;
  top: 4px;
  width: 7px;
  height: 14px;
  left: -8px;
  border-left: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}
@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}
.modeling-business-component--prompteditor .select-field_tip {
  width: 100%;
  padding: 12px 20px 8px 20px;
  border-radius: 3px 3px 0 0;
  border: 1px solid #EAEAEA;
  background: #FFF;
  display: inline-block;
  border-bottom: 0;
  margin-bottom: -6px;
  box-sizing: border-box;
}
.modeling-business-component--prompteditor .select-field_tip span {
  float: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  color: #999999;
  text-overflow: ellipsis;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.modeling-business-component--prompteditor .select-field_tip > span + span {
  margin-left: 4px;
}
.modeling-business-component--prompteditor .select-field_tip .select-field_icon {
  font-size: 14px;
}
]]></runtimeCode>
            </style>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'title',
  label: '编辑器标题',
  designer: {
    component: 'Input',
    props: {}
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'change',
  label: '提示词数据改变',
  description: '提示词数据改变'
},
{
  name: 'mode',
  label: '少样本模式数据改变',
  description: '少样本模式数据改变'
}]]]></event>
            <interface><![CDATA[[{
  name: 'setData',
  label: '设置提示词数据',
  description: '设置提示词数据',
  params:[],
  return: ''
},
{
  name: 'validate',
  label: '校验参数',
  description: '校验参数',
  params:[],
  return: ''
},
{
  name: 'setMode',
  label: '设置少样本模式',
  description: '设置少样本模式',
  params:[],
  return: ''
}]]]></interface>
            <slot><![CDATA[[{
  name: 'demoSlot', //插槽名称
  label: 'demo插槽', // 插槽标题
  description: 'demo使用插槽', // 插槽描述
  //插槽属性
  props: [
    {
      name: 'formData',
      label: '扩展数据',
      type: 'Object' // 参数类型：String | Number | Object | Array
    }
  ]
}]]]></slot>
        </config>
    </componentInfo>
    <childComponents>
        <childComponent componentGuid="25a688f2-52b2-4ab7-a0eb-a591cc25f3d1" componentName="选择字段弹框" componentId="PromptEditorSelectField">
            <componentInfo>
                <content>
                    <layout><![CDATA[<div class="select-field">
  <div class="select-field_tip">
    <span class="mp-icon-information select-field_icon"></span>
    <span>插入前置步骤的输出数据作为变量</span>
  </div>
  <hc-grid id-field="id" parent-field="pid" :data="data" :show-refresh="false" :show-index="true" @row-click="onSelectChange">
    <hc-grid-columns tree-column="text">
      <hc-grid-column label="字段" field="text">
        <template slot-scope="scope">
          {{scope.row.text}}{{scope.row.code ? '（' + scope.row.code + '）' : ''}}
        </template>
      </hc-grid-column>
    </hc-grid-columns>
  </hc-grid>
</div>
]]></layout>
                    <script><![CDATA[module.exports = {
  props: {
    gridData: Object
  },
  data: function () {
    return {}
  },
  computed: {
    data: function () {
      return this.gridData || {}
    }
  },
  methods: {
    onSelectChange(event) {
      this.$emit('select', event.row)
    }
  }
}]]></script>
                    <style>
                        <code><![CDATA[.select-field {
  .fc-table-body {
    height: 385px;
    overflow-y: auto;
  }
}]]></code>
                        <runtimeCode><![CDATA[.modeling-business-component--prompteditor .select-field .fc-table-body {
  height: 385px;
  overflow-y: auto;
}
]]></runtimeCode>
                    </style>
                </content>
            </componentInfo>
        </childComponent>
    </childComponents>
    <childModules />
    <dependentScripts />
    <dependentUrls>
        <dependentUrl value="/std/42001202/08dc8e73-b5ba-4e57-828c-c6c2bedc7bbf" />
    </dependentUrls>
    <dependentResources />
    <dependentPackages />
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents />
    <apis />
    <flows />
    <langs />
    <subControls />
</businessComponent>