<?xml version="1.0" encoding="utf-8"?>
<businessComponent xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-07-19T11:16:46.0134975+08:00" modifiedBy="彭宗一" modifiedOn="2025-06-09T23:08:49.9424545+08:00" metadataStatus="Product" deviceType="default" componentGuid="08dca7a1-388d-438c-8d1b-7f0eec022fd1" functionGuid="08dc64ca-b4b8-4c56-8225-302339e29baf" application="4200" componentName="提示词样本" componentId="EditorSample" componentType="0" type="3" kind="General" openness="currentApplication" componentIcon="" exposed="false" extendMode="0">
    <componentInfo>
        <content>
            <layout><![CDATA[<div class="sample" v-if="visible" ref="refSample" :class="{dragging: dragging, 'enable-editing': allowEdit}">
  <div class="sample-editor" v-for="(item, key) in messages" :key="'message_'+refreshKey+key">
    <prompt-editor 
      placeholder="请输入" 
      :allow-del="allowDel" 
      :ref="'editor'+key" 
      :enable-sample="false" 
      :title="getTitle(item.role, key+1)" 
      :form="form"
      :minHeight="80"
      :maxHeight="400" 
      @change="(value) => handleChange(key, value)" 
      @delete="handleDel(key)"
    ></prompt-editor>
    <div class="handle" v-if="allowEdit">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.40039 3.2C4.40039 3.86274 4.93765 4.4 5.60039 4.4C6.26313 4.4 6.80039 3.86274 6.80039 3.2C6.80039 2.53726 6.26313 2 5.60039 2C4.93765 2 4.40039 2.53726 4.40039 3.2ZM9.20039 3.2C9.20039 3.86274 9.73765 4.4 10.4004 4.4C11.0631 4.4 11.6004 3.86274 11.6004 3.2C11.6004 2.53726 11.0631 2 10.4004 2C9.73765 2 9.20039 2.53726 9.20039 3.2ZM5.60039 9.2C4.93765 9.2 4.40039 8.66274 4.40039 8C4.40039 7.33726 4.93765 6.8 5.60039 6.8C6.26313 6.8 6.80039 7.33726 6.80039 8C6.80039 8.66274 6.26313 9.2 5.60039 9.2ZM9.20039 8C9.20039 8.66274 9.73765 9.2 10.4004 9.2C11.0631 9.2 11.6004 8.66274 11.6004 8C11.6004 7.33726 11.0631 6.8 10.4004 6.8C9.73765 6.8 9.20039 7.33726 9.20039 8ZM5.60039 14C4.93765 14 4.40039 13.4627 4.40039 12.8C4.40039 12.1373 4.93765 11.6 5.60039 11.6C6.26313 11.6 6.80039 12.1373 6.80039 12.8C6.80039 13.4627 6.26313 14 5.60039 14ZM9.20039 12.8C9.20039 13.4627 9.73765 14 10.4004 14C11.0631 14 11.6004 13.4627 11.6004 12.8C11.6004 12.1373 11.0631 11.6 10.4004 11.6C9.73765 11.6 9.20039 12.1373 9.20039 12.8Z" fill="#999999"/>
      </svg>
    </div>
  </div>
  <div class="sample-add_box">
    <hc-popover trigger="hover" position="bottom" :show-arrow="false" min-width="120px" :content-style="{'margin-top': '0px', 'padding': '2px 12px'}">
      <div class="sample-add" v-if="allowEdit">
        <span class="icon mp-icon-plus-2"></span>添加对话
      </div>
      <template #content>
        <div class="sample-item" @click="handleAdd('user')">
          用户提问
        </div>
        <div class="sample-item" @click="handleAdd('assistant')">
          AI回复
        </div>
      </template>
    </hc-popover>
  </div>
</div>]]></layout>
            <script><![CDATA[var $win = this
module.exports = {
  props:{},
  data: function () {
    return {
      defaultData: [
        {
          role: 'user',
          content: ''
        },
        {
          role: 'assistant',
          content: ''
        }
      ],
      messages: [],
      visible: true,
      dragging: false,
      refreshKey: 0
    }
  },
  computed: {
    form: function () {
      return $form
    },
    allowDel: function () {
      return this.messages.length > 1
    },
    allowEdit: function () {
      var params = $page.getParams()
      return params.mode === '1' || params.mode === '2'
    }
  },
  watch: {
    messages: {
      handler(val) {
        this.$emit('change', {value: this.getData()})
      }
    }
  },
  mounted: function () {
    this.onSort()
    var defaultData = $form.getData('Mode') === 1 ? this.defaultData : []
    try {
      var data = $_.parseJSON($form.getData('MessageContent')) || defaultData
    } catch(err) {
      data = defaultData
    }
    this.messages = data
    this.setData()
  },
  methods: {
    onSort: function() {
      var _this = this
      new $3rd.Sortable(this.$refs.refSample, {
        animation: 150,
        handle: '.handle',
        onStart: function () {
          _this.dragging = true
        },
        onEnd: function (evt) {
          var data = _this.messages
          var item = data[evt.oldIndex]
          data.splice(evt.oldIndex, 1)
          data.splice(evt.newIndex, 0, item)
          _this.messages = $_.cloneDeep(data)
          _this.dragging = false
          _this.setData(true)
        }
      })
    },
    setData: function(prohibitDelay) {
      this.refreshKey += 1
      var $this = this
      this.$nextTick(function () {
        $this.messages.forEach(function(item, key) {
          var editor = $this.$refs['editor'+key]
          if (editor && editor.length && editor[0].setData) {
            editor[0].setData(item.content, !!prohibitDelay)
          }
        })
      })
    },
    getData: function() {
      const data = $_.cloneDeep(this.messages)
      data.forEach(function(item, key) {
        item.Index = key + 1
      })
      return data
    },
    handleAdd: function(role) {
      let key = this.messages.length
      if (key >= 10) {
        $notify.warning('样本数据最多支持10条！')
        return
      }
      this.messages.push({
        role: role,
        content: ''
      })
      this.setData(true)
    },
    getTitle: function(role, key) {
      var text = ''
      var _i = 0
      var currentI = 0
      this.messages.some(function (item, k) {
        if (item.role === 'user') {
          _i += 1
        }
        if ((k + 1) === key) {
          currentI = _i
          return true
        }
        return false
      })
      if (role === 'user') {
        text = '用户提问_' + currentI
      }
      if (role === 'assistant') {
        text = currentI < 1 ? 'AI回复' : 'AI回复_' + (currentI)
      }
      return text + ' #' + role
    },
    handleChange: function(key, value) {
      var data = $_.cloneDeep(this.messages)
      var item = data[key]
      if (item) {
        item.content = value.value
      }
      this.messages = data
    },
    handleDel: function(key) {
      var $this = this
      $notify.confirm('删除确认', '请确认是否删除当前数据？', 'warn').then(
        function(){
          $this.messages.splice(key, 1)
          $this.setData()
        },
        function(){}
      );
    },
    setVisible: function(v) {
      var content = $form.getData('MessageContent')
      if (this.visible === v && content && content !== '[]') {
        return
      }
      this.visible = v
      if (v) {
        this.$nextTick(this.onSort)
      }
      this.messages = v ? this.defaultData : []
      this.setData()
    }
  }
}]]></script>
            <style>
                <code><![CDATA[@color-primary: #266EFF;
.sample-add {
  display: inline-block;
  cursor: pointer;
  .icon {
    color: @color-primary;
    width: 20px;
  }
}
.sample-item {
  line-height: 30px;
  color: #333;
  font-size: 13px;
  cursor: pointer;
  &:hover {
    color: @color-primary;
  }
}
.sample-editor {
  margin-top: 12px;
}
.sample-add_box {
  margin-top: 8px;
}
.sample.enable-editing .sample-editor {
  position: relative;
  .handle {
    position: absolute;
    top: 4px;
    left: 8px;
    display: inline-block;
    cursor: all-scroll;
  }
  .pd-input-textarea__header {
    padding-left: 32px;
  }
}
.sample.dragging {
  .pd-input-textarea {
    pointer-events: none;
  }
}]]></code>
                <runtimeCode><![CDATA[.modeling-business-component--editorsample .sample-add {
  display: inline-block;
  cursor: pointer;
}
.modeling-business-component--editorsample .sample-add .icon {
  color: #266EFF;
  width: 20px;
}
.modeling-business-component--editorsample .sample-item {
  line-height: 30px;
  color: #333;
  font-size: 13px;
  cursor: pointer;
}
.modeling-business-component--editorsample .sample-item:hover {
  color: #266EFF;
}
.modeling-business-component--editorsample .sample-editor {
  margin-top: 12px;
}
.modeling-business-component--editorsample .sample-add_box {
  margin-top: 8px;
}
.modeling-business-component--editorsample .sample.enable-editing .sample-editor {
  position: relative;
}
.modeling-business-component--editorsample .sample.enable-editing .sample-editor .handle {
  position: absolute;
  top: 4px;
  left: 8px;
  display: inline-block;
  cursor: all-scroll;
}
.modeling-business-component--editorsample .sample.enable-editing .sample-editor .pd-input-textarea__header {
  padding-left: 32px;
}
.modeling-business-component--editorsample .sample.dragging .pd-input-textarea {
  pointer-events: none;
}
]]></runtimeCode>
            </style>
        </content>
        <config>
            <property><![CDATA[[{
  name: 'editMode',
  label: '编辑状态',
  designer: {
    component: 'Radio',
    props: {
      data: [{
        text: '编辑',
        value: '2'
      }, {
        text: '查看',
        value: '3'
      }],
      enableRule: true
    }
  }
}]]]></property>
            <event><![CDATA[[{
  name: 'change',
  label: '数据改变',
  description: '数据改变',
  params:[]
}]]]></event>
            <interface><![CDATA[[{
  name: 'setVisible',
  label: '设置显示隐藏',
  description: '设置显示隐藏',
  params:[],
  return: ''
}]]]></interface>
            <slot><![CDATA[[{
  name: 'demoSlot', //插槽名称
  label: 'demo插槽', // 插槽标题
  description: 'demo使用插槽', // 插槽描述
  //插槽属性
  props: [
    {
      name: 'formData',
      label: '扩展数据',
      type: 'Object' // 参数类型：String | Number | Object | Array
    }
  ]
}]]]></slot>
        </config>
    </componentInfo>
    <childComponents />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages>
        <dependentPackage packageId="Sortable" thirdPartyPackageId="7d7eb7e7-186e-721c-f4bf-456e383c8daf" />
    </dependentPackages>
    <dependentDataSources />
    <dependentLangs />
    <dependentComponents>
        <dependentComponent componentGuid="08dc6749-32e7-4799-82ed-0b375c316938" dependentId="prompt-editor" />
    </dependentComponents>
    <apis />
    <flows />
    <langs />
    <subControls />
</businessComponent>