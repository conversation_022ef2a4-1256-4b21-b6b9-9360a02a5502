<?xml version="1.0" encoding="utf-8"?>
<businessScript xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="胡新涛" createdOn="2025-07-24T19:48:56.2276466+08:00" modifiedBy="胡新涛" modifiedOn="2025-07-24T20:46:30.8228398+08:00" metadataStatus="Product" scriptGuid="08ddcaa8-1207-4802-81c7-902c8cfa3b37" functionGuid="08dc64ca-bbbd-4dd6-800e-756ba4e71547" application="4200" scriptName="知识库选择器公共脚本" scriptId="KnowledgeSelector" exposed="false">
    <scriptContent><![CDATA[module.exports = {
  data: [],
  clicking: false,

  click: function (v) {
    var me = this
    this.clicking = v
    $util.delayFinal(function(){
      me.clicking = false
    }, 50, 'clicking')
  },
  getData: function () {
    return this.data
  },
  load: function () {
    var data = $page.getDialogPageData()
    this.data = data.map(function (v) { return v.value })
  },
  deselect: function (key) {
    if (!this.clicking) {
      return
    }

    this.data = this.data.filter(function (id) { return id === key })

    console.log(this.data)
  },
  select: function (key) {
    if (!this.clicking) {
      return
    }

    var r = this.data.find(function (id) { return id === key })
    if (!r) {
      this.data.push(key)
    }
  }
}]]></scriptContent>
    <scriptConfig><![CDATA[[{name:"load",description:"",category:"function"},{name:"deselect",description:"",category:"function"},{name:"select",description:"",category:"function"},{name:"click",description:"",category:"function"},{name:"getData",description:"",category:"function"}]]]></scriptConfig>
    <dependentScripts />
    <dependentUrls />
    <apis />
    <flows />
    <dependentLangs />
</businessScript>