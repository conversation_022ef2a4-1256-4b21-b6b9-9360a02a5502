<?xml version="1.0" encoding="utf-8"?>
<businessScript xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-20T10:55:21.3501052+08:00" modifiedBy="万桥" modifiedOn="2024-12-26T14:34:51.4645739+08:00" metadataStatus="Product" scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" functionGuid="08dc64ca-a030-45c5-89a5-9c708ee220b0" application="4200" scriptName="工具方法" scriptId="Utility" exposed="true">
    <scriptContent><![CDATA[var $window = this
module.exports = {
  validateCode: function ($e) {
    if ($e.value.length > 64) {
      $e.isValid = false
      $e.errorText = "编码长度不能超过64"
      return 
    }
    const regex = /^[A-Za-z0-9_]*$/
    $e.isValid = regex.test($e.value)
    if(!$e.isValid) {
      $e.errorText = "编码支持使用字母、数字和下划线"
    }
    return $e.isValid
  },
  validateGridCode: function ($e, gridData) {
    var res = this.validateCode($e)
    if (res) {
      var isRepeat = gridData.some(function(item) {
        if (item.ParamGUID === $e.row.ParamGUID) return false
        return item.ParamCode === $e.value
      })
      if (isRepeat) {
        $e.isValid = false
        $e.errorText = "编码不允许重复"
      }
    }
  },
  getDoc: function (doc, type = 'img') {
    function replaceAll(str, find, replace) {  
        return str.replace(new RegExp(find, 'g'), replace);  
    }
    function removeSpecialCharacters(text = "") {
      return text.replace(/[\n\r\t\f\v\u2028\u2029]/g, "")
    }
    function customSourceSplitter(text) {
      var content = removeSpecialCharacters(text)
      var tokens = []
      var regex = /{{image:([^{}]+)}}/g // 正则表达式匹配 {{image:}} 形式的文本

      let lastIndex = 0
      let match

      while ((match = regex.exec(content)) !== null) {
        // 解析 {entity.field} 形式的字段
        var [fullMatch, fieldText] = match
        if (fullMatch && fieldText) {
          tokens.push({
            fullMatch: fullMatch,
            fieldText: fieldText
          })
        }

        lastIndex = match.index + fullMatch.length
      }

      return tokens
    }
    var res = customSourceSplitter(doc)
    var content = doc
    res.forEach(function(item, index) {
      if (type === 'text') {
        content = replaceAll(content, item.fullMatch, '图片'+ (index + 1) )
      }
      content = replaceAll(content, item.fullMatch, '<p><img style="width: 300px;" src=\"/ajax/Mysoft.Map6.Modeling.Handlers.ControlAjaxHandler/RichtextEditorGetImage?fileGuid=' + item.fieldText + '" ></img></p>')
    })
    return content
  },
  getParams: function(queryString, key) {
    var params = {}
    var pairs = queryString.split('?')
    if (pairs.length > 1) {
      pairs = pairs[1].split('&')
    
      pairs.forEach(pair => {  
          var [key, value] = pair.split('=')
          params[decodeURIComponent(key)] = decodeURIComponent(value || '')
      });
    }
    if (key) return params[key] || ''
    return params
  },
  formatDoc: function (doc) {
    var dom = $window.document.createElement('div')
    dom.innerHTML = doc
    var images = dom.querySelectorAll('img')
    var $this = this
    images.forEach(function (item) {
      if (item.src.indexOf('?fileGuid=') !== -1) {
        item.innerText = '{{image:'+ $this.getParams(item.src, 'fileGuid') +'}}'
      }
    })
    return dom.innerText
  },
  enableEdit: function () {
    $page.redirect({mode: 2})
  },
  generateGptCode: function() {
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
    const length = 15

    let code = Array.from({ length }, () => characters.charAt(Math.floor(Math.random() * characters.length)));

    code.unshift('gpt');

    return code.join('');
  },
  generateChannelCode: function (codeLength) {
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'

    var code = ''
    for (var i = 0; i < codeLength; i++) {
        var randomChar = characters.charAt(Math.floor(Math.random() * characters.length))
        code += randomChar
    }

    return code
  },
  validateSpace: function($e) {
    var filters = $e.options && $e.options.filters
    var hasSpaceGUID = false
    var _this = this
    if (filters && filters.length) {
      hasSpaceGUID = filters.some(function(item) {
        if (item.condition && item.condition.length) {
          return item.condition.some(function (condition) {
            if (condition.field === 'SpaceGUID') {
              if (condition.value && _this.queryNum > 0) {
                $api.assistant.refreshSpaceGUIDCache({
                  data: {
                    spaceGUID: condition.value
                  }
                })
              }
              return !!condition.value
            }
            return false
          })
        }
        return false
      })
    }
    if (!hasSpaceGUID) {
      $e.cancel = true
    }
    this.updateQueryNum()
  },
  queryNum: 0,
  updateQueryNum: function () {
    this.queryNum += 1
  }
}]]></scriptContent>
    <scriptConfig><![CDATA[[{name:"validateCode",description:"",category:"function"},{name:"getDoc",description:"",category:"function"},{name:"validateGridCode",description:"",category:"function"},{name:"formatDoc",description:"",category:"function"},{name:"getParams",description:"",category:"function"},{name:"enableEdit",description:"",category:"function"},{name:"generateGptCode",description:"",category:"function"},{name:"generateChannelCode",description:"",category:"function"},{name:"validateSpace",description:"",category:"function"},{name:"updateQueryNum",description:"",category:"function"}]]]></scriptConfig>
    <dependentScripts />
    <dependentUrls />
    <apis>
        <api functionCode="42000101" service="assistant" action="refreshSpaceGUIDCache" type="0" />
    </apis>
    <flows />
    <dependentLangs />
</businessScript>