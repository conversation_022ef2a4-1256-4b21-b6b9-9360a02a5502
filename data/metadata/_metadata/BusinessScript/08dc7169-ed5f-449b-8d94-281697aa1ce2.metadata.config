<?xml version="1.0" encoding="utf-8"?>
<businessScript xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-05-11T11:24:54.7723081+08:00" modifiedBy="郑越" modifiedOn="2024-05-11T11:34:47.9944721+08:00" metadataStatus="Product" scriptGuid="08dc7169-ed5f-449b-8d94-281697aa1ce2" functionGuid="08dc64ca-c4a8-45cd-8370-41dffb9f3437" application="4200" scriptName="模型服务上下文" scriptId="ModelContext" exposed="false">
    <scriptContent><![CDATA[// 请勿在 module.exports 外部申明变量、方法
module.exports = {
  /**
   * 公开方法，可以在外部引用公共脚本调用
   * 说明：
   * 公共脚本中可以通过 $ctx、$page、……使用系统内置工具函数
   * 详细参考文档： https://open.mingyuanyun.com/docs/platform/latest/%E5%BC%80%E5%8F%91%E6%8C%87%E5%8D%97/%E5%BB%BA%E6%A8%A13.0/%E5%9C%A8%E7%BA%BF%E7%BC%96%E7%A0%81/API/API%E7%AE%80%E4%BB%8B
   * $page  页面操作，如页面刷新、跳转、弹窗等操作
   * $ctx  环境信息
   * $notify  消息提示
   * $util、$_  工具函数
   */
  allModelData:[],
  setAllModelData: function (args) {
    this.allModelData = args
    // todo
    // return returnValue
  },
  getAllModelData:function(){
    return this.allModelData;
  }
}]]></scriptContent>
    <scriptConfig><![CDATA[[{name:"setAllModelData",description:"",category:"function"},{name:"getAllModelData",description:" todo",category:"function"}]]]></scriptConfig>
    <dependentScripts />
    <dependentUrls />
    <apis />
    <flows />
    <dependentLangs />
</businessScript>