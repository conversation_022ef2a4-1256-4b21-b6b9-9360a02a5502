<?xml version="1.0" encoding="utf-8"?>
<businessScript xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-17T15:16:27.4305424+08:00" modifiedBy="郑越" modifiedOn="2024-07-17T15:17:48.2399161+08:00" metadataStatus="Product" scriptGuid="08dca630-5fb8-4090-8798-ce553c9227d9" functionGuid="08dc64ca-bbbd-4dd6-800e-756ba4e71547" application="4200" scriptName="知识库问题公共脚本" scriptId="KnowledgeQuestion" exposed="true">
    <scriptContent><![CDATA[// 请勿在 module.exports 外部申明变量、方法
module.exports = {
  /**
   * 公开方法，可以在外部引用公共脚本调用
   * 说明：
   * 公共脚本中可以通过 $ctx、$page、……使用系统内置工具函数
   * 详细参考文档： https://open.mingyuanyun.com/docs/platform/latest/%E5%BC%80%E5%8F%91%E6%8C%87%E5%8D%97/%E5%BB%BA%E6%A8%A13.0/%E5%9C%A8%E7%BA%BF%E7%BC%96%E7%A0%81/API/API%E7%AE%80%E4%BB%8B
   * $page  页面操作，如页面刷新、跳转、弹窗等操作
   * $ctx  环境信息
   * $notify  消息提示
   * $util、$_  工具函数
   */
  knowledgeGUID:'',
  publicFunction: function (args) {
    // todo
    // return returnValue
  },
  getKnowledgeGUID:function(){
    return this.knowledgeGUID
  },
  setKnowledgeGUID:function(guid){
    this.knowledgeGUID = guid
  }
}]]></scriptContent>
    <scriptConfig><![CDATA[[{name:"publicFunction",description:"",category:"function"},{name:"getKnowledgeGUID",description:"",category:"function"},{name:"setKnowledgeGUID",description:"",category:"function"}]]]></scriptConfig>
    <dependentScripts />
    <dependentUrls />
    <apis />
    <flows />
    <dependentLangs />
</businessScript>