<?xml version="1.0" encoding="utf-8"?>
<appCard xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-17T07:32:30Z" modifiedBy="郑越" modifiedOn="2024-07-26T19:33:29.1698267+08:00" metadataStatus="Product" id="08dca632-9ddd-4eaf-8095-082f1bed21ea" title="分段列表" application="4200" functionPageId="08dca632-8523-4994-8a02-11cbb2a2607f">
    <businessComponents />
    <layout pageSize="20" autoHeight="false" autoLoad="true" isDisableWildcard="false" isUnionSearch="false" displayStyle="standard" pageStyle="default" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" entityName="gpt_KnowledgeFileSection" entityId="08dc73df-5a86-44ef-8666-9846cc1cdb8d" layoutType="card" multiLayout="false" showListHeader="true" showTitle="false" enableColumnAlign="false" perRowColumnCount="4" layoutMode="topBottomLayout" toolBarLeftItemDisplayMode="gridItemSelected" enableCustomSearchLayout="true" showViewSummaryData="false">
        <views>
            <view xmlAttributeId="08dca632-9ddc-4abe-8354-896d426f14e4" viewId="08dca632-9ddd-4ec0-82c9-01af8ef64b51" name="所有数据" isDefault="true" entityId="08dc73df-5a86-44ef-8666-9846cc1cdb8d">
                <dataSource keyName="KnowledgeFileSectionGUID" entity="gpt_KnowledgeFileSection" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QAxASwlBlKwwDoBhG6EDOAQwGdZHWXoAaALAAoJKky4CRMhSjRqdACKkObAEYRYnWMtUaQQ0cnTY8IQiXIhK8mrRnWsAVwBOkEAFEMTgLZauDsDObp7ePoZiJpLm0lY2CrQSZhYOAOIAqgCSiv6wSVKWEBnZEcb5MYXWcgkAsiDQbPhsDbl1DU0NpeKmBYHVdgAKbC5s8COIABYAKqTQmtpDI2NskzNzBiKR5Slx/XTxNAByvmogLrkHGMc+py5dUcmxslR2l8CkAF4gF3vvX/fbJ5VF77PZTLAAaxYXG0l3BUIwHABPQqfRBtCmE1ILnwmXwuUx2Nx+GR0R2z1sdHoLhAzXMxRy2mptOY+AZpMelUuDBpdPwhzYPm+TN5rIFQo5vV26OZfJmQtystZ8o2Rm6ZKB3MBXL2DNy2rRlPZmzKKPJwMptBqWHwpAAZqR6VlGVxrbaHU6Sib1ZzDbUbfbHfzBcLXQGPcGJd6HlKKf73UGVa1w4nSArhMQXFg/FszZq9rAAIJcXManUg2CwYQAdQmZ2+AAoAIwAXibAEogA===]]></command>
                    <fields>
                        <field name="KnowledgeFileSectionGUID" allowPopulate="false" entity="gpt_KnowledgeFileSection" field="KnowledgeFileSectionGUID" entityAlias="gpt_KnowledgeFileSection" metadataStatus="Product" />
                        <field name="SectionNumber" allowPopulate="false" entity="gpt_KnowledgeFileSection" field="SectionNumber" entityAlias="gpt_KnowledgeFileSection" metadataStatus="Product" />
                        <field name="KnowledgeFileGUID" allowPopulate="false" entity="gpt_KnowledgeFileSection" field="KnowledgeFileGUID" entityAlias="gpt_KnowledgeFileSection" metadataStatus="Product" />
                        <field name="ParagraphTitle" allowPopulate="false" entity="gpt_KnowledgeFileSection" field="ParagraphTitle" entityAlias="gpt_KnowledgeFileSection" metadataStatus="Product" />
                        <field name="Content" allowPopulate="false" entity="gpt_KnowledgeFileSection" field="Content" entityAlias="gpt_KnowledgeFileSection" metadataStatus="Product" />
                        <field name="FileName" allowPopulate="false" entity="gpt_KnowledgeFile" field="FileName" entityAlias="gpt_KnowledgeFile" metadataStatus="Product" />
                        <field name="Name" allowPopulate="false" entity="gpt_Knowledge" field="Name" entityAlias="gpt_Knowledge" metadataStatus="Product" />
                        <field name="KnowledgeGUID" allowPopulate="false" entity="gpt_KnowledgeFile" field="KnowledgeGUID" entityAlias="gpt_KnowledgeFile" metadataStatus="Product" />
                    </fields>
                    <availableFields>
                        <availableField name="Content" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Content" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="Disable" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Disable" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="整数" />
                        <availableField name="FileSourceEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileSourceEnum" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="整数" />
                        <availableField name="KnowledgeFileGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeFileGUID" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="Guid" />
                        <availableField name="Metadata" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Metadata" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ParagraphTitle" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ParagraphTitle" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="SectionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SectionNumber" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="整数" />
                        <availableField name="SectionSize" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SectionSize" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="整数" />
                        <availableField name="SectionTokens" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SectionTokens" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="整数" />
                        <availableField name="ThirdId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ThirdId" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="日期与时间" />
                        <availableField name="KnowledgeFileSectionGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeFileSectionGUID" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="日期与时间" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeFileSection" entityType="0" attributeType="时间戳" />
                        <availableField name="DocumentGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DocumentGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                        <availableField name="ExecutionSetting" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExecutionSetting" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="FileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="FileSize" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileSize" entity="gpt_KnowledgeFile" entityType="0" attributeType="长整型" />
                        <availableField name="FileSourceEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="gpt_KnowledgeFile_FileSourceEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                        <availableField name="FileType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileType" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="IndexStatusEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IndexStatusEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                        <availableField name="IsUploadImage" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsUploadImage" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                        <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                        <availableField name="QuestionGenerateEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="QuestionGenerateEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                        <availableField name="SectionTypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SectionTypeEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                        <availableField name="ThirdId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="gpt_KnowledgeFile_ThirdId" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ThirdViewURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ThirdViewURL" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_CreatedGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_CreatedName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_CreatedTime" entity="gpt_KnowledgeFile" entityType="0" attributeType="日期与时间" />
                        <availableField name="KnowledgeFileGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_KnowledgeFileGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_ModifiedGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_ModifiedName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_ModifiedTime" entity="gpt_KnowledgeFile" entityType="0" attributeType="日期与时间" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_KnowledgeFile_VersionNumber" entity="gpt_KnowledgeFile" entityType="0" attributeType="时间戳" />
                        <availableField name="Code" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Code" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="Description" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Description" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="EmbeddingModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="EmbeddingModelCode" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="IsHasQuestion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsHasQuestion" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="Name" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Name" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="SearchServicesEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="SearchServicesParams" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesParams" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="SearchServicesURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesURL" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="StatusEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StatusEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="TypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="TypeEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_CreatedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_CreatedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_CreatedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
                        <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_KnowledgeGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_ModifiedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_ModifiedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_ModifiedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_Knowledge_VersionNumber" entity="gpt_Knowledge" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dca632-9dde-4208-8428-a0bef85640ea" id="08dc73df-5a86-44ef-8666-9846cc1cdb8d" name="gpt_KnowledgeFileSection" primaryField="KnowledgeFileSectionGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                        <diagram xmlAttributeId="08dca635-9391-474c-8c51-7cdfc008e348" isCurrent="true" id="08dc73de-e215-471f-89fd-2c401e869336" name="gpt_KnowledgeFile" primaryField="KnowledgeFileGUID" parentId="08dc73df-5a86-44ef-8666-9846cc1cdb8d" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="false" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_KnowledgeFile.KnowledgeGUID" operatorType="eq" id="5123f718-ea9b-45c9-8562-89df099b29b3" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:knowledgeGUID]</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                            <diagramRelation primaryEntityName="gpt_KnowledgeFileSection" primaryFieldName="KnowledgeFileGUID" entityName="gpt_KnowledgeFile" fieldName="KnowledgeFileGUID" relation="gpt_KnowledgeFileSection.KnowledgeFileGUID = gpt_KnowledgeFile.KnowledgeFileGUID" Type="OneToMore" />
                        </diagram>
                        <diagram xmlAttributeId="08dcad48-b6e5-4b82-8ec7-e13ce6c988a2" isCurrent="true" id="08dc7306-ca78-4497-874c-1891cf5d86b7" name="gpt_Knowledge" primaryField="KnowledgeGUID" parentId="08dc73de-e215-471f-89fd-2c401e869336" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="false" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                            <diagramRelation primaryEntityName="gpt_KnowledgeFile" primaryFieldName="KnowledgeGUID" entityName="gpt_Knowledge" fieldName="KnowledgeGUID" relation="gpt_KnowledgeFile.KnowledgeGUID = gpt_Knowledge.KnowledgeGUID" Type="OneToMore" />
                        </diagram>
                    </diagrams>
                </dataSource>
                <layout idField="KnowledgeFileSectionGUID" multiSelect="true" allowSelectAll="false">
                    <cardConfigs />
                    <events />
                    <sorts>
                        <sort field="SectionNumber" title="切片顺序" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <columns>
                        <images>
                            <column id="36c1769b-2a4e-4ca9-91bd-6d814e80274c" size="small" disableIcon="true" />
                        </images>
                        <titles>
                            <column id="72f3ca21-7563-4bb9-8364-bf716eaa504f" field="customhtml_onch" fieldType="string" label="自定义HTML" fontColor="" isBold="false" displayMode="customHtml" isHidden="false" isDynamicLabel="false">
                                <events />
                                <customHtml id="380854f4-a99c-4a19-9176-67ddbf1de460">
                                    <componentInfo>
                                        <content>
                                            <layout><![CDATA[<!-- Demo HTML content -->
<div class="title" @click="clickHandler">
  <span>#{{$row.SectionNumber}} {{$row.ParagraphTitle}}</span>
</div>]]></layout>
                                            <script><![CDATA[/**
  * Demo JS
  */
module.exports = {
  data: function () {
    return {
      result: null
    }
  },
  methods: {
    /**
    * 点击事件
    */
    clickHandler: function () {
      console.log('click handler')
    }
  }
}]]></script>
                                            <style>
                                                <code><![CDATA[/* Demo CSS content */
.title {
  display: inline;
}
.title label{
  color: blue;
}]]></code>
                                                <runtimeCode><![CDATA[.vuetemplate-380854f4-a99c-4a19-9176-67ddbf1de460 {
  /* Demo CSS content */
}
.vuetemplate-380854f4-a99c-4a19-9176-67ddbf1de460 .title {
  display: inline;
}
.vuetemplate-380854f4-a99c-4a19-9176-67ddbf1de460 .title label {
  color: blue;
}
]]></runtimeCode>
                                            </style>
                                        </content>
                                    </componentInfo>
                                    <dependentResources />
                                    <dependentLangs />
                                    <apis />
                                </customHtml>
                            </column>
                        </titles>
                        <labels />
                        <descriptions>
                            <column id="aa1fe335-5136-42ba-b598-8a8cc5c8cd4b" field="Content" fieldType="string" label="描述" fontColor="" isBold="false" isHidden="false" displayMode="string">
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                            <column id="313696af-e12b-4952-8ef7-e10a8a7a1671" field="FileName" fieldType="string" label="文件名称" fontColor="" isBold="false" isHidden="true" displayMode="string">
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                            <column id="921cb48d-c619-4d91-b4b9-bc8336d3a373" field="Name" fieldType="string" label="知识库名称" fontColor="" isBold="false" isHidden="false" displayMode="string">
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                        </descriptions>
                        <indicators />
                    </columns>
                    <contentLayouts>
                        <contentLayout>
                            <list autoAlignIndicator="false" rowButtonStyle="link">
                                <rows>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <image id="9b4a554d-d8ab-4097-81df-7995b1a35003" columnId="36c1769b-2a4e-4ca9-91bd-6d814e80274c" />
                                                <rows />
                                            </column>
                                        </columns>
                                    </row>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <title id="cd4a6b0c-1cfd-41f3-b9fd-243467211134" columnId="72f3ca21-7563-4bb9-8364-bf716eaa504f" />
                                                <rows />
                                            </column>
                                        </columns>
                                    </row>
                                    <row>
                                        <columns />
                                    </row>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <rows>
                                                    <row>
                                                        <columns>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="b0f26fe6-07a3-4cce-8717-58b4e7880dd8" columnId="921cb48d-c619-4d91-b4b9-bc8336d3a373" />
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <description id="02106ca9-9bab-4e94-a327-ca82350a2291" columnId="aa1fe335-5136-42ba-b598-8a8cc5c8cd4b" />
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="1872f481-247a-43c1-b446-318801e6fa8f" columnId="313696af-e12b-4952-8ef7-e10a8a7a1671" />
                                                                <rows />
                                                            </column>
                                                        </columns>
                                                    </row>
                                                </rows>
                                            </column>
                                        </columns>
                                    </row>
                                </rows>
                            </list>
                        </contentLayout>
                    </contentLayouts>
                    <hiddens />
                </layout>
            </view>
        </views>
        <filter filterId="08dca632-9ddd-4f08-8ce8-8621e1507b23" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="19e678d4-dedf-4019-acec-932251db5359" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="421b83d1-4ec1-43ee-b5b9-fdc241079784" ref="KnowledgeFileGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="7f66988f-edce-4d55-a017-e02f2989ae4e" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="1cf319d1-2fad-4ae4-86ae-7f3ccbf14f94" ref="ParagraphTitle" />
                    <template><![CDATA[]]></template>
                    <searchType>like</searchType>
                </condition>
                <condition id="e8b051ab-7c55-4d64-a186-8b902542c3bb" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="c1ead2c2-072d-434a-aaff-c14758fa3d1d" ref="Content" />
                    <template><![CDATA[]]></template>
                    <searchType>like</searchType>
                </condition>
            </conditions>
            <components>
                <component id="989449ff-df5c-4a8c-90bc-eb0ea86bf425" name="KnowledgeFileGUID" metadataStatus="Product">
                    <label title="文档" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="KnowledgeFileGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="false" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <options />
                        <optionsDataSource>
                            <dataSource keyName="KnowledgeFileGUID" entity="gpt_KnowledgeFile" withNoLock="true" mode="1">
                                <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QAxASwgDoARLSAVwFsQNoBxAVQEkrYBDAZ1g16TFh24AaALAAoJKky4CRMpQCiADyh1opLBlDRdGeH0Gbtu/YePxpc5Omx4QhEuRAVVIAHK8mZrDefkz28k5KrioeXh7ApABeIIHe8Ulhjooubt6xEMBYdABOkCBqGIwpcYUlZRUMGQrOyu6U3gAqAJ6IyQJBHl09jRHZ0ZScGPggGsDQvNB0/OWVfRNTM3MLS/XDWS25nPzsiGBYvPicDLxEgYfHp+eX1yC7zVGtnnvv4jx9X24/V6RHIxACKdBA/CsGFYzBARXmdRWgnBkOhsIw8MRywasnC/zGnlAMD0GEGSIYgWJ0PJOKBow+FHaAAtSEULvhAiy2Rz6fsYtz2QA1UggHDsABKABkuazhaLxdK+e9cgBhIogRH4H6BdWa6CuQF4zJvEGUPVakK9QQWg34K3Ks2eW2udqkAJ9F34N2hY1NYGEigEj46v6mwlGhz+hm5ACyWHwpAAZqLtVxfoJ44mU4b047A1nk6mrYFCzn7f4Xn6RvzKGXUz7rbB6673ckZMQilhKfjwx9YABBQS9gP92AyADqzPhyQAFABGAC884AlEA]]></command>
                                <fields>
                                    <field name="FileName" allowPopulate="false" entity="gpt_KnowledgeFile" field="FileName" entityAlias="gpt_KnowledgeFile" metadataStatus="Product" />
                                    <field name="KnowledgeFileGUID" allowPopulate="false" entity="gpt_KnowledgeFile" field="KnowledgeFileGUID" entityAlias="gpt_KnowledgeFile" metadataStatus="Product" />
                                </fields>
                                <availableFields>
                                    <availableField name="DocumentGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DocumentGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                                    <availableField name="ExecutionSetting" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExecutionSetting" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(max)）" />
                                    <availableField name="FileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(512)）" />
                                    <availableField name="FileSize" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileSize" entity="gpt_KnowledgeFile" entityType="0" attributeType="长整型" />
                                    <availableField name="FileSourceEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileSourceEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                                    <availableField name="FileType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FileType" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="IndexStatusEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IndexStatusEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                                    <availableField name="IsUploadImage" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsUploadImage" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                                    <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                                    <availableField name="QuestionGenerateEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="QuestionGenerateEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                                    <availableField name="SectionTypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SectionTypeEnum" entity="gpt_KnowledgeFile" entityType="0" attributeType="整数" />
                                    <availableField name="ThirdId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ThirdId" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="ThirdViewURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ThirdViewURL" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(512)）" />
                                    <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                                    <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeFile" entityType="0" attributeType="日期与时间" />
                                    <availableField name="KnowledgeFileGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeFileGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                                    <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeFile" entityType="0" attributeType="Guid" />
                                    <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeFile" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeFile" entityType="0" attributeType="日期与时间" />
                                    <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeFile" entityType="0" attributeType="时间戳" />
                                </availableFields>
                                <fixedSortings />
                                <summaries />
                                <diagrams>
                                    <diagram xmlAttributeId="08dca633-0792-4600-8106-4e30ff0f6fe4" id="08dc73de-e215-471f-89fd-2c401e869336" name="gpt_KnowledgeFile" primaryField="KnowledgeFileGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                        <conditions>
                                            <condition field="gpt_KnowledgeFile.KnowledgeGUID" operatorType="eq" id="b0d7f1ef-d212-4c14-96d9-97d2d4768a9a" dataType="string" valueType="1" leftValueType="field">
                                                <actions />
                                                <Value xsi:type="xsd:string">[query:knowledgeGUID]</Value>
                                            </condition>
                                        </conditions>
                                        <resourceFilters />
                                        <projectInterfaceFilters />
                                    </diagram>
                                </diagrams>
                            </dataSource>
                            <extendFields>
                                <extendField displayName="文本" name="FileName" entity="gpt_KnowledgeFile" isQueryField="false" mapType="Text" />
                                <extendField displayName="值" name="KnowledgeFileGUID" entity="gpt_KnowledgeFile" isQueryField="false" mapType="Value" />
                            </extendFields>
                            <sortFields />
                        </optionsDataSource>
                    </comboBox>
                </component>
                <component id="75ac83e4-fa96-4d8f-8f96-558b35e73b1f" name="ParagraphTitle" metadataStatus="Product">
                    <label title="按标题" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <textBox field="ParagraphTitle" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                        <customProps />
                        <events />
                    </textBox>
                </component>
                <component id="74ecd5c7-5921-42ae-a5a1-39c5aec21f3d" name="Content" metadataStatus="Product">
                    <label title="按描述" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <textBox field="Content" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                        <customProps />
                        <events />
                    </textBox>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <rule>
            <configs />
            <groups />
        </rule>
        <toolbars>
            <toolbar toolbarId="08dca632-9ddd-4eea-8e2d-048b6113f476" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dca632-9ddd-4ef9-8b83-df75f1e0369a" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onselect" functionName="_appCard_select" enabled="true" metadataStatus="Product" />
            <event name="ondeselect" functionName="_appCard_deSelect" enabled="true" metadataStatus="Product" />
            <event name="onquery" functionName="_appCard_query" enabled="true" metadataStatus="Product" />
        </events>
    </layout>
    <codes>
        <code id="14eb7a82-efe6-4f22-a193-58855d4e2c10" controlId="08dca632-9ddd-4eaf-8095-082f1bed21ea" controlType="Card" controlAction="_appCard_select">
            <script><![CDATA[$util.message.trigger('knowledge-selecteds', {data: $_.toJSON($card.getSelected())})]]></script>
        </code>
        <code id="143628d5-dbca-4f88-b0a1-f68e240c0d0b" controlId="08dca632-9ddd-4eaf-8095-082f1bed21ea" controlType="Card" controlAction="_appCard_deSelect">
            <script><![CDATA[$util.message.trigger('knowledge-selecteds', {data: $_.toJSON($card.getSelected())})]]></script>
        </code>
        <code id="119c9b3a-fc0d-40ae-bf6a-89ba097b5254" controlId="08dca632-9ddd-4eaf-8095-082f1bed21ea" controlType="Card" controlAction="_appCard_query">
            <script><![CDATA[
$util.message.on('knowledge-selecteds-close', function () {
  $util.message.off('knowledge-selecteds-close')
  $page.close()
})]]></script>
        </code>
    </codes>
    <apis />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
</appCard>