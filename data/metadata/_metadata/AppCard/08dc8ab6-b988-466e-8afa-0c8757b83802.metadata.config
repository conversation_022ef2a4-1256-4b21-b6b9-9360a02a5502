<?xml version="1.0" encoding="utf-8"?>
<appCard xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-06-12T08:07:38Z" modifiedBy="万桥" modifiedOn="2024-07-22T10:14:17.330107+08:00" metadataStatus="Product" id="08dc8ab6-b988-466e-8afa-0c8757b83802" title="卡片控件" application="4200" functionPageId="08dc8ab6-b988-44d6-8fca-60aea13be14d">
    <businessComponents />
    <layout pageSize="20" autoHeight="false" autoLoad="false" isDisableWildcard="false" isUnionSearch="false" displayStyle="standard" pageStyle="none" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" dataApi="/api/42000401/KnowledgeExcuteTestController" layoutType="card" multiLayout="false" showListHeader="true" showTitle="false" enableColumnAlign="false" perRowColumnCount="4" layoutMode="topBottomLayout" enableCustomSearchLayout="true" showViewSummaryData="false">
        <views>
            <view xmlAttributeId="08dc8ab6-b988-45d6-8a8b-fb8133381e9d" viewId="08dc8ab6-b988-467e-851c-fea26fa824b4" name="所有数据" isDefault="true" entityId="00000000-0000-0000-0000-000000000000">
                <dataSource keyName="knowledgeRecordGUID" withNoLock="true" mode="2">
                    <fields />
                    <availableFields>
                        <availableField name="TopK" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="TopK" entityType="2" />
                        <availableField name="MinScore" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="MinScore" entityType="2" />
                        <availableField name="QueryInfo" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="QueryInfo" entityType="2" />
                        <availableField name="KnowledgeCodes" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeCodes" entityType="2" />
                        <availableField name="content" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="content" entityType="2" />
                        <availableField name="score" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="score" entityType="2" />
                        <availableField name="knowledgeName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="knowledgeName" entityType="2" />
                        <availableField name="fileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="fileName" entityType="2" />
                        <availableField name="icon" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="icon" entityType="2" />
                        <availableField name="sectionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="sectionNumber" entityType="2" />
                        <availableField name="knowledgeGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="knowledgeGUID" entityType="2" />
                        <availableField name="originFileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="originFileName" entityType="2" />
                        <availableField name="knowledgeFileGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="knowledgeFileGUID" entityType="2" />
                        <availableField name="fileSourceEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="fileSourceEnum" entityType="2" />
                        <availableField name="knowledgeRecordGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="knowledgeRecordGUID" entityType="2" />
                        <availableField name="title" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="title" entityType="2" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams />
                </dataSource>
                <layout idField="knowledgeRecordGUID" multiSelect="false" allowSelectAll="false">
                    <cardConfigs />
                    <events />
                    <sorts />
                    <columns>
                        <images>
                            <column id="e814f255-fd2d-49b3-8393-f84a500e0fd9" size="small">
                                <customIcon iconType="text" iconClass="" iconColor="" iconField="icon" iconShowType="default" defaultType="default" defaultField="" defaultShowType="default" />
                            </column>
                        </images>
                        <titles>
                            <column id="49acd271-e881-49e0-968d-5dde63f8cc50" field="fileName" fieldType="string" label="文件名称" fontColor="" isBold="false" displayMode="string" isHidden="false" isDynamicLabel="false">
                                <events>
                                    <event name="onbeforeopen" functionName="_appCard_vv08dc8avv_titles_fileName_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="panel" targetDisplayType="fixedWidth" type="page" url="/std/42000402/08dc8b52-ab19-48d2-8d10-953e66c86be7" id="08dc8b52-ab19-48d2-8d10-953e66c86be7" itemId="e14f1b6d-930d-46e9-87d9-e1376d86d6ab" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="800" />
                                        <option key="slipMode" value="standard" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="oid" value="knowledgeRecordGUID" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                            </column>
                        </titles>
                        <labels />
                        <descriptions>
                            <column id="e35e4851-7192-4f2f-b245-7836514cd463" field="score" fieldType="number" label="匹配度" fontColor="" isBold="false" isHidden="false" displayMode="number">
                                <number prefix="" suffix="" showThousand="true" showPercent="false" decimalPlaces="2" decimalPlacesType="0" decimalPlacesBizParam="" enableRounding="true" rounding="2" />
                                <events />
                            </column>
                            <column id="54642b33-f930-46d2-b673-41702ae8debb" field="knowledgeName" fieldType="string" label="知识库名称" fontColor="" isBold="false" isHidden="false" displayMode="string">
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                            <column id="09e34654-fd44-460e-9ce3-e07ab60c18ad" field="customhtml_4m3r" fieldType="string" label="自定义HTML" fontColor="" isBold="false" isHidden="false" displayMode="customHtml">
                                <customHtml id="505343ac-1b98-464f-a357-87e71f800436">
                                    <componentInfo>
                                        <content>
                                            <layout><![CDATA[<div class="title">
  <div class="fe-card-item-quota__label content-label">命中内容：</div>
  <div class="content">{{content}}</div>
</div>
]]></layout>
                                            <script><![CDATA[module.exports = {
  data: function () {
    return {
      result: null
    }
  },
  computed: {
    content: function () {
      return this.getDoc(this.$row.content, 'text')
    }
  },
  methods: {
    getDoc: function (doc, type = 'img') {
      function replaceAll(str, find, replace) {
        return str.replace(new RegExp(find, 'g'), replace);
      }
      function removeSpecialCharacters(text = "") {
        return text.replace(/[\n\r\t\f\v\u2028\u2029]/g, "")
      }
      function customSourceSplitter(text) {
        var content = removeSpecialCharacters(text)
        var tokens = []
        var regex = /{{image:([^{}]+)}}/g // 正则表达式匹配 {{image:}} 形式的文本

        let lastIndex = 0
        let match

        while ((match = regex.exec(content)) !== null) {
          // 解析 {entity.field} 形式的字段
          var [fullMatch, fieldText] = match
          if (fullMatch && fieldText) {
            tokens.push({
              fullMatch: fullMatch,
              fieldText: fieldText
            })
          }

          lastIndex = match.index + fullMatch.length
        }

        return tokens
      }
      var res = customSourceSplitter(doc)
      var content = doc
      res.forEach(function (item, index) {
        if (type === 'text') {
          content = replaceAll(content, item.fullMatch, '[图片' + (index + 1) + ']')
        }
        content = replaceAll(content, item.fullMatch, '<p><img style="width: 300px;" src=\"/ajax/Mysoft.Map6.Modeling.Handlers.ControlAjaxHandler/RichtextEditorGetImage?fileGuid=' + item.fieldText + '" ></img></p>')
      })
      return content
    }
  }
}]]></script>
                                            <style>
                                                <code><![CDATA[.content {
  white-space: normal;
  word-break: break-word;
  word-wrap: break-word;
  display: inline-block;
  padding-right: 100px;
  box-sizing: border-box;
}
.content-label {
  display: inline-block;
  vertical-align: top;
}
]]></code>
                                                <runtimeCode><![CDATA[.vuetemplate-505343ac-1b98-464f-a357-87e71f800436 .content {
  white-space: normal;
  word-break: break-word;
  word-wrap: break-word;
  display: inline-block;
  padding-right: 100px;
  box-sizing: border-box;
}
.vuetemplate-505343ac-1b98-464f-a357-87e71f800436 .content-label {
  display: inline-block;
  vertical-align: top;
}
]]></runtimeCode>
                                            </style>
                                        </content>
                                    </componentInfo>
                                    <dependentResources />
                                    <dependentLangs />
                                    <apis />
                                </customHtml>
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                            <column id="abbf8e5e-b185-4bd2-b025-85b0983b9cd1" field="title" fieldType="string" label="切片标题" fontColor="" isBold="false" isHidden="false" displayMode="string">
                                <data type="options">
                                    <options />
                                </data>
                                <events />
                            </column>
                        </descriptions>
                        <indicators />
                    </columns>
                    <contentLayouts>
                        <contentLayout>
                            <list autoAlignIndicator="false" rowButtonStyle="link">
                                <rows>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <image id="07214057-545a-49e7-810a-446e561b1b8f" columnId="e814f255-fd2d-49b3-8393-f84a500e0fd9" />
                                                <rows />
                                            </column>
                                        </columns>
                                    </row>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <title id="32bc8c33-be97-4732-a709-7bf884469e19" columnId="49acd271-e881-49e0-968d-5dde63f8cc50" />
                                                <rows />
                                            </column>
                                        </columns>
                                    </row>
                                    <row>
                                        <columns />
                                    </row>
                                    <row>
                                        <columns>
                                            <column align="auto" isHidden="false">
                                                <rows>
                                                    <row>
                                                        <columns>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="ca36377c-2305-4b81-94ba-d9e3cd86a5c9" columnId="e35e4851-7192-4f2f-b245-7836514cd463" />
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="f88565d3-a1f9-47fe-8c47-ed46f52614a3" columnId="54642b33-f930-46d2-b673-41702ae8debb" />
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="d0daa8b3-3336-457e-8bc9-b76a539fc499" columnId="abbf8e5e-b185-4bd2-b025-85b0983b9cd1" />
                                                                <rows />
                                                            </column>
                                                        </columns>
                                                    </row>
                                                    <row>
                                                        <columns>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <indicator id="daad9f7c-6b59-45e1-a9ef-2bfc5f535dc2" columnId="09e34654-fd44-460e-9ce3-e07ab60c18ad" />
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <rows />
                                                            </column>
                                                            <column colSpan="1" align="auto" isHidden="false">
                                                                <rows />
                                                            </column>
                                                        </columns>
                                                    </row>
                                                </rows>
                                            </column>
                                        </columns>
                                    </row>
                                </rows>
                            </list>
                        </contentLayout>
                    </contentLayouts>
                    <hiddens />
                </layout>
            </view>
        </views>
        <filter filterId="08dc8ab6-b9c6-44fc-84e2-ea88bac88ad9" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="true" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="ff5b5447-2d56-4f8a-812e-5930afe328b2" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="734c7383-e9f4-43b7-83dc-b69ce44eb4a0" ref="KnowledgeCodes" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="1b527524-d34e-4823-a1f6-bb80dd2fe8b3" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="23ad64b6-2e88-40ae-a067-8e9a3b963f27" ref="TopK" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="9343bcb6-4bef-471e-81f2-5af322f0e83a" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="d09ac3aa-bfe5-44b5-b5a8-efa6c858fc34" ref="MinScore" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="5e80a37d-c3c5-4258-9137-f419fbb2cfd2" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="ab36ee18-c85a-49fd-be6f-e0de682be899" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="037c6518-6e07-402f-a324-649f3e582d1d" name="KnowledgeCodes" metadataStatus="Product">
                    <label title="知识库" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <treeSelect field="KnowledgeCodes" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="pageHandler" disableFolderSelect="true" showFolderCheckBox="false" multiSelect="false" popupHeight="200" showLongText="false" valueFromSelect="false" allowEmpty="false" checkRecursive="false" allowClear="false" filterable="false" filterType="Current" disableCheckAll="false" isShowHoverLongText="false" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <pageHandler pageHandlerId="/api/42001301/WorkSpaceKnowledge" method="Load" extMethod="" apiSourceType="" />
                        <options />
                    </treeSelect>
                </component>
                <component id="c77ca810-5f8c-4f35-b346-a61b1577aa1b" name="TopK" metadataStatus="Product">
                    <label title="最大结果数量" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <spinner field="TopK" errorMode="default" readonlyMode="none" defaultValue="3" requirementLevel="none" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="true" showPercentage="false" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                        <customProps />
                        <events />
                    </spinner>
                </component>
                <component id="dc44dbc5-21a7-4105-97e1-b5b9d91a368a" name="MinScore" metadataStatus="Product">
                    <label title="最小匹配度" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <spinner field="MinScore" errorMode="default" readonlyMode="none" defaultValue="5" requirementLevel="none" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="true" showPercentage="false" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                        <customProps />
                        <events />
                    </spinner>
                </component>
                <component id="361ea6bc-7b77-4ea3-a2c4-d3dc01e6e75a" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" union="false" wildcard="true">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="QueryInfo" title="查询内容" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <rule>
            <configs />
            <groups />
        </rule>
        <toolbars>
            <toolbar toolbarId="08dc8ab6-b9c6-44dd-845a-ae66b93a5eaa" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc8ab6-b9c6-44ed-8751-b6d29b36ce97" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
    </layout>
    <codes>
        <code id="ac668e78-0c30-4a3a-90c2-dc740715e10a" controlId="49acd271-e881-49e0-968d-5dde63f8cc50" controlType="CardColumn" controlAction="_appCard_vv08dc8avv_titles_fileName_beforeOpen">
            <script><![CDATA[$e.options.pageData = $e.data
$e.options.title = $e.data.fileName]]></script>
        </code>
    </codes>
    <apis />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
</appCard>