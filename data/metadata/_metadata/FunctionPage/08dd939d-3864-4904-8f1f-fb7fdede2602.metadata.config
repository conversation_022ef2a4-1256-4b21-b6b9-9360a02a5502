<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="彭宗一" createdOn="2025-05-15T18:42:42.2390452+08:00" modifiedBy="彭宗一" modifiedOn="2025-05-15T19:14:22.9826708+08:00" metadataStatus="Product" functionPageId="08dd939d-3864-4904-8f1f-fb7fdede2602" name="08dd939d-3864-48e8-8772-264510f1afa5" application="4200" functionGUID="08dd929a-074d-4c55-8715-e1ba36aaadd4" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="true" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="规则" pageName="单规则表单" titleEn="Edit_8292" titleSuffix="表单页面" url="/std/********/08dd939d-3864-48e8-8772-264510f1afa5" pageType="0" functionCode="********" loadDefaultResource="false" description="单规则表单" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_8292" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dd939d-3864-4928-86b7-c346c359193c" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dd939d-3864-4942-820f-56682b20b104" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2025-05-15T19:14:22.9798795+08:00" modifiedOn="2025-05-15T19:14:22.9798786+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dd939d-3865-41cf-83a8-2c8a7d639393" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="规则表表单控件" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
</functionPage>