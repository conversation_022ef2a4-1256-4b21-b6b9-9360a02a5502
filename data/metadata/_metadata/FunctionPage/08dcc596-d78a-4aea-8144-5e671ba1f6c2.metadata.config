<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-08-26T14:18:03.1754869+08:00" modifiedBy="夏娜" modifiedOn="2024-09-06T10:47:33.1693375+08:00" metadataStatus="Product" functionPageId="08dcc596-d78a-4aea-8144-5e671ba1f6c2" name="08dcc596-d78a-4afb-8378-00b2e63d69b1" application="4200" functionGUID="08dc64ca-a030-45c5-89a5-9c708ee220b0" templateId="a3f74fdd-f05b-4392-89ab-d8a44bbf7929" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-********0000" pageHandlerExPluginTypeId="********-0000-0000-0000-********0000" entityId="********-0000-0000-0000-********0000" title="初始化事件" pageName="初始化事件" titleEn="CodingPage_0137" titleSuffix="自定义页面" url="/std/********/08dcc596-d78a-4afb-8378-00b2e63d69b1" pageType="6" functionCode="********" loadDefaultResource="false" description="初始化事件" isAllowEdit="false" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-********0000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.M********.CodingPage_0137" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells />
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <codingPageInfo>
        <content>
            <layout><![CDATA[<div class="event">
  <js-editor ref="jsEditor"></js-editor>
  <div class="event-footer">
    <hc-button class="miniux-button-inverse" v-if="visible" type="primary" @click="handleSubmit">确认</hc-button>
    <hc-button class="miniux-button-default" @click="handleCancel">取消</hc-button>
  </div>
</div>]]></layout>
            <script><![CDATA[module.exports={
    data: function () {
      return {}
    },
    computed: {
      visible: function () {
        return $page.getParams().mode !== '3'
      }
    },
    mounted: function () {
      var data = $page.getDialogPageData()
      if (typeof data.event === 'string') {
        this.$refs.jsEditor.setData(data.event)
      }
    },
    methods: {
      handleCancel: function () {
        $page.close()
      },
      handleSubmit: function () {
        var data = this.$refs.jsEditor.getData()
        $page.close({event: data})
      }
    }
}]]></script>
            <style>
                <code><![CDATA[.event {
  .event-footer {
    text-align: right;
    padding-top: 10px;
    padding-right: 14px;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
    border-top: 1px solid #eaeaea;
  }
  .hc-button {
    min-width: 80px;
  }
  .hc-button {
    margin: 0 6px;
    float: right;
  }
}]]></code>
                <runtimeCode><![CDATA[.function-page-coding--08dcc596-d78a-4aea-8144-5e671ba1f6c2 .event .event-footer {
  text-align: right;
  padding-top: 10px;
  padding-right: 14px;
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #eaeaea;
}
.function-page-coding--08dcc596-d78a-4aea-8144-5e671ba1f6c2 .event .hc-button {
  min-width: 80px;
}
.function-page-coding--08dcc596-d78a-4aea-8144-5e671ba1f6c2 .event .hc-button {
  margin: 0 6px;
  float: right;
}
]]></runtimeCode>
            </style>
        </content>
    </codingPageInfo>
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents>
        <dependentComponent componentGuid="08dcb857-9797-47d8-86e5-80c24bc2ff8d" dependentId="js-editor" />
    </dependentComponents>
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>