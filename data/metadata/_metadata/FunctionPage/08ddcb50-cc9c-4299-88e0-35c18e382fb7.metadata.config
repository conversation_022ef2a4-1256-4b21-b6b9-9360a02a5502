<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-25T15:56:44.7527948+08:00" modifiedBy="黄浩翔" modifiedOn="2025-07-28T17:08:35.6228496+08:00" metadataStatus="Product" functionPageId="08ddcb50-cc9c-4299-88e0-35c18e382fb7" name="08ddcb50-cc9c-4283-8cf1-eb8756da7ea3" application="4200" functionGUID="08dc7185-2812-495e-84cf-a3dd114408da" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="工具详情" pageName="工具详情" titleEn="Edit_1834" titleSuffix="表单页面" url="/std/********/08ddcb50-cc9c-4283-8cf1-eb8756da7ea3" pageType="0" functionCode="********" loadDefaultResource="false" description="MCP服务工具表表单" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_1834" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08ddcb50-cc9c-479f-82e4-353ccf659247" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08ddcb50-cc9c-47b5-84d3-7595f662e019" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2025-07-28T17:08:35.6206675+08:00" modifiedOn="2025-07-28T17:08:35.6206667+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08ddcb50-cc9d-4734-8d9b-8c7926c04182" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="工具详情" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams>
            <fixedParam name="toolGUID" code="toolGUID" valueSource="UrlParam" value="[query:toolGUID]" businessTypeId="********-0000-0000-0000-************" />
        </fixedParams>
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
</functionPage>