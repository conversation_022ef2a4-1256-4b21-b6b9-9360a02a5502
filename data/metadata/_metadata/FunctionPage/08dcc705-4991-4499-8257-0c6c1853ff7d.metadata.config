<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-08-28T10:01:10.2813819+08:00" modifiedBy="夏娜" modifiedOn="2024-08-29T15:07:19.5135944+08:00" metadataStatus="Product" functionPageId="08dcc705-**************-0c6c1853ff7d" name="08dcc705-4991-44aa-838e-9760d60b51b1" application="4200" functionGUID="08dc64ca-a030-45c5-89a5-9c708ee220b0" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-********0000" pageHandlerExPluginTypeId="********-0000-0000-0000-********0000" entityId="********-0000-0000-0000-********0000" title="" pageName="应用管理（GPT助手-应用管理）" titleEn="Ref_2148" titleSuffix="引入页面" url="/std/********/08dcc705-4991-44aa-838e-9760d60b51b1" pageType="7" functionCode="********" parentPage="08dc86a0-8a40-4c0a-80d8-643044d176a3" loadDefaultResource="false" description="" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" refPageId="08dcaa27-c1ad-46ea-8a63-8fa73bc86424" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-********0000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.M********.Ref_2148" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells />
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>