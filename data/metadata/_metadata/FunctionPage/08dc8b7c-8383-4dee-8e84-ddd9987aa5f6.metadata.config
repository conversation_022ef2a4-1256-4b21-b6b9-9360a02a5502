<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-06-13T15:43:28.866068+08:00" modifiedBy="夏娜" modifiedOn="2024-09-27T11:18:11.3075116+08:00" metadataStatus="Product" functionPageId="08dc8b7c-8383-4dee-8e84-ddd9987aa5f6" name="08dc8b7c-8383-4e0e-8739-c28f29f61e9f" application="4200" functionGUID="08dc8b75-8664-4aac-8fa3-89702bd0b9ac" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="true" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-********0000" pageHandlerExPluginTypeId="********-0000-0000-0000-********0000" entityId="********-0000-0000-0000-********0000" title="提示词" pageName="提示词主表表单" titleEn="Edit_5850" titleSuffix="表单页面" url="/std/********/08dc8b7c-8383-4e0e-8739-c28f29f61e9f" pageType="0" functionCode="********" parentPage="08dc8b7b-8da4-4228-883d-ec0c6a6646b9" loadDefaultResource="false" description="提示词主表表单" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-********0000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_5850" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dc8b7c-8385-456e-8fdc-87aae26e3385" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dc8b7c-8385-4588-8635-2e853750a2f6" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-09-27T11:18:11.2956655+08:00" modifiedOn="2024-09-27T11:18:11.2956613+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dc8b7c-83c0-46c3-8aaf-9772686b026b" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="提示词主表表单控件" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>