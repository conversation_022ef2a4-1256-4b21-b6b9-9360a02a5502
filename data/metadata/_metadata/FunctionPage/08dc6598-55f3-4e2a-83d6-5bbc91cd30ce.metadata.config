<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-04-26T10:26:53.3351794+08:00" modifiedBy="夏娜" modifiedOn="2024-04-26T10:26:53.5012169+08:00" metadataStatus="Product" functionPageId="08dc6598-55f3-4e2a-83d6-5bbc91cd30ce" name="08dc6598-55f3-4e3b-838b-f55241c4f5ae" application="4200" functionGUID="08dc64ca-c4a8-45cd-8370-41dffb9f3437" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-********0000" pageHandlerExPluginTypeId="********-0000-0000-0000-********0000" entityId="********-0000-0000-0000-********0000" title="模型服务列表" pageName="模型服务列表" titleEn="List_9211" titleSuffix="列表页面" url="/std/********/08dc6598-55f3-4e3b-838b-f55241c4f5ae" pageType="0" functionCode="********" loadDefaultResource="false" description="模型服务列表" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-********0000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.List_9211" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dc6598-55f4-4363-8e9f-d0a38028e0f3" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dc6598-55f4-4375-8417-121e4dc0b0a5" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="0001-01-01T00:00:00" modifiedOn="0001-01-01T00:00:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dc6598-55f7-4901-8b3b-7e8d6f84cf72" templateId="CFD8041A-0A92-4815-AB27-ECF447877C48" entityName="主实体" reserved="0" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>