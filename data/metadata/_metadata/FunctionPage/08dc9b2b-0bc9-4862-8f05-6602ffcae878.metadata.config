<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-03T14:40:37.2749506+08:00" modifiedBy="郑越" modifiedOn="2024-07-03T15:38:59.6293807+08:00" metadataStatus="Product" functionPageId="08dc9b2b-0bc9-4862-8f05-6602ffcae878" name="08dc9b2b-0bc9-4873-8fcb-721ec45e4074" application="4200" functionGUID="08dc8b4c-e4ca-45a8-8528-a91d9416ac98" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="评测数据" pageName="评测数据" titleEn="List_8675" titleSuffix="列表页面" url="/std/********/08dc9b2b-0bc9-4873-8fcb-721ec45e4074" pageType="0" functionCode="********" loadDefaultResource="false" description="评测数据" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.List_8675" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dc9b2b-0bc9-4c70-8c37-f43bd20223b1" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dc9b2b-0bc9-4c81-8a80-d3b1d3c753d9" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-07-03T15:38:59.6266477+08:00" modifiedOn="2024-07-03T15:38:59.6266467+08:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dc9b2b-0bcc-4914-862b-60a1db630787" templateId="C3189F1F-C58D-4F5F-9B72-9EBBC281D9A3" entityName="主实体" reserved="0" title="新增关键词" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>