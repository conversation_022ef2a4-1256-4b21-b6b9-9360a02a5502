<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-07-17T16:01:29.3640371+08:00" modifiedBy="万桥" modifiedOn="2024-07-20T17:39:49.3284069+08:00" metadataStatus="Product" functionPageId="08dca636-aa05-49b6-827f-9803b2173cfa" name="08dca636-aa05-49d5-8524-cec0c00a5eb9" application="4200" functionGUID="08dc9c1c-08db-41d7-857c-77c732c33550" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="true" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="工作空间" pageName="工作空间" titleEn="Edit_8013" titleSuffix="表单页面" url="/std/********/08dca636-aa05-49d5-8524-cec0c00a5eb9" pageType="0" functionCode="********" loadDefaultResource="false" description="工作空间表单" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_8013" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dca636-aa05-4a29-8b60-a7c518ccdaf9" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dca636-aa05-4a4c-83a4-cbea271fb9e4" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-07-20T17:39:49.3212951+08:00" modifiedOn="2024-07-20T17:39:49.3212915+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dca636-aa0c-428e-80b7-5a5877632cd4" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="工作空间表单控件" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>