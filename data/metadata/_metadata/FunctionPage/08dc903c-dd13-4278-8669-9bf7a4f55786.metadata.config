<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-06-19T16:50:26.4119813+08:00" modifiedBy="万桥" modifiedOn="2024-06-19T16:50:26.6266469+08:00" metadataStatus="Product" functionPageId="08dc903c-dd13-4278-8669-9bf7a4f55786" name="08dc903c-dd13-4298-8629-ebe8ed40bf5b" application="4200" functionGUID="08dc64ca-b4b8-4c56-8225-302339e29baf" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="技能引用列表查询" pageName="技能引用列表查询" titleEn="List_8117" titleSuffix="列表页面" url="/std/********/08dc903c-dd13-4298-8629-ebe8ed40bf5b" pageType="0" functionCode="********" loadDefaultResource="false" description="技能引用列表查询" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.List_8117" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dc903c-dd14-4fc4-899f-5df9d09d7582" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dc903c-dd14-4fe3-8e81-69c590e2a471" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="0001-01-01T00:00:00" modifiedOn="0001-01-01T00:00:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dc903c-dd16-4dab-8e87-662a5ddeb674" templateId="C3189F1F-C58D-4F5F-9B72-9EBBC281D9A3" entityName="主实体" reserved="0" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>