<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-11-26T17:56:31.0805866+08:00" modifiedBy="万桥" modifiedOn="2024-11-26T17:56:31.1203431+08:00" metadataStatus="Product" functionPageId="08dd0e00-9a53-4490-8007-9d72af081194" name="08dd0e00-9a53-44d3-89a4-51b24084b1f3" application="4200" functionGUID="08dd0865-0193-4a23-8d40-e5ea4704a6d0" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="空间列表查询" pageName="空间列表查询" titleEn="List_2055" titleSuffix="列表页面" url="/std/********/08dd0e00-9a53-44d3-89a4-51b24084b1f3" pageType="0" functionCode="********" loadDefaultResource="false" description="空间列表查询" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.List_2055" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dd0e00-9a54-4460-84e5-0b742a5bb794" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dd0e00-9a54-4487-80dd-c08c0016beae" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="0001-01-01T00:00:00" modifiedOn="0001-01-01T00:00:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dd0e00-9a56-4853-887f-051532307805" templateId="C3189F1F-C58D-4F5F-9B72-9EBBC281D9A3" entityName="主实体" reserved="0" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
</functionPage>