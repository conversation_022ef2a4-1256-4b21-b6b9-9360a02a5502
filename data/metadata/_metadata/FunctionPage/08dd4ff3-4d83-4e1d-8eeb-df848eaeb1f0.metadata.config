<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2025-02-18T16:07:35.3023172+08:00" modifiedBy="李昂" modifiedOn="2025-02-18T16:07:35.324903+08:00" metadataStatus="Product" functionPageId="08dd4ff3-4d83-4e1d-8eeb-df848eaeb1f0" name="08dd4ff3-4d83-4df1-89a5-acc04abbf1b9" application="4200" functionGUID="08dd2652-9bf6-4335-84f4-8ffb8614a7f2" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-********0000" pageHandlerExPluginTypeId="********-0000-0000-0000-********0000" entityId="********-0000-0000-0000-********0000" title="技能评测记录列表" pageName="技能评测记录列表" titleEn="List_2147" titleSuffix="列表页面" url="/std/********/08dd4ff3-4d83-4df1-89a5-acc04abbf1b9" pageType="0" functionCode="********" loadDefaultResource="false" description="技能评测记录列表" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-********0000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.List_2147" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dd4ff3-4d84-4862-8d85-cc0d14c11acd" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dd4ff3-4d84-488c-8ebc-207f0789d773" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="0001-01-01T00:00:00" modifiedOn="0001-01-01T00:00:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dd4ff3-4d86-4279-8102-85988680c2dd" templateId="CFD8041A-0A92-4815-AB27-ECF447877C48" entityName="主实体" reserved="0" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
</functionPage>