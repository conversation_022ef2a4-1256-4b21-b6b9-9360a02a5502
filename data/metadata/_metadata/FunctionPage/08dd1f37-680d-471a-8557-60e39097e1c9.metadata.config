<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-12-18T15:41:38.7896091+08:00" modifiedBy="万桥" modifiedOn="2024-12-18T17:54:32.0640221+08:00" metadataStatus="Product" functionPageId="08dd1f37-680d-471a-8557-60e39097e1c9" name="08dd1f37-680d-476a-86d2-55997a84e04c" application="4200" functionGUID="08dc64ca-b4b8-4c56-8225-302339e29baf" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="角色授权" pageName="角色授权" titleEn="Edit_4593" titleSuffix="表单页面" url="/std/********/08dd1f37-680d-476a-86d2-55997a84e04c" pageType="0" functionCode="********" loadDefaultResource="false" description="授权页面" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_4593" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dd1f37-680f-4ec3-81ca-98d305d64636" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dd1f37-6810-4053-800e-49ae2488f3b1" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-12-18T17:54:32.0468973+08:00" modifiedOn="2024-12-18T17:54:32.0468904+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dd1f37-681d-4539-8fd1-fce1cb4e313f" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="GPT资源角色关系表表单控件" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
</functionPage>