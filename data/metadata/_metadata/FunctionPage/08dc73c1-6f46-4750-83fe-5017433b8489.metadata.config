<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-14T10:56:21.2539663+08:00" modifiedBy="夏娜" modifiedOn="2024-06-17T15:26:39.604806+08:00" metadataStatus="Product" functionPageId="08dc73c1-6f46-4750-83fe-5017433b8489" name="08dc73c1-6f46-4762-8628-1a9f995acd25" application="4200" functionGUID="08dc64ca-bbbd-4dd6-800e-756ba4e71547" templateId="a3f74fdd-f05b-4392-89ab-d8a44bbf7929" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="00000000-0000-0000-0000-000000000000" pageHandlerExPluginTypeId="00000000-0000-0000-0000-000000000000" entityId="00000000-0000-0000-0000-000000000000" title="查看文档" pageName="查看文档" titleEn="CodingPage_4488" titleSuffix="自定义页面" url="/std/42000401/08dc73c1-6f46-4762-8628-1a9f995acd25" pageType="6" functionCode="42000401" loadDefaultResource="false" description="查看文档" isAllowEdit="false" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.M42000401.CodingPage_4488" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells />
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <codingPageInfo>
        <content>
            <layout><![CDATA[<div class="document-detail">
  <div class="document-grid">
    <hc-grid
      ref="grid"
      :data="gridData"
      id-field="sectionNumber"
      :show-refresh="false"
      @row-click="rowClick"
    >
      <hc-grid-columns>
        <hc-grid-column label="索引" field="id">
          <template slot-scope="scope">#{{scope.row.sectionNumber}}</template>
        </hc-grid-column>
        <hc-grid-column label="文档字数" field="sectionSize"></hc-grid-column>
      </hc-grid-columns>
    </hc-grid>
  </div>
  <div class="document-editor">
    <div class="document-editor_header">
      <div class="title">文档预览</div>
      <div class="toolbar">
        <template v-if="!enableEdit">
          <hc-button type="text" @click="handleEdit"><span class="icon mp-icon-edit-1"></span>编辑</hc-button>
        </template>
        <template v-else>
          <hc-button type="text" @click="handleCancel"  v-if="!isLoading">
            <span class="icon">
              <svg width="16" height="24" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.71289 2.34136C4.8926 2.55102 4.86833 2.86667 4.65867 3.04638L2.73534 4.695L4.68683 6.64654C4.88208 6.84181 4.88208 7.15839 4.68682 7.35365C4.49155 7.54891 4.17497 7.5489 3.97971 7.35364L1.64644 5.02031C1.54797 4.92183 1.49502 4.78674 1.50037 4.64757C1.50571 4.5084 1.56886 4.37777 1.6746 4.28713L4.00787 2.28713C4.21753 2.10742 4.53318 2.1317 4.71289 2.34136Z" fill="#266EFF"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M1.5 4.66675C1.5 4.39061 1.72386 4.16675 2 4.16675H9.6646C12.2275 4.16675 14.3965 6.25235 14.4964 8.81393C14.602 11.5214 12.3721 13.8334 9.6646 13.8334H3.99947C3.72332 13.8334 3.49947 13.6096 3.49947 13.3334C3.49947 13.0573 3.72332 12.8334 3.99947 12.8334H9.6646C11.8059 12.8334 13.5806 10.9918 13.4972 8.8529C13.4182 6.82808 11.6904 5.16675 9.6646 5.16675H2C1.72386 5.16675 1.5 4.94289 1.5 4.66675Z" fill="#266EFF"/>
              </svg>
            </span>
            取消
            </hc-button>
          <hc-button type="text" @click="handleSubmit" :loading="isLoading"><span v-if="!isLoading" class="icon mp-icon-save-2"></span>确认</hc-button>
        </template>
      </div>
    </div>
    <document-editor-component ref="editor"></document-editor-component>
  </div>
</div>]]></layout>
            <script><![CDATA[var $window = this
module.exports = {
  data: function () {
    return {
      content: '',
      enableEdit: false,
      gridData: [],
      isLoading: false
    }
  },
  mounted: function() {
    this.getData()
  },
  watch: {
    enableEdit: function (v) {
      this.$refs.editor.setMode(v ? 2 : 3)
    }
  },
  computed: {
    isUploadFile: function () {
      var params = $page.getParams()
      return params.source === '0'
    }
  },
  methods:{
    getData: function() {
      var params = $page.getParams()
      var $this = this
      $api.knowledge.getKnowledgeFileSection({knowledgeFile: params.oid}).then(function(data) {
        $this.gridData = data
        if (data.length) {
          $this.rowClick({row: data[0]})
        }
      })
    },
    handleEdit: function() {
      this.isLoading = false
      this.oldValue = this.content
      this.enableEdit = true
    },
    getParams: function(queryString, key) {
        var params = {}
        var pairs = queryString.split('?')
        if (pairs.length > 1) {
          pairs = pairs[1].split('&')
        
          pairs.forEach(pair => {  
              var [key, value] = pair.split('=')
              params[decodeURIComponent(key)] = decodeURIComponent(value || '')
          });
        }
        if (key) return params[key] || ''
        return params
    },
    handleSubmit: function() {
      var data = this.$refs.editor.getData()
      var dom = $window.document.createElement('div')
      dom.innerHTML = data.content
      var images = dom.querySelectorAll('img')
      var $this = this
      images.forEach(function (item) {
        if (item.src.indexOf('?fileGuid=') !== -1) {
          item.innerText = '{{image:'+ $this.getParams(item.src, 'fileGuid') +'}}'
        }
      })
      var saveText = this.isUploadFile ? dom.innerText : data.content
      this.activeRow.content = saveText
      this.isLoading = true
      return $api.knowledge.saveKnowledgeFileSection({data: this.activeRow}).then(function (res) {
        $this.isLoading = false
        $notify.success('修改成功！')
        $this.gridData.some(function (row) {
          if (row.knowledgeFileSectionGUID === res.knowledgeFileSectionGUID) {
            $this.$refs.grid.updateRow(row, {sectionSize: res.sectionSize})
            return true
          }
          return false
        })
        $this.enableEdit = false
      })
    },
    handleCancel: function() {
      this.enableEdit = false
      this.content = this.oldValue
      this.$refs.editor.setData(this.content)
    },
    onAnalysis: function() {
      function removeSpecialCharacters(text = "") {
        return text.replace(/[\n\r\t\f\v\u2028\u2029]/g, "")
      }
      function customSourceSplitter(text) {
        var content = removeSpecialCharacters(text)
        var tokens = []
        var regex = /{{image:([^{}]+)}}/g // 正则表达式匹配 {{image:}} 形式的文本

        let lastIndex = 0
        let match

        while ((match = regex.exec(content)) !== null) {
          // 解析 {entity.field} 形式的字段
          var [fullMatch, fieldText] = match
          if (fullMatch && fieldText) {
            tokens.push({
              fullMatch: fullMatch,
              fieldText: fieldText
            })
          }

          lastIndex = match.index + fullMatch.length
        }

        return tokens
      }
      var data = customSourceSplitter(this.content, this.editorSourceMap)
      var $this = this
      var content = this.content
      data.forEach(function(item) {
        content = $this.replaceAll(content, item.fullMatch, '<p><img src=\"/ajax/Mysoft.Map6.Modeling.Handlers.ControlAjaxHandler/RichtextEditorGetImage?fileGuid=' + item.fieldText + '" ></img></p>')
      })
      content = '<p>' + content + '</p>'
      this.$refs.editor.setData(content)
    },
    rowClick: function(data) {
      this.enableEdit = false
      this.activeRow = data.row
      this.content = data.row.content
      this.onAnalysis()
      this.$refs.grid.selects([data.row])
      this.$refs.editor.setMode(3)
    },
    replaceAll: function(str, find, replace) {  
        return str.replace(new RegExp(find, 'g'), replace);  
    }
  }
}]]></script>
            <style>
                <code><![CDATA[.document-editor {
  padding-left: 12px;
  box-sizing: border-box;
  position: relative;
  .hc-richtext__menubar {
    height: 38px;
    /* display: none; */
  }
  .hc-richtext__workspace {
    height: calc(100% - 43px);
  }
  .document-editor_header {
    border: 1px solid #EAEAEA;
    border-bottom: 0;
    background: #F5F5F5;
    height: 40px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 999;
    left: 12px;
  }
  .title {
    padding: 9px 20px;
    color: #333333;
    text-align: center;
    font-size: 13px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    float: left;
    box-sizing: border-box;
  }
  .toolbar {
    float: right;
    height: 100%;
    margin-right: 12px;
  }
  .hc-button {
    height: 100%;
    line-height: 24px;
    min-width: 30px;
    .icon {
      display: inline-block;
      margin-right: 4px;
      float: left;
      line-height: 24px;
    }
    + .hc-button {
      margin-left: 8px;
    }
  }
  .miniux-form-cell {
    padding: 0;
  }
  .miniux-form {
    margin-bottom: 0 !important;
  }
  .miniux-form-section-content {
    padding: 0 !important;
  }
  .hc-richtext--view.is-readonly {
    margin-top: 40px;
    padding: 10px;
    border: 1px solid #ddd;
    height: 463px !important;
  }
}
.document-detail {
  display: grid;
  grid-template-columns: 250px calc(100% - 250px);
  height: 500px;
  padding: 20px;
}
.hc-grid,
.fc-table {
  height: 100%;
}
.hc-empty {
  position: absolute;
  top: 0;
}
.document-grid {
  height: 500px;
  overflow-y: auto;
  .fc-table.is-border {
    border-radius: 4px;
  }
  .fc-table-header,
  .fc-table-header-column {
    height: 41px;
  }
}
.fc-table-body {
  height: calc(500px - 35px);
}]]></code>
                <runtimeCode><![CDATA[.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor {
  padding-left: 12px;
  box-sizing: border-box;
  position: relative;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-richtext__menubar {
  height: 38px;
  /* display: none; */
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-richtext__workspace {
  height: calc(100% - 43px);
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .document-editor_header {
  border: 1px solid #EAEAEA;
  border-bottom: 0;
  background: #F5F5F5;
  height: 40px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 999;
  left: 12px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .title {
  padding: 9px 20px;
  color: #333333;
  text-align: center;
  font-size: 13px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  float: left;
  box-sizing: border-box;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .toolbar {
  float: right;
  height: 100%;
  margin-right: 12px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-button {
  height: 100%;
  line-height: 24px;
  min-width: 30px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-button .icon {
  display: inline-block;
  margin-right: 4px;
  float: left;
  line-height: 24px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-button + .hc-button {
  margin-left: 8px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .miniux-form-cell {
  padding: 0;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .miniux-form {
  margin-bottom: 0 !important;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .miniux-form-section-content {
  padding: 0 !important;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-editor .hc-richtext--view.is-readonly {
  margin-top: 40px;
  padding: 10px;
  border: 1px solid #ddd;
  height: 463px !important;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-detail {
  display: grid;
  grid-template-columns: 250px calc(100% - 250px);
  height: 500px;
  padding: 20px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .hc-grid,
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .fc-table {
  height: 100%;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .hc-empty {
  position: absolute;
  top: 0;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-grid {
  height: 500px;
  overflow-y: auto;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-grid .fc-table.is-border {
  border-radius: 4px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-grid .fc-table-header,
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .document-grid .fc-table-header-column {
  height: 41px;
}
.function-page-coding--08dc73c1-6f46-4750-83fe-5017433b8489 .fc-table-body {
  height: calc(500px - 35px);
}
]]></runtimeCode>
            </style>
        </content>
    </codingPageInfo>
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents>
        <dependentComponent componentGuid="08dc73c1-4090-4274-8fe8-5fbd2d2e2293" dependentId="document-editor-component" />
    </dependentComponents>
    <dependentModules />
    <apis>
        <api functionCode="42000401" service="knowledge" action="getKnowledgeFileSection" type="0" />
        <api functionCode="42000401" service="knowledge" action="saveKnowledgeFileSection" type="0" />
    </apis>
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>