<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-10-11T10:00:26.1434257+08:00" modifiedBy="李昂" modifiedOn="2024-10-11T15:49:36.8005471+08:00" metadataStatus="Product" functionPageId="08dce998-793b-4d6a-8ac6-6d3f2b75af19" name="08dce998-793b-4d8b-80d8-46f6a9d513ba" application="4200" functionGUID="08dcd959-9caf-410e-8f67-cbb7263e2157" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="true" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="分组" pageName="分组" titleEn="Edit_5132" titleSuffix="表单页面" url="/std/********/08dce998-793b-4d8b-80d8-46f6a9d513ba" pageType="0" functionCode="********" loadDefaultResource="false" description="规则分组表表单" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Edit_5132" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="OneColumn" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="08dce998-793c-445f-8118-b4a1bb860700" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="08dce998-793c-447f-89c9-28798877c020" width="100%" height="auto" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-10-11T15:49:36.7958304+08:00" modifiedOn="2024-10-11T15:49:36.7958273+08:00" metadataStatus="Product" id="appForm" type="Mysoft.Map6.Modeling.Controls.AppForm" metadataId="08dce998-793d-44fe-8ed1-6f5a86ddd0f7" templateId="32E54132-83AE-4CAE-885A-CD8AE57F6F55" entityName="主实体" reserved="0" title="规则分组表表单控件" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules />
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>