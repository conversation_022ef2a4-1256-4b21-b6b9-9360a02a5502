<?xml version="1.0" encoding="utf-8"?>
<functionPage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-17T11:29:26.3437865+08:00" modifiedBy="郑越" modifiedOn="2024-07-18T11:13:05.9901626+08:00" metadataStatus="Product" functionPageId="08dca610-a8eb-4225-858e-7079aeaa9538" name="08dca610-a8eb-4235-82ca-4862657af1e3" application="4200" functionGUID="08dc64ca-bbbd-4dd6-800e-756ba4e71547" templateId="2c63e14e-ff7e-4e0b-ae15-fefb8da34213" autoTitle="false" isDefault="false" pageHandlerPluginTypeId="********-0000-0000-0000-************" pageHandlerExPluginTypeId="********-0000-0000-0000-************" entityId="********-0000-0000-0000-************" title="相关问题" pageName="相关问题" titleEn="Component_0696" titleSuffix="自定义页面" url="/std/********/08dca610-a8eb-4235-82ca-4862657af1e3" pageType="5" functionCode="********" loadDefaultResource="false" description="相关问题" isAllowEdit="true" exposed="false" enablePageRouting="false" editMode="AllowAll" isRevisedId="1" openness="none" metaDataExtendType="None" inheritFrom="********-0000-0000-0000-************" enableInherited="false">
    <page>
        <controls />
        <pageparams />
        <libraries />
        <events>
            <event name="window.load" enabled="false" metadataStatus="Product" />
            <event name="document.ready" enabled="false" metadataStatus="Product" />
            <event name="control.ready" functionName="Mysoft.gptbuilder.*********.Component_0696" enabled="true" metadataStatus="Product" />
        </events>
        <wizards />
        <controlOpenInfos />
    </page>
    <pageLayout pageLayoutCategory="LeftSidebar" layoutType="0" pageMargin="0" pageHeightSetting="Repeat">
        <cells>
            <cell id="e49bea23-32db-4123-ac6a-1ea405898474" width="300px" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                <cells>
                    <cell id="46b3c2aa-e732-4b22-b0fa-f5c519e898ff" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-07-18T11:13:05.9881373+08:00" modifiedOn="2024-07-18T11:13:05.9881364+08:00" metadataStatus="Product" id="appGrid" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dca610-bcc0-436f-8d7d-9eb8722d47e2" reserved="-1" title="列表" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
            <cell id="f10f005c-1f75-47d5-910d-e7d91a63e5a3" width="*" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="true" direction="column">
                <cells>
                    <cell id="9cddcdb7-d6b1-4ee2-b1e8-62f3cbf3a130" width="100%" height="100%" isHidden="false" w="0" h="0" x="0" y="0" rowIndex="0" extensible="false" direction="column">
                        <control createdOn="2024-07-18T11:13:05.988138+08:00" modifiedOn="2024-07-18T11:13:05.9881379+08:00" metadataStatus="Product" id="appGrid1" type="Mysoft.Map6.Modeling.Controls.AppGrid" metadataId="08dca610-bcd9-4312-8f3a-8d1ad5719626" reserved="-1" title="列表" autoHeight="1" />
                        <cells />
                    </cell>
                </cells>
            </cell>
        </cells>
        <relations />
        <linkageRules>
            <linkageRule controlId="08dca610-bcc0-436f-8d7d-9eb8722d47e2" field="KnowledgeGUID" filterType="Current" controlType="0">
                <linkageControls>
                    <linkageControl controlId="08dca610-bcd9-4312-8f3a-8d1ad5719626" field="KnowledgeGUID" filterType="Current" controlType="0">
                        <linkageControls />
                    </linkageControl>
                </linkageControls>
            </linkageRule>
        </linkageRules>
        <rule>
            <configs />
            <groups />
        </rule>
    </pageLayout>
    <components />
    <childModules />
    <dependentScripts />
    <dependentUrls />
    <dependentResources />
    <dependentPackages />
    <dependentComponents />
    <dependentModules />
    <apis />
    <flows />
    <dependentLangs />
    <codes />
    <customizeScope openness="Never">
        <regionConfigure enable="false" />
        <propertyConfigure enableSystemProp="false" />
        <slotConfigure enableCustomizeSlot="false" />
    </customizeScope>
    <inheritScope enableInherited="false" inheritOpenness="Full">
        <controlOpenInfos />
    </inheritScope>
    <functionPageParameters>
        <fixedParams />
    </functionPageParameters>
    <inheritConfigRule>
        <rules />
    </inheritConfigRule>
    <extendFunctionPage xsi:nil="true" />
</functionPage>