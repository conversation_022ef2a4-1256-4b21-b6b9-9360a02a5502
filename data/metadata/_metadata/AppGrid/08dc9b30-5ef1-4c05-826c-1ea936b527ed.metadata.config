<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-03T07:18:43Z" modifiedBy="郑越" modifiedOn="2024-07-03T19:35:44.4939229+08:00" metadataStatus="Product" name="评测数据从表" controlId="appGridckhx" functionPageId="08dc9b2f-a3ba-475d-89d4-54a4b069f826" description="" htmlCache="default" enableUserSettings="false" gridId="08dc9b30-5ef1-4c05-826c-1ea936b527ed" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="e8e11389-7916-4004-b1f6-73145ca45164" controlId="2006aedf-0f38-4891-a051-a5b79cf2f66b" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()">
            <script><![CDATA[]]></script>
        </code>
        <code id="e1ab055a-a05b-4090-b828-d978e0df4932" controlId="f558b362-b266-4e16-a343-c7eaec12c291" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Grid.defaultAdd()">
            <script><![CDATA[]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_KnowledgeEvaluatingDataDetail" entityId="08dc9b26-81bc-494f-8bd1-b103cb1ba7dd" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc9b30-5ef1-4c3f-8a26-5138d9941990" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="f558b362-b266-4e16-a343-c7eaec12c291" title="新增" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_add" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.defaultAdd()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc9b30-5ef1-4c4e-8eb1-8fbcf2506393" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="2006aedf-0f38-4891-a051-a5b79cf2f66b" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_del" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc9b30-5ef1-4c5e-83a5-a83228e9c6fe" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="1d99ddd9-ae02-4d87-a237-a099d3881f6e" title="新规则" controlId="3319782e-8ad5-4f2e-bd7e-0c05740284b1" controlType="column" controlSubType="" controlProp="allowEdit" controlName="Question" metadataStatus="Product">
                    <handles>
                        <handle handleId="92eb20ab-ea0e-45ea-aed0-a98ccf53fb6f" ruleId="5246b989-4ff0-43bb-984f-79483e0a77d6" action="readonly" value="true" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="663568ad-4843-42d8-a551-b1a6b2754fed" title="新规则" controlId="927db825-42e2-4832-9819-42b6a3272d69" controlType="column" controlSubType="" controlProp="allowEdit" controlName="Answer" metadataStatus="Product">
                    <handles>
                        <handle handleId="6b8334bb-8ce7-4f37-9c36-ae15f540dfa2" ruleId="5246b989-4ff0-43bb-984f-79483e0a77d6" action="readonly" value="true" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="5246b989-4ff0-43bb-984f-79483e0a77d6" title="查看模式" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dc9b30-5ef1-4b64-88d5-6ef437bec96a" viewId="08dc9b30-5ef1-4c1f-863c-d749cb5bb2c9" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc9b26-81bc-494f-8bd1-b103cb1ba7dd" isLookup="false">
                <dataSource keyName="KnowledgeEvaluatingDataDetailGUID" entity="gpt_KnowledgeEvaluatingDataDetail" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QBRANwEMwBXc6ASw3gBFbymRpy6wA6AQQwBnHCABOscoNgDhYgDQBYAFBJUmXASJlKNeoxad2nbjzQgAngHUso/BKlmrN/IpXJ02PCEIkK1WgzMrEZcvOpePtr+ekGcAOIAqgCSTPaw4Zq+OgH6rIkprqoeGt5afrqBBmwcoTwAilQggvRYGGkNTS0Yhe4ZpVnRlcE1JgDCoiC03vmpkrDjk9DTyUw9ap6ZURW5hiO8C1P4AHLkALYgaQdLx2cga8URZdkxVSFjE4cAKnTnlx/X33O9z6kXKOVi1WMYQ2/S24NeexmaRBT0GO0hoRmwJhoOeQ12UJ4AFksPg6AAzOjLFJpElkynU1bKIoogbbCFvXh0ilUm6/Obchl8u7M3o41HshGEwW8wEXAWknneOWwJQAMVEWFOCDFJVxaI5e2kUhZ4rZ8OGUNgqssAAsxBcABQARgAvM6AJRAA=]]></command>
                    <fields>
                        <field name="Answer" allowPopulate="true" entity="gpt_KnowledgeEvaluatingDataDetail" field="Answer" entityAlias="gpt_KnowledgeEvaluatingDataDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="KeyWord" allowPopulate="true" entity="gpt_KnowledgeEvaluatingDataDetail" field="KeyWord" entityAlias="gpt_KnowledgeEvaluatingDataDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="KnowledgeEvaluatingDataDetailGUID" allowPopulate="true" entity="gpt_KnowledgeEvaluatingDataDetail" field="KnowledgeEvaluatingDataDetailGUID" entityAlias="gpt_KnowledgeEvaluatingDataDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Question" allowPopulate="true" entity="gpt_KnowledgeEvaluatingDataDetail" field="Question" entityAlias="gpt_KnowledgeEvaluatingDataDetail" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="Answer" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Answer" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="日期与时间" />
                        <availableField name="KeyWord" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KeyWord" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="KnowledgeEvaluatingDataDetailGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeEvaluatingDataDetailGUID" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="Guid" />
                        <availableField name="KnowledgeEvaluatingDataGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeEvaluatingDataGUID" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="日期与时间" />
                        <availableField name="Question" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Question" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeEvaluatingDataDetail" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc9b30-5ef1-4e62-8020-b193b92ea3a7" id="08dc9b26-81bc-494f-8bd1-b103cb1ba7dd" name="gpt_KnowledgeEvaluatingDataDetail" primaryField="KnowledgeEvaluatingDataDetailGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="80" frozenToolbar="true" idField="KnowledgeEvaluatingDataDetailGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="true" fixedColumns="0" maxWrapRow="0" editMode="1" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="问题" width="120" field="Question" allowEdit="true" customizeReferenceable="false" id="3319782e-8ad5-4f2e-bd7e-0c05740284b1" isHidden="false" disableUserHide="2" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps>
                                <props>allowEdit</props>
                            </customProps>
                            <textArea id="9d3c3b93-cc69-46db-b292-4965a2259fd2" field="Question" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" height="60" maxLength="512" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                        <column title="预期回答" width="120" field="Answer" allowEdit="true" customizeReferenceable="false" id="927db825-42e2-4832-9819-42b6a3272d69" isHidden="false" disableUserHide="2" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps>
                                <props>allowEdit</props>
                            </customProps>
                            <textArea id="ac5c8b1f-fae0-4eab-b2fd-45b29697932c" field="Answer" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                        <column title="匹配关键词" width="120" field="KeyWord" allowEdit="true" customizeReferenceable="false" id="04594d42-61f7-4f99-b275-957aa9d3d697" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="&lt;p&gt;多个关键词用换行分隔&lt;/p&gt;" metadataStatus="Product">
                            <customProps />
                            <textArea id="e1557db7-a4ec-444f-b261-d3becc5f6fb7" field="KeyWord" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="de57cb46-34c8-422d-891c-6423d063e656" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>