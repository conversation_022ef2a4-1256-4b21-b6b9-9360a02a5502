<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-10T07:45:38Z" modifiedBy="夏娜" modifiedOn="2025-03-21T11:24:07.4599945+08:00" metadataStatus="Product" name="技能基础表列表控件" functionPageId="08dc70c5-2f8b-491c-8b1f-f97996181f3f" description="" htmlCache="default" enableUserSettings="false" gridId="08dc70c5-2f8f-431e-804b-86f4390c99d6" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="7e8febc6-9eb1-4081-94d0-9bcd2a759ea7" controlId="08dc70c5-2f8c-492d-8663-92195c274949" controlType="ToolbarItem" controlAction="_appGrid_mAdd_beforeOpen">
            <script><![CDATA[if ($e.params.spaceGUID == null || $e.params.spaceGUID == '') {
  $e.cancel = true 
  $notify.warning('请先选择工作空间');
  $grid.reload()
}]]></script>
        </code>
        <code id="be3c5957-b626-4720-ba0a-e2029a0e545e" controlId="08dc70c5-2f8f-431e-804b-86f4390c99d6" controlType="Grid" controlAction="_appGrid_query">
            <script><![CDATA[$Utility.validateSpace($e)]]></script>
        </code>
        <code id="1594abcd-2609-49d4-b21f-c72125602f36" controlId="12f091d3-a97e-4c61-8ccf-19426323191a" controlType="ToolbarItem" controlAction="_appGrid_button_58240648042867_click">
            <script><![CDATA[$notify.confirm('确认删除','请确认是否删除选中的记录？', 'warn').then(
  function(){
    var row = $e.selecteds[0]
    return $api.skill.del({
      skillGUID: row.SkillGUID
    }).then(function () {
      $notify.success('删除成功！')
      $grid.reload()
    })
  },
  function() {}
)]]></script>
        </code>
        <code id="d4e983ec-a223-48b5-bb5e-6c50534d4dc0" controlId="2ac8dac5-9f1f-4d08-b33b-35d64e84c57f" controlType="ToolbarItem" controlAction="_appGrid_button_58240543271760_click">
            <script><![CDATA[var filters = $grid.getFilterOptions().filters
var space = ''
if (filters.length) {
  filters.find(function(item) {
    if (item.condition && item.condition.length) {
      if (item.condition[0].field === 'SpaceGUID') {
        space = item.condition[0].value
        return true
      }
    }
    return false
  })
}
var row = $e.selecteds[0]
var params = {_mp: 'empty', oid: row.SkillGUID, space: space, mode: row.Mode, _aidesigner: 1}
if (row.Mode === 'agent') {
  params._aicloseable = 0
  params._aicontainer = '#agentPreview'
}
$page.open('/std/42000301/08dc748a-f8d5-40e3-8957-2bfb5915185c', params)]]></script>
        </code>
        <code id="2a8c680d-474b-4661-b6ee-f20c6068af60" controlId="2cbfffa0-02e1-4ec2-8281-03dedcbcde79" controlType="ToolbarItem" controlAction="_appGrid_button_58224991225344_dialogClose">
            <script><![CDATA[$grid.reload()]]></script>
        </code>
        <code id="a4d275a2-9d55-4ae3-9f1d-4509e9084343" controlId="69d02577-12af-4fbd-96bb-b0c5015e75c4" controlType="ToolbarItem" controlAction="_appGrid_button_58240648052095_dialogClose">
            <script><![CDATA[$grid.reload()]]></script>
        </code>
        <code id="ae08237c-c801-4322-8cb0-************" controlId="bd393cc5-22df-4b53-9b51-7fd1cb2aa55e" controlType="ToolbarItem" controlAction="_appGrid_button_58240883918695_click">
            <script><![CDATA[(function () {
 if (this._gpt?.open) {
    this._gpt.debug = true
    var id = $e.selecteds[0].SkillCode
    console.log(id)
    if (!id) return
    this._gpt.open({
      skills: [id],
      defaultSkill: id,
    })
  }
})()]]></script>
        </code>
        <code id="7b736284-3c32-4688-b047-3dece69eb0ad" controlId="eb06926b-70d3-425a-a934-94a68aa048bc" controlType="ToolbarItem" controlAction="_appGrid_button_58238029902454_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */
$page.refreshData()]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000301" service="skill" action="del" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls>
        <dependentUrl value="/std/42000301/08dc748a-f8d5-40e3-8957-2bfb5915185c" />
    </dependentUrls>
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_Skill" entityId="08dc70ba-c30b-4a8d-893d-813f8d335ba6" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc70c5-2f8f-45c6-8ef3-e305d6951b36" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dc70c5-2f8c-492d-8663-92195c274949" title="创建技能" isHighlight="true" type="button" id="mAdd" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onbeforeopen" functionName="_appGrid_mAdd_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000301/08dd5c58-db26-4945-8174-3e3acde34d41" id="08dd5c58-db26-4945-8174-3e3acde34d41" itemId="7ee1d82e-4d36-4cd1-8d27-c117ea7b6cfd" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="840" />
                                        <option key="height" value="626" />
                                    </options>
                                    <params>
                                        <param type="filter" key="spaceGUID" value="[complex:SpaceGUID]" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                </behavior>
                                <customProps>
                                    <props>isDisabled</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc70c5-2f8f-45d8-83f9-80044d1d2c00" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="2ac8dac5-9f1f-4d08-b33b-35d64e84c57f" title="编排" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240543271760" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58240543271760_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="bd393cc5-22df-4b53-9b51-7fd1cb2aa55e" title="预览" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240883918695" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58240883918695_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="a9543996-3b95-4a3d-937f-49a893d23747" title="更多" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="menu_58240648066238" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="44619a7f-0e2d-4f48-ad3f-87dea941e05e" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240543003432" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events />
                                        <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000301/08dc89df-f23b-4061-85de-f2f20054f617" id="08dc89df-f23b-4061-85de-f2f20054f617" itemId="2508e5ed-6dd0-4a5c-ac90-0425e6cf9c2e" metadataStatus="Product">
                                            <options>
                                                <option key="jumpMode" value="self" />
                                            </options>
                                            <params>
                                                <param type="filter" key="spaceGUID" value="[complex:SpaceGUID]" />
                                                <param type="data" key="oid" value="SkillGUID" />
                                                <param type="text" key="mode" value="2" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="2cbfffa0-02e1-4ec2-8281-03dedcbcde79" title="授权" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58224991225344" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appGrid_button_58224991225344_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000301/08dd1f37-680d-476a-86d2-55997a84e04c" id="08dd1f37-680d-476a-86d2-55997a84e04c" itemId="bbba2580-d07a-4841-8299-7889e3045fd1" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="960" />
                                                <option key="height" value="600" />
                                                <option key="slipTitleMode" value="custom" />
                                                <option key="slipTitleContent" value="角色授权" />
                                            </options>
                                            <params>
                                                <param type="data" key="ObjectGUID" value="SkillGUID" />
                                                <param type="text" key="ObjectType" value="SKill" />
                                                <param type="data" key="ObjectCode" value="SkillCode" />
                                                <param type="data" key="ObjectName" value="SkillName" />
                                                <param type="text" key="mode" value="1" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="69d02577-12af-4fbd-96bb-b0c5015e75c4" title="复制" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240648052095" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appGrid_button_58240648052095_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000301/08dc7336-b31c-440a-8476-ee684b031ea9" id="08dc7336-b31c-440a-8476-ee684b031ea9" itemId="23efc84d-36ae-4a69-bf1b-585eccb5de8b" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="720" />
                                                <option key="height" value="360" />
                                            </options>
                                            <params>
                                                <param type="data" key="oid" value="SkillGUID" />
                                                <param type="text" key="mode" value="2" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps>
                                            <props>isHidden</props>
                                            <props>isDisabled</props>
                                        </customProps>
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="eb06926b-70d3-425a-a934-94a68aa048bc" title="调整空间" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58238029902454" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="true" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appGrid_button_58238029902454_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42001301/08dca725-9a6e-4024-8d15-0577a7d00fb7" id="08dca725-9a6e-4024-8d15-0577a7d00fb7" itemId="5bb5f73f-752f-4c79-95d3-15b80c24167a" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="720" />
                                                <option key="height" value="360" />
                                            </options>
                                            <params>
                                                <param type="data" key="spaceGUID" value="SpaceGUID" />
                                                <param type="text" key="scene" value="SKILL" />
                                                <param type="data" key="businessGUID" value="SkillGUID" />
                                                <param type="data" key="businessCode" value="SkillCode" />
                                                <param type="data" key="businessName" value="SkillName" />
                                                <param type="text" key="mode" value="1" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="12f091d3-a97e-4c61-8ccf-19426323191a" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240648042867" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="_appGrid_button_58240648042867_click" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps>
                                            <props>isHidden</props>
                                        </customProps>
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onquery" functionName="_appGrid_query" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dc70c5-2f8f-45e8-8d9d-9790dbc2ec80" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="a927a624-7ea3-482a-a5a4-89f0a466d087" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="7d439afc-99f4-4328-9f63-fa98b779bc85" ref="SpaceGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="ddb45d26-0101-4165-af35-70eaf7cf0aad" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="4b9e0305-6653-4c22-a86b-2c8471a624e8" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="7d439afc-99f4-4328-9f63-fa98b779bc85" name="SpaceGUID" metadataStatus="Product">
                    <label title="工作空间" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="SpaceGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="pageHandler" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="false" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <pageHandler pageHandlerId="/api/42001301/workspaceList" method="Load" extMethod="" apiSourceType="" />
                        <options />
                    </comboBox>
                </component>
                <component id="4b9e0305-6653-4c22-a86b-2c8471a624e8" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" union="false" wildcard="false">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="SkillName" title="技能名称" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                            <findField field="SkillCode" title="技能编码" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="1a313c7b-9b02-468d-8152-72033a79ab35" title="新规则" controlId="12f091d3-a97e-4c61-8ccf-19426323191a" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58240648042867" metadataStatus="Product">
                    <handles>
                        <handle handleId="0e7dd6fb-8fbd-4d87-a7a3-acc9be8e6870" ruleId="779b408f-53de-496c-97a3-9f8cc5edd62a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="557af0ef-7a22-4926-96e4-df5e15761766" title="新规则" controlId="69d02577-12af-4fbd-96bb-b0c5015e75c4" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58240648052095" metadataStatus="Product">
                    <handles>
                        <handle handleId="2aeee5ed-ece5-4c8b-8f3e-318aab84c4dc" ruleId="779b408f-53de-496c-97a3-9f8cc5edd62a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="92687529-2a73-425f-97db-8eb68c9cf95f" title="新规则" controlId="2ac8dac5-9f1f-4d08-b33b-35d64e84c57f" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58240543271760" metadataStatus="Product">
                    <handles>
                        <handle handleId="e947838b-a7ad-4b22-a802-edb625363a3c" ruleId="779b408f-53de-496c-97a3-9f8cc5edd62a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="a3ddb3e7-9c9a-455b-a2ca-79cb4a71ddc2" title="新规则" controlId="69d02577-12af-4fbd-96bb-b0c5015e75c4" controlType="toolbaritem" controlSubType="row" controlProp="isDisabled" controlName="button_58240648052095" metadataStatus="Product">
                    <handles>
                        <handle handleId="60acd218-cadb-4971-bbd2-9de34fecaa4d" ruleId="2e52fab7-89e9-41bd-a496-c13aa4b15353" action="enable" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="07bb173e-e0dc-4377-8cba-615309e65160" title="新规则" controlId="08dc70c5-2f8c-492d-8663-92195c274949" controlType="toolbaritem" controlSubType="global" controlProp="isDisabled" controlName="mAdd" metadataStatus="Product">
                    <handles>
                        <handle handleId="b9794bd1-e870-4bb2-af23-06f64bd42013" ruleId="2e52fab7-89e9-41bd-a496-c13aa4b15353" action="enable" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="779b408f-53de-496c-97a3-9f8cc5edd62a" title="系统级" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;IsSystem&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="2e52fab7-89e9-41bd-a496-c13aa4b15353" title="是否独立版" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;authFunctionCode&quot;,&quot;field&quot;:&quot;_authFunctionCode&quot;,&quot;operator&quot;:&quot;is_exist&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;42001399&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dc70c5-2f8e-479d-87ba-b8612d472afa" viewId="08dc70c5-2f8f-4351-8b51-95a9a5b7589f" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc70ba-c30b-4a8d-893d-813f8d335ba6" isLookup="false">
                <dataSource keyName="SkillGUID" entity="gpt_Skill" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9YBrAlmMA6AIiAM6QBOmARiLAIZGyEnlUA0AsAFBKobZ4DiAV0wATanVhDRIdl2RosOXAAk6+APaRBAWxAA7aAEE9NMAE8imIgDl1Y2vVVENW3QeOmLV22NncFfCp0AJLaNPAgHuaWNnbijiFhEVFesb6c/rxKTsEGIKQ0MJgAbiAAwjSkIj7xsDl5BUWlFVU1fvJZeMGQ6noOsN297TyKXXqY0ACipQb9uRPT+tDDAUrBRMAW0CDacxtbOyuduACyINA0IjQX/WcXVxdHo6fnNPjXNABq+Za9t6/vC7fUi/PRPQInOK3OLgpSQsRgXJEC56SDlKESeHgJEotFlGEZDrPADyiH0+EwpnU8AA6pg9CJ1AB3fqk8mUsDUukM5mwvCdCrbeDqUhmfgAVWC+HoEgF1xAwtFEqlRD5uAFAAtrvjBLNZaMylroDqDGqBRj6Ob0nIRoFzTgoMb1Lq4Pq+PiHTATctCbalJ1rDRdP0A0GZL7VvzRsDQTLLdGfpheqqI8dgIhCiBlfgQxm0dmzRdSNBxYgAApgQTwenZkNFkvlyvVvQF1PPYD10sAFXU6jAtdlncQPb7rZtkfVF2ggjjsA71xnatLnMuNAoEFny/Uq/XxDVNPAPWDEgPYCP4fHxzKpBA8pEA/o19v23vkvwaqfd8Dx8fN6/YY/P8Xy7TAf1gT9gNAi9MmeeFMAAM0wEBXylaERAQpCUPfNsITsDDkO/Wo4MQgiAJwuE8JIkQQLA4jMJo6CiTtUYHznFi31gDgADFSHUXYYL4WBDHoAScFgTiaQ1fJqAACgARgAXjkgBKIA=]]></command>
                    <fields>
                        <field name="IsSystem" allowPopulate="false" entity="gpt_Skill" field="IsSystem" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Mode" allowPopulate="true" entity="gpt_Skill" field="Mode" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedName" allowPopulate="true" entity="gpt_Skill" field="ModifiedName" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_Skill" field="ModifiedTime" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SkillCode" allowPopulate="true" entity="gpt_Skill" field="SkillCode" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SkillGUID" allowPopulate="false" entity="gpt_Skill" field="SkillGUID" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SkillName" allowPopulate="true" entity="gpt_Skill" field="SkillName" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SpaceGUID" allowPopulate="false" entity="gpt_Skill" field="SpaceGUID" entityAlias="gpt_Skill" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
                        <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="Guide" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Guide" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="HasDocumentAnalysisNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasDocumentAnalysisNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="HasImageAnalysisNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasImageAnalysisNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="HasInteractiveCardNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasInteractiveCardNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="Icon" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Icon" entity="gpt_Skill" entityType="0" attributeType="图片" />
                        <availableField name="InitEvent" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InitEvent" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="Metadata" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Metadata" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="MetaDataVersion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="MetaDataVersion" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="Mode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Mode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(16)）" />
                        <availableField name="ModelInstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelInstanceCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
                        <availableField name="OpenDialogWindow" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="OpenDialogWindow" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="SkillCategoryGUIDs" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillCategoryGUIDs" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(1024)）" />
                        <availableField name="SkillChatCount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillChatCount" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="SkillCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="SkillCollectCount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillCollectCount" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="SkillGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                        <availableField name="SkillName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="SkillVersions" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillVersions" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                        <availableField name="StartUpPluginGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpPluginGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                        <availableField name="StartUpToolGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpToolGUID" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Status" entity="gpt_Skill" entityType="0" attributeType="整数" />
                        <availableField name="Uploadables" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Uploadables" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Skill" entityType="0" attributeType="时间戳" />
                        <availableField name="Welcome" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Welcome" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc70c5-2f91-4581-80ff-fc27178386ff" id="08dc70ba-c30b-4a8d-893d-813f8d335ba6" name="gpt_Skill" primaryField="SkillGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="1" frozenToolbar="true" idField="SkillGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="技能名称" width="200" field="SkillName" allowEdit="false" customizeReferenceable="false" id="7dad3d79-856a-43ff-bd80-1a7a91124b41" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="da4b6eb1-f149-4345-a9e5-95dbb1f849fe" field="SkillName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000301/08dc89df-f23b-4061-85de-f2f20054f617" id="08dc89df-f23b-4061-85de-f2f20054f617" itemId="622a20c5-7e8d-4176-a7b1-5103da35a566" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="filter" key="spaceGUID" value="[complex:SpaceGUID]" />
                                        <param type="data" key="oid" value="SkillGUID" />
                                        <param type="text" key="mode" value="3" />
                                    </params>
                                    <events />
                                </behavior>
                            </behaviors>
                        </column>
                        <column title="技能编码" width="120" field="SkillCode" allowEdit="false" customizeReferenceable="false" id="2300a37d-dac2-4b74-acbc-20f455167328" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="8985d9e9-5e0a-4f60-9274-8fb0a9b7c853" field="SkillCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="技能类型" width="120" field="Mode" allowEdit="false" customizeReferenceable="false" id="eb713f34-7b7a-4093-a028-ab08833dd149" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product">
                            <customProps />
                            <statusLabel id="4e11c625-5db2-47db-baee-bafcf9e5352f" field="Mode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options">
                                <customProps />
                                <events />
                                <options>
                                    <option value="flow" text="任务编排" style="success" />
                                    <option value="agent" text="智能体" style="warning" />
                                </options>
                            </statusLabel>
                            <behaviors />
                        </column>
                        <column title="修改人" width="120" field="ModifiedName" allowEdit="false" customizeReferenceable="false" id="5d4a9fa7-2967-4e32-937d-ffb0fd8270ac" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="d36f1307-ca88-4fea-bc6b-fb2dc99b153a" field="ModifiedName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="修改时间" width="120" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="148c7255-d63e-4df4-94f1-6271fe70dbeb" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="left" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <datePicker id="818ec1a6-4074-42c4-b287-9d80537436d8" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="e497d3ca-8148-42fe-ba98-5ca618ce286a" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>