<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-11-19T07:22:07Z" modifiedBy="彭宗一" modifiedOn="2025-06-20T16:16:41.0619104+08:00" metadataStatus="Product" name="导入导出任务表列表控件" functionPageId="08dd086a-e04c-4eae-8c91-eabac952133f" description="" htmlCache="default" enableUserSettings="false" gridId="08dd086a-e04d-4d90-8980-522f2ddb6700" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="3ee78bc7-6061-4feb-b4ef-338414290ec3" controlId="08dd086a-e04d-4d90-8980-522f2ddb6700" controlType="Grid" controlAction="_appGrid_init">
            <script><![CDATA[/**
 *  列表初始化完成事件
 *  @example
 *  //设置当前列表行数据
 *  $grid.setData({data:[{Name:'小明'，Age: 20},{Name:"李雷", Age:23}], total: 2})
 */
var conn = $util.notification();
conn.start();
conn.subscribe('GPT_ImportTaskStatus',function(data){
  $page.refreshData()
})]]></script>
        </code>
        <code id="5a867151-65e5-45c0-be98-305cbb5f6159" controlId="08dd086a-e04d-4d90-8980-522f2ddb6700" controlType="Grid" controlAction="_appGrid_query">
            <script><![CDATA[var callback = function (e, data) {
  $page.refreshData()
}
$util.message.on("gpt_closeimport", callback)]]></script>
        </code>
        <code id="666bdf22-63ce-4261-bd6a-cf2ec5a06f67" controlId="948ce998-58f7-46f6-abcf-d90afc19431a" controlType="ToolbarItem" controlAction="_appGrid_button_58222308526061_click">
            <script><![CDATA[return $api.importAndExportTask.uploadDocument({
  data: {
  }
}).then(function (data) {  
})]]></script>
        </code>
        <code id="ae796b33-dbac-4be9-b6f9-02aa9456e62b" controlId="ec31ed5d-c8c3-4046-9976-2f6b19da44b3" controlType="ToolbarItem" controlAction="_appGrid_button_58227398900859_beforeOpen">
            <script><![CDATA[/**
 *  页面打开前事件
 *  @example
 *  //阻止弹出框弹出
 *  $e.cancel = true 
 * 
 *  //用来给弹出页面传递数据
 *  //在弹出页面中可以通过$page.getDialogPageData来进行获取
 *  $e.options.pageData = {} 
 * 
 *  //传递页面参数
 *  $e.params = {businessCode:'0001'}
 */
$e.options.title = '导入明细']]></script>
        </code>
        <code id="15940bf7-af74-4602-a3d1-7237d8a9580a" controlId="fb1783ff-bfc8-49c1-9c08-3c589b09705b" controlType="ToolbarItem" controlAction="_appGrid_button_58227669406889_click">
            <script><![CDATA[$util.document.download($e.selecteds[0].DocumentGUID)]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42001302" service="importAndExportTask" action="uploadDocument" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_ImportExportTask" entityId="08dd0869-74bc-4e32-862a-d94020ff9008" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd086a-e04d-4e9f-8f2d-f963637e33fb" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="def4345d-ecb7-4ed0-aaef-26b09108159d" title="新增导入" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58227669392639" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42001302/08dd0a94-ad68-4ffc-82d6-9b1a24545a73" id="08dd0a94-ad68-4ffc-82d6-9b1a24545a73" itemId="e93e9b5f-fd47-4b8f-a6ed-eee1c683e55c" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="1300" />
                                        <option key="height" value="700" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="948ce998-58f7-46f6-abcf-d90afc19431a" title="同步静态文件" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58222308526061" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58222308526061_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd086a-e04d-4ed4-8f5d-b6fb7c581678" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="fb1783ff-bfc8-49c1-9c08-3c589b09705b" title="下载包" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58227669406889" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58227669406889_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="ec31ed5d-c8c3-4046-9976-2f6b19da44b3" title="导入明细" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58227398900859" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onbeforeopen" functionName="_appGrid_button_58227398900859_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42001302/08dd0a18-7e66-4ab6-8da9-acbcf7bd2e60" id="08dd0a18-7e66-4ab6-8da9-acbcf7bd2e60" itemId="90f08325-e09d-47df-a769-8868ea73fe6e" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="960" />
                                        <option key="height" value="600" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="taskGUID" value="ImportExportTaskGUID" />
                                        <param type="text" key="isImport" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="oninit" functionName="_appGrid_init" enabled="true" metadataStatus="Product" />
            <event name="onquery" functionName="_appGrid_query" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dd086a-e04d-4f09-86e7-a921052f094a" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition isCurrent="true" id="5593967b-5cb7-43e5-bf58-5eaef28a9883" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="319c48c5-0d83-4684-887b-e81763bbdecb" ref="CreatedTime" />
                    <template><![CDATA[]]></template>
                    <searchType />
                </condition>
            </conditions>
            <components>
                <component id="319c48c5-0d83-4684-887b-e81763bbdecb" name="CreatedTime" metadataStatus="Product">
                    <label title="导入时间" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <dateRange field="CreatedTime" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" dataFrom="options" allowClear="true">
                        <customProps />
                        <events />
                        <options />
                    </dateRange>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="a2d9a3b9-4a48-4326-baab-3fa67c440957" title="新规则" controlId="ec31ed5d-c8c3-4046-9976-2f6b19da44b3" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58227398900859" metadataStatus="Product">
                    <handles>
                        <handle handleId="68be86d1-3415-4aaf-ac2a-914e09426b77" ruleId="cb84da59-3699-4c0c-abea-69edf2eb123a" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="cb84da59-3699-4c0c-abea-69edf2eb123a" title="导入成功" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;TaskStatus&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;2&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dd086a-e04d-4a4e-8403-d7434fa461ff" viewId="08dd086a-e04d-4df9-846b-e60d39dbac51" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dd0869-74bc-4e32-862a-d94020ff9008" isLookup="false">
                <dataSource keyName="ImportExportTaskGUID" entity="gpt_ImportExportTask" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AkgW0QewE7QFEAPHfAFQEMBnAawDoAREKyXASwCMRZrYmX2XADQBYAFBJUmUoRJ5olWo2yQArhhAA7aAHEAqmgY8qfFeq26DDUROTos84jMX0AYmwgA5ChuOx3Xj4gNpL2Mk7yLnQuwNAU0KomvDFxCVQhdtKOcuTU9C5kAJ6I3Ml5RSUZUg74EblKAMK4IPEgACb6hn5NLdDtndbioVm1OQp5dD2tbd6+vFN9M0FVYdnOEwvtZGxzJptt2xorI7LrSid147QDfhdjLgPHNaeREwCy2G1sAGZs/VZ+D5fX7/QxPcL3d6fH5/Ja7WBAmHtWbBIaZZ6XKKIkEHHalEzY2GHbhiVy4bAYBDoiFnGiwACCJmGGMhtFgsDEAHUABYgZqwAAUAEYALxCgCUQA==]]></command>
                    <fields>
                        <field name="CreatedTime" allowPopulate="false" entity="gpt_ImportExportTask" field="CreatedTime" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="DocumentGUID" allowPopulate="true" entity="gpt_ImportExportTask" field="DocumentGUID" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="FileName" allowPopulate="true" entity="gpt_ImportExportTask" field="FileName" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ImportExportTaskGUID" allowPopulate="true" entity="gpt_ImportExportTask" field="ImportExportTaskGUID" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedName" allowPopulate="true" entity="gpt_ImportExportTask" field="ModifiedName" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_ImportExportTask" field="ModifiedTime" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TaskStatus" allowPopulate="true" entity="gpt_ImportExportTask" field="TaskStatus" entityAlias="gpt_ImportExportTask" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_ImportExportTask" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_ImportExportTask" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_ImportExportTask" entityType="0" attributeType="日期与时间" />
                        <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Describe" entity="gpt_ImportExportTask" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="DocumentGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DocumentGUID" entity="gpt_ImportExportTask" entityType="0" attributeType="附件" />
                        <availableField name="FileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="FileName" entity="gpt_ImportExportTask" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ImportExportTaskGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ImportExportTaskGUID" entity="gpt_ImportExportTask" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_ImportExportTask" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_ImportExportTask" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_ImportExportTask" entityType="0" attributeType="日期与时间" />
                        <availableField name="TaskStatus" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskStatus" entity="gpt_ImportExportTask" entityType="0" attributeType="整数" />
                        <availableField name="TaskType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskType" entity="gpt_ImportExportTask" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_ImportExportTask" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd086a-e04e-4aac-8504-540fa8095344" id="08dd0869-74bc-4e32-862a-d94020ff9008" name="gpt_ImportExportTask" primaryField="ImportExportTaskGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_ImportExportTask.TaskType" operatorType="eq" id="d047c0e0-8de8-4f7c-8f6b-b9ba29c4dc92" dataType="number" valueType="0" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:long">1</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="FileName" allowUnRef="false" allowRef="false">
                        <column title="导入文件名称" width="120" field="FileName" allowEdit="false" customizeReferenceable="false" id="a61fa292-3057-43b3-820f-13e62fb82cbe" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="f6286926-8378-41f6-8360-cd36e0387b7f" field="FileName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="16" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ModifiedName" allowUnRef="false" allowRef="false">
                        <column title="操作人" width="120" field="ModifiedName" allowEdit="false" customizeReferenceable="false" id="935a1a7b-1ccd-405b-8d11-73fe904feb0f" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="7b624d07-f64b-4a9f-84eb-768e0abbaa00" field="ModifiedName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ModifiedTime" allowUnRef="false" allowRef="false">
                        <column title="导入时间" width="120" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="cedcfb60-8c97-4608-a6b3-01a530d0b553" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <datePicker id="b2f72ba3-8a71-41fc-a784-051e48b87a9d" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm:ss" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </component>
                    <component name="TaskStatus" allowUnRef="false" allowRef="false">
                        <column title="任务状态" width="120" field="TaskStatus" allowEdit="false" customizeReferenceable="false" id="9fbc5392-c2d7-478d-8bb7-109dee670731" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <statusLabel id="ad7b7175-a61f-43bc-959b-adc7810a3b9c" field="TaskStatus" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options">
                                <customProps />
                                <events />
                                <options>
                                    <option value="1" text="导入中" style="warning" />
                                    <option value="2" text="成功" style="success" />
                                    <option value="3" text="失败" style="danger" />
                                    <option value="0" text="新增" style="dignify" />
                                </options>
                            </statusLabel>
                            <behaviors />
                        </column>
                    </component>
                    <component name="DocumentGUID" allowUnRef="false" allowRef="false">
                        <column title="文件GUID" width="120" field="DocumentGUID" allowEdit="false" customizeReferenceable="false" id="f9b2d3f9-07a1-4153-8e89-abfafb55da32" isHidden="true" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="true" hasExportIgnoreRule="false" printIgnore="true" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <fileUpload id="0b051ed4-602f-4ed2-a0a1-48ff32a454ba" field="DocumentGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" limitFileCount="1" limitSize="50" fileLimitSize="5" limitType="*.*" enableAllDownloads="false" showTips="false" showDragArea="false">
                                <customProps />
                                <events />
                                <rightsAddRule />
                                <rightsDelRule />
                            </fileUpload>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ImportExportTaskGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="FileName" width="200" />
                        <component ref="ModifiedName" width="120" />
                        <component ref="TaskStatus" width="120" />
                        <component ref="DocumentGUID" width="120" />
                        <component ref="ModifiedTime" width="120" />
                    </columnRefs>
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="f9452591-f64a-412e-ac4c-bc011b0dfc48" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>