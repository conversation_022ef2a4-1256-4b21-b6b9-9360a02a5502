<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-06-13T07:48:48Z" modifiedBy="夏娜" modifiedOn="2024-12-31T14:11:17.0847832+08:00" metadataStatus="Product" name="提示词输入参数" controlId="appGridpwUz" functionPageId="08dc8b7c-8383-4dee-8e84-ddd9987aa5f6" description="" htmlCache="default" enableUserSettings="false" gridId="08dc8b7d-42d5-42c1-8093-c60a2c5e5e43" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="fbb2ae05-6b45-4d78-8d6f-4d2cf9d5e3ed" controlId="0026f84f-da87-4d5d-ba99-f7cb3e0b1029" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()">
            <script><![CDATA[]]></script>
        </code>
        <code id="25cb74f7-3ebc-4135-a25f-5d21080ba31c" controlId="08dc8b7d-42d5-42c1-8093-c60a2c5e5e43" controlType="Grid" controlAction="_appGridpwUz_rowSort">
            <script><![CDATA[/**
 * 行拖拽事件
 * @param $e.row 拖拽行数据
 * @param $e.target 拖拽目标行数据
 * @param $e.action 拖拽动作，'before' 插入目标数据前面，'after' 插入目标数据后面，'disabled' 目标行禁止插入数据
 * @example
 * $e.action = 'disabled'
 * //do something
 */
var d = $util.deferred()
$_.delay(function () {
  d.resolve()
}, 10)
$_.delay(function() {
  var data = $grid.getData()
  data.forEach((item, key) => {
    $grid.updateRow(item, {Sort: key})
  })
}, 100)
return d
]]></script>
        </code>
        <code id="f679edb0-4f0a-458b-95e2-0811df5c88da" controlId="258be359-45d6-4d4d-820e-fc188327c839" controlType="ToolbarItem" controlAction="_appGridpwUz_button_58240714131625_click">
            <script><![CDATA[var len = $grid.getData().length
var newRow = $grid.addRow({Sort: len}, null, true); // 在最后一行新增空行，并从服务器生成行ID
$grid.editRow(newRow);]]></script>
        </code>
        <code id="7202083a-73dc-485b-a3f5-13121ef14c2d" controlId="8f9e6710-1d7e-46ed-9489-17910ebc0e81" controlType="ColumnControl" controlAction="_appGridpwUz_paramName_validation">
            <script><![CDATA[var isRepeat = $grid.getData().some(function(item) {
    if (item.ParamGUID === $e.row.ParamGUID) return false
    return item.ParamName === $e.value
  })
  if (isRepeat) {
    $e.isValid = false
    $e.errorText = "参数名称不允许重复"
  }]]></script>
        </code>
        <code id="9e3d2a2e-b0a9-483a-bfc4-badf6c49c245" controlId="dde95615-1a86-49e0-b670-6b75db834cac" controlType="ColumnControl" controlAction="_appGridpwUz_paramCode_validation">
            <script><![CDATA[$Utility.validateGridCode($e, $grid.getData())]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="none" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_PromptParam" entityId="08dc8b7a-033c-4f51-8562-af8ed210903a" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="true" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc8b7d-42d7-41e4-846c-2fc3ab78dbb7" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="258be359-45d6-4d4d-820e-fc188327c839" title="新增参数" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58240714131625" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGridpwUz_button_58240714131625_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc8b7d-42d7-4276-8884-2ff7fb6d9cba" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="0026f84f-da87-4d5d-ba99-f7cb3e0b1029" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240714148904" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onrowsort" functionName="_appGridpwUz_rowSort" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dc8b7d-42d7-42db-8e88-8844774ba399" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="36965c8f-c008-4505-bc92-ff95401deaff" title="新规则" controlId="258be359-45d6-4d4d-820e-fc188327c839" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58240714131625" metadataStatus="Product">
                    <handles>
                        <handle handleId="e6888a97-be77-477d-9891-9320207103f4" ruleId="2869eaec-f269-4da0-bc06-7f6163f2256a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="e27d30a0-5a17-453a-b0ce-5d9b9b4b5a7a" title="新规则" controlId="08dc8b7d-42d5-45ff-891f-8da5fe07fc09" controlType="gridview" controlSubType="" controlProp="hideToolbar" controlName="08dc8b7d-42d5-45ff-891f-8da5fe07fc09" metadataStatus="Product">
                    <handles>
                        <handle handleId="e2460353-f93c-4058-97b1-b6429dccdf7f" ruleId="2869eaec-f269-4da0-bc06-7f6163f2256a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="2869eaec-f269-4da0-bc06-7f6163f2256a" title="查看模式" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dc8b7d-42d4-4ac9-8f25-2a2468b1d281" viewId="08dc8b7d-42d5-45ff-891f-8da5fe07fc09" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc8b7a-033c-4f51-8562-af8ed210903a" isLookup="false">
                <dataSource keyName="ParamGUID" entity="gpt_PromptParam" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9ACgJwPYFtloEMMCcA6AERADMCBXMaANQLFpFgIGdZKb6mWbADQBYAFBJUmXPiIkKITpAwBLAEbsuPRcvUhRE5Omx5ohYmQBiKiABMAKgE9Em7tbtOXBycZlm5ZACSnABKIACOtCoYILYc3MFhkdGx3kbSpubywcCOnNAgOFk48bA5eQVFAWlSJrIWpMUAwli2rrDNrfriPhn18sUAciTtQyM1vpkBjQGeo7PO3Ya1fsUzMQB20IFxWuYgWzsTff4NJwDiAKqB5KUX1+THdafywFgYcFpvH0+r000xAgFWxXG6lAEgIGxUGPHrpZ5rCFQ2zDHDtJHA1FLXoI/6A4H2FRo8H42KEtG/KYNACyrRUVBU0IepVptnpjJBD0p/TIrPZsSxLLpDIF4zhKyp8j5IocRPa0o55Ox8L+ZwCMLu6uZYksJgQKslJQAgtwcaqSLBYGIAOoACxAMVgAAoAIwAXhdAEogA]]></command>
                    <fields>
                        <field name="Describe" allowPopulate="true" entity="gpt_PromptParam" field="Describe" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="FiledType" allowPopulate="true" entity="gpt_PromptParam" field="FiledType" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="IsRequired" allowPopulate="true" entity="gpt_PromptParam" field="IsRequired" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ParamCode" allowPopulate="true" entity="gpt_PromptParam" field="ParamCode" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ParamGUID" allowPopulate="true" entity="gpt_PromptParam" field="ParamGUID" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ParamName" allowPopulate="true" entity="gpt_PromptParam" field="ParamName" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ParamType" allowPopulate="true" entity="gpt_PromptParam" field="ParamType" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Sort" allowPopulate="true" entity="gpt_PromptParam" field="Sort" entityAlias="gpt_PromptParam" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_PromptParam" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_PromptParam" entityType="0" attributeType="日期与时间" />
                        <availableField name="DefaultValue" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DefaultValue" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="FiledType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="FiledType" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="IsRequired" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsRequired" entity="gpt_PromptParam" entityType="0" attributeType="整数" />
                        <availableField name="IsSystemParam" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystemParam" entity="gpt_PromptParam" entityType="0" attributeType="整数" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_PromptParam" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_PromptParam" entityType="0" attributeType="日期与时间" />
                        <availableField name="ParamCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ParamCode" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ParamGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ParamGUID" entity="gpt_PromptParam" entityType="0" attributeType="Guid" />
                        <availableField name="ParamName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ParamName" entity="gpt_PromptParam" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ParamType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ParamType" entity="gpt_PromptParam" entityType="0" attributeType="整数" />
                        <availableField name="ParentId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ParentId" entity="gpt_PromptParam" entityType="0" attributeType="Guid" />
                        <availableField name="PromptGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="PromptGUID" entity="gpt_PromptParam" entityType="0" attributeType="Guid" />
                        <availableField name="Sort" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Sort" entity="gpt_PromptParam" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_PromptParam" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc8b7d-42e6-4520-80b5-2acb152f002b" id="08dc8b7a-033c-4f51-8562-af8ed210903a" name="gpt_PromptParam" primaryField="ParamGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_PromptParam.ParamType" operatorType="eq" id="5de88d25-ee09-4efa-a48d-80744753715d" dataType="number" valueType="0" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:long">1</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ParamGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="true" fixedColumns="0" maxWrapRow="0" editMode="2" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="参数名称" width="120" field="ParamName" allowEdit="true" customizeReferenceable="false" id="e7200239-2d49-4075-8f7f-c07699f24c3a" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="8f9e6710-1d7e-46ed-9489-17910ebc0e81" field="ParamName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events>
                                    <event name="onvalidation" functionName="_appGridpwUz_paramName_validation" enabled="true" metadataStatus="Product" />
                                </events>
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="参数编码" width="120" field="ParamCode" allowEdit="true" customizeReferenceable="false" id="964e6c0e-4e83-4206-996b-c1e6524d47f3" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="dde95615-1a86-49e0-b670-6b75db834cac" field="ParamCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events>
                                    <event name="onvalidation" functionName="_appGridpwUz_paramCode_validation" enabled="true" metadataStatus="Product" />
                                </events>
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="是否必填" width="120" field="IsRequired" allowEdit="true" customizeReferenceable="false" id="5821f1d9-128e-47be-9258-5aae62501e63" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <comboBox id="d08a808d-6527-4878-b6e2-bc2541d4036f" field="IsRequired" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="1" text="是" isDefault="true" disabled="false" />
                                    <option value="0" text="否" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                        <column title="字段类型" width="120" field="FiledType" allowEdit="true" customizeReferenceable="false" id="1620f9eb-c501-42df-9c43-92268aaf36c2" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <comboBox id="8d4d6032-9c46-4859-ac49-5a45e326fb9e" field="FiledType" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="string" text="文本" isDefault="true" disabled="false" />
                                    <option value="number" text="数值" isDefault="false" disabled="false" />
                                    <option value="date" text="日期" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                        <column title="参数描述" width="120" field="Describe" allowEdit="true" customizeReferenceable="false" id="a463f31c-b579-45e9-b14e-4165a43a0f03" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="70e5e3d2-ac77-464a-a9f2-faffa7781618" field="Describe" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="排序" width="120" field="Sort" allowEdit="false" customizeReferenceable="false" id="929e3359-76cc-4f22-96c9-f14d44213b0e" isHidden="true" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <spinner id="eb2e46c4-9984-4131-bf36-4d6a432c7b33" field="Sort" errorMode="default" readonlyMode="none" defaultValue="0.00" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="2" precisionType="0" rounding="-1" roundingType="0" showThousandths="false" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="Sort" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="92e9fd0f-082d-44fe-8884-d8d0d1054c19" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens>
                        <hidden id="08dc8b7a-21a0-49e5-8211-8f8ab8bdb7c0" field="ParamType" errorMode="default" readonlyMode="none" defaultValue="1" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false">
                            <customProps />
                            <events />
                        </hidden>
                    </hiddens>
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>