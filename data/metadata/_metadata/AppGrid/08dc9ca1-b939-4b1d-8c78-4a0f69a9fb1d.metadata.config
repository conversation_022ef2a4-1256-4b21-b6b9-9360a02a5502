<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-05T03:22:39Z" modifiedBy="郑越" modifiedOn="2024-07-08T17:00:57.2010548+08:00" metadataStatus="Product" name="keyword" controlId="appGridd6I3" functionPageId="08dc9ca0-4f5f-45bb-86b1-7d7b8eccb2c0" description="" htmlCache="default" enableUserSettings="false" gridId="08dc9ca1-b939-4b1d-8c78-4a0f69a9fb1d" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="none" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityId="08dc9b2a-a112-4a7e-897b-88ed13137a51" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc9ca1-b939-4b52-8858-d57280b284ae" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc9ca1-b939-4b62-881f-04e02d61c5c8" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc9ca1-b939-4b72-85f1-8bd65e77e887" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dc9ca1-b938-4fdf-87c3-5710690dcb89" viewId="08dc9ca1-b939-4b2f-8f80-8a2e74864709" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc9b2a-a112-4a7e-897b-88ed13137a51" isLookup="false">
                <dataSource keyName="KnowledgeTaskKeyWordGUID" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QBRANwEMwBXc6ASw3gBVyBnAawBERpy6w0IAJ4B1LACd8AOgDCWALaJy4kACUQrKmDhtYcxcrUat0ADQBYAFBJUmXASJlKNeoxYduvfoNETpASQx8EAAPWF1A4JCLa2R0bDwQQhIKaloGZjYuHj4BYTFJKR8C/HDWWGK/GJt4+yTHVJcM92yvPN9Cu0Tkls9cgHEAVX9OMoqEhxBenP4hkeq4rsmnNNdMjxn2ktkVWiS50d0ZXeh94c4F2wn6lOd0tyy+73y/HZA9/AA5cnkQMeP3qcvj8QJdat0Gnc1tM2pVCgCPkw6L9/ickkjfmCljcVk0HhtYS9Otcelk4fgDmNsaSOOSDliSZDVs1HptyVIALJYfB0ABmdDOIzGXJ5/MFFysNWpTLx61auXZIr5AuBKN0SrFqtBksWjNuzPx8ueHWkGpVGL+6u5yvRyL+lgAYuIFAhdXVkrj7nKnls/LAAILlKV6z3Q1mEk2wWCWEQACxAKlgAAoAIwAXhTAEogA===]]></command>
                    <fields>
                        <field name="CompareResult" allowPopulate="true" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" field="CompareResult" entityAlias="gpt_KnowledgeEvaluatingTaskDetailKeyWord" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Index" allowPopulate="false" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" field="Index" entityAlias="gpt_KnowledgeEvaluatingTaskDetailKeyWord" metadataStatus="Product" />
                        <field name="KeyWord" allowPopulate="true" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" field="KeyWord" entityAlias="gpt_KnowledgeEvaluatingTaskDetailKeyWord" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="KnowledgeTaskKeyWordGUID" allowPopulate="true" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" field="KnowledgeTaskKeyWordGUID" entityAlias="gpt_KnowledgeEvaluatingTaskDetailKeyWord" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CompareResult" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="CompareResult" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="日期与时间" />
                        <availableField name="Index" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Index" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="整数" />
                        <availableField name="KeyWord" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KeyWord" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="KnowledgeTaskDetailGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeTaskDetailGUID" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="Guid" />
                        <availableField name="KnowledgeTaskKeyWordGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeTaskKeyWordGUID" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="日期与时间" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeEvaluatingTaskDetailKeyWord" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc9ca1-b93a-41a3-8557-d50136c936b3" id="08dc9b2a-a112-4a7e-897b-88ed13137a51" name="gpt_KnowledgeEvaluatingTaskDetailKeyWord" primaryField="KnowledgeTaskKeyWordGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="true" rowToolbarWidth="0" frozenToolbar="true" idField="KnowledgeTaskKeyWordGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="关键词" width="203" field="KeyWord" allowEdit="false" customizeReferenceable="false" id="782df8af-4dc5-44bc-9835-67484d919d6f" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="2d437414-ac33-4916-b09b-835d5691d7a2" field="KeyWord" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="命中结果(自动检测)" width="166" field="CompareResult" allowEdit="false" customizeReferenceable="false" id="c85d3af0-c78a-43e2-bf04-f93da9a5d5b1" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="45311c33-94b8-458b-a0d8-49f1466e66ae" field="CompareResult" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="Index" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="21e8dff5-8baa-4b16-a71c-00b26b9119b6" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>