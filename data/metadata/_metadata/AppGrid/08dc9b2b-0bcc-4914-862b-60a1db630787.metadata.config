<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-03T06:40:36Z" modifiedBy="郑越" modifiedOn="2024-07-08T17:51:00.4169027+08:00" metadataStatus="Product" name="新增关键词" functionPageId="08dc9b2b-0bc9-4862-8f05-6602ffcae878" description="" htmlCache="default" enableUserSettings="false" gridId="08dc9b2b-0bcc-4914-862b-60a1db630787" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="c63324d3-d69b-49c8-ba7f-8ff608d4a307" controlId="c111e865-22cb-42ed-964d-2ff94f87b142" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Grid.deleteCurrentRow()">
            <script><![CDATA[]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" showTitle="false" entityName="gpt_KnowledgeEvaluatingData" entityId="08dc9b26-66f1-47fc-8f0c-b72468fc97c2" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc9b2b-0bcc-49ad-826f-b0d54d7e3f79" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="cef8d116-a5ea-465e-8012-db7aeb55b9b9" title="新增测试集" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_add" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000402/08dc9b2f-a3ba-476d-8749-b20bd2707882" id="08dc9b2f-a3ba-476d-8749-b20bd2707882" itemId="03acd6c7-e2e3-429e-9301-c2cbe4e4a535" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc9b2b-0bcc-49bc-87fd-04dccd248b34" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="33fd7feb-9c3a-4f1d-8e58-57fbe51af3e2" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_edit" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000402/08dc9b2f-a3ba-476d-8749-b20bd2707882" id="08dc9b2f-a3ba-476d-8749-b20bd2707882" itemId="189e046f-47e6-46b5-8c92-5975f4efd113" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="data" key="oid" value="KnowledgeEvaluatingDataGUID" />
                                        <param type="text" key="mode" value="2" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="c111e865-22cb-42ed-964d-2ff94f87b142" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_del" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.deleteCurrentRow()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc9b2b-0bcc-49cb-89d7-d4ffda54150f" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="20ed0493-43bb-40db-a431-8ea2809ab86f" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="600d816e-f7bc-4855-9139-57264fa55580" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="600d816e-f7bc-4855-9139-57264fa55580" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" union="false" wildcard="true">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="Name" title="数据集名称" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dc9b2b-0bcb-4ecc-8d0b-226ba9081a76" viewId="08dc9b2b-0bcc-4950-8ff1-6e1d6e527bdd" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc9b26-66f1-47fc-8f0c-b72468fc97c2" isLookup="false">
                <dataSource keyName="KnowledgeEvaluatingDataGUID" entity="gpt_KnowledgeEvaluatingData" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QBRANwEMwBXc6ASw3gBFbyA6AOXIFsRZyAzrC68ANAFgAUElSZcBImUo16jFtHYAlEN3IAnANb8h23YYnTk6bHhCESFarQbNWbAMJ6QtOwHEAqgCSTMawnt7QfkFMFjLW8naKjiou6uzhPvgifIJhXpnZsVZytvZKTqquGh75kfgAKnS8oRl1jWJScSUKDsrOam7dib0VqawBwaFDZcn9VeQTMZ3FNj3lKQPVALJY+HQAZnRRk7k7e4fHS5ayq8Prc2lsZwdHWTw5Qs8Xbx3X8aVJPqVR5fV7tD6wUF2cGwSQAMT0WG4CBWCRmQLGGlgAEEhF1bujRptyLBYQB1AAWIC8sAAFABGAC89IAlEA===]]></command>
                    <fields>
                        <field name="KnowledgeEvaluatingDataGUID" allowPopulate="true" entity="gpt_KnowledgeEvaluatingData" field="KnowledgeEvaluatingDataGUID" entityAlias="gpt_KnowledgeEvaluatingData" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedName" allowPopulate="true" entity="gpt_KnowledgeEvaluatingData" field="ModifiedName" entityAlias="gpt_KnowledgeEvaluatingData" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_KnowledgeEvaluatingData" field="ModifiedTime" entityAlias="gpt_KnowledgeEvaluatingData" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Name" allowPopulate="true" entity="gpt_KnowledgeEvaluatingData" field="Name" entityAlias="gpt_KnowledgeEvaluatingData" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="日期与时间" />
                        <availableField name="KnowledgeEvaluatingDataGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeEvaluatingDataGUID" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="日期与时间" />
                        <availableField name="Name" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Name" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="Remark" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Remark" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeEvaluatingData" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc9b2b-0bcc-4d92-8c19-b2aefba4aa6e" id="08dc9b26-66f1-47fc-8f0c-b72468fc97c2" name="gpt_KnowledgeEvaluatingData" primaryField="KnowledgeEvaluatingDataGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="KnowledgeEvaluatingDataGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="数据集名称" width="120" field="Name" allowEdit="false" customizeReferenceable="false" id="3f84b284-272c-4bac-825b-661fb157cec1" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="0cd4fc37-d87b-4b39-b490-9b7ca9d1cf66" field="Name" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000402/08dc9b2f-a3ba-476d-8749-b20bd2707882" id="08dc9b2f-a3ba-476d-8749-b20bd2707882" itemId="451f693e-a894-4f2c-bdb0-633f4c087d46" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="data" key="oid" value="KnowledgeEvaluatingDataGUID" />
                                        <param type="text" key="mode" value="3" />
                                    </params>
                                    <events />
                                </behavior>
                            </behaviors>
                        </column>
                        <column title="数据量" width="48" field="DetailNum" allowEdit="false" customizeReferenceable="false" id="c854e7fe-5221-4bbb-aad6-170c179a3af9" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="number" align="right" dataSourceType="CalculateColumn" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <spinner id="b7c4ff21-d1c1-4938-b835-997bea7e6fe6" field="DetailNum" errorMode="default" readonlyMode="none" defaultValue="0.00" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="-1" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <subControlFormula category="Sql" queryMode="Subquery" fieldName="DetailNum">
                                <Id>00000000-0000-0000-0000-000000000000</Id>
                                <RelationTables />
                                <moql>
                                    <content><![CDATA[select count(1) from gpt_KnowledgeEvaluatingDataDetail a 
where a.KnowledgeEvaluatingDataGUID = gpt_KnowledgeEvaluatingData.KnowledgeEvaluatingDataGUID]]></content>
                                </moql>
                                <databaseSqls />
                            </subControlFormula>
                            <behaviors />
                        </column>
                        <column title="修改人" width="80" field="ModifiedName" allowEdit="false" customizeReferenceable="false" id="bc4e5c6a-318f-4481-a04c-9c6112be05cf" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="c6a1183b-583f-4ee6-a04d-6ff873053f85" field="ModifiedName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="修改时间" width="90" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="d38bfd5e-78d7-464c-9ffe-1c724bce9080" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <datePicker id="4878355d-c9dd-4123-9f2a-946f33686e95" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm:ss" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="f41e05e3-c7aa-480d-b649-d9926ea34c7e" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>