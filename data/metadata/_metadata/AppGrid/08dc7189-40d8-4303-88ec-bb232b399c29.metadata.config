<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-11T07:09:09Z" modifiedBy="万桥" modifiedOn="2025-02-06T17:16:36.4368402+08:00" metadataStatus="Product" name="插件列表控件" functionPageId="08dc7189-40d5-42f8-86eb-18a23ba89901" description="" htmlCache="default" enableUserSettings="false" gridId="08dc7189-40d8-4303-88ec-bb232b399c29" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="9fb25aca-f10a-43b2-97d1-10108e2df035" controlId="08dc7189-40d8-4303-88ec-bb232b399c29" controlType="Grid" controlAction="_appGrid_query">
            <script><![CDATA[$Utility.validateSpace($e)]]></script>
        </code>
        <code id="29c66389-fa9e-44f1-84ad-cabac8946613" controlId="1dbea1c4-ce72-4cbb-9abf-d70c4903c0a5" controlType="ToolbarItem" controlAction="_appGrid_button_58243211035231_beforeOpen">
            <script><![CDATA[if ($e.params.SpaceGUID == null || $e.params.SpaceGUID == '') {
  $e.cancel = true 
  $notify.warning('请先选择工作空间');
  $grid.reload()
}]]></script>
        </code>
        <code id="7c8ef5a0-5fe0-4cbd-a084-28c6ad0079bf" controlId="d07c19c6-9fc3-4b24-b659-160bf3b22c16" controlType="ToolbarItem" controlAction="_appGrid_button_58238029603643_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */
$page.refreshData()
]]></script>
        </code>
        <code id="e0a70303-f5a1-408c-bfe5-8f56aa84e41f" controlId="e5906614-144f-42e8-8648-7e2dde0dc575" controlType="ToolbarItem" controlAction="_appGrid_button_58243222082737_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

$notify.confirm('确认删除','请确认是否删除选中的记录？', 'warn').then(
  function(){
    var row = $e.selecteds[0]
    console.log(row)
    return $api.pluginManager.deletePlugin({pluginGUID: row.PluginGUID}).then(function () {
      $notify.success('删除成功！')
      $grid.reload()
    })
  },
  function(){}
)]]></script>
        </code>
        <code id="5d4146ed-87ce-4720-bce0-f6c8933e4c67" controlId="f5922cc1-bdba-4008-8c4d-6282c4bf1521" controlType="ToolbarItem" controlAction="_appGrid_button_58238029602710_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

$notify.confirm('确认删除','请确认是否删除选中的记录？', 'warn').then(
  function(){
    var row = $e.selecteds[0]
    console.log(row)
    return $api.pluginManager.deletePlugin({pluginGUID: row.PluginGUID}).then(function () {
      $notify.success('删除成功！')
      $grid.reload()
    })
  },
  function(){}
)]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000801" service="pluginManager" action="deletePlugin" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_Plugin" entityId="08dc7166-aeee-4ad2-8270-5173d61f54e9" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc7189-40d8-45d8-82c4-767ed031b8eb" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="1dbea1c4-ce72-4cbb-9abf-d70c4903c0a5" title="新增插件" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58243211035231" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onbeforeopen" functionName="_appGrid_button_58243211035231_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000801/08dc7189-cb47-4d27-8792-4f44334f7d44" id="08dc7189-cb47-4d27-8792-4f44334f7d44" itemId="29e27073-6d7c-4e1f-a886-4fb2cf6b4845" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="filter" key="SpaceGUID" value="[complex:SpaceGUID]" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc7189-40d8-45ea-823d-4e2d0be2ee64" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="7ac7ee57-97ce-4004-96bd-aca6641ed913" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243222083435" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000801/08dc7189-cb47-4d27-8792-4f44334f7d44" id="08dc7189-cb47-4d27-8792-4f44334f7d44" itemId="6c16b8f5-6b40-418f-a406-d59aad14550a" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="text" key="oid" value="[oid]" />
                                        <param type="text" key="mode" value="2" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="e5906614-144f-42e8-8648-7e2dde0dc575" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243222082737" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="true" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58243222082737_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="8d267a28-3e70-4247-911b-83465b67f8e4" title="查看引用" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243222081929" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="true" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="73a736b6-e200-4d06-8f9f-c2c2e9cbc3b2" title="更多" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="menu_58238029612791" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="d07c19c6-9fc3-4b24-b659-160bf3b22c16" title="调整空间" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58238029603643" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="true" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appGrid_button_58238029603643_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42001301/08dca725-9a6e-4024-8d15-0577a7d00fb7" id="08dca725-9a6e-4024-8d15-0577a7d00fb7" itemId="1eace157-8be1-4a36-8b62-a953529b63bf" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="720" />
                                                <option key="height" value="360" />
                                            </options>
                                            <params>
                                                <param type="data" key="spaceGUID" value="SpaceGUID" />
                                                <param type="text" key="scene" value="PLUGIN" />
                                                <param type="data" key="businessGUID" value="PluginGUID" />
                                                <param type="data" key="businessCode" value="PluginCode" />
                                                <param type="data" key="businessName" value="PluginName" />
                                                <param type="text" key="mode" value="1" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="f5922cc1-bdba-4008-8c4d-6282c4bf1521" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58238029602710" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="_appGrid_button_58238029602710_click" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onquery" functionName="_appGrid_query" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dc7189-40d8-45fb-84a9-ce99659376ca" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="835a5c7a-a8a3-45e6-abef-b412de1ebadb" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="7e2d789b-20ec-4bce-9d5e-3bf1808f3d59" ref="SpaceGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="62b4d77a-00df-46eb-833f-07a2d2ee9cf4" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="49372ceb-4cd8-4a53-b725-000a35126453" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="7e2d789b-20ec-4bce-9d5e-3bf1808f3d59" name="SpaceGUID" metadataStatus="Product">
                    <label title="工作空间" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="SpaceGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="pageHandler" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="false" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <pageHandler pageHandlerId="/api/42001301/workspaceList" method="Load" extMethod="" apiSourceType="" />
                        <options />
                    </comboBox>
                </component>
                <component id="49372ceb-4cd8-4a53-b725-000a35126453" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" union="false" wildcard="false">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="PluginName" title="插件名称" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                            <findField field="PluginCode" title="插件编码" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dc7189-40d7-475f-8da0-06449b480bdc" viewId="08dc7189-40d8-434a-8673-dfcb98ce50b7" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc7166-aeee-4ad2-8270-5173d61f54e9" isLookup="false">
                <dataSource keyName="PluginGUID" entity="gpt_Plugin" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9ACmArvAlgOwDoBBTaACwFkB7AExFgEMBnWE86ugGgFgAoJVBmz4CAYQBOIBtBAAVKgGsQeAKriwjFhKkz5S1ep79k6LLkIAREE0jicAI3rNYVm3cdGBp4YQCSTYABPJhkAW01YfyCQkFDPEyFzAjQGcQZQgBkcEIiUtMzs6HjBMxFE/FFaJxZyvEquPi9a5NK8ADl06thajtCQYu8k2tlAxC7h0f7GhNaWnzUNZ1qFgebgKkxxSC71ze3V2d2tkCJERHqdjePT86qDnwJgRAZtgHEVXwsIp5eQd8/7kltNIQDR/l9nMCZGCPhZASIoaDel1ETRkfDCKjZDg+hEsTipsYSg8ODgAGY4UHgiKkilU2EYgi0ylozo02jklno6bEpLM0HY3HOfk0QWEpqzWrUpatam8ABi4io4QlPlYLFV5lgsF4AHUyCBJLAABQARgAvKaAJRAA=]]></command>
                    <fields>
                        <field name="ModifiedName" allowPopulate="true" entity="gpt_Plugin" field="ModifiedName" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_Plugin" field="ModifiedTime" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PluginCode" allowPopulate="true" entity="gpt_Plugin" field="PluginCode" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PluginGUID" allowPopulate="true" entity="gpt_Plugin" field="PluginGUID" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PluginName" allowPopulate="true" entity="gpt_Plugin" field="PluginName" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PluginType" allowPopulate="true" entity="gpt_Plugin" field="PluginType" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Source" allowPopulate="true" entity="gpt_Plugin" field="Source" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SpaceGUID" allowPopulate="false" entity="gpt_Plugin" field="SpaceGUID" entityAlias="gpt_Plugin" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="AuthMode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="AuthMode" entity="gpt_Plugin" entityType="0" attributeType="整数" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Plugin" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Plugin" entityType="0" attributeType="日期与时间" />
                        <availableField name="CreateTokenUrl" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreateTokenUrl" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Plugin" entityType="0" attributeType="整数" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Plugin" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Plugin" entityType="0" attributeType="日期与时间" />
                        <availableField name="ParamList" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ParamList" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="PluginCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="PluginCode" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="PluginGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PluginGUID" entity="gpt_Plugin" entityType="0" attributeType="Guid" />
                        <availableField name="PluginName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="PluginName" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="PluginType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="PluginType" entity="gpt_Plugin" entityType="0" attributeType="整数" />
                        <availableField name="PluginUrl" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PluginUrl" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="Source" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Source" entity="gpt_Plugin" entityType="0" attributeType="整数" />
                        <availableField name="SourceAppCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SourceAppCode" entity="gpt_Plugin" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Plugin" entityType="0" attributeType="Guid" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Plugin" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc7189-40d9-4f43-8f11-ca22c1fd7c6f" id="08dc7166-aeee-4ad2-8270-5173d61f54e9" name="gpt_Plugin" primaryField="PluginGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="PluginGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="插件名称" width="200" field="PluginName" allowEdit="false" customizeReferenceable="false" id="622572a3-e262-436d-b9ed-ab8fc6ef33e7" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="6df1d660-32b7-4bc1-9528-ffffa2852e71" field="PluginName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000801/08dc7189-cb47-4d27-8792-4f44334f7d44" id="08dc7189-cb47-4d27-8792-4f44334f7d44" itemId="21a10652-5353-40ba-bc6b-fd1813956e6f" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="text" key="oid" value="[oid]" />
                                        <param type="text" key="mode" value="3" />
                                    </params>
                                    <events />
                                </behavior>
                            </behaviors>
                        </column>
                        <column title="插件编码" width="120" field="PluginCode" allowEdit="false" customizeReferenceable="false" id="e391a6d8-fc24-47fe-a51f-78d553ce5493" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="1ccab4df-4d8c-45fb-9c90-bc007005d054" field="PluginCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="插件类型" width="120" field="PluginType" allowEdit="false" customizeReferenceable="false" id="32ca06e5-ddaa-4dad-958a-9515dc1ddc86" isHidden="true" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <comboBox id="b0e5646b-2c58-4cdc-8094-7592c807def2" field="PluginType" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="0" text="服务插件" isDefault="false" disabled="false" />
                                    <option value="1" text="交互插件" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                        <column title="插件类型" width="120" field="Source" allowEdit="false" customizeReferenceable="false" id="f291cb32-4e25-407c-9ff9-633e78a847eb" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <statusLabel id="9085a197-a89f-4aaf-b083-539d7c0b3019" field="Source" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options">
                                <customProps />
                                <events />
                                <options>
                                    <option value="0" text="明源应用API" style="success" />
                                    <option value="1" text="第三方API（集成）" style="primary" />
                                    <option value="2" text="第三方API" style="dignify" />
                                </options>
                            </statusLabel>
                            <behaviors />
                        </column>
                        <column title="修改人" width="120" field="ModifiedName" allowEdit="false" customizeReferenceable="false" id="df4a91d2-ba77-4246-957c-84ae5fd80dc7" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="19b62c8b-7960-476b-8b75-37822ebb955d" field="ModifiedName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="修改时间" width="120" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="191c5d0c-ff9c-4063-8bd4-549100a3963d" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <datePicker id="669e2444-8067-44a8-9cef-52319aaab80e" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="19eb4d16-50f1-48f7-95ee-9a3563eb44fe" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>