<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-24T08:12:08Z" modifiedBy="黄浩翔" modifiedOn="2025-07-24T16:58:34.3665518+08:00" metadataStatus="Product" name="工具参数" controlId="appGridweYi" functionPageId="08ddc91e-15c7-4736-8850-be390e43c1e8" description="" htmlCache="default" enableUserSettings="false" gridId="08ddca89-c88f-4b34-86d9-205f44a972b0" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="false" templateStyle="default" pageStyle="none" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_McpServiceTool" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08ddca89-c88f-4dc4-8861-b21a613fe1e5" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08ddca89-c88f-4dda-8b3e-38dcbbf311ab" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08ddca89-c88f-4dee-8cc3-9c3aba444109" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08ddca89-c88f-49f9-8f88-7863ebc16592" viewId="08ddca89-c88f-4b7f-89c1-4405bdca41ac" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" isLookup="false">
                <dataSource keyName="ToolGUID" entity="gpt_McpServiceTool" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AspRoBOA3AlpCACoD2pYAdAJIB2iArtMJABYgC2AhrFwM6w6jZm05cANAFgAUElQYsIPIRLkqAGX7RiIPtCFNeAzXp16D0KbOTpMOAkTIVKJ7bugB5JsKOxXZz28mKzlbRWVHNUovaGEWdm5fGLjRbhCbBXsVJyosogBxAFVqABFfPJAi0vT5OyUHVWdgRC4C4rL+WGbWyvaasIqcymBoLmgGAU6RsYn+zPrsqJyS3UhsfGR8UlpfZdX1ze25uojGqhyAOS4OEF21K5vj8IahnOJ8aAg7infPkCfBlEAMLYEBjEAAEyqHQEILB0Eh0IBC0izjh4IhD1unXRCMx13+MlC81OQ1xkPeN185IhlMJ1lqz0WzjQpAh+AAZvhEe1fKz2VyedUiRkTi8ovzOdz8VTOpLBTL6cSxcyqPLpXS+WypRT8I8RYzAc4ctDvmBTdIAGLYUgcBCipmosCwACCAmVjrOsFg0gA6uxQbAABQARgAvCGAJRAA]]></command>
                    <fields>
                        <field name="ToolGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product" />
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="InputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestInput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestInput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestOutput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestOutput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="OutputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="OutputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ServiceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_McpServiceTool" entityType="0" attributeType="整数" />
                        <availableField name="ToolDescription" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolDescription" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ToolGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ToolName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ToolTitle" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolTitle" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_McpServiceTool" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08ddca89-c890-44e6-84f4-bc0c4a873024" id="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" name="gpt_McpServiceTool" primaryField="ToolGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components>
                    <component name="name" allowUnRef="false" allowRef="false">
                        <column title="参数名称" width="120" field="name" allowEdit="false" customizeReferenceable="false" id="26ae9155-b23e-45a2-8eb0-b751d26cb5a4" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="64763251-a900-4680-94ec-96fd9965b9ac" field="name" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                        </column>
                    </component>
                    <component name="type" allowUnRef="false" allowRef="false">
                        <column title="参数类型" width="120" field="type" allowEdit="false" customizeReferenceable="false" id="bd5c3418-fd6f-4743-97a3-1da520dd5eb7" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="13a4c061-ddc8-4e3e-87f3-ad1524c6af0a" field="type" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                        </column>
                    </component>
                    <component name="require" allowUnRef="false" allowRef="false">
                        <column title="是否必填" width="120" field="require" allowEdit="false" customizeReferenceable="false" id="e2e31b56-faee-4ce5-b424-8e555f0f6226" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="dc91625b-21b7-42f3-979a-a7126a30f732" field="require" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                        </column>
                    </component>
                    <component name="des" allowUnRef="false" allowRef="false">
                        <column title="参数说明" width="120" field="des" allowEdit="false" customizeReferenceable="false" id="7bb86471-6203-4933-9917-0167338ed2ff" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="ad82d8ab-3901-4e09-8aad-01eb135a5a24" field="des" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="true" rowToolbarWidth="0" frozenToolbar="true" idField="ToolGUID" multiSelect="false" showIndexColumn="false" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="name" width="120" />
                        <component ref="type" width="120" />
                        <component ref="require" width="120" />
                        <component ref="des" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="6b899399-13fd-433b-a235-c1d2e4c56ba1" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>