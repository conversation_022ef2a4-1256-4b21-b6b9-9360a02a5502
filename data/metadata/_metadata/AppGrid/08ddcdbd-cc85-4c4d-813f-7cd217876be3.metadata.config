<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-28T10:02:02Z" modifiedBy="黄浩翔" modifiedOn="2025-07-28T18:02:33.0991849+08:00" metadataStatus="Product" name="参数列表2" controlId="appGridjavw" functionPageId="08ddcb50-cc9c-4299-88e0-35c18e382fb7" description="" htmlCache="default" enableUserSettings="false" gridId="08ddcdbd-cc85-4c4d-813f-7cd217876be3" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" showTitle="false" entityName="gpt_McpServiceTool" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08ddcdbd-cc85-4c8a-8499-9b505a257a89" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08ddcdbd-cc85-4c9d-8742-eccf2183fb7c" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08ddcdbd-cc85-4cb0-895e-fe4423149287" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08ddcdbd-cc85-4b8b-8291-de048acee34a" viewId="08ddcdbd-cc85-4c63-82c8-9386c943dac9" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" isLookup="false">
                <dataSource keyName="ToolGUID" entity="gpt_McpServiceTool" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AspRoBOA3AlpCACoD2pYAdAJIB2iArtMJABYgC2AhrFwM6w6jZm05cANAFgAUElQYsIPIRLkqAGX7RiIPtCFNeAzXp16D0KbOTpMOAkTIVKJ7bugB5JsKOxXZz28mKzlbRWVHNUovaGEWdm5fGLjRbhCbBXsVJyosogBxAFVqABFfPJAi0vT5OyUHVWdgRC4C4rL+WGbWyvaasIqcymBoLmgGAU6RsYn+zPrsqJyS3UhsfGR8UlpfZdX1ze25uojGqhyAOS4OEF21K5vj8IahnOJ8aAg7infPkCfBlEAMLYEBjEAAEyqHQEILB0Eh0IBC0izjh4IhD1unXRCMx13+MlC81OQ1xkPeN185IhlMJ1lqz0WzjQpAh+AAZvhEe1fKz2VyedUiRkTi8ovzOdz8VTOpLBTL6cSxcyqPLpXS+WypRT8I8RYzAc4ctDvmBTdIAGLYUgcBCipmosCwACCAmVjrOsFg0gA6uxQbAABQARgAvCGAJRAA]]></command>
                    <fields>
                        <field name="ToolGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product" />
                        <field name="ToolName" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolName" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ToolTitle" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolTitle" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="InputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestInput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestInput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestOutput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestOutput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="OutputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="OutputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ServiceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_McpServiceTool" entityType="0" attributeType="整数" />
                        <availableField name="ToolDescription" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolDescription" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ToolGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ToolName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ToolTitle" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolTitle" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_McpServiceTool" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08ddcdbd-cc85-4f05-82ef-6897a62533ca" id="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" name="gpt_McpServiceTool" primaryField="ToolGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components>
                    <component name="ToolName" allowUnRef="false" allowRef="false">
                        <column title="工具名称" width="120" field="ToolName" allowEdit="false" customizeReferenceable="false" id="fbdc90a6-26d3-4bd4-8576-6d7114043f89" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="341ff36e-25ff-4c69-a48e-a5da6f968d12" field="ToolName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                            </textBox>
                        </column>
                    </component>
                    <component name="ToolTitle" allowUnRef="false" allowRef="false">
                        <column title="工具标题" width="120" field="ToolTitle" allowEdit="false" customizeReferenceable="false" id="03367c89-1d04-4f25-9e11-517b69d5942c" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="6afe641b-a11c-463b-95b2-3755dde72583" field="ToolTitle" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                            </textBox>
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ToolGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="ToolName" width="120" />
                        <component ref="ToolTitle" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="3041064e-53a4-48ea-b21c-3f965f9499e2" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>