<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-25T07:19:23Z" modifiedBy="黄浩翔" modifiedOn="2025-07-28T18:47:25.8177068+08:00" metadataStatus="Product" name="工具列表" controlId="appGridZLB2" functionPageId="08ddc912-7e0d-4506-8f35-55a3fd3af0ee" description="" htmlCache="default" enableUserSettings="false" gridId="08ddcb4b-94fa-422c-89fb-4ee683a4c929" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_McpServiceTool" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08ddcb4b-94fa-4268-8c72-6490545fce3e" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08ddcb4b-94fa-427b-8f67-f87f8f12b786" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="d19a2eac-695a-4490-86a4-12f1bfa8df16" title="测试运行" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58206501481800" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="panel" targetDisplayType="fixedWidth" type="page" url="/std/42000801/08ddcb50-cc9c-4283-8cf1-eb8756da7ea3" id="08ddcb50-cc9c-4283-8cf1-eb8756da7ea3" itemId="66fd4247-0378-4e39-9137-2956cdf55e99" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="1200" />
                                        <option key="slipMode" value="showMask" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="toolGUID" value="ToolGUID" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08ddcb4b-94fa-428e-86ea-e2f6dbe09a32" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08ddcb4b-94fa-4172-8249-dad9967997ec" viewId="08ddcb4b-94fa-4245-83b7-f89aac6f6b34" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" isLookup="false">
                <dataSource keyName="ToolGUID" entity="gpt_McpServiceTool" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AspRoBOA3AlpCACoD2pYAdAJIB2iArtMJABYgC2AhrFwM6w6jZm05cANAFgAUElQYsIPIRLkqAGX7RiIPtCFNeAzXp16D0KbOTpMOAkTIVKJ7bugB5JsKOxXZz28mKzlbRWVHNUovaGEWdm5fGLjRbhCbBXsVJyosogBxAFVqABFfPJAi0vT5OyUHVWdgRC4C4rL+WGbWyvaasIqcymBoLmgGAU6RsYn+zPrsqJyS3UhsfGR8UlpfZdX1ze25uojGqhyAOS4OEF21K5vj8IahnOJ8aAg7infPkCfBlEAMLYEBjEAAEyqHQEILB0Eh0IBC0izjh4IhD1unXRCMx13+MlC81OQ1xkPeN185IhlMJ1lqz0WzjQpAh+AAZvhEe1fKz2VyedUiRkTi8ovzOdz8VTOpLBTL6cSxcyqPLpXS+WypRT8I8RYzAc4ctDvmBTdIAGLYUgcBCipmosCwACCAmVjrOsFg0gA6uxQbAABQARgAvCGAJRAA]]></command>
                    <fields>
                        <field name="ServiceGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ServiceGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ToolDescription" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolDescription" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ToolGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ToolName" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolName" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="InputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestInput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestInput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="LastTestOutput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestOutput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
                        <availableField name="OutputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="OutputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ServiceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_McpServiceTool" entityType="0" attributeType="整数" />
                        <availableField name="ToolDescription" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolDescription" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ToolGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
                        <availableField name="ToolName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ToolTitle" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolTitle" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_McpServiceTool" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08ddcb4b-94fa-4430-8bf6-95f36778348f" id="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" name="gpt_McpServiceTool" primaryField="ToolGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="ToolName" allowUnRef="false" allowRef="false">
                        <column title="工具名称" width="120" field="ToolName" allowEdit="false" customizeReferenceable="false" id="a34c0bea-29ae-4239-abc8-a3282e0053e3" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="ed1691d7-b375-47f4-b818-08a9f4827eb2" field="ToolName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ToolDescription" allowUnRef="false" allowRef="false">
                        <column title="工具介绍" width="120" field="ToolDescription" allowEdit="false" customizeReferenceable="false" id="5e37db8a-8062-44aa-9e9d-24ba09260d58" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="09ae5026-3cc6-446f-9293-1d90fa76935f" field="ToolDescription" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ServiceGUID" allowUnRef="false" allowRef="false">
                        <column title="所属服务GUID" width="120" field="ServiceGUID" allowEdit="false" customizeReferenceable="false" id="639f216f-0045-440e-b6d7-157334644259" isHidden="true" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="4f6c7af0-2351-41c9-8ddd-f25e912bf6ad" field="ServiceGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ToolGUID" multiSelect="false" showIndexColumn="false" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="ToolName" width="120" />
                        <component ref="ToolDescription" width="120" />
                        <component ref="ServiceGUID" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="c8da6a8f-d531-4b41-8390-7fd2524742e5" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>