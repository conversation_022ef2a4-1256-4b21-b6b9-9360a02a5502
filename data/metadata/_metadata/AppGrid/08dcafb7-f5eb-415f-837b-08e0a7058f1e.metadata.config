<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-07-29T10:19:41Z" modifiedBy="郑越" modifiedOn="2025-03-26T09:42:27.5561261+08:00" metadataStatus="Product" name="用户会话统计基础表列表控件" functionPageId="08dcafb7-f5e6-4067-84cc-6e55041c3244" description="" htmlCache="default" enableUserSettings="false" gridId="08dcafb7-f5eb-415f-837b-08e0a7058f1e" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="19ce3512-88ca-4e9e-9036-111ab3173714" controlId="08dcafb7-f5eb-415f-837b-08e0a7058f1e" controlType="Grid" controlAction="_appGrid_query">
            <script><![CDATA[$Utility.validateSpace($e)]]></script>
        </code>
        <code id="500b4453-0714-4fad-86db-528623cf0e80" controlId="f46e2ef4-ca85-4b8a-bd41-f25dbcf98347" controlType="ToolbarItem" controlAction="_appGrid_button_58217064274391_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
var selectData = $e.selecteds;
console.log("selectData:",selectData)
if (selectData.length <=0 ) {
  $notify.alert("请选择导出对象");
  return;
}
if (selectData.length  > 20 ) {
  $notify.alert("最多支持导出20条");
  return;
}
var data = [];

selectData.forEach(function (row) {
  data.push({
    "chatGUID":row["ChatGUID"],
    "batchGUID":row["BatchGUID"],
    "skillGUID":row["skillGUID"],
  })
});
console.log(data)
$page.open("/api/42000701/chat/downloadEvalDataset",
{"downloadData":$_.toJSON(data)})]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityId="08dcafb7-5e2f-4a9b-8575-350d6268f31c" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dcafb7-f5eb-44d0-80c1-b063fad62eea" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items>
                            <item itemId="f46e2ef4-ca85-4b8a-bd41-f25dbcf98347" title="导出评测数据集" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58217064274391" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58217064274391_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dcafb7-f5e7-43df-880d-7e1eb316ec81" title=" 更多操作" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="mMore" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="08dcafb7-f5e7-440a-80e4-43694b03e878" title="打印" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mPrint" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.print(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="08dcafb7-f5e7-442c-88d3-f8d53fedc26e" title="导出" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mExport" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.exportExcel(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dcafb7-f5eb-44f1-8186-8f4e2d6ee02c" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="d7538988-c84a-44e8-8107-c3be967b5da0" title="详情" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58232757684728" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000701/08dc9657-e048-48d3-87ea-5e10b589949d" id="08dc9657-e048-48d3-87ea-5e10b589949d" itemId="e6d36244-b5fb-4f72-81d5-96ef55f6b42b" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="blank" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="chat_guid" value="ChatGUID" />
                                        <param type="data" key="assistant_guid" value="AssistantGUID" />
                                        <param type="data" key="skill_guid" value="skillGUID" />
                                        <param type="data" key="batch_guid" value="BatchGUID" />
                                        <param type="data" key="chat_user" value="UserName" />
                                        <param type="data" key="chat_time" value="CreatedTime" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onquery" functionName="_appGrid_query" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dcafb7-f5eb-450b-8b45-74c8ded00b10" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="0705fd2b-0d40-41d2-a6f6-ea1e1d9b489f" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="2" metadataStatus="Product">
                    <component id="c4d6d922-ddf0-4dee-97dc-a2e6f417122d" ref="SpaceGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="59d97bcb-c3e0-499a-9cc8-ba7f9a7f979e" showAllItems="false" dependField="SpaceGUID" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="2" metadataStatus="Product">
                    <component id="9d487613-deb6-4a89-a08a-384ed73f2bc0" ref="skillGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition isCurrent="true" id="0a6d432b-5dbf-4d3a-890c-fbe2fc2926f0" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="99958f43-c6f1-4c71-a011-46a8dee18808" ref="CreatedTime" />
                    <template><![CDATA[]]></template>
                    <searchType />
                </condition>
                <condition id="bc47ed82-523e-470f-a726-31d48e8e24bd" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="e40b5888-8022-49ed-a074-15ab75fad5f2" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="4204e5b2-56f3-45d8-952c-b00075d53073" name="SpaceGUID" metadataStatus="Product">
                    <label title="工作空间" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="SpaceGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="pageHandler" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="false" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <pageHandler pageHandlerId="/api/42001301/workspaceList" method="Load" extMethod="" apiSourceType="" />
                        <options />
                    </comboBox>
                </component>
                <component id="9d487613-deb6-4a89-a08a-384ed73f2bc0" name="skillGUID" metadataStatus="Product">
                    <label title="技能" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="skillGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="true" allowClear="true" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <options />
                        <optionsDataSource>
                            <dataSource keyName="SkillGUID" entity="gpt_Skill" withNoLock="true" mode="1">
                                <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9YBrAlmMA6AIiAM6QBOmARiLAIZGyEnlUA0AsAFBKobZ4DiAV0wATanVhDRIdl2RosOXAAk6+APaRBAWxAA7aAEE9NMAE8imIgDl1Y2vVVENW3QeOmLV22NncFfCp0AJLaNPAgHuaWNnbijiFhEVFesb6c/rxKTsEGIKQ0MJgAbiAAwjSkIj7xsDl5BUWlFVU1fvJZeMGQ6noOsN297TyKXXqY0ACipQb9uRPT+tDDAUrBRMAW0CDacxtbOyuduACyINA0IjQX/WcXVxdHo6fnNPjXNABq+Za9t6/vC7fUi/PRPQInOK3OLgpSQsRgXJEC56SDlKESeHgJEotFlGEZDrPADyiH0+EwpnU8AA6pg9CJ1AB3fqk8mUsDUukM5mwvCdCrbeDqUhmfgAVWC+HoEgF1xAwtFEqlRD5uAFAAtrvjBLNZaMylroDqDGqBRj6Ob0nIRoFzTgoMb1Lq4Pq+PiHTATctCbalJ1rDRdP0A0GZL7VvzRsDQTLLdGfpheqqI8dgIhCiBlfgQxm0dmzRdSNBxYgAApgQTwenZkNFkvlyvVvQF1PPYD10sAFXU6jAtdlncQPb7rZtkfVF2ggjjsA71xnatLnMuNAoEFny/Uq/XxDVNPAPWDEgPYCP4fHxzKpBA8pEA/o19v23vkvwaqfd8Dx8fN6/YY/P8Xy7TAf1gT9gNAi9MmeeFMAAM0wEBXylaERAQpCUPfNsITsDDkO/Wo4MQgiAJwuE8JIkQQLA4jMJo6CiTtUYHznFi31gDgADFSHUXYYL4WBDHoAScFgTiaQ1fJqAACgARgAXjkgBKIA=]]></command>
                                <fields>
                                    <field name="SkillName" allowPopulate="false" entity="gpt_Skill" field="SkillName" entityAlias="gpt_Skill" metadataStatus="Product" />
                                    <field name="SkillGUID" allowPopulate="false" entity="gpt_Skill" field="SkillGUID" entityAlias="gpt_Skill" metadataStatus="Product" />
                                    <field name="SpaceGUID" allowPopulate="false" entity="gpt_Skill" field="SpaceGUID" entityAlias="gpt_Skill" metadataStatus="Product" />
                                </fields>
                                <availableFields>
                                    <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                                    <availableField name="Guide" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Guide" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                                    <availableField name="HasDocumentAnalysisNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasDocumentAnalysisNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="HasImageAnalysisNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasImageAnalysisNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="HasInteractiveCardNode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="HasInteractiveCardNode" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="Icon" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Icon" entity="gpt_Skill" entityType="0" attributeType="图片" />
                                    <availableField name="InitEvent" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InitEvent" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                                    <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="Metadata" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Metadata" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                                    <availableField name="MetaDataVersion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="MetaDataVersion" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="Mode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Mode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(16)）" />
                                    <availableField name="ModelInstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelInstanceCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="OpenDialogWindow" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="OpenDialogWindow" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="SkillCategoryGUIDs" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillCategoryGUIDs" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(1024)）" />
                                    <availableField name="SkillChatCount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillChatCount" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="SkillCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="SkillCollectCount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillCollectCount" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="SkillName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="SkillVersions" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillVersions" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(64)）" />
                                    <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                                    <availableField name="StartUpPluginGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpPluginGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                                    <availableField name="StartUpToolGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpToolGUID" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                                    <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Status" entity="gpt_Skill" entityType="0" attributeType="整数" />
                                    <availableField name="Uploadables" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Uploadables" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
                                    <availableField name="Welcome" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Welcome" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
                                    <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                                    <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
                                    <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                                    <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
                                    <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
                                    <availableField name="SkillGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
                                    <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Skill" entityType="0" attributeType="时间戳" />
                                </availableFields>
                                <fixedSortings />
                                <summaries />
                                <diagrams>
                                    <diagram xmlAttributeId="08dcb2a3-531a-41d2-8fb9-8668c8a1d7c3" id="08dc70ba-c30b-4a8d-893d-813f8d335ba6" name="gpt_Skill" primaryField="SkillGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                        <conditions />
                                        <resourceFilters />
                                        <projectInterfaceFilters />
                                    </diagram>
                                </diagrams>
                                <performanceOptimizeHints />
                            </dataSource>
                            <extendFields>
                                <extendField displayName="文本" name="SkillName" entity="gpt_Skill" isQueryField="true" mapType="Text" />
                                <extendField displayName="值" name="SkillGUID" entity="gpt_Skill" isQueryField="false" mapType="Value" />
                                <extendField displayName="父级字段" name="SpaceGUID" entity="gpt_Skill" isQueryField="false" mapType="Parent" />
                            </extendFields>
                            <sortFields />
                        </optionsDataSource>
                    </comboBox>
                </component>
                <component id="99958f43-c6f1-4c71-a011-46a8dee18808" name="CreatedTime" metadataStatus="Product">
                    <label title="会话时间" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <dateRange field="CreatedTime" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" dataFrom="options" allowClear="true">
                        <customProps />
                        <events />
                        <options />
                    </dateRange>
                </component>
                <component id="e40b5888-8022-49ed-a074-15ab75fad5f2" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" union="false" wildcard="true">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="UserName" title="用户" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                            <findField field="Content" title="用户提问" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dcafb7-f5ea-4029-89a4-27de0d0e4169" viewId="08dcafb7-f5eb-41ae-83ed-b60f97f47c2d" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dcafb7-5e2f-4a9b-8575-350d6268f31c" isLookup="false">
                <dataSource keyName="ChatMessageGUID" entity="vs_ChatMessageBase" withNoLock="true" mode="1">
                    <command type="sql" queryDb=""><![CDATA[MoUwNiDGAuAEBuBnA+gYQBYENoFkSMUwHMQAhTREAOgEEA7RAdxACdYLZ6nWAaAWABQSNFlz5CJcpVoEAlomiY60AOIBVAJIARdok5yFS1Zq38hKDNjwFiZCtRoHFygHKYAtiF37E859DdPM2FLMRtJeypyaEh0dW1vaNj400EQ0WsJO2lQlO9ck2CLDPFbKWpQzNs8jkrSkhSikSt67IqAe2UQZXzO6G7oJrrwtqpUFhBsEAATGr1xyf7ZwrTilpHysYmp6cCvWu2lvaGSjciFnYAVWU98w5nroNXmsKzN1ABXBXbPFg1p/JfaA/Vj/E7rN7nIEglh7QHfX7HZ7DSHSABmIBmACNMJAANbeDHY3F48GvMqRABisggcI41NpHhAZKqEWkdA+7m8HPcLNamwASu0IN4hRA+WdpMA8TSwKh2tN9nppbL5YqJajqIgZWAwHNYNrZY1kadNVRDbq6XoLWAkeYXqzRsAAA64kD6l1u432lEU6RqSgsfUB1je9IQv3UEOwpneaNwgSUlg/BBrclsryOVMO/n2WCwAQAdXQrC8AAoAIwAXgrAEogA==]]></command>
                    <fields>
                        <field name="Answer" allowPopulate="false" entity="vs_ChatMessageBase" field="Answer" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="AssistantGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="AssistantGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="AssistantName" allowPopulate="false" entity="vs_ChatMessageBase" field="AssistantName" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="BatchGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="BatchGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ChatGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="ChatGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ChatMessageGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="ChatMessageGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Content" allowPopulate="false" entity="vs_ChatMessageBase" field="Content" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="CreatedTime" allowPopulate="false" entity="vs_ChatMessageBase" field="CreatedTime" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="feedback" allowPopulate="false" entity="vs_ChatMessageBase" field="feedback" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="FileName" allowPopulate="false" entity="vs_ChatMessageBase" field="FileName" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="num" allowPopulate="false" entity="vs_ChatMessageBase" field="num" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="skillGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="skillGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="skillName" allowPopulate="false" entity="vs_ChatMessageBase" field="skillName" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SpaceGUID" allowPopulate="false" entity="vs_ChatMessageBase" field="SpaceGUID" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="UserName" allowPopulate="false" entity="vs_ChatMessageBase" field="UserName" entityAlias="vs_ChatMessageBase" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="Answer" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Answer" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="AssistantGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="AssistantGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="AssistantName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="AssistantName" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="BatchGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="BatchGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="ChatGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ChatGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="ChatMessageGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ChatMessageGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="Content" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Content" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="CustomerId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CustomerId" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="CustomerName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CustomerName" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="feedback" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="feedback" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="FileName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="FileName" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="num" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="num" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="Role" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Role" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="SkillCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillCode" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="skillGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="skillGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="skillName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="skillName" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="UserGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="UserGUID" entity="vs_ChatMessageBase" entityType="1" />
                        <availableField name="UserName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="UserName" entity="vs_ChatMessageBase" entityType="1" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dcafb7-f5eb-4e03-8fd2-ec867108ff3a" id="08dcafb7-5e2f-4a9b-8575-350d6268f31c" name="vs_ChatMessageBase" primaryField="ChatMessageGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="1" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ChatMessageGUID" multiSelect="true" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="true">
                    <columns>
                        <column title="助手" width="120" field="AssistantName" allowEdit="false" customizeReferenceable="false" id="1e112f43-7edd-419d-8cee-ed44d81abb69" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="668f6159-3b03-4002-8639-00d1c1ca0a1a" field="AssistantName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="技能" width="120" field="skillName" allowEdit="false" customizeReferenceable="false" id="c08b7cfd-13bc-48f7-966c-ae127474968a" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="ccfd50a0-a581-419f-8a2f-e043b698ab6d" field="skillName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="用户" width="80" field="UserName" allowEdit="false" customizeReferenceable="false" id="064fb1f5-1f02-4c9e-98e1-49e255cd803a" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="4a4fc252-d0c6-49b5-917e-eeb945d990cb" field="UserName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="用户提问" width="300" field="input" allowEdit="false" customizeReferenceable="false" id="8969f807-b946-4e1b-83cb-e930ee36391c" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="text" align="left" dataSourceType="CalculateColumn" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="120defee-8e1b-4ba5-aea3-612fe95e5dc4" field="input" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <subControlFormula text="vs_ChatMessageBase.Content + CASE(vs_ChatMessageBase.FileName,'','','【' +  vs_ChatMessageBase.FileName + '】')" category="Simple" originalSqlExpression="CONCAT((vs_ChatMessageBase.Content),(CASE vs_ChatMessageBase.FileName  WHEN '' THEN '' ELSE CONCAT((CONCAT(('【'),(vs_ChatMessageBase.FileName))),('】')) END ))" fieldSqlExpression="CONCAT((vs_ChatMessageBase.Content),(CASE vs_ChatMessageBase.FileName  WHEN '' THEN '' ELSE CONCAT((CONCAT(('【'),(vs_ChatMessageBase.FileName))),('】')) END ))" aggreSqlExpression="CONCAT((vs_ChatMessageBase.Content),(CASE vs_ChatMessageBase.FileName  WHEN  THEN  ELSE CONCAT((CONCAT((),(vs_ChatMessageBase.FileName))),()) END ))" fontExpression="f.add(Content,f.case(FileName,&quot;&quot;,&quot;&quot;,f.add(f.add(&quot;【&quot;,FileName),&quot;】&quot;)))" queryMode="Subquery" tableName="vs_ChatMessageBase" expressionValueClrType="System.String" fieldName="input">
                                <Id>08dcd304-2c42-4e5c-868a-0c91a410f27b</Id>
                                <RelationTables>
                                    <RelationTable name="vs_ChatMessageBase" id="08dcafb7-5e2f-4a9b-8575-350d6268f31c">
                                        <RelationFields>
                                            <RelationField name="Content" id="08dcafb7-3c1e-458a-87e4-0e2f331b15db" aliasName="Content" type="string" />
                                            <RelationField name="FileName" id="08dcd07d-3806-486b-811a-a22640f63d39" aliasName="FileName" type="string" />
                                        </RelationFields>
                                    </RelationTable>
                                </RelationTables>
                                <databaseSqls />
                            </subControlFormula>
                            <behaviors />
                        </column>
                        <column title="AI回答" width="322" field="Answer" allowEdit="false" customizeReferenceable="false" id="d4d23b22-949a-4c80-ac10-f9622c9eed5e" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="&lt;p&gt;AI回答内容只显示前1024 字符内容&lt;/p&gt;" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="6a8281df-e54d-449f-9c23-63ee82edd2d1" field="Answer" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="会话时间" width="120" field="CreatedTime" allowEdit="false" customizeReferenceable="false" id="7bc7d893-1095-4914-b4de-c640383a4d36" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <datePicker id="f77d9c68-0307-471d-8cac-619d6c4b0045" field="CreatedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                        <column title="反馈状态" width="60" field="feedback" allowEdit="false" customizeReferenceable="false" id="4443964a-73b2-43f1-8d19-70191f733070" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <statusLabel id="66cfbb6e-accf-4ae4-9e46-c1c467877fbe" field="feedback" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options">
                                <customProps />
                                <events />
                                <options>
                                    <option value="1" text="点赞" style="success" />
                                    <option value="2" text="点踩" style="danger" />
                                </options>
                            </statusLabel>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="CreatedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                        <sort field="num" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="ba15ca4a-f038-4b2a-95af-02ae6cc362f6" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>