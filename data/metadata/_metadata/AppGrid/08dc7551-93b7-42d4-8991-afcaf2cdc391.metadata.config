<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-05-16T02:40:41Z" modifiedBy="李昂" modifiedOn="2024-05-16T10:45:36.3258955+08:00" metadataStatus="Product" name="督办表列表控件" functionPageId="08dc7551-93b0-4b46-8882-a86548edda51" description="" htmlCache="default" enableUserSettings="false" gridId="08dc7551-93b7-42d4-8991-afcaf2cdc391" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" showTitle="false" entityName="gpt_Supervision" entityId="08dc747f-3239-4635-8aa4-c839f01a4ef2" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc7551-93b7-4775-80eb-bff9ce303ad1" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dc7551-93b2-4194-860d-d9b46d55bf55" title="新增" isHighlight="true" type="button" id="mAdd" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42009001/08dc7551-93b1-4457-820d-c85d38b876d9" id="08dc7551-93b1-4457-820d-c85d38b876d9" metadataStatus="Product">
                                    <options />
                                    <params>
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc7551-93b2-4232-833f-9824f9815adb" title=" 更多操作" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="mMore" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="08dc7551-93b2-4256-8551-d90ed93b3e29" title="打印" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mPrint" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.print(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="08dc7551-93b2-427a-81a9-b63d65dec2cb" title="导出" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mExport" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.exportExcel(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc7551-93b7-4796-81b1-2cab7c9fcaa6" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dc7551-93b2-429c-861e-9824bd38a257" title="编辑" isHighlight="false" type="button" id="mEditRow" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42009001/08dc7551-93b1-4457-820d-c85d38b876d9" id="08dc7551-93b1-4457-820d-c85d38b876d9" metadataStatus="Product">
                                    <options />
                                    <params>
                                        <param type="text" key="mode" value="2" />
                                        <param type="text" key="oid" value="[oid]" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc7551-93b2-42c3-88c5-7b39ac03a4eb" title="详情" isHighlight="false" type="button" id="mDetails" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42009001/08dc7551-93b1-4457-820d-c85d38b876d9" id="08dc7551-93b1-4457-820d-c85d38b876d9" metadataStatus="Product">
                                    <options />
                                    <params>
                                        <param type="text" key="mode" value="3" />
                                        <param type="text" key="oid" value="[oid]" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc7551-93b2-42e9-89e9-8da958bee61b" title="删除" isHighlight="false" type="button" id="mDelRow" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.deleteCurrentRow()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc7551-93b7-47b5-8cd6-7d88e2592d09" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dc7551-93b5-4e49-8b08-5a4b5b4bf170" viewId="08dc7551-93b7-4341-8a5c-19c7163ac897" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc747f-3239-4635-8aa4-c839f01a4ef2" isLookup="false">
                <dataSource keyName="SupervisionGUID" entity="gpt_Supervision" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9YBXRIBOA3AlgM74D2AdgHQCChhJk+AhtCACqOEDWAcowLYhYHWDToNmbDj34gANAFgAUElQYseIqUoARdNACeAVUI5eAoYVi6Dx0zIXLkaTDgLFyFAKIAPLDBAAJgBi+GREABbaEhawPn4swaERUSwOKs7qbloUAEogfIzYnDF5BUVpTmqumh4AwtggEgEA4oYAktox9Y0JrR0Vqi4a7pTdTWaCwmMJEwMZ1SMU04Gs+OZTDU2rAnNVw9kAsiQB+ABm+IF9ncJHJ+eX7dq7Q1ket2cXARMx7/df9kp0ntXpRfp9tpNLGCVms5IDKi8apRgUirjEUSM0YogtgSHwEAjMkiRJYgYiRrBYIoAOrhHCCAAUAEYALxMgCUQA==]]></command>
                    <fields>
                        <field name="AssociateTaskName" allowPopulate="true" entity="gpt_Supervision" field="AssociateTaskName" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="CreatedName" allowPopulate="true" entity="gpt_Supervision" field="CreatedName" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="CreatedTime" allowPopulate="true" entity="gpt_Supervision" field="CreatedTime" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="DutyUserName" allowPopulate="true" entity="gpt_Supervision" field="DutyUserName" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExpectedFinishDate" allowPopulate="true" entity="gpt_Supervision" field="ExpectedFinishDate" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Remark" allowPopulate="true" entity="gpt_Supervision" field="Remark" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SupervisionGUID" allowPopulate="true" entity="gpt_Supervision" field="SupervisionGUID" entityAlias="gpt_Supervision" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="AssociateTaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="AssociateTaskName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
                        <availableField name="DutyUserName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DutyUserName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ExpectedFinishDate" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExpectedFinishDate" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
                        <availableField name="Remark" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Remark" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="SupervisionGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SupervisionGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Supervision" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc7551-93b8-4ba9-8844-cba42c8fbb28" id="08dc747f-3239-4635-8aa4-c839f01a4ef2" name="gpt_Supervision" primaryField="SupervisionGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="SupervisionGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="督办标题" width="120" field="AssociateTaskName" allowEdit="false" customizeReferenceable="false" id="55c79af7-fcbd-45b0-a364-8f71b65d6347" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="5288ecc8-af46-4453-946b-12daf88c7eca" field="AssociateTaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                            </textBox>
                        </column>
                        <column title="主责人" width="120" field="DutyUserName" allowEdit="false" customizeReferenceable="false" id="d2047fe8-84ff-4fc8-bfd7-841e87e348e1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="72d66a7b-2bd1-4b28-a317-fef2237f191f" field="DutyUserName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                            </textBox>
                        </column>
                        <column title="要求完成日期" width="120" field="ExpectedFinishDate" allowEdit="false" customizeReferenceable="false" id="bbde53ba-e371-4192-8b88-39588b7941c6" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <datePicker id="7b5e290a-5edf-4d87-9c57-77bfbb85ef93" field="ExpectedFinishDate" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" allowClear="false">
                                <customProps />
                            </datePicker>
                        </column>
                        <column title="创建时间" width="120" field="CreatedTime" allowEdit="false" customizeReferenceable="false" id="c96018ad-fb47-47df-b319-1c7bf2af8532" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <datePicker id="b086c70f-3d5b-491f-912b-e64d7d500bdf" field="CreatedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" allowClear="false">
                                <customProps />
                            </datePicker>
                        </column>
                        <column title="创建人名称" width="120" field="CreatedName" allowEdit="false" customizeReferenceable="false" id="f8b30744-9f25-4e74-87d9-c5c129d0b741" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="4f42a5e5-7c3f-4463-aebe-9c3bdc1e3ca4" field="CreatedName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                            </textBox>
                        </column>
                        <column title="事项说明" width="120" field="Remark" allowEdit="false" customizeReferenceable="false" id="0722ddb0-1629-42a2-a58d-77324b98bb60" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="48a694a6-125e-41c3-9a23-7ec92acf8e36" field="Remark" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512">
                                <customProps />
                            </textBox>
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="6d1b1056-0516-4d63-b857-bb5a12ffaeda" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>