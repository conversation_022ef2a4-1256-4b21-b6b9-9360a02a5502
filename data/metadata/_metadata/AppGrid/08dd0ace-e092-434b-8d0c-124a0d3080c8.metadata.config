<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-11-22T08:23:00Z" modifiedBy="彭宗一" modifiedOn="2025-06-20T16:11:51.1154835+08:00" metadataStatus="Product" name="选择对象并导入" functionPageId="08dd0ace-e090-40d0-84d3-1c3c4b322564" description="" htmlCache="default" enableUserSettings="false" gridId="08dd0ace-e092-434b-8d0c-124a0d3080c8" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="52de05c9-bfe1-4486-8b09-c7d1cdd89c27" controlId="4204d53c-b86a-4765-84c8-c9a1f7d68b24" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Wizard.prevStep()">
            <script><![CDATA[]]></script>
        </code>
        <code id="7f0b30fc-3337-48f9-9314-69486286b69c" controlId="f61bbf18-f810-45cc-8e9e-186573cca0cf" controlType="ToolbarItem" controlAction="_appGrid_button_58227406562858_click">
            <script><![CDATA[var selectData = $e.selecteds;
if (selectData.length > 0) {
  var pageData = $page.getParams();
  $api.importAndExportTask.importTaskSave({
    data: {
      fileName:pageData.fileName,
      importExportTaskGUID:pageData.taskGUID,
      documentGUID:pageData.documentGUID,
      importExportTaskDetailDto :selectData
    }
  }).then(function (data) {
    if(!data){
      $notify.error('导入对象数据检查失败')
    }
    $page.close()
    $util.message.trigger('gpt_closeimport', {
      close: true
    });
  })

}else{
  $notify.warning("请选择需要导入的对象数据!")
}
]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42001302" service="importAndExportTask" action="importTaskSave" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_ImportExportTaskDetail" entityId="08dd0a0a-32b2-454c-82ee-6e2c8b2617cc" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd0ace-e092-43dd-8951-c56f94719cce" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="4204d53c-b86a-4765-84c8-c9a1f7d68b24" title="上一步" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58227406566209" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Wizard.prevStep()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="f61bbf18-f810-45cc-8e9e-186573cca0cf" title="导入" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58227406562858" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58227406562858_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd0ace-e092-43fd-8d5a-85a42020aaaf" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dd0ace-e092-441f-8bbd-3436e1196508" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition isCurrent="true" id="9cade737-941a-4112-9ffc-4d1e86a18314" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="e92e8065-1527-4f05-bdc6-c9ea3ae5a568" ref="ObjectSource" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="b1e5c958-2ab8-4bc1-9912-29e5dcba1d3b" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="a9ec99b4-a1d1-445b-897d-7bc0ee2a261f" ref="ObjectName" />
                    <template><![CDATA[]]></template>
                    <searchType>like</searchType>
                </condition>
            </conditions>
            <components>
                <component id="e92e8065-1527-4f05-bdc6-c9ea3ae5a568" name="ObjectSource" metadataStatus="Product">
                    <label title="对象类型" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="ObjectSource" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="true" allowClear="true" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <options>
                            <option value="2" text="GPT助手" isDefault="false" disabled="false" />
                            <option value="3" text="技能" isDefault="false" disabled="false" />
                            <option value="4" text="知识库" isDefault="false" disabled="false" />
                            <option value="5" text="智能检查方案" isDefault="false" disabled="false" />
                            <option value="6" text="提示词" isDefault="false" disabled="false" />
                            <option value="7" text="提示词模板" isDefault="false" disabled="false" />
                            <option value="8" text="插件" isDefault="false" disabled="false" />
                            <option value="9" text="模型服务" isDefault="false" disabled="false" />
                            <option value="10" text="规则库" isDefault="false" disabled="false" />
                        </options>
                    </comboBox>
                </component>
                <component id="a9ec99b4-a1d1-445b-897d-7bc0ee2a261f" name="ObjectName" metadataStatus="Product">
                    <label title="对象名称" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <textBox field="ObjectName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                        <customProps />
                        <events />
                    </textBox>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="440b4a34-9b8f-459f-be6c-3cd58327c019" title="新规则" controlId="08dd0ace-e092-436c-8b61-545821aea339" controlType="gridview" controlSubType="" controlProp="multiSelect" controlName="08dd0ace-e092-436c-8b61-545821aea339" metadataStatus="Product">
                    <handles>
                        <handle handleId="ce234ad8-ad2f-4f18-a68a-9de0ffdddaa1" ruleId="0dd34df7-8ceb-4bce-8322-1d1647b02d33" action="multiSelect" value="hide" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="0dd34df7-8ceb-4bce-8322-1d1647b02d33" title="不允许导入" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;IsAllowImport&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;0&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dd0ace-e091-4e7e-8b14-5f8d3a369e47" viewId="08dd0ace-e092-436c-8b61-545821aea339" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dd0a0a-32b2-454c-82ee-6e2c8b2617cc" isLookup="false">
                <dataSource keyName="ImportExportTaskDetailGUID" entity="gpt_ImportExportTaskDetail" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AkgW0QewE7QFEAPHfAFQEMBnAawBERoKBLMAOgN1zwCURrsAO1jVYnbrj4DBAGgCwAKCSpMpQiTzRKtBk1ZtVm4mu00A4gFU0dEVViH8xzacvX5S5OixGN5avUYWdgctAE9EEFt7b3JwkHdlLzUnPx1A/TQqAEEwMGwAdxCozJy8wpjoBM8QlK1/XSCDKiLRTJCqlQra0wb9AHkAIwArKGgAYWwAE0jRQZGYCemOpJ8TevT2OdHXG1nh7as6ZZrfOrS9Tf2YADkKDBm7LZu7+MVEk7XzxqfoYGwAV1wkAesB+f0BwOOXVOPQ2bB+ZDiaEE0yIUQRSJRICIUOSMPWFzYAAUKLgQIJoD8dlESWSKVTDrjVs4CY1gIgKMDFiD2ZyQNymY58V99LzgdTRGKQDtBepPgFCVLbvcokqXrLuqz9GMyRRoCBJhK7Dr+PrDYy3tVofLeuwTXqDcqQfazU6NcKFY0XQayMwVaJvZNffd3Ta4R8WSKwEboniwxcZZbOnHI579ABZKbMABmzANMczkxzefNbiTKyF8caheLjpeURrubrIfLEdSafYjZLwZBXZ9fsiCgAYtwMAgrSn27bYFk7O9ranp7AFAB1AAWIDJsAAFABGAC8u4AlEA===]]></command>
                    <fields>
                        <field name="ErrorReason" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="ErrorReason" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ImportExportTaskDetailGUID" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="ImportExportTaskDetailGUID" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="IsAllowImport" allowPopulate="false" entity="gpt_ImportExportTaskDetail" field="IsAllowImport" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="ModifiedTime" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ObjectName" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="ObjectName" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ObjectSource" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="ObjectSource" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SpaceName" allowPopulate="true" entity="gpt_ImportExportTaskDetail" field="SpaceName" entityAlias="gpt_ImportExportTaskDetail" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="日期与时间" />
                        <availableField name="ErrorReason" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ErrorReason" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ImportExportTaskDetailGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ImportExportTaskDetailGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ImportExportTaskGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ImportExportTaskGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ImportType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ImportType" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="整数" />
                        <availableField name="IsAllowImport" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsAllowImport" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="整数" />
                        <availableField name="IsImport" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsImport" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="整数" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="日期与时间" />
                        <availableField name="ObjectCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ObjectCode" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ObjectGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ObjectGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="ObjectName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ObjectName" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="ObjectSource" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ObjectSource" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="ObjectTypeIndex" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ObjectTypeIndex" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="整数" />
                        <availableField name="ParentObjectGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ParentObjectGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="SpaceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceCode" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="Guid" />
                        <availableField name="SpaceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceName" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="文本（nvarchar(512)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_ImportExportTaskDetail" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd0ace-e092-4ec6-86f5-21daf5df81ff" id="08dd0a0a-32b2-454c-82ee-6e2c8b2617cc" name="gpt_ImportExportTaskDetail" primaryField="ImportExportTaskDetailGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" logicFormula="" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_ImportExportTaskDetail.ImportExportTaskGUID" operatorType="eq" id="0a1ebed4-8e28-4c94-ba52-4403731b5551" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:taskGUID]</Value>
                                </condition>
                                <condition field="gpt_ImportExportTaskDetail.ObjectSource" operatorType="ne" id="9aa963be-fabe-4c48-ab04-6d86ea368ef7" dataType="string" valueType="0" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">99</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="SpaceName" allowUnRef="false" allowRef="false">
                        <column title="空间名称" width="120" field="SpaceName" allowEdit="false" customizeReferenceable="false" id="6bbc1c50-fcdc-4c8f-9ba8-9a2affb2c518" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="bcde6dcc-19ff-4e9e-aa94-e8736e983a5d" field="SpaceName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ObjectName" allowUnRef="false" allowRef="false">
                        <column title="对象名称" width="120" field="ObjectName" allowEdit="false" customizeReferenceable="false" id="d88dd11a-14df-45f6-b482-218e62d9cca5" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="714a39f2-79b9-4cca-b591-7c2c50277936" field="ObjectName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ErrorReason" allowUnRef="false" allowRef="false">
                        <column title="冲突检查" width="120" field="ErrorReason" allowEdit="false" customizeReferenceable="false" id="71db93cd-04e1-499a-9fa6-b3d87b4becdc" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="1f97cfdb-f200-47ed-85c8-ff7be88d50ed" field="ErrorReason" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ModifiedTime" allowUnRef="false" allowRef="false">
                        <column title="修改时间" width="120" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="d5face3b-48bb-4761-9615-2c4065b82e49" isHidden="true" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <datePicker id="0c6b8154-3b90-423d-8383-6a3c80e6e63a" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm:ss" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ObjectSource" allowUnRef="false" allowRef="false">
                        <column title="对象类型" width="120" field="ObjectSource" allowEdit="false" customizeReferenceable="false" id="0f049cda-1633-4d95-bb1f-a98319a3855b" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="true" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <statusLabel id="783b682a-90aa-4d5b-9532-d14a487dd25b" field="ObjectSource" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options">
                                <customProps />
                                <events />
                                <options>
                                    <option value="3" text="技能" style="normal" />
                                    <option value="2" text="GPT助手" style="normal" />
                                    <option value="6" text="提示词" style="normal" />
                                    <option value="8" text="插件" style="normal" />
                                    <option value="5" text="智能检查方案" style="normal" />
                                    <option value="4" text="知识库" style="normal" />
                                    <option value="7" text="提示词模板" style="normal" />
                                    <option value="1" text="应用" style="normal" />
                                    <option value="0" text="工作空间" style="normal" />
                                    <option value="9" text="模型服务" style="normal" />
                                    <option value="10" text="规则库" style="normal" />
                                </options>
                            </statusLabel>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="ImportExportTaskDetailGUID" multiSelect="true" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="true">
                    <columnRefs>
                        <component ref="SpaceName" width="120" />
                        <component ref="ObjectSource" width="120" />
                        <component ref="ObjectName" width="200" />
                        <component ref="ErrorReason" width="455" />
                        <component ref="ModifiedTime" width="120" />
                    </columnRefs>
                    <sorts>
                        <sort field="ObjectSource" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="da87e44e-3595-4e2a-bcca-4faf3934fb67" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens>
                        <hidden id="6cb1671c-bdea-402b-8c5e-d427ae0193b4" field="documentGUID" errorMode="default" readonlyMode="none" defaultValue="[query:documentGUID]" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="true">
                            <customProps />
                            <events />
                        </hidden>
                        <hidden id="d647d92c-b2eb-48e8-b6ee-222f6ec71ebf" field="taskGUID" errorMode="default" readonlyMode="none" defaultValue="[query:taskGUID]" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="true">
                            <customProps />
                            <events />
                        </hidden>
                    </hiddens>
                </layout>
            </view>
        </views>
    </layout>
</grid>