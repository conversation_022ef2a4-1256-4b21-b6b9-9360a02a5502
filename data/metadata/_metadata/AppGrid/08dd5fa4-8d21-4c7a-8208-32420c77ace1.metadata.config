<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2025-03-10T07:24:10Z" modifiedBy="黄诚" modifiedOn="2025-07-25T16:44:15.3671757+08:00" metadataStatus="Product" name="知识库主表列表控件" functionPageId="08dd5fa4-8d1b-443e-8203-724ff447ec00" description="" htmlCache="default" enableUserSettings="false" gridId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="126ffa15-8f6f-405d-9e1e-0c17cb5ae043" controlId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" controlType="Grid" controlAction="_appGrid_deSelect">
            <script><![CDATA[$KnowledgeSelector.deselect($e.row.KnowledgeGUID)]]></script>
        </code>
        <code id="ca8e4ec7-8c41-46b6-afe1-dc5112f9e4a9" controlId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" controlType="Grid" controlAction="_appGrid_init">
            <script><![CDATA[$KnowledgeSelector.load()]]></script>
        </code>
        <code id="9c127e24-e75e-4556-b2b6-b7eb02aff936" controlId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" controlType="Grid" controlAction="_appGrid_load">
            <script><![CDATA[var ids = $KnowledgeSelector.getData()
var m = ids.reduce(function (r, id) {
  r[id] = true;
  return r
}, {})

var data = $grid.getData() || []
var rows = data.filter(function (row) {
  return m[row.KnowledgeGUID]
})
$grid.selects(rows);
]]></script>
        </code>
        <code id="86fcae27-5def-4627-9ac8-1a2bbcceb2db" controlId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" controlType="Grid" controlAction="_appGrid_rowBeforeClick">
            <script><![CDATA[$KnowledgeSelector.click(true)]]></script>
        </code>
        <code id="9be42da3-2f1e-45c5-93af-c8da770db533" controlId="08dd5fa4-8d21-4c7a-8208-32420c77ace1" controlType="Grid" controlAction="_appGrid_select">
            <script><![CDATA[$KnowledgeSelector.select($e.row.KnowledgeGUID)]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08ddcaa8-1207-4802-81c7-902c8cfa3b37" dependentId="$KnowledgeSelector" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_Knowledge" entityId="08dc7306-ca78-4497-874c-1891cf5d86b7" toolBarLeftItemDisplayMode="always" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="List" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd5fa4-8d23-4550-83ef-3cd6d98e6379" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dd5fa4-8d1f-440c-8688-ffef9d093358" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.Utility.closeOwnerDialog()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dd5fa4-8d1f-43e4-83b8-b28a9228b23b" title="确定" isHighlight="true" type="button" id="mSelectData" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.returnSelectData()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd5fa4-8d23-4566-8193-0b93b01a2262" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="oninit" functionName="_appGrid_init" enabled="true" metadataStatus="Product" />
            <event name="onrowbeforeclick" functionName="_appGrid_rowBeforeClick" enabled="true" metadataStatus="Product" />
            <event name="onselect" functionName="_appGrid_select" enabled="true" metadataStatus="Product" />
            <event name="ondeselect" functionName="_appGrid_deSelect" enabled="true" metadataStatus="Product" />
            <event name="onload" functionName="_appGrid_load" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dd5fa4-8d23-4577-8539-03d1daac6860" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dd5fa4-8d21-418d-868f-f9ed9d51d0a3" viewId="08dd5fa4-8d21-4cde-852c-b900e4c59bfa" name="知识库" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc7306-ca78-4497-874c-1891cf5d86b7" isLookup="false">
                <dataSource keyName="KnowledgeGUID" entity="gpt_Knowledge" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QA6AYS3xFgEMBnWcygGgFgAoJVTXAo4gERC1IAJwCWyMVgw16g4eMnTWHZOmx4QhEgFEAtgCMt+MRngBZCuEZU6sfUfwmzlymBsrO6nlr4BJWgAJOgBFAFchaCkZOwDg2nDI6M81bk1tYgDgAE9aaBA9WVgs3Py9FK4NXhIAOWo9W3o6horvdL5QahFIAAtQEQA3MUghHQwwwrtO7r6QQeHR8fL2LzTq4mne/qGR2gAFLvr6KZAurbmdoQORI9a13xJN2fndgFUAJQAZIqfthdoPp87lUHhtENQRgBxV5+fg/cFQmH8YE+DLAaDUaBhWhjCY/DFYnFLFHtEgAFWyiBAuMm9ApVJpJPWpBEp3y+GhsKKLLZWk5yJWqRBGR5mK0zUaDFZYvwEqZoNF7LJYga3OlSpVIHlGXu2n5RV1RH52r4rjEADMxHykUUzZbrbCTSQ7VbZfVJS7xe6ncRPfhlaq7H6A1Q2AAxERYQqrYVEWAAQXoMdRcdgbAA6j05lQABQARgAvHmAJRAA=]]></command>
                    <fields>
                        <field name="Code" allowPopulate="true" entity="gpt_Knowledge" field="Code" entityAlias="gpt_Knowledge" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Description" allowPopulate="true" entity="gpt_Knowledge" field="Description" entityAlias="gpt_Knowledge" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="KnowledgeGUID" allowPopulate="true" entity="gpt_Knowledge" field="KnowledgeGUID" entityAlias="gpt_Knowledge" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Name" allowPopulate="true" entity="gpt_Knowledge" field="Name" entityAlias="gpt_Knowledge" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TypeEnum" allowPopulate="true" entity="gpt_Knowledge" field="TypeEnum" entityAlias="gpt_Knowledge" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="Code" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Code" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
                        <availableField name="Description" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Description" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="EmbeddingModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="EmbeddingModelCode" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="IsHasQuestion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsHasQuestion" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
                        <availableField name="Name" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Name" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="SearchServicesEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="SearchServicesParams" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesParams" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="SearchServicesURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesURL" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
                        <availableField name="StatusEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StatusEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="TypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="TypeEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Knowledge" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd5fa4-8d25-4bef-8988-5ef61c410584" id="08dc7306-ca78-4497-874c-1891cf5d86b7" name="gpt_Knowledge" primaryField="KnowledgeGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_Knowledge.SpaceGUID" operatorType="eq" id="f9f5bb91-51f7-429b-8bf1-8cbfded594ea" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:SpaceGUID]</Value>
                                </condition>
                                <condition field="gpt_Knowledge.TypeEnum" operatorType="eq" id="5cb20304-58e3-4202-8089-216c0310e7bc" dataType="number" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:type]</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="Code" allowUnRef="false" allowRef="false">
                        <column title="编号" width="120" field="Code" allowEdit="false" customizeReferenceable="false" id="f2cadc18-b6fc-45d2-80d5-70ce44ff2e89" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="b7f56b88-010f-4db9-b39b-f40bb0806796" field="Code" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="Name" allowUnRef="false" allowRef="false">
                        <column title="知识库名称" width="120" field="Name" allowEdit="false" customizeReferenceable="false" id="d7a5b0a6-a5da-44a1-a5df-c9abb7d78435" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="76bbedfd-fbc4-4f39-8350-ff650af80727" field="Name" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="Description" allowUnRef="false" allowRef="false">
                        <column title="描述" width="120" field="Description" allowEdit="false" customizeReferenceable="false" id="ca1b3de1-35f6-4129-bed1-a172d8cf8bb0" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="1c923f10-8fee-416c-b5fd-5e653e242a47" field="Description" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <dataIndicator enable="false" layoutType="standard" showIcon="false">
                    <indicators />
                </dataIndicator>
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="KnowledgeGUID" multiSelect="true" showIndexColumn="false" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="true">
                    <columnRefs>
                        <component ref="Name" width="120" />
                        <component ref="Code" width="120" />
                        <component ref="Description" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="86575752-9fca-4df5-83c2-16b290e5fb40" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens>
                        <hidden id="08dc7307-2209-4677-8c9b-577651cd6485" field="TypeEnum" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false" />
                    </hiddens>
                </layout>
            </view>
        </views>
    </layout>
</grid>