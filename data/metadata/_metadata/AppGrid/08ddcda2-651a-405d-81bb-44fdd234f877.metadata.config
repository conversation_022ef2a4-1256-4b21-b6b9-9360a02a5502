<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-28T06:45:52Z" modifiedBy="黄浩翔" modifiedOn="2025-07-29T14:52:50.1957986+08:00" metadataStatus="Product" name="参数列表" controlId="appGridVI6X" functionPageId="08ddcb50-cc9c-4299-88e0-35c18e382fb7" description="" htmlCache="default" enableUserSettings="false" gridId="08ddcda2-651a-405d-81bb-44fdd234f877" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="35a946ca-2d18-4434-9101-e91611b80db4" controlId="08ddcda2-651a-405d-81bb-44fdd234f877" controlType="Grid" controlAction="_appGridVI6X_init">
            <script><![CDATA[/**
 *  列表初始化完成事件
 *  @example
 *  //设置当前列表行数据
 *  $grid.setData({data:[{Name:'小明'，Age: 20},{Name:"李雷", Age:23}], total: 2})
 */

]]></script>
        </code>
        <code id="aef6a767-d97b-4892-8723-6b072d5db688" controlId="6bf283bd-1233-423f-8c56-d31932dfa00e" controlType="ToolbarItem" controlAction="_appGridVI6X_button_58206842196835_click">
            <script><![CDATA[var params = $page.getParams()

// 组装所有入参
var data = $grid.getData()
var arguments = {}
for (var i = 0; i < data.length; i++) {
    arguments[data[i]["paramName"]] = data[i]["paramValue"]
}

// 执行mcp获取数据
$page.loading.show()
$api.mcpToolParameter.executeMcpTool({
  data:{
    toolGUID:params.toolGUID,
    arguments: arguments
  }
}).then(function(res){
  $page.loading.hide()
  $form.setData("LastTestOutput", $_.toJSON(res))
  $page.setChangeStatus()
})]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000801" service="mcpToolParameter" action="executeMcpTool" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="none" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" dataApi="/api/42000801/mcpToolParameter" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tips="&lt;p&gt;输入 [参数值] 后，点击 [运行] 按钮，若输出参数有误，则表示该工具运行失败&lt;/p&gt;" tipsType="1" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08ddcda2-6548-48a4-86f9-07b8fc694c18" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="6bf283bd-1233-423f-8c56-d31932dfa00e" title="运行" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58206842196835" rowButtonStyle="2" defaultTips="" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGridVI6X_button_58206842196835_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08ddcda2-6548-48bc-896f-3dfa164595dc" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="oninit" functionName="_appGridVI6X_init" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08ddcda2-6548-48d1-8da6-ee07cc3a1917" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08ddcda2-6519-4faf-8577-2dc8b2f6e8ff" viewId="08ddcda2-651a-4072-8a3d-5ac4ae4ca8b5" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="00000000-0000-0000-0000-000000000000" isLookup="false">
                <dataSource withNoLock="true" mode="2">
                    <fields>
                        <field name="paramName" allowPopulate="false" entity="MCP_TOOL_PARAMETER" field="paramName" entityAlias="MCP_TOOL_PARAMETER" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="paramType" allowPopulate="false" entity="MCP_TOOL_PARAMETER" field="paramType" entityAlias="MCP_TOOL_PARAMETER" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="paramValue" allowPopulate="false" entity="MCP_TOOL_PARAMETER" field="paramValue" entityAlias="MCP_TOOL_PARAMETER" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="toolGUID" allowPopulate="false" entity="MCP_TOOL_PARAMETER" field="toolGUID" entityAlias="MCP_TOOL_PARAMETER" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="paramName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="paramName" entity="MCP_TOOL_PARAMETER" entityType="2" />
                        <availableField name="paramType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="paramType" entity="MCP_TOOL_PARAMETER" entityType="2" />
                        <availableField name="paramValue" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="paramValue" entity="MCP_TOOL_PARAMETER" entityType="2" />
                        <availableField name="toolGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="toolGUID" entity="MCP_TOOL_PARAMETER" entityType="2" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams />
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="paramName" allowUnRef="false" allowRef="false">
                        <column title="参数名称" width="120" field="paramName" allowEdit="false" customizeReferenceable="false" id="54f759fb-afc1-440b-9c55-85b1e934ccc4" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="5069fad3-c24f-4636-9fda-451f8cb31d88" field="paramName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="paramType" allowUnRef="false" allowRef="false">
                        <column title="参数类型" width="120" field="paramType" allowEdit="false" customizeReferenceable="false" id="be223225-f1d0-49e7-a8dc-4d978346907a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="b2e83443-cc07-4c23-866f-7772084205b8" field="paramType" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="paramValue" allowUnRef="false" allowRef="false">
                        <column title="参数值" width="120" field="paramValue" allowEdit="true" customizeReferenceable="false" id="fb3a2c8b-e26c-4e53-a8f2-2026b4822c42" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="c0e2f99d-7b71-4351-8255-4d69aa9aa6d7" field="paramValue" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" placeholder="请输入参数值" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="true" rowToolbarWidth="0" frozenToolbar="true" idField="toolGUID" multiSelect="false" showIndexColumn="false" isSimulationEditing="false" allowEdit="true" fixedColumns="0" maxWrapRow="0" editMode="1" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="paramName" width="120" />
                        <component ref="paramType" width="120" />
                        <component ref="paramValue" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="c2dc9860-4b70-46cf-89af-b7ccce3b31b3" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>