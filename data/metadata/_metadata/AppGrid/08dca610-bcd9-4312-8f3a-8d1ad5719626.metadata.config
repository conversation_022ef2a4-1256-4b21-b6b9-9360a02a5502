<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-07-17T03:29:59Z" modifiedBy="万桥" modifiedOn="2024-07-25T17:41:56.1600564+08:00" metadataStatus="Product" name="列表" functionPageId="08dca610-a8eb-4225-858e-7079aeaa9538" description="" htmlCache="default" enableUserSettings="false" gridId="08dca610-bcd9-4312-8f3a-8d1ad5719626" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="85b1698b-7063-45f8-b974-fbe785a9d064" controlId="08dca610-bcd9-4312-8f3a-8d1ad5719626" controlType="Grid" controlAction="_appGrid1_init">
            <script><![CDATA[/**
 *  列表初始化完成事件
 *  @example
 *  //设置当前列表行数据
 *  $grid.setData({data:[{Name:'小明'，Age: 20},{Name:"李雷", Age:23}], total: 2})
 */
// var callback = function(event, data)
// { 
//   $KnowledgeQuestion.setKnowledgeGUID(data.KnowledgeGUID);
//   var options = {
//   filters: [{
//     type: "and", // 当前条件对象中存在多个时，用and或者or来执行查询条件
//     condition: [{
//       field: "KnowledgeGUID",
//       operatorType: "eq", // like,le,ge可以选择
//       value: data.KnowledgeGUID
//     }]
//   }]
//   }
//   $grid.doQuery(options);
// };
// $util.message.on("knowledgeQuestionInit", callback);
]]></script>
        </code>
        <code id="3d0ba9f9-fae0-4844-9b7d-c3a128b9b3f3" controlId="0cc7f901-6b27-41b3-b7ec-ae20b249f4b5" controlType="ToolbarItem" controlAction="_appGrid1_button_batchDel_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
var questionGUIDs = $_.map($e.selecteds,function(item){
  return item.KnowledgeQuestionGUID;
}) 
$notify.confirm('删除问题','确认删除该问题？').then(
  function(){
    // 确定按钮
    return $api.knowledgeQuestion.deleteKnowledgeQuestion({knowledgeQuestionGUIDs:questionGUIDs})
            .then(function(){
              $notify.success("删除成功");
              $grid.doQuery();
              $util.message.trigger("deleteKnowledgeQuestion","");
            },function(){
              $notify.warning("删除失败");
            });
  },
  function(){
    // 取消按钮
  }
)]]></script>
        </code>
        <code id="ab0c5d55-4e16-47a5-8d21-4c039ce7b25e" controlId="7524a197-1ef8-467b-9fa7-38c0e8209495" controlType="ToolbarItem" controlAction="_appGrid1_button_58238214251920_beforeOpen">
            <script><![CDATA[console.info($e.params)
if ($e.params.knowledgeGUID == null || $e.params.knowledgeGUID == '') {
  $e.cancel = true 
  $notify.warning('请先选择知识库');
  $grid.reload()
}]]></script>
        </code>
        <code id="d59a2dc8-198e-4341-bd13-4bf6213fdb67" controlId="bf07beb6-57f4-46e7-8493-4fa948d3b9b4" controlType="ToolbarItem" controlAction="_appGrid1_button_del_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
var questionGUID = $e.selecteds[0].KnowledgeQuestionGUID;
$notify.confirm('删除问题','确认删除该问题？').then(
  function(){
    // 确定按钮
    return $api.knowledgeQuestion.deleteKnowledgeQuestion({knowledgeQuestionGUIDs:[questionGUID]})
            .then(function(){
              $notify.success("删除成功");
              // $grid.doQuery();
              $util.message.trigger("deleteKnowledgeQuestion","");
            },function(){
              $notify.warning("删除失败");
            });
  },
  function(){
    // 取消按钮
  }
)
]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000401" service="knowledgeQuestion" action="deleteKnowledgeQuestion" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dca630-5fb8-4090-8798-ce553c9227d9" dependentId="$KnowledgeQuestion" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="false" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_KnowledgeQuestion" entityId="08dca60d-2c61-4349-8ebb-62481c75be51" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dca610-bcd9-433c-850d-53cba1af7549" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items>
                            <item itemId="0cc7f901-6b27-41b3-b7ec-ae20b249f4b5" title="批量删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_batchDel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid1_button_batchDel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="7524a197-1ef8-467b-9fa7-38c0e8209495" title="创建问题" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58238214251920" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onbeforeopen" functionName="_appGrid1_button_58238214251920_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000401/08dca62b-8295-4a84-89cb-2f31186236b1" id="08dca62b-8295-4a84-89cb-2f31186236b1" itemId="95b744db-ed58-46c1-84fc-0521c39379d9" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="filter" key="knowledgeGUID" value="[linkage:KnowledgeGUID]" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dca610-bcd9-434b-88d6-7d4cee5864c1" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="7f5c32ae-3c0f-48e0-8b45-4d421ee87997" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_edit" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000401/08dca62b-8295-4a84-89cb-2f31186236b1" id="08dca62b-8295-4a84-89cb-2f31186236b1" itemId="688ceb88-763e-4cd5-83d0-d4f28b0fd3f1" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                    </options>
                                    <params>
                                        <param type="data" key="knowledgeGUID" value="KnowledgeGUID" />
                                        <param type="data" key="oid" value="KnowledgeQuestionGUID" />
                                        <param type="text" key="mode" value="2" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="bf07beb6-57f4-46e7-8493-4fa948d3b9b4" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_del" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid1_button_del_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="oninit" functionName="_appGrid1_init" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dca610-bcd9-435b-81ae-9c2266d1472e" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions>
                <condition id="3ac6a19b-6555-4e04-a195-fbca6542b9e5" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="3179631b-94fb-4aac-a1dd-1f8cdb6653ab" ref="Search" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="3179631b-94fb-4aac-a1dd-1f8cdb6653ab" name="Search" metadataStatus="Product">
                    <label title="快速筛选" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <search field="Search" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" union="false" wildcard="true">
                        <customProps />
                        <events />
                        <fields>
                            <findField field="Question" title="问题" dataType="Mysoft.Map6.Metadata.Models.SubControls.TextBox, Mysoft.Map6.Metadata.Models" isEncryption="false" unencryptionSide="0" unencryptedLength="0" metadataStatus="Product" />
                        </fields>
                    </search>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups>
                <group id="6a5bdda0-a006-4b93-a913-6a1172ec9269" title="有选中行" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;selected&quot;,&quot;field&quot;:&quot;_selected&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dca610-bcd9-427a-8c1e-f1ceb6e84250" viewId="08dca610-bcd9-4322-8fb1-5ca30936aad8" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dca60d-2c61-4349-8ebb-62481c75be51" isLookup="false">
                <dataSource keyName="KnowledgeQuestionGUID" entity="gpt_KnowledgeQuestion" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9A0gOwPYHcIBN4QBFAVxAGdoBLLDAOgBFqKBDAIwllYtmbc4gANAFgAUElSZcBImUo069aXhCEQAcQCqASUbdeK2Zt2NRE5Omyr18qrQZ3FGA7CcPzkqzLVzy9pQBhACcQVmg1bT1XELCI/CizcS8jXxJ/Z3pY8LUAOVYAWxAY0Jz8fKLPS1TbDIcs0viAFWoikri1Fsrk6utjdyUavwUHRNch9JG6RKqpPrSBhgBZLHxqADNqSNNXFbXN7b1Z7xthgOXVja3ywuKeWD2rvNvjicX6R4P8LrveT+ufrAxAAxYJYAoIXo+WpTFwAQV4KXmMPOsCBAHUABYgUKwAAUAEYALwEgCUQA==]]></command>
                    <fields>
                        <field name="KnowledgeGUID" allowPopulate="false" entity="gpt_KnowledgeQuestion" field="KnowledgeGUID" entityAlias="gpt_KnowledgeQuestion" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="KnowledgeQuestionGUID" allowPopulate="true" entity="gpt_KnowledgeQuestion" field="KnowledgeQuestionGUID" entityAlias="gpt_KnowledgeQuestion" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="true" entity="gpt_KnowledgeQuestion" field="ModifiedTime" entityAlias="gpt_KnowledgeQuestion" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Question" allowPopulate="true" entity="gpt_KnowledgeQuestion" field="Question" entityAlias="gpt_KnowledgeQuestion" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="日期与时间" />
                        <availableField name="Disable" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Disable" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="整数" />
                        <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="KnowledgeGUID" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="Guid" />
                        <availableField name="KnowledgeQuestionGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeQuestionGUID" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="日期与时间" />
                        <availableField name="Question" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Question" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_KnowledgeQuestion" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dca610-bcd9-452c-8799-0fa929d505a1" id="08dca60d-2c61-4349-8ebb-62481c75be51" name="gpt_KnowledgeQuestion" primaryField="KnowledgeQuestionGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="KnowledgeQuestionGUID" multiSelect="true" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="true">
                    <columns>
                        <column title="问题" width="175" field="Question" allowEdit="false" customizeReferenceable="false" id="51cd9e86-9533-440f-b36a-183f94a601dc" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="6032d856-0173-4825-bf59-658e8f901c55" field="Question" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="已关联文档分段数" width="63" field="sectionCount" allowEdit="false" customizeReferenceable="false" id="66ae68e0-66a5-4f41-ac1e-e4b69e2c1c28" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="text" align="left" dataSourceType="CalculateColumn" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="439b1e0e-9c43-47b4-8863-ca547aa2d991" field="sectionCount" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                <customProps />
                                <events />
                            </textBox>
                            <subControlFormula category="Sql" queryMode="Subquery" fieldName="sectionCount">
                                <Id>00000000-0000-0000-0000-000000000000</Id>
                                <RelationTables />
                                <moql>
                                    <content><![CDATA[select COUNT(1) from gpt_KnowledgeQuestionRelation
where gpt_KnowledgeQuestionRelation.KnowledgeQuestionGUID
 = gpt_KnowledgeQuestion.KnowledgeQuestionGUID]]></content>
                                </moql>
                                <databaseSqls />
                            </subControlFormula>
                            <behaviors />
                        </column>
                        <column title="修改时间" width="98" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="aaaf7f7a-1b87-4e43-9bb2-a08aa2813050" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <datePicker id="d048f997-34f3-4d47-8573-78ccd1c4e137" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm:ss" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="c8e3a4cb-ceb1-40c4-9cd4-68c6940ad385" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>