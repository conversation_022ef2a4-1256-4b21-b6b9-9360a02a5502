<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-10T09:26:57Z" modifiedBy="夏娜" modifiedOn="2024-05-20T10:45:23.5087023+08:00" metadataStatus="Product" name="技能输入参数" functionPageId="00000000-0000-0000-0000-000000000000" description="" htmlCache="default" componentGuid="08dc70d3-56e0-4710-84bc-7f0e3a477fb6" enableUserSettings="false" gridId="08dc70d3-56e2-4416-86fc-57110a02ff6c" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="0bd90f58-4053-483c-96b1-2410bf29f8f6" controlId="103ffcbb-6449-46a8-a8ef-8f6d3c11a956" controlType="ToolbarItem" controlAction="_SkillInput_appGrid_button_58243300112896_click">
            <script><![CDATA[var row = $e.selecteds[0]
$grid.removeRow(row)
var data = $GridTools.onUpdateSortKey($grid.getData())
$grid.setData({data: data, total: data.length})]]></script>
        </code>
        <code id="16dfec7d-acfa-410c-9e6d-01ea8bb8abd4" controlId="3602cda4-c75c-44da-91f4-b2441fcb90d4" controlType="ToolbarItem" controlAction="_SkillInput_appGrid_button_58243300145890_click">
            <script><![CDATA[$grid.addRow({required: true, type: 'string'}, 0, true)
var data = $GridTools.onUpdateSortKey($grid.getData())
$grid.setData({data: data, total: data.length})]]></script>
        </code>
        <code id="7953f382-0c2b-41ff-8fff-957817d32cfd" controlId="f91554fe-db0a-4316-bd41-64da0d28fa08" controlType="ToolbarItem" controlAction="_SkillInput_appGrid_button_58243300103904_click">
            <script><![CDATA[var row = $e.selecteds[0]
var data = $GridTools.onMove($grid.getData(), row, 'down')
$grid.setData({data: data})]]></script>
        </code>
        <code id="cb8b9393-3b2b-4c21-a0d1-1258a1d25e41" controlId="ff9de539-0ef8-4916-9b68-14f0af9e9e43" controlType="ToolbarItem" controlAction="_SkillInput_appGrid_button_58243300104584_click">
            <script><![CDATA[var row = $e.selecteds[0]
var data = $GridTools.onMove($grid.getData(), row, 'up')
$grid.setData({data: data})]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7323-098d-4513-8638-0468d3a024f7" dependentId="$GridTools" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="false" templateStyle="default" pageStyle="none" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc70d3-56e2-443a-808b-6ce3c38c9546" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="3602cda4-c75c-44da-91f4-b2441fcb90d4" title="新增参数" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58243300145890" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_SkillInput_appGrid_button_58243300145890_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc70d3-56e2-4449-8d32-b18ee69349bc" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="103ffcbb-6449-46a8-a8ef-8f6d3c11a956" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243300112896" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_SkillInput_appGrid_button_58243300112896_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="ff9de539-0ef8-4916-9b68-14f0af9e9e43" title="上移" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243300104584" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_SkillInput_appGrid_button_58243300104584_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="f91554fe-db0a-4316-bd41-64da0d28fa08" title="下移" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58243300103904" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_SkillInput_appGrid_button_58243300103904_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc70d3-56e2-445a-85e9-5ae2c76705c0" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="2b9082e9-8fd7-4e2b-a271-10f6e1acbd81" title="新规则" controlId="ff9de539-0ef8-4916-9b68-14f0af9e9e43" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58243300104584" metadataStatus="Product">
                    <handles>
                        <handle handleId="1a0b76f8-843b-4b91-b40b-313604f6575c" ruleId="73d82ca5-3689-4504-822a-aaf761a92653" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="83813b55-f9b7-4711-b62a-61bbffe50e0f" title="新规则" controlId="f91554fe-db0a-4316-bd41-64da0d28fa08" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58243300103904" metadataStatus="Product">
                    <handles>
                        <handle handleId="cdf8d236-3198-48c7-aa87-3dec9cd23f4c" ruleId="e2ca9db1-a9cb-4bab-a2a0-7c98c444fa0d" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="73d82ca5-3689-4504-822a-aaf761a92653" title="上移" rule="{&quot;condition&quot;:&quot;OR&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;rowType&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;-1&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;rowType&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;0&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="e2ca9db1-a9cb-4bab-a2a0-7c98c444fa0d" title="下移" rule="{&quot;condition&quot;:&quot;OR&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;rowType&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;rowType&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;0&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dc70d3-56e2-4344-838c-9b526cdec1ba" viewId="08dc70d3-56e2-4428-8827-67491811ad52" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="00000000-0000-0000-0000-000000000000" isLookup="false">
                <dataSource keyName="PrimaryKey" withNoLock="false" mode="3">
                    <fields>
                        <field name="code" allowPopulate="false" field="code" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="description" allowPopulate="false" field="description" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="name" allowPopulate="false" field="name" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PrimaryKey" allowPopulate="false" field="PrimaryKey" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="required" allowPopulate="false" field="required" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="rowType" allowPopulate="false" field="rowType" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="type" allowPopulate="false" field="type" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields />
                    <fixedSortings />
                    <summaries />
                    <diagrams />
                    <customDataSource>
                        <customFields>
                            <customField name="PrimaryKey" displayName="主键" attributeType="guid" isRequired="true" isPrimaryKey="true" />
                            <customField name="code" displayName="参数编码" attributeType="string" isRequired="false" isPrimaryKey="false" />
                            <customField name="name" displayName="参数名称" attributeType="string" isRequired="false" isPrimaryKey="false" />
                            <customField name="type" displayName="类型" attributeType="string" isRequired="false" isPrimaryKey="false" />
                            <customField name="description" displayName="描述" attributeType="string" isRequired="false" isPrimaryKey="false" />
                            <customField name="required" displayName="必填" attributeType="int" isRequired="false" isPrimaryKey="false" />
                            <customField name="defaultValue" displayName="默认值" attributeType="string" isRequired="false" isPrimaryKey="false" />
                            <customField name="rowType" displayName="行状态" attributeType="int" isRequired="false" isPrimaryKey="false" />
                        </customFields>
                    </customDataSource>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="PrimaryKey" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="true" fixedColumns="0" maxWrapRow="0" editMode="1" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="参数名称" width="120" field="name" allowEdit="true" customizeReferenceable="false" id="d2a6ea39-0eed-4813-aad4-95b5745dfab4" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="cbc84d84-9f25-4213-b9d8-2a05bd0d7e00" field="name" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="参数编码" width="120" field="code" allowEdit="true" customizeReferenceable="false" id="3bd2afd2-0c14-4b11-8c03-0076cb012851" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="fdb7d02d-45c4-40cd-9116-2dfc0fd3ed73" field="code" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="类型" width="120" field="type" allowEdit="true" customizeReferenceable="false" id="28db82c8-2483-4aac-b0db-d720896c5f87" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <comboBox id="1fb7d165-f5ef-4415-9bb0-86abfe662dbd" field="type" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="string" text="文本" isDefault="true" disabled="false" />
                                    <option value="number" text="数值" isDefault="false" disabled="false" />
                                    <option value="date" text="日期" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                        <column title="描述" width="120" field="description" allowEdit="true" customizeReferenceable="false" id="386ade65-044e-4735-a3a9-46c8aa8130a6" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="f1c3e6c5-a90d-4f1a-8cb2-23013ecff3b5" field="description" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="必填" width="120" field="required" allowEdit="false" customizeReferenceable="false" id="57552486-2fe5-45fd-8f48-45c4f72bc326" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="center" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <ToggleSwitch id="180dbc46-46c7-46b2-8d45-c41e6a318b6c" field="required" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" trueValue="必填" falseValue="选填">
                                <customProps />
                                <events />
                            </ToggleSwitch>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="1b7b5eed-f7cd-40b7-96a5-7e3ce91099ac" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>