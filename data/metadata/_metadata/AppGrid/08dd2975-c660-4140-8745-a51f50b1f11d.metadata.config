<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-12-31T08:33:17Z" modifiedBy="郑越" modifiedOn="2025-03-14T17:29:16.5579629+08:00" metadataStatus="Product" name="结果评测任务表列表控件" functionPageId="08dd2975-c65d-4406-8d37-29451b91f371" description="" htmlCache="default" enableUserSettings="false" gridId="08dd2975-c660-4140-8745-a51f50b1f11d" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="dbfff815-d6a5-4b0e-ada0-661f622914fb" controlId="08dd2975-c660-4140-8745-a51f50b1f11d" controlType="Grid" controlAction="_appGrid_query">
            <script><![CDATA[/**
 * 数据加载前事件
 * @example
 * if(true) $e.cancel=true;
 * //这里不要使用e.options={}的方式赋值,这样会导致引用链断开，无法正确赋值。
 * $e.options.filters.push({type:"and",condition:[{field:"name",operatorType:"eq",value:"xxxx"}]});
 * $e.options.context.key = "xxx";
 */

$Utility.validateSpace($e)]]></script>
        </code>
        <code id="fde79f2c-c7de-4560-8c4e-6732ea9d1fa6" controlId="72d124eb-932a-456e-82c7-51fc5a30d178" controlType="ToolbarItem" controlAction="_appGrid_button_58225072652250_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
var grid = $grid
var evalTaskGUID = $e.selecteds[0].EvalTaskGUID;
var evalObjectType = $e.selecteds[0].EvalObjectType;
$notify.confirm('确认删除','请确认是否删除评测任务？', 'warn').then(
  function(){
    return $api.evalTask.delete({"evalTaskGUID":evalTaskGUID, "evalObjectType":evalObjectType}).then(function(){
        $notify.success("删除成功");
        $page.refreshData();
    })
  },
  function() {}
)]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000703" service="evalTask" action="delete" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="vs_EvalTaskView" entityId="08dd2e29-657f-4c10-861f-2bb3f83e35ca" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd2975-c660-4fb5-8423-77b114412adb" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="38d395e3-1c1f-4eb6-8375-9fdb54d6efab" title="新增" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58225072698881" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000703/08dd2977-9878-4e45-8bea-77c08d79640a" id="08dd2977-9878-4e45-8bea-77c08d79640a" itemId="7e1abe78-ae8c-4491-a10d-c4e298473779" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="filter" key="SpaceGUID" value="[complex:SpaceGUID]" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd2975-c660-4fe4-8005-ed06668dffe5" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="eb9ef0fe-1d8f-4fc3-9f3a-41a85cf348af" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58225072659583" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000703/08dd2977-9878-4e45-8bea-77c08d79640a" id="08dd2977-9878-4e45-8bea-77c08d79640a" itemId="1f568f84-363b-4f8a-bf79-97a958c99ef8" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="filter" key="SpaceGUID" value="[complex:SpaceGUID]" />
                                        <param type="data" key="oid" value="EvalTaskGUID" />
                                        <param type="text" key="mode" value="2" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="72d124eb-932a-456e-82c7-51fc5a30d178" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58225072652250" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGrid_button_58225072652250_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onquery" functionName="_appGrid_query" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dd2975-c661-4011-881a-8765bbce7090" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition isCurrent="true" id="79fdaae3-ef7d-4b19-8aa8-a468b3c0d8c6" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="81c5748f-9716-4ab1-8802-570b9bd41011" ref="SpaceGUID" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="571ac39e-e265-4daa-a41d-d3fd2cb713af" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="bb85ec14-888f-4fd9-bfff-1f223c8cddbc" ref="TaskName" />
                    <template><![CDATA[]]></template>
                    <searchType>like</searchType>
                </condition>
            </conditions>
            <components>
                <component id="81c5748f-9716-4ab1-8802-570b9bd41011" name="SpaceGUID" metadataStatus="Product">
                    <label title="工作空间" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="SpaceGUID" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionsType="pageHandler" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="false" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <pageHandler pageHandlerId="/api/42001301/workspaceList" method="Load" extMethod="" apiSourceType="" />
                        <options />
                    </comboBox>
                </component>
                <component id="bb85ec14-888f-4fd9-bfff-1f223c8cddbc" name="TaskName" metadataStatus="Product">
                    <label title="任务名称" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <textBox field="TaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                        <customProps />
                        <events />
                    </textBox>
                </component>
            </components>
            <events />
            <dataSource type="bizParam" />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dd2975-c65f-4f0b-84a8-2f71f99a5247" viewId="08dd2975-c660-4257-8928-fdaec03456cb" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dd2e29-657f-4c10-861f-2bb3f83e35ca" isLookup="false">
                <dataSource keyName="EvalTaskGUID" entity="vs_EvalTaskView" withNoLock="true" mode="1">
                    <command type="sql" queryDb="false"><![CDATA[MoUwNiDGAuAEBuBnA+gUXgQzAFQ4g1gGoCWIA7gHQDCA9gHaRgCuix9tTdcestDzrdjU7QANAFgAUEjSYceIqUrosAeQBGAKyjdEsFWA3aYE6SgO4CJchQNGdAOQwBbELB52tjlyFMyLCtbKcvYw2ACeAA5uHiFeYVG+Uv5yloo2AQQA4gCqAJIAIu56mfi5hX7mqYFKFAAyeNAASlA0AE4AJuVFPA2Iza2d3ZWyWGlBFACyNB3EAGakHdjErsWw07MLIEsrSWaj8la1wJEYkCDdaydnF/kFI6UTwNAY0CxXL2+ID9VHNmlOVY8AE+H5jGr/GgvMBrbBQrCwSQAMTaNGcCCq4L+ZFgAEE9CksekcYiAOoACxAbTcAAoAIwAXjpAEogA=]]></command>
                    <fields>
                        <field name="ConclusionCount" allowPopulate="false" entity="vs_EvalTaskView" field="ConclusionCount" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="EvalObjectName" allowPopulate="true" entity="vs_EvalTaskView" field="EvalObjectName" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="EvalObjectType" allowPopulate="true" entity="vs_EvalTaskView" field="EvalObjectType" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="EvalTaskGUID" allowPopulate="true" entity="vs_EvalTaskView" field="EvalTaskGUID" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ModifiedTime" allowPopulate="false" entity="vs_EvalTaskView" field="ModifiedTime" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SpaceGUID" allowPopulate="false" entity="vs_EvalTaskView" field="SpaceGUID" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Status" allowPopulate="true" entity="vs_EvalTaskView" field="Status" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TaskName" allowPopulate="true" entity="vs_EvalTaskView" field="TaskName" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="Total" allowPopulate="false" entity="vs_EvalTaskView" field="Total" entityAlias="vs_EvalTaskView" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="ConclusionCount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ConclusionCount" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="EvalObject" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalObject" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="EvalObjectName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalObjectName" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="EvalObjectType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalObjectType" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="EvalTaskGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskGUID" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="LastRecordGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastRecordGUID" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="TaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskName" entity="vs_EvalTaskView" entityType="1" />
                        <availableField name="Total" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Total" entity="vs_EvalTaskView" entityType="1" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd2e29-b31d-413e-8d4d-a20b15ee2578" id="08dd2e29-657f-4c10-861f-2bb3f83e35ca" name="vs_EvalTaskView" primaryField="EvalTaskGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="1" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="TaskName" allowUnRef="false" allowRef="false">
                        <column title="任务名称" width="120" field="TaskName" allowEdit="false" customizeReferenceable="false" id="2c7ebd9e-d003-4bcc-8d80-6b2af754ccee" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="a4a86ba5-7495-40e7-b186-73588f20a5dd" field="TaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="EvalObjectType" allowUnRef="false" allowRef="false">
                        <column title="评测对象类型" width="120" field="EvalObjectType" allowEdit="false" customizeReferenceable="false" id="66ca67a0-e11f-44ea-8a46-ea18d3f2571b" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <comboBox id="b5a4c414-10c0-4b65-84a0-8c24fae967bc" field="EvalObjectType" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="1" text="智能检查方案" isDefault="true" disabled="false" />
                                    <option value="2" text="技能" isDefault="false" disabled="false" />
                                    <option value="3" text="提示词" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="EvalObjectName" allowUnRef="false" allowRef="false">
                        <column title="测评对象" width="120" field="EvalObjectName" allowEdit="false" customizeReferenceable="false" id="78127a9f-9a20-4729-b7cd-5e323def5fda" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="6aa134b9-cdc9-481b-a9bf-e6dbc0c9fc21" field="EvalObjectName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="64" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="Status" allowUnRef="false" allowRef="false">
                        <column title="状态" width="120" field="Status" allowEdit="false" customizeReferenceable="false" id="2cdb67cb-7aff-40b4-8ab9-2f6256c9ca5b" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" exportTemplateIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <comboBox id="cd79a6eb-e3b7-4bbb-acca-eaee8c443401" field="Status" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options>
                                    <option value="0" text="未开始" isDefault="true" disabled="false" />
                                    <option value="1" text="执行中" isDefault="false" disabled="false" />
                                    <option value="2" text="已完成" isDefault="false" disabled="false" />
                                    <option value="3" text="失败" isDefault="false" disabled="false" />
                                </options>
                            </comboBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ModifiedTime" allowUnRef="false" allowRef="false">
                        <column title="最后一次执行时间" width="120" field="ModifiedTime" allowEdit="false" customizeReferenceable="false" id="3d1f1011-9209-4fef-9dcc-211e7d465087" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" allowSort="true" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <datePicker id="622a8877-e20c-4807-8e07-03ea61b828a5" field="ModifiedTime" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" format="yyyy-MM-dd HH:mm:ss" metadataStatus="Product" allowClear="false">
                                <customProps />
                                <events />
                            </datePicker>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ConclusionCountRate" allowUnRef="false" allowRef="false">
                        <column title="测试通过率" width="120" field="ConclusionCountRate" allowEdit="false" customizeReferenceable="false" id="c18dc4f1-f655-4c51-a97a-8236da50f43e" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" importIgnore="true" dataType="number" align="right" dataSourceType="CalculateColumn" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="69eec932-ed1f-4020-82f6-626f4008531a" field="ConclusionCountRate" errorMode="default" readonlyMode="none" defaultValue="0.00" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="2" precisionType="0" rounding="2" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <subControlFormula text="CASEWHEN(ISNULL(vs_EvalTaskView.Total,0) == 0,0, ROUND((vs_EvalTaskView.ConclusionCount * 100/vs_EvalTaskView.Total),2)) &amp; '%'" category="Simple" originalSqlExpression="CONCAT((CONCAT((CASE  WHEN (IFNULL((vs_EvalTaskView.Total),0))=(0) THEN 0 ELSE ROUND((((((vs_EvalTaskView.ConclusionCount)*(100)))/(vs_EvalTaskView.Total))),2) END),'')),('%'))" fieldSqlExpression="CONCAT((CONCAT((CASE  WHEN (IFNULL((vs_EvalTaskView.Total),0))=(0) THEN 0 ELSE ROUND((((((vs_EvalTaskView.ConclusionCount)*(100)))/(vs_EvalTaskView.Total))),2) END),'')),('%'))" aggreSqlExpression="CONCAT(((CASE  WHEN (IFNULL((vs_EvalTaskView.Total),0))=(0) THEN 0 ELSE ROUND((((((vs_EvalTaskView.ConclusionCount)*(100)))/(vs_EvalTaskView.Total))),2) END)),())" fontExpression="f.connect(f.tostring(f.casewhen((f.isnull(Total,0))==(0),0,f.round(f.divide(f.multiply(ConclusionCount,100),Total),2))),&quot;%&quot;)" queryMode="Subquery" tableName="vs_EvalTaskView" expressionValueClrType="System.String" fieldName="ConclusionCountRate">
                                <Id>526975f5-4186-4cad-8050-08dd62d8dbe2</Id>
                                <RelationTables>
                                    <RelationTable name="vs_EvalTaskView" id="08dd2e29-657f-4c10-861f-2bb3f83e35ca">
                                        <RelationFields>
                                            <RelationField name="Total" id="08dd37a0-58e4-483a-880e-020da902c6c4" aliasName="Total" type="int32" />
                                            <RelationField name="ConclusionCount" id="08dd37a0-d216-4b3d-845d-a435f26eed12" aliasName="ConclusionCount" type="int32" />
                                        </RelationFields>
                                    </RelationTable>
                                </RelationTables>
                            </subControlFormula>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="EvalTaskGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="TaskName" width="120" />
                        <component ref="EvalObjectType" width="120" />
                        <component ref="EvalObjectName" width="120" />
                        <component ref="Status" width="120" />
                        <component ref="ConclusionCountRate" width="120" />
                        <component ref="ModifiedTime" width="120" />
                    </columnRefs>
                    <sorts>
                        <sort field="ModifiedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="5a0ec588-3d43-443b-9adc-2c9f2a6de259" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>