<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-06-11T07:05:53Z" modifiedBy="夏娜" modifiedOn="2024-06-12T10:45:18.6092169+08:00" metadataStatus="Product" name="使用示例（新）" controlId="appGridmJw2" functionPageId="08dc89df-f23a-4ffa-8008-c91cc9f4a4ab" description="" htmlCache="default" enableUserSettings="false" gridId="08dc89e4-eebb-4946-8de7-dbf30c8be708" application="4200" isSeparatedLayout="false" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="00ca9aec-8def-41d3-8023-ff92cd8d08b2" controlId="88bad054-0d39-4d9f-95c8-9ca1bed6835f" controlType="ToolbarItem" controlAction="_appGridmJw2_button_58240889537353_click">
            <script><![CDATA[var data = $grid.getData()
if (data.length >= 9) {
  $notify.warning('使用示例最多添加9个')
  return
}
var newRow = $grid.addRow({}, 0, true); // 在第一行新增空行，并从服务器生成行ID
$grid.editRow(newRow);]]></script>
        </code>
        <code id="e31266b5-f2d6-49dd-9e51-2ccddcbd5909" controlId="f0fc3eb1-d71e-4113-9797-a1c008f3fd7f" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()">
            <script><![CDATA[]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_SkillUserExample" entityId="08dc89c1-a112-4a7e-8114-77d828961b61" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterMethod="0" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tips="&lt;p&gt;通过一问一答方式，提供使用指引示例&lt;/p&gt;" tipsType="1" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dc89e4-eebb-4ba1-895b-5576d4353de7" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="88bad054-0d39-4d9f-95c8-9ca1bed6835f" title="新增示例" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58240889537353" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appGridmJw2_button_58240889537353_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dc89e4-eebb-4ce4-8f32-0394204f780a" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="f0fc3eb1-d71e-4113-9797-a1c008f3fd7f" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240889570370" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.removeCurrentRow()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dc89e4-eebb-4d08-8a2f-17528fc5d981" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dc89e4-eebb-4825-8964-7d7a5d2155a1" viewId="08dc89e4-eebb-4963-8e9c-adc00f79a2a6" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dc89c1-a112-4a7e-8114-77d828961b61" isLookup="false">
                <dataSource keyName="SkillUserExampleGUID" entity="gpt_SkillUserExample" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9YBrAlmMBVAziAE4CiAHgIYC2iEAdAIL76b7QUB20DH+A7sVgV8sJizaduvAUQA0AWABQSVBmx5CpSjXpqcAcVwBJACJCResIdMLlyNFhwFi5arRB18AeyJxhsb19bFQd1Zy03enCARQBXEDZMLw5zWBj4xOTg+0tw1x0PAGEiEApoEAATazN/YtLyquMTbNVHDRdtdzo6ssqAOWoQVJ6GgaoQFtCnTXyukcqAFUxx4ZLeiqXxydyZzvoAWS8KzAAzTErq1MPjs4um7ba8vY9r0/OKsaH/V9uPwYewrtIi8jm9FssviIfu9NhMlCEdh1gXRERECpd/KjZhBLooAGJELxUBA5R5AgqiEQIslIimwRQAdQAFsQhgAKACMAF4OQBKIA==]]></command>
                    <fields>
                        <field name="AssistantAnswer" allowPopulate="true" entity="gpt_SkillUserExample" field="AssistantAnswer" entityAlias="gpt_SkillUserExample" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="SkillUserExampleGUID" allowPopulate="true" entity="gpt_SkillUserExample" field="SkillUserExampleGUID" entityAlias="gpt_SkillUserExample" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="UserQuestion" allowPopulate="true" entity="gpt_SkillUserExample" field="UserQuestion" entityAlias="gpt_SkillUserExample" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="AssistantAnswer" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="AssistantAnswer" entity="gpt_SkillUserExample" entityType="0" attributeType="文本（nvarchar(1024)）" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_SkillUserExample" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_SkillUserExample" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_SkillUserExample" entityType="0" attributeType="日期与时间" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_SkillUserExample" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_SkillUserExample" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_SkillUserExample" entityType="0" attributeType="日期与时间" />
                        <availableField name="SkillGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillGUID" entity="gpt_SkillUserExample" entityType="0" attributeType="Guid" />
                        <availableField name="SkillUserExampleGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillUserExampleGUID" entity="gpt_SkillUserExample" entityType="0" attributeType="Guid" />
                        <availableField name="sort" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="sort" entity="gpt_SkillUserExample" entityType="0" attributeType="整数" />
                        <availableField name="UserQuestion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="UserQuestion" entity="gpt_SkillUserExample" entityType="0" attributeType="文本（nvarchar(1024)）" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_SkillUserExample" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dc89e4-eebc-4a45-8942-311307b953ce" id="08dc89c1-a112-4a7e-8114-77d828961b61" name="gpt_SkillUserExample" primaryField="SkillUserExampleGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions />
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="SkillUserExampleGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="true" fixedColumns="0" maxWrapRow="0" editMode="1" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columns>
                        <column title="用户输入" width="200" field="UserQuestion" allowEdit="true" customizeReferenceable="false" id="33b34daf-a553-4313-ab16-6e6e90d62284" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="5de10a7b-beeb-4f3a-b811-856bf21a769f" field="UserQuestion" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="1024">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                        <column title="助手回答" width="200" field="AssistantAnswer" allowEdit="true" customizeReferenceable="false" id="8e9da6a6-851d-48ed-b4b3-3fbe9b727fdf" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <textBox id="24ea3511-3084-487a-9383-96af5ace1fb0" field="AssistantAnswer" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="1024">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="13703c47-eb32-4583-b7cd-368b8926a55c" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
    <extendGrid xsi:nil="true" />
</grid>