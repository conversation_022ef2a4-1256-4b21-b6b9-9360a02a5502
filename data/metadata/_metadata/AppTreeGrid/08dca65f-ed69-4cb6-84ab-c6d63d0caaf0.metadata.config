<?xml version="1.0" encoding="utf-8"?>
<treeGrid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-07-17T12:56:51Z" modifiedBy="万桥" modifiedOn="2024-07-18T11:23:14.6643008+08:00" metadataStatus="Product" name="树网格控件" functionPageId="08dca65f-ed68-4d04-87d1-3d7e0ba7ff12" htmlCache="default" enableUserSettings="false" isUserDefined="false" entityId="00000000-0000-0000-0000-000000000000" treeGridId="08dca65f-ed69-4cb6-84ab-c6d63d0caaf0" isSeparatedLayout="false" application="4200" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="0433567e-bbc3-46ca-b29d-fbbdfd19664f" controlId="8257fdda-eb18-4100-a708-3db3de36bcdb" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.TreeGrid.returnSelectData()">
            <script><![CDATA[]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" showListHeader="false" searchResultHighlight="false" title="" showTitle="false" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="List" autoHeight="false" virtualScroll="true" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" dataApi="/api/42001301/gptRole" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" isUserDefined="false" onlySelectLeaf="false" searchResultHideParents="false" searchResultShowChilds="false" showLevel="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dca65f-edb0-4c18-82ea-9fe3b87c8986" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="fd05258b-7099-4d8a-8974-d470836f8b74" title="取消" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58238138351133" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="8257fdda-eb18-4100-a708-3db3de36bcdb" title="确定" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58238138342039" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.TreeGrid.returnSelectData()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dca65f-edb0-4c7d-8ac0-583302785eb8" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dca65f-edb0-4d9a-84ad-a2b2a0886b3f" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="a60c7c7b-65e8-4178-9b78-7871e1a4c385" title="新规则" controlId="08dca65f-ed69-4cd6-8d34-3b4ac2fdaf0d" controlType="gridview" controlSubType="" controlProp="multiSelect" controlName="08dca65f-ed69-4cd6-8d34-3b4ac2fdaf0d" metadataStatus="Product">
                    <handles>
                        <handle handleId="c4b273bd-869e-465f-a679-89dd34973d41" ruleId="22f38758-075c-44bc-be1c-d7b3eed40150" action="multiSelect" value="show" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="22f38758-075c-44bc-be1c-d7b3eed40150" title="允许选择" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;type&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dca65f-ed69-486c-89cf-c079901b8f4e" viewId="08dca65f-ed69-4cd6-8d34-3b4ac2fdaf0d" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" isLookup="false" entityId="00000000-0000-0000-0000-000000000000">
                <dataSource withNoLock="true" mode="2">
                    <fields>
                        <field name="myStandardRoleId" allowPopulate="false" entity="GPT_ROLE" field="myStandardRoleId" entityAlias="GPT_ROLE" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="orderCode" allowPopulate="false" entity="GPT_ROLE" field="orderCode" entityAlias="GPT_ROLE" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="parentGUID" allowPopulate="false" entity="GPT_ROLE" field="parentGUID" entityAlias="GPT_ROLE" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="standardRoleName" allowPopulate="false" entity="GPT_ROLE" field="standardRoleName" entityAlias="GPT_ROLE" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="type" allowPopulate="false" entity="GPT_ROLE" field="type" entityAlias="GPT_ROLE" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="buGuid" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="buGuid" entity="GPT_ROLE" entityType="2" />
                        <availableField name="fullName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="fullName" entity="GPT_ROLE" entityType="2" />
                        <availableField name="GPT_ROLE" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="GPT_ROLE" entityType="2" />
                        <availableField name="myStandardRoleId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="myStandardRoleId" entity="GPT_ROLE" entityType="2" />
                        <availableField name="myStandardRoleTypeId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="myStandardRoleTypeId" entity="GPT_ROLE" entityType="2" />
                        <availableField name="orderCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="orderCode" entity="GPT_ROLE" entityType="2" />
                        <availableField name="parentGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="parentGUID" entity="GPT_ROLE" entityType="2" />
                        <availableField name="standardRoleCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="standardRoleCode" entity="GPT_ROLE" entityType="2" />
                        <availableField name="standardRoleName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="standardRoleName" entity="GPT_ROLE" entityType="2" />
                        <availableField name="type" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="type" entity="GPT_ROLE" entityType="2" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams />
                </dataSource>
                <components />
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="false" idField="myStandardRoleId" multiSelect="true" showIndexColumn="false" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" autoEdit="false" parentField="parentGUID" nodeField="myStandardRoleId" treeColumn="standardRoleName" expandOnLoad="2" allowDrop="false" lazyLoad="false" checkRecursive="true" editMode="0" allowHeaderWrap="false" disableFolderSelect="false">
                    <columns>
                        <column title="标准角色名称" width="685" field="standardRoleName" allowEdit="false" customizeReferenceable="false" id="8b690a0e-7d0d-44c7-b5d5-4f1c05c22875" isHidden="false" disableUserHide="2" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                            <customProps />
                            <comboBox id="5738ad67-cb9d-406b-82cc-b6930c3dbc68" field="standardRoleName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                <customProps />
                                <events />
                                <options />
                            </comboBox>
                            <behaviors />
                        </column>
                    </columns>
                    <columnRefs />
                    <sorts>
                        <sort field="orderCode" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <events />
                    <attributes />
                    <hiddens />
                    <icons field="_level">
                        <icon value="0" class="mp-icon-home-moon" url="" />
                    </icons>
                </layout>
            </view>
        </views>
    </layout>
    <extendTreeGrid xsi:nil="true" />
</treeGrid>