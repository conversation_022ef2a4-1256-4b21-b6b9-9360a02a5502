<?xml version="1.0" encoding="utf-8"?>
<treeGrid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="彭宗一" createdOn="2025-05-14T06:48:06Z" modifiedBy="彭宗一" modifiedOn="2025-06-09T21:18:27.6959534+08:00" metadataStatus="Product" name="规则分组" functionPageId="08dd92b2-f8ff-487d-85d8-ad5d0223cd9e" description="规则分组表树网格" htmlCache="default" enableUserSettings="false" isUserDefined="false" entityId="08dd929a-2960-4795-8f1a-7a9daad64693" treeGridId="08dd92b3-4840-4725-8f52-44edb7966f44" isSeparatedLayout="true" application="4200" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes>
        <code id="9900d1fb-188e-46e7-9bce-3b51b2227315" controlId="08dd92b3-4840-4725-8f52-44edb7966f44" controlType="Grid" controlAction="_appTreeGrid1_load">
            <script><![CDATA[/**
 * 数据加载后事件
 * @example
 * //$e可以拿到返回的结果
 * console.log($e.result)
 * //do somthing
 */
const currentSelected = $GlobalSelectedRuleGroup.getCurrent()
console.log('[[[[currentSelected', currentSelected)

if ($e.result.data && $e.result.data.length > 0) {
  // 如果当前没有选中，那就选中第一个一级分组
  if (!currentSelected) {
    const first = $e.result.data.filter(i => !i.ParentGUID)
    $treeGrid.selects(first[0].RuleGroupGUID)
  } else {
    // 有选中就选中当前选中的
    $treeGrid.selects(currentSelected.RuleGroupGUID)
  }
}]]></script>
        </code>
        <code id="9c6aaedf-e680-4b0f-912f-766fbbcbde39" controlId="08dd92b3-4840-4725-8f52-44edb7966f44" controlType="Grid" controlAction="_appTreeGrid1_select">
            <script><![CDATA[/**
 * 行选中后事件
 * @example
 * console.log($e.row)
 * //do something
 */

$util.message.trigger('onSelectedRuleGroup', $e.row)]]></script>
        </code>
        <code id="9aff7f1a-0c6e-4aac-9cfa-dc4b053b25b6" controlId="6a35368d-656f-4fef-88a8-5995eff77bef" controlType="ToolbarItem" controlAction="_appTreeGrid1_button_58211942670526_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */
$treeGrid.reload()
]]></script>
        </code>
        <code id="39c40bfc-7d8a-4ded-9614-fb851ee96ca1" controlId="732a7abd-559f-4c3c-9495-97b7ba1ef2db" controlType="ToolbarItem" controlAction="_appTreeGrid1_button_58211942488314_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
var row = $e.selecteds[0]
$notify.confirm('确认删除',`请确认是否删除分组【${row.GroupName}】吗？`, 'warn').then(
  function(){
    return $api.ruleGroup.delete({
      data: {
        RuleGroupGUID: row.RuleGroupGUID
      },
    }).then(function () {
      $notify.success('删除成功！')
      $page.refreshData()
    })
  },
  function() {}
)
]]></script>
        </code>
        <code id="aceaca9f-2805-41a9-af00-0f390f871445" controlId="73f4ca53-eec8-489b-9d83-ede1db7f2716" controlType="ToolbarItem" controlAction="_appTreeGrid1_button_58211942673260_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */

$treeGrid.reload()
]]></script>
        </code>
        <code id="1a847020-587f-4262-9f83-c68323855943" controlId="c58e16ed-49b3-48ec-9577-71565d6dd527" controlType="ToolbarItem" controlAction="_appTreeGrid1_button_58212027511568_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */
$treeGrid.reload()]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000403" service="ruleGroup" action="delete" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dd9e5d-4506-490d-82ab-725e70878e90" dependentId="$GlobalSelectedRuleGroup" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="simple" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_RuleGroup" entityId="08dd929a-2960-4795-8f1a-7a9daad64693" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="List" autoHeight="false" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectFilterLabel="" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" isUserDefined="false" onlySelectLeaf="false" searchResultShowChilds="false" showLevel="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd92b3-4840-4768-853c-b7a6af5c74df" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="c58e16ed-49b3-48ec-9577-71565d6dd527" title="新增" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58212027511568" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="ondialogclose" functionName="_appTreeGrid1_button_58212027511568_dialogClose" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000403/08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" id="08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" itemId="5eb2a6b1-dfc0-4897-9d9d-8150ccf0a342" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="600" />
                                        <option key="height" value="200" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="query" key="libraryId" value="oid" />
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd92b3-4840-4781-8443-353355b7a4a2" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="b696df2b-2a0a-49bc-a601-7de0cbf6d09c" title="更多" isHighlight="false" type="menu" iconClassUrl="" iconClass="mp-icon-more-1" id="menu_58211942675944" rowButtonStyle="1" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="73f4ca53-eec8-489b-9d83-ede1db7f2716" title="新增下级分组" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58211942673260" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appTreeGrid1_button_58211942673260_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000403/08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" id="08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" itemId="7a77260b-3c3f-4000-ae1c-249d7e2ec5d4" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="600" />
                                                <option key="height" value="200" />
                                                <option key="slipTitleMode" value="show" />
                                                <option key="slipTitleContent" value="" />
                                            </options>
                                            <params>
                                                <param type="data" key="parentId" value="RuleGroupGUID" />
                                                <param type="query" key="libraryId" value="oid" />
                                                <param type="text" key="mode" value="1" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps>
                                            <props>isHidden</props>
                                        </customProps>
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="6a35368d-656f-4fef-88a8-5995eff77bef" title="编辑" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58211942670526" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="ondialogclose" functionName="_appTreeGrid1_button_58211942670526_dialogClose" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000403/08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" id="08dd92b9-8bcc-4204-8ea8-d68bbdfc08e1" itemId="82e71187-aa69-4eed-8338-44c0e5d0092e" metadataStatus="Product">
                                            <options>
                                                <option key="width" value="600" />
                                                <option key="height" value="200" />
                                                <option key="slipTitleMode" value="show" />
                                                <option key="slipTitleContent" value="" />
                                            </options>
                                            <params>
                                                <param type="data" key="parentId" value="ParentGUID" />
                                                <param type="query" key="libraryId" value="oid" />
                                                <param type="data" key="oid" value="RuleGroupGUID" />
                                                <param type="text" key="mode" value="2" />
                                            </params>
                                            <events />
                                        </behavior>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="732a7abd-559f-4c3c-9495-97b7ba1ef2db" title="删除" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58211942488314" rowButtonStyle="2" isMenuButton="true" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="_appTreeGrid1_button_58211942488314_click" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appTreeGrid1_load" enabled="true" metadataStatus="Product" />
            <event name="onselect" functionName="_appTreeGrid1_select" enabled="true" metadataStatus="Product" />
        </events>
        <filter filterId="08dd92b3-4840-479a-8ae4-cd8a0aa85a8b" searchType="0" enableCriteriaSave="false" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions />
            <components />
            <events />
            <dataSource type="bizParam" />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="08f24473-d190-433d-b3b3-a6f64c8f0851" title="新规则" controlId="73f4ca53-eec8-489b-9d83-ede1db7f2716" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="button_58211942673260" metadataStatus="Product">
                    <handles>
                        <handle handleId="c091bed4-425b-478a-8998-440c133b6530" ruleId="e764e4ec-22a4-42c6-8d06-6ed0b462d96a" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="3fa12c51-7293-4349-83da-79d8dc39d8cf" title="新规则" controlId="c58e16ed-49b3-48ec-9577-71565d6dd527" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58212027511568" metadataStatus="Product">
                    <handles>
                        <handle handleId="8508cb58-7568-4183-ad79-2a93421e597d" ruleId="3b13830c-7384-4eec-945e-70773e57e97f" action="hide" value="" metadataStatus="Product" />
                        <handle handleId="a38324bb-39f5-4d1f-b330-9dd1720c5074" ruleId="f8bd80a4-7d3b-4a78-b9c2-7d1784c3a460" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="412d4b99-8c31-48c8-94fb-d4106773d272" title="新规则" controlId="b696df2b-2a0a-49bc-a601-7de0cbf6d09c" controlType="toolbaritem" controlSubType="row" controlProp="isHidden" controlName="menu_58211942675944" metadataStatus="Product">
                    <handles>
                        <handle handleId="d3548f04-d691-4700-a7cf-ecc737e2a0f5" ruleId="3b13830c-7384-4eec-945e-70773e57e97f" action="hide" value="" metadataStatus="Product" />
                        <handle handleId="253e0a16-4e29-45f7-9cad-66a85a4c9a33" ruleId="f8bd80a4-7d3b-4a78-b9c2-7d1784c3a460" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="3b13830c-7384-4eec-945e-70773e57e97f" title="开启api管理不可修改" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;urlParam&quot;,&quot;field&quot;:&quot;readonly&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;1&quot;}]}" metadataStatus="Product" />
                <group id="f8bd80a4-7d3b-4a78-b9c2-7d1784c3a460" title="没有编辑权限" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;actionCode&quot;,&quot;field&quot;:&quot;_actionCode&quot;,&quot;operator&quot;:&quot;not_contains&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;02&quot;}]}" metadataStatus="Product" />
                <group id="e764e4ec-22a4-42c6-8d06-6ed0b462d96a" title="二级分组" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ParentGUID&quot;,&quot;operator&quot;:&quot;is_not_null_and_undefined&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dd92b3-4840-4656-83f8-ff7b1f5c1bb3" viewId="08dd92b3-4840-4741-83cf-5cce7bdbff79" name="规则分组" isDefault="false" isHidden="false" templateStyle="simple" metadataStatus="Product" isLookup="false" entityId="08dd929a-2960-4795-8f1a-7a9daad64693">
                <dataSource keyName="RuleGroupGUID" entity="gpt_RuleGroup" withNoLock="true" mode="1">
                    <command type="" queryDb="false"><![CDATA[Select gpt_RuleGroup.GroupName as GroupName,
gpt_RuleGroup.ParentGUID as ParentGUID,
gpt_RuleGroup.RuleLibraryGUID as RuleLibraryGUID,
gpt_RuleGroup.Sort as Sort,
gpt_RuleGroup.CreatedGUID as CreatedGUID,
gpt_RuleGroup.CreatedName as CreatedName,
gpt_RuleGroup.CreatedTime as CreatedTime,
gpt_RuleGroup.ModifiedGUID as ModifiedGUID,
gpt_RuleGroup.ModifiedName as ModifiedName,
gpt_RuleGroup.ModifiedTime as ModifiedTime,
gpt_RuleGroup.RuleGroupGUID as RuleGroupGUID 
From gpt_RuleGroup As gpt_RuleGroup  
Where (1=1)]]></command>
                    <fields>
                        <field name="CreatedTime" allowPopulate="false" entity="gpt_RuleGroup" field="CreatedTime" entityAlias="gpt_RuleGroup" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="GroupName" allowPopulate="true" entity="gpt_RuleGroup" field="GroupName" entityAlias="gpt_RuleGroup" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ParentGUID" allowPopulate="false" entity="gpt_RuleGroup" field="ParentGUID" entityAlias="gpt_RuleGroup" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="RuleGroupGUID" allowPopulate="true" entity="gpt_RuleGroup" field="RuleGroupGUID" entityAlias="gpt_RuleGroup" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_RuleGroup" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_RuleGroup" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_RuleGroup" entityType="0" attributeType="日期与时间" />
                        <availableField name="GroupName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="GroupName" entity="gpt_RuleGroup" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_RuleGroup" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_RuleGroup" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_RuleGroup" entityType="0" attributeType="日期与时间" />
                        <availableField name="ParentGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ParentGUID" entity="gpt_RuleGroup" entityType="0" attributeType="Guid" />
                        <availableField name="RuleGroupGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="RuleGroupGUID" entity="gpt_RuleGroup" entityType="0" attributeType="Guid" />
                        <availableField name="RuleLibraryGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="RuleLibraryGUID" entity="gpt_RuleGroup" entityType="0" attributeType="Guid" />
                        <availableField name="Sort" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Sort" entity="gpt_RuleGroup" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_RuleGroup" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd92b3-4840-492f-8eb1-81ef241ecf3f" id="08dd929a-2960-4795-8f1a-7a9daad64693" name="gpt_RuleGroup" primaryField="RuleGroupGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" logicFormula="" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_RuleGroup.RuleLibraryGUID" operatorType="eq" id="8f326987-a455-42ff-89a5-edd7c12dfb66" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:oid]</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="GroupName" allowUnRef="false" allowRef="false">
                        <column title="规则分组" width="120" field="GroupName" allowEdit="false" customizeReferenceable="false" id="fa1a1941-d6c5-4a3d-965b-fad648b45382" isHidden="false" disableUserHide="2" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="e0b8bfac-fd06-439d-afb3-e8ab5117bfe3" field="GroupName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="true" hideToolbar="false" rowToolbarWidth="20" frozenToolbar="true" idField="RuleGroupGUID" multiSelect="false" showIndexColumn="false" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" autoEdit="false" parentField="ParentGUID" nodeField="RuleGroupGUID" treeColumn="GroupName" expandOnLoad="2" allowDrop="false" lazyLoad="false" checkRecursive="true" editMode="1" allowHeaderWrap="false" disableFolderSelect="false">
                    <columnRefs>
                        <component ref="GroupName" width="120" />
                    </columnRefs>
                    <sorts>
                        <sort field="CreatedTime" defaultDirection="desc" isDefault="true" metadataStatus="Product" />
                    </sorts>
                    <events />
                    <attributes />
                    <hiddens />
                    <icons field="_level">
                        <icon value="0" class="icon-folder" url="" />
                    </icons>
                </layout>
            </view>
        </views>
    </layout>
</treeGrid>