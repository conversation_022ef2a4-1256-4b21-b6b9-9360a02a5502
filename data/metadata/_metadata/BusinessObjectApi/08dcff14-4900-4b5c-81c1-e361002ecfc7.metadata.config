<?xml version="1.0" encoding="utf-8"?>
<businessObjectApi xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" businessObjectApiId="08dcff14-4900-4b5c-81c1-e361002ecfc7" metadataversion="6" metadataStatus="Product" application="4200" functionGUID="00000000-0000-0000-0000-000000000000" name="dataObject_gptbuilder_Scheme" displayName="方案" remark="" apiType="QUERY" entityId="08dcd959-b6c9-4b80-8cec-c7cce8fdcd11" createdBy="李昂" CreatedOn="2024-11-07T18:09:36.8985002+08:00" modifiedBy="李昂" modifiedOn="2024-11-07T18:10:24.2481766+08:00" isPublic="true">
    <dataSource keyName="PlanGUID" entity="gpt_plan" withNoLock="true" mode="1">
        <command type="sql" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9RYCGA7AdAQUXQE9hIQsRYMBnWA408kAGgFgAoJVdbHAEQzQaAewCuAJzJVaAodTGSWHLmky4+IapHEBLAEaUasDVt0G2nZKt4BZYQBNDtO44sqeuAApqAwg6ew3th+rspWHjhBWAByGAC2AVGxCW7hapHiwnHILomZ2dC5qdzpwIgYZADiAKoAknzSsGUVIDX1xda41dQgAGJgwgDujd19A4MdEQBqIOLUOsJYjTNzC1iT6T7iIIIg9m0NRls70HsHG7zHu/bJAVenN/FKliWX29cAKjoJjfd7Xykwq9cC4dAAzHRnOqHZwOcGQ/bQi4guEQva3RqgtGPQEvTo4LEIgEBQn/b7PdzpKIHRrU6Gwdi9fIINLYOi0Sls9gAdQAFrNKJyvGoDgBeAACwh09iAA==]]></command>
        <diagrams>
            <diagram xmlAttributeId="08dcff14-4900-4d2f-82ad-817e5583596c" id="08dcd959-b6c9-4b80-8cec-c7cce8fdcd11" name="gpt_plan" primaryField="PlanGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" logicFormula="" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions>
                    <condition field="gpt_plan.Mode" operatorType="eq" id="9db30480-e5b2-4542-a9e7-0e0b7bb50d79" dataType="number" valueType="1" leftValueType="field">
                        <Value xsi:type="xsd:string">[query:mode]</Value>
                    </condition>
                </conditions>
            </diagram>
        </diagrams>
    </dataSource>
    <fieldSets>
        <field metadataStatus="Product" field="ApplyScene" entityName="gpt_plan" name="gpt_plan.ApplyScene" alias="ApplyScene" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="Datasource" entityName="gpt_plan" name="gpt_plan.Datasource" alias="Datasource" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="Describe" entityName="gpt_plan" name="gpt_plan.Describe" alias="Describe" entityAlias="gpt_plan" type="string" />
        <field metadataStatus="Product" field="Mode" entityName="gpt_plan" name="gpt_plan.Mode" alias="Mode" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="PlanCode" entityName="gpt_plan" name="gpt_plan.PlanCode" alias="PlanCode" entityAlias="gpt_plan" type="string" />
        <field metadataStatus="Product" field="PlanName" entityName="gpt_plan" name="gpt_plan.PlanName" alias="PlanName" entityAlias="gpt_plan" type="string" />
        <field metadataStatus="Product" field="PromptMode" entityName="gpt_plan" name="gpt_plan.PromptMode" alias="PromptMode" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="SpaceGUID" entityName="gpt_plan" name="gpt_plan.SpaceGUID" alias="SpaceGUID" entityAlias="gpt_plan" type="guid" />
        <field metadataStatus="Product" field="UseFlow" entityName="gpt_plan" name="gpt_plan.UseFlow" alias="UseFlow" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="Version" entityName="gpt_plan" name="gpt_plan.Version" alias="Version" entityAlias="gpt_plan" type="int32" />
        <field metadataStatus="Product" field="CreatedGUID" entityName="gpt_plan" name="gpt_plan.CreatedGUID" alias="CreatedGUID" entityAlias="gpt_plan" type="guid" />
        <field metadataStatus="Product" field="CreatedName" entityName="gpt_plan" name="gpt_plan.CreatedName" alias="CreatedName" entityAlias="gpt_plan" type="string" />
        <field metadataStatus="Product" field="CreatedTime" entityName="gpt_plan" name="gpt_plan.CreatedTime" alias="CreatedTime" entityAlias="gpt_plan" type="datetime" />
        <field metadataStatus="Product" field="ModifiedGUID" entityName="gpt_plan" name="gpt_plan.ModifiedGUID" alias="ModifiedGUID" entityAlias="gpt_plan" type="guid" />
        <field metadataStatus="Product" field="ModifiedName" entityName="gpt_plan" name="gpt_plan.ModifiedName" alias="ModifiedName" entityAlias="gpt_plan" type="string" />
        <field metadataStatus="Product" field="ModifiedTime" entityName="gpt_plan" name="gpt_plan.ModifiedTime" alias="ModifiedTime" entityAlias="gpt_plan" type="datetime" />
        <field metadataStatus="Product" field="PlanGUID" entityName="gpt_plan" name="gpt_plan.PlanGUID" alias="PlanGUID" entityAlias="gpt_plan" type="guid" />
    </fieldSets>
    <parameters>
        <parameter name="pageNo" displayName="分页数" type="int32" required="false" in="query" defaultValue="1" />
        <parameter name="pageSize" displayName="分页大小" type="int32" required="false" in="query" defaultValue="20" />
        <parameter name="mode" displayName="mode" type="int64" required="false" in="query" defaultValue="0" />
    </parameters>
    <pagination enabled="true" />
</businessObjectApi>