<?xml version="1.0" encoding="utf-8"?>
<MetadataEntity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EntityId="08dd4c26-3bb7-4724-89ea-4879e39c666d" Name="gpt_PromptEvalResult" DisplayName="提示词评测结果" Application="4200" Remark="" Scope="Current" IsOpenApprove="false" IsOpenOData="false" IsEnableIsolateForCompany="false" CreatedBy="李昂" CreatedOn="2025-02-13T20:02:05.0824831+08:00" ModifiedBy="李昂" ModifiedOn="2025-02-13T20:06:56.2505625+08:00" metadataStatus="Product" metadataversion="3.0" functionGUID="08dd2652-9bf6-4335-84f4-8ffb8614a7f2" enableSoftDelete="false" enableEntityChangegLog="true" customizeExtensible="false" customizeReferenceable="false" enableFieldAttribute="true">
    <Attributes>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-afc0-44d1-88c2-4c6dd7357ce8</AttributeId>
            <Name>ActualResult</Name>
            <DisplayName>实际结果</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <multiLineText displayName="实际结果" isRequired="false" fillExample="" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-88a8-4d47-8348-2c73d65e337a</AttributeId>
            <Name>BatchNo</Name>
            <DisplayName>批次号</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <digit displayName="批次号" isRequired="false" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-d21d-4848-82a2-6ed2197cc41d</AttributeId>
            <Name>ChatGUID</Name>
            <DisplayName>会话GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="会话GUID" isRequired="false" fillExample="" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-c6e7-4b5c-8340-aaef60a09458</AttributeId>
            <Name>EvalConclusion</Name>
            <DisplayName>评测结论</DisplayName>
            <AttributeType>文本（nvarchar(32)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>32</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="评测结论" isRequired="false" fillExample="" maxLength="32" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-52a7-41e2-8f5c-594a3ed4e786</AttributeId>
            <Name>EvalTaskGUID</Name>
            <DisplayName>评测任务GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="评测任务GUID" isRequired="false" fillExample="" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-738e-4de7-8b01-f90c7c4e8a02</AttributeId>
            <Name>EvalTaskRecordGUID</Name>
            <DisplayName>评测任务记录GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="评测任务记录GUID" isRequired="false" fillExample="" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-e041-4247-841b-11ca3f239425</AttributeId>
            <Name>ExecutionTime</Name>
            <DisplayName>执行时间</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <digit displayName="执行时间" isRequired="false" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-b6a1-4728-8db8-a6ed72e2bef9</AttributeId>
            <Name>ExpectedResult</Name>
            <DisplayName>预期结果</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <multiLineText displayName="预期结果" isRequired="false" fillExample="" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-9f88-4de8-8ca8-9ab2ce4ca413</AttributeId>
            <Name>PromptCode</Name>
            <DisplayName>提示词CODE</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="提示词CODE" isRequired="false" fillExample="" maxLength="64" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-98d7-4c16-8820-488be9815a1b</AttributeId>
            <Name>PromptName</Name>
            <DisplayName>提示词名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="提示词名称" isRequired="false" fillExample="" maxLength="128" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-80f0-4903-8ca0-e0ed5880c8f3</AttributeId>
            <Name>TaskName</Name>
            <DisplayName>任务名称</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="任务名称" isRequired="false" fillExample="" maxLength="64" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-e93d-4d8a-8ad9-3d1d8f1ab514</AttributeId>
            <Name>TokenTotal</Name>
            <DisplayName>Token总数</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <digit displayName="Token总数" isRequired="false" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-4244-8c68-2138441d7670</AttributeId>
            <Name>CreatedGUID</Name>
            <DisplayName>创建人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>3</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="创建人GUID" isRequired="true" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-4246-8723-beda2bfa84c1</AttributeId>
            <Name>CreatedName</Name>
            <DisplayName>创建人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>4</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="创建人名称" isRequired="true" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-423f-8f8d-b322a87e23ec</AttributeId>
            <Name>CreatedTime</Name>
            <DisplayName>创建时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>2</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <dateTime displayName="创建时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-424a-8819-81ddd70c4ef8</AttributeId>
            <Name>ModifiedGUID</Name>
            <DisplayName>修改人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>6</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="修改人GUID" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-424c-8208-fe04d671aa7c</AttributeId>
            <Name>ModifiedName</Name>
            <DisplayName>修改人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>7</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="修改人名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-4248-81c9-719cd3a7d347</AttributeId>
            <Name>ModifiedTime</Name>
            <DisplayName>修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>5</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <dateTime displayName="修改时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-4237-8b07-90d57b802bd3</AttributeId>
            <Name>PromptEvalResultGUID</Name>
            <DisplayName>提示词评测结果主键</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>required</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>1</ColumnNumber>
            <IsPrimaryAttribute>true</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="提示词评测结果主键" isRequired="true" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4c26-3b78-424d-88c4-9b443d43726d</AttributeId>
            <Name>VersionNumber</Name>
            <DisplayName>时间戳</DisplayName>
            <AttributeType>时间戳</AttributeType>
            <DbType>timestamp</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>8</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
        </MetadataAttribute>
    </Attributes>
    <Relationships />
</MetadataEntity>