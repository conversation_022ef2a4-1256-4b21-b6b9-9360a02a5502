<?xml version="1.0" encoding="utf-8"?>
<MetadataEntity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EntityId="08dcb824-deb3-4ca5-8ec8-aeae9ff8959a" Name="gpt_ChatMessageFile" DisplayName="会话记录附件表" Application="4200" Remark="" Scope="Current" IsOpenApprove="false" IsOpenOData="false" IsEnableIsolateForCompany="false" CreatedBy="万桥" CreatedOn="2024-08-09T11:39:27.4425981+08:00" ModifiedBy="万桥" ModifiedOn="2024-10-09T09:45:22.9602676+08:00" metadataStatus="Product" metadataversion="3.0" functionGUID="08dc64ca-a030-45c5-89a5-9c708ee220b0" enableSoftDelete="false" enableEntityChangegLog="true" customizeExtensible="false" customizeReferenceable="false" enableFieldAttribute="false">
    <Attributes>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-0475-428e-88be-1f44af11feeb</AttributeId>
            <Name>ChatMessageGUID</Name>
            <DisplayName>会话记录GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dce803-c7f5-4d58-82ef-ebeee9875419</AttributeId>
            <Name>DownloadUrl</Name>
            <DisplayName>下载地址</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-1fb6-4617-8ac9-9a285497b379</AttributeId>
            <Name>FileId</Name>
            <DisplayName>文件ID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-2c1c-4d0a-8c5f-46e636341b02</AttributeId>
            <Name>FileName</Name>
            <DisplayName>文件名称</DisplayName>
            <AttributeType>文本（nvarchar(512)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>512</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb853-a793-47a2-8ac0-e93341ec6061</AttributeId>
            <Name>NodeType</Name>
            <DisplayName>节点类型</DisplayName>
            <AttributeType>文本（nvarchar(32)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>32</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-378e-4350-8e7d-07c60d9138f7</AttributeId>
            <Name>PreviewUrl</Name>
            <DisplayName>地址</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-4ce9-4ce5-8d8b-ad568d6d5551</AttributeId>
            <Name>Size</Name>
            <DisplayName>大小</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-5883-4f0a-822f-cb17843cf3e7</AttributeId>
            <Name>Status</Name>
            <DisplayName>状态</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb825-6282-429a-8459-7eaa49e4d602</AttributeId>
            <Name>Type</Name>
            <DisplayName>类型</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-4177-8f7d-17909dac1c68</AttributeId>
            <Name>ChatMessageFileGUID</Name>
            <DisplayName>会话记录附件表主键</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>required</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>1</ColumnNumber>
            <IsPrimaryAttribute>true</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-419d-8358-03768e854f15</AttributeId>
            <Name>CreatedGUID</Name>
            <DisplayName>创建人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>3</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-419e-8abd-0d1990a28bb9</AttributeId>
            <Name>CreatedName</Name>
            <DisplayName>创建人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>4</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-4182-8599-eb9031102e77</AttributeId>
            <Name>CreatedTime</Name>
            <DisplayName>创建时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>2</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-41a1-8cae-2e6ed1df8dab</AttributeId>
            <Name>ModifiedGUID</Name>
            <DisplayName>修改人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>6</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-41a2-8ea4-de9aa4fc715c</AttributeId>
            <Name>ModifiedName</Name>
            <DisplayName>修改人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>7</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-419f-8fb8-19d5d0e3a2ac</AttributeId>
            <Name>ModifiedTime</Name>
            <DisplayName>修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>5</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcb824-deab-41a3-8cfc-4ff1c6d89033</AttributeId>
            <Name>VersionNumber</Name>
            <DisplayName>时间戳</DisplayName>
            <AttributeType>时间戳</AttributeType>
            <DbType>timestamp</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>8</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
    </Attributes>
    <Relationships />
</MetadataEntity>