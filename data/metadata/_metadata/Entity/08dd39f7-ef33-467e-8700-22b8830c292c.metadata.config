<?xml version="1.0" encoding="utf-8"?>
<MetadataEntity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EntityId="08dd39f7-ef33-467e-8700-22b8830c292c" Name="gpt_ImportExportFile" DisplayName="导入导出文件" Application="4200" Remark="" Scope="Current" IsOpenApprove="false" IsOpenOData="false" IsEnableIsolateForCompany="false" CreatedBy="万桥" CreatedOn="2025-01-21T16:45:18.9307451+08:00" ModifiedBy="万桥" ModifiedOn="2025-01-21T17:02:20.5725262+08:00" metadataStatus="Product" metadataversion="3.0" functionGUID="08dd0865-0193-4a23-8d40-e5ea4704a6d0" enableSoftDelete="false" enableEntityChangegLog="true" customizeExtensible="false" customizeReferenceable="false" enableFieldAttribute="true">
    <Attributes>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39fa-4ead-4225-86bc-67bc190e89fb</AttributeId>
            <Name>BusinessGUID</Name>
            <DisplayName>业务数据GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="业务数据GUID" isRequired="false" fillExample="" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f8-ce10-460e-87bc-e1832cb9c6f8</AttributeId>
            <Name>BusinessScene</Name>
            <DisplayName>业务场景</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <digit displayName="业务场景" isRequired="false" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f9-6fdb-4c9a-81fa-c4835c0b2fca</AttributeId>
            <Name>Data</Name>
            <DisplayName>文件数据</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <multiLineText displayName="文件数据" isRequired="false" fillExample="" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f9-3898-4ba6-8970-31891928cdb6</AttributeId>
            <Name>DocType</Name>
            <DisplayName>文件类型</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="文件类型" isRequired="false" fillExample="" maxLength="128" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f8-1f35-48d5-8798-3608aff640fe</AttributeId>
            <Name>DocumentGUID</Name>
            <DisplayName>文件GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="文件GUID" isRequired="false" fillExample="" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f8-37a2-4a46-8fd9-090c325db163</AttributeId>
            <Name>DocumentName</Name>
            <DisplayName>文件名</DisplayName>
            <AttributeType>文本（nvarchar(512)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>512</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="文件名" isRequired="false" fillExample="" maxLength="512" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f8-fae2-4c69-85b7-5e1bd1af9122</AttributeId>
            <Name>FileLength</Name>
            <DisplayName>文件长度</DisplayName>
            <AttributeType>长整型</AttributeType>
            <DbType>bigint</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <digit displayName="文件长度" isRequired="false" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4bef-8135-d9ff75126e1e</AttributeId>
            <Name>CreatedGUID</Name>
            <DisplayName>创建人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>3</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="创建人GUID" isRequired="true" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4bf0-805f-8e6b00212e21</AttributeId>
            <Name>CreatedName</Name>
            <DisplayName>创建人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>4</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="创建人名称" isRequired="true" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4be8-889a-3ca619862589</AttributeId>
            <Name>CreatedTime</Name>
            <DisplayName>创建时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>2</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="创建时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4bd7-8505-d21d40c5d3a8</AttributeId>
            <Name>ImportExportFileGUID</Name>
            <DisplayName>导入导出文件主键</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>required</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>1</ColumnNumber>
            <IsPrimaryAttribute>true</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="导入导出文件主键" isRequired="true" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4c03-8f6d-9742eac70406</AttributeId>
            <Name>ModifiedGUID</Name>
            <DisplayName>修改人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>6</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="修改人GUID" isRequired="false" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4c04-8f3a-8822fb2908c9</AttributeId>
            <Name>ModifiedName</Name>
            <DisplayName>修改人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>7</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="修改人名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4c00-84dc-62b5c2e10d60</AttributeId>
            <Name>ModifiedTime</Name>
            <DisplayName>修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>5</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="修改时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd39f7-ef27-4c05-8c47-aae7665f0052</AttributeId>
            <Name>VersionNumber</Name>
            <DisplayName>时间戳</DisplayName>
            <AttributeType>时间戳</AttributeType>
            <DbType>timestamp</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>8</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
    </Attributes>
    <Relationships />
</MetadataEntity>