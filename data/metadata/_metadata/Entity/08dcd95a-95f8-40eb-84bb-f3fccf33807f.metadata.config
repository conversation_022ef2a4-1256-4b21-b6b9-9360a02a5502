<?xml version="1.0" encoding="utf-8"?>
<MetadataEntity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EntityId="08dcd95a-95f8-40eb-84bb-f3fccf33807f" Name="gpt_planRule" DisplayName="规则详情表" Application="4200" Remark="" Scope="Current" IsOpenApprove="false" IsOpenOData="false" IsEnableIsolateForCompany="false" CreatedBy="李昂" CreatedOn="2024-09-20T17:57:06.6113741+08:00" ModifiedBy="许筱欢" ModifiedOn="2025-05-15T10:25:24.9610684+08:00" metadataStatus="Product" metadataversion="3.0" functionGUID="08dcd959-9caf-410e-8f67-cbb7263e2157" enableSoftDelete="false" enableEntityChangegLog="true" customizeExtensible="false" customizeReferenceable="false" enableFieldAttribute="false">
    <Attributes>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd09cc-54cd-4b68-87d3-2396dbe506f9</AttributeId>
            <Name>Describe</Name>
            <DisplayName>描述</DisplayName>
            <AttributeType>文本（nvarchar(1024)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>1024</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="描述" isRequired="false" fillExample="" maxLength="1024" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dce904-24f2-45c0-80cf-b3285b25a1e7</AttributeId>
            <Name>Disable</Name>
            <DisplayName>禁用</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <digit displayName="禁用" isRequired="false" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-f08c-401c-832c-1be7bed292f5</AttributeId>
            <Name>PlanGUID</Name>
            <DisplayName>方案ID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <guid displayName="方案ID" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd9357-ab6c-4cad-84d0-8b4c6b9cf9d0</AttributeId>
            <Name>RefRuleCode</Name>
            <DisplayName>引入规则编码</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark>动态引入规则编码</Remark>
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="引入规则编码" isRequired="false" fillExample="" maxLength="64" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd9357-**************-c8132fb9af69</AttributeId>
            <Name>RefRuleGUID</Name>
            <DisplayName>引入规则ID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark>直接引入规则ID</Remark>
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <guid displayName="引入规则ID" isRequired="false" fillExample="" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd9357-a074-485d-8c3b-37919fc707c1</AttributeId>
            <Name>RefRuleName</Name>
            <DisplayName>引入规则名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark>动态引入规则名称</Remark>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <singleLineText displayName="引入规则名称" isRequired="false" fillExample="" maxLength="64" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcfd43-85d8-4a6b-8472-693ba2c34360</AttributeId>
            <Name>RiskLevel</Name>
            <DisplayName>风险等级</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <digit displayName="风险等级" isRequired="false" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-b0f5-4765-8983-4b34402f8d07</AttributeId>
            <Name>RuleCheckContent</Name>
            <DisplayName>规则检查内容</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <multiLineText displayName="规则检查内容" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcea5b-fba6-47e7-8a62-0cbb152481ab</AttributeId>
            <Name>RuleCheckResultDemo</Name>
            <DisplayName>规则检查结果示例</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <multiLineText displayName="规则检查结果示例" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcdc46-1553-4719-8cca-e2e36ba2bac4</AttributeId>
            <Name>RuleCheckSource</Name>
            <DisplayName>检查来源</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>true</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos>
                <FieldEnumInfo>
                    <Value>0</Value>
                    <Text>表单数据</Text>
                </FieldEnumInfo>
                <FieldEnumInfo>
                    <Value>1</Value>
                    <Text>检查附件</Text>
                </FieldEnumInfo>
            </EnumInfos>
            <digit displayName="检查来源" isRequired="false" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-dfcd-40c1-8bfe-f3e64e9e87e4</AttributeId>
            <Name>RuleGroupGUID</Name>
            <DisplayName>方案规则分组ID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <guid displayName="方案规则分组ID" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-a1dd-4913-81b3-120d2c7a11ba</AttributeId>
            <Name>RuleName</Name>
            <DisplayName>规则名称</DisplayName>
            <AttributeType>文本（nvarchar(64)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>64</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="规则名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd9357-7ee3-4ce3-8e82-0c6ebbb0a75a</AttributeId>
            <Name>RuleSource</Name>
            <DisplayName>规则来源</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark>规则来源：0=页面添加，1=直接引入，2=动态引入</Remark>
            <Length>0</Length>
            <IsNullable>false</IsNullable>
            <DefaultValue>0</DefaultValue>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <digit displayName="规则来源" isRequired="true" fillExample="" unitText="" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcdc46-2cae-4101-8fb5-c1b83c2bf79c</AttributeId>
            <Name>RuleType</Name>
            <DisplayName>规则类型</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>true</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos>
                <FieldEnumInfo>
                    <Value>0</Value>
                    <Text>AI规则</Text>
                </FieldEnumInfo>
            </EnumInfos>
            <digit displayName="规则类型" isRequired="false" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-fc06-4988-8f80-e47233c96d07</AttributeId>
            <Name>Sort</Name>
            <DisplayName>排序</DisplayName>
            <AttributeType>整数</AttributeType>
            <DbType>int</DbType>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>4</UnencryptedLength>
            <EnumInfos />
            <digit displayName="排序" isRequired="false" unitTextType="0" precision="0" precisionType="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4151-80fc-152a41773416</AttributeId>
            <Name>CreatedGUID</Name>
            <DisplayName>创建人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>3</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="创建人GUID" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4152-8c0f-d67aa055f727</AttributeId>
            <Name>CreatedName</Name>
            <DisplayName>创建人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>4</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="创建人名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-414a-8530-16c70b5cb288</AttributeId>
            <Name>CreatedTime</Name>
            <DisplayName>创建时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>2</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="创建时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4157-85da-843ed319a24c</AttributeId>
            <Name>ModifiedGUID</Name>
            <DisplayName>修改人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>6</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="修改人GUID" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4158-8fed-af25c5f4b015</AttributeId>
            <Name>ModifiedName</Name>
            <DisplayName>修改人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>7</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="修改人名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4154-89dd-ddcdd5541b6b</AttributeId>
            <Name>ModifiedTime</Name>
            <DisplayName>修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>5</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="修改时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-4142-8783-480ccd15cf7a</AttributeId>
            <Name>RuleGUID</Name>
            <DisplayName>规则详情表主键</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>required</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>1</ColumnNumber>
            <IsPrimaryAttribute>true</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="规则详情表主键" isRequired="false" optionsType="bizParam" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dcd95a-95e6-415a-88e4-2f386892985f</AttributeId>
            <Name>VersionNumber</Name>
            <DisplayName>时间戳</DisplayName>
            <AttributeType>时间戳</AttributeType>
            <DbType>timestamp</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>8</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
    </Attributes>
    <Relationships />
</MetadataEntity>