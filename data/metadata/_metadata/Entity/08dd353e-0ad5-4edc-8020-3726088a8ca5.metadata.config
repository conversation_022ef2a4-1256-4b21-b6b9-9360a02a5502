<?xml version="1.0" encoding="utf-8"?>
<MetadataEntity xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EntityId="08dd353e-0ad5-4edc-8020-3726088a8ca5" Name="gpt_ChatMessageCheck_History" DisplayName="内容审查不合规记录历史表" Application="4200" Remark="" Scope="Current" IsOpenApprove="false" IsOpenOData="false" IsEnableIsolateForCompany="false" CreatedBy="万桥" CreatedOn="2025-01-15T16:24:34.2515175+08:00" ModifiedBy="万桥" ModifiedOn="2025-02-07T14:50:24.5215613+08:00" metadataStatus="Product" metadataversion="3.0" functionGUID="08dc64ca-d629-45a7-82b0-240f207b4b88" enableSoftDelete="false" enableEntityChangegLog="true" customizeExtensible="false" customizeReferenceable="false" enableFieldAttribute="true">
    <Attributes>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-431b-8384-b07c62bd8aed</AttributeId>
            <Name>ChartGUID</Name>
            <DisplayName>会话ID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="会话ID" isRequired="false" fillExample="" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-435b-85dc-4ff1ddd5480c</AttributeId>
            <Name>CheckResult</Name>
            <DisplayName>审查不合规原因</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <multiLineText displayName="审查不合规原因" isRequired="false" fillExample="" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-43a1-846a-e8ead43db593</AttributeId>
            <Name>CheckSource</Name>
            <DisplayName>检测来源</DisplayName>
            <AttributeType>文本（nvarchar(512)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>512</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="检测来源" isRequired="false" fillExample="" maxLength="512" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd4743-ae41-4280-8a5c-7e05445499ec</AttributeId>
            <Name>HistoryTime</Name>
            <DisplayName>历史修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <dateTime displayName="历史修改时间" isRequired="false" fillExample="" format="yyyy-MM-dd HH:mm:ss" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-43f7-8b53-0fc264510b33</AttributeId>
            <Name>Input</Name>
            <DisplayName>输入</DisplayName>
            <AttributeType>文本（nvarchar(1024)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>1024</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="输入" isRequired="false" fillExample="" maxLength="1024" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-44d6-8ab8-f06962f0971c</AttributeId>
            <Name>MessageSource</Name>
            <DisplayName>消息来源</DisplayName>
            <AttributeType>文本（nvarchar(1024)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>1024</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="消息来源" isRequired="false" fillExample="" maxLength="1024" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4535-8fa8-e99a02985830</AttributeId>
            <Name>ModelInstanceGUID</Name>
            <DisplayName>模型实例GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="模型实例GUID" isRequired="false" fillExample="" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4585-848f-637dc20683cc</AttributeId>
            <Name>Result</Name>
            <DisplayName>输出</DisplayName>
            <AttributeType>文本（nvarchar(max)）</AttributeType>
            <DbType>nvarchar</DbType>
            <Remark />
            <Length>-1</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <multiLineText displayName="输出" isRequired="false" fillExample="" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-45c9-818e-7a0bb58769be</AttributeId>
            <Name>SkillGUID</Name>
            <DisplayName>技能GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <Remark />
            <Length>0</Length>
            <IsNullable>true</IsNullable>
            <DefaultValue />
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>0</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision>0</DecimalPrecision>
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="技能GUID" isRequired="false" fillExample="" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cb1-8f68-0b74c4534383</AttributeId>
            <Name>ChatMessageCheckGUID</Name>
            <DisplayName>内容审查不合规记录历史表主键</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>required</RequiredLevel>
            <Remark />
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>1</ColumnNumber>
            <IsPrimaryAttribute>true</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="内容审查不合规记录历史表主键" isRequired="true" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cbd-8e6e-f407be16e458</AttributeId>
            <Name>CreatedGUID</Name>
            <DisplayName>创建人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>3</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="创建人GUID" isRequired="true" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cbe-8cd4-90dcec904a83</AttributeId>
            <Name>CreatedName</Name>
            <DisplayName>创建人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>4</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="创建人名称" isRequired="true" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cb9-8e1d-a357636cffd1</AttributeId>
            <Name>CreatedTime</Name>
            <DisplayName>创建时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>2</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="创建时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cc4-8546-ea5428180f9b</AttributeId>
            <Name>ModifiedGUID</Name>
            <DisplayName>修改人GUID</DisplayName>
            <AttributeType>Guid</AttributeType>
            <DbType>uniqueidentifier</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>6</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <guid displayName="修改人GUID" isRequired="false" optionsType="bizParam">
                <options />
            </guid>
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cc5-837f-814232060eb4</AttributeId>
            <Name>ModifiedName</Name>
            <DisplayName>修改人名称</DisplayName>
            <AttributeType>文本（nvarchar(128)）</AttributeType>
            <DbType>nvarchar</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length>128</Length>
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>7</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <singleLineText displayName="修改人名称" isRequired="false" maxLength="0" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cc3-8216-85421b4e2aaf</AttributeId>
            <Name>ModifiedTime</Name>
            <DisplayName>修改时间</DisplayName>
            <AttributeType>日期与时间</AttributeType>
            <DbType>datetime</DbType>
            <DisplayFormat>日期与时间</DisplayFormat>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>true</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>5</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
            <dateTime displayName="修改时间" isRequired="false" format="yyyy-MM-dd" />
        </MetadataAttribute>
        <MetadataAttribute metadataStatus="Product">
            <AttributeId>08dd353e-0acf-4cc6-811d-a57babcd28a0</AttributeId>
            <Name>VersionNumber</Name>
            <DisplayName>时间戳</DisplayName>
            <AttributeType>时间戳</AttributeType>
            <DbType>timestamp</DbType>
            <RequiredLevel>none</RequiredLevel>
            <Length xsi:nil="true" />
            <IsNullable>false</IsNullable>
            <BusinessTypeId xsi:nil="true" />
            <ColumnNumber>8</ColumnNumber>
            <IsPrimaryAttribute>false</IsPrimaryAttribute>
            <DecimalPrecision xsi:nil="true" />
            <RelationshipId xsi:nil="true" />
            <AllowQuickFind>false</AllowQuickFind>
            <MultiSelect>false</MultiSelect>
            <LookupPrimaryEntityId xsi:nil="true" />
            <IsThousandth>false</IsThousandth>
            <IsRedundance>false</IsRedundance>
            <IsIdentity>false</IsIdentity>
            <IsEnum>false</IsEnum>
            <IsEncryption>false</IsEncryption>
            <UnencryptionSide>0</UnencryptionSide>
            <UnencryptedLength>0</UnencryptedLength>
            <EnumInfos />
        </MetadataAttribute>
    </Attributes>
    <Relationships />
</MetadataEntity>