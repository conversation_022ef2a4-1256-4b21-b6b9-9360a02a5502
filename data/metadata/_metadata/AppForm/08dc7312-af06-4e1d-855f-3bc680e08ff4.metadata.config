<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2024-05-13T06:05:26Z" modifiedBy="黄诚" modifiedOn="2025-07-23T15:13:36.5713938+08:00" metadataStatus="Product" formId="08dc7312-af06-4e1d-855f-3bc680e08ff4" name="知识库主表表单控件" isSeparatedLayout="false" entityId="08dc7306-ca78-4497-874c-1891cf5d86b7" functionPageId="08dc7312-af03-4b43-8456-216327ee05b7" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="KnowledgeGUID" entity="gpt_Knowledge" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_Knowledge.Code as Code,
gpt_Knowledge.Description as Description,
gpt_Knowledge.EmbeddingModelCode as EmbeddingModelCode,
gpt_Knowledge.IsHasQuestion as IsHasQuestion,
gpt_Knowledge.IsSystem as IsSystem,
gpt_Knowledge.Name as Name,
gpt_Knowledge.SearchServicesEnum as SearchServicesEnum,
gpt_Knowledge.SearchServicesParams as SearchServicesParams,
gpt_Knowledge.SearchServicesURL as SearchServicesURL,
gpt_Knowledge.SpaceGUID as SpaceGUID,
gpt_Knowledge.StatusEnum as StatusEnum,
gpt_Knowledge.TypeEnum as TypeEnum,
gpt_Knowledge.CreatedGUID as CreatedGUID,
gpt_Knowledge.CreatedName as CreatedName,
gpt_Knowledge.CreatedTime as CreatedTime,
gpt_Knowledge.KnowledgeGUID as KnowledgeGUID,
gpt_Knowledge.ModifiedGUID as ModifiedGUID,
gpt_Knowledge.ModifiedName as ModifiedName,
gpt_Knowledge.ModifiedTime as ModifiedTime 
From gpt_Knowledge As gpt_Knowledge 
Where gpt_Knowledge.KnowledgeGUID=@oid]]></command>
        <fields>
            <field name="Code" allowPopulate="true" entity="gpt_Knowledge" field="Code" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="Description" allowPopulate="true" entity="gpt_Knowledge" field="Description" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="EmbeddingModelCode" allowPopulate="true" entity="gpt_Knowledge" field="EmbeddingModelCode" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="KnowledgeGUID" allowPopulate="true" entity="gpt_Knowledge" field="KnowledgeGUID" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="Name" allowPopulate="true" entity="gpt_Knowledge" field="Name" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="SearchServicesEnum" allowPopulate="true" entity="gpt_Knowledge" field="SearchServicesEnum" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="SearchServicesURL" allowPopulate="true" entity="gpt_Knowledge" field="SearchServicesURL" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceGUID" allowPopulate="true" entity="gpt_Knowledge" field="SpaceGUID" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="subGrid_gpt_KnowledgeFile_KnowledgeGUID" allowPopulate="true" entity="gpt_Knowledge" field="KnowledgeGUID" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="subGrid_gpt_KnowledgeParams_KnowledgeGUID" allowPopulate="true" entity="gpt_Knowledge" field="KnowledgeGUID" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
            <field name="TypeEnum" allowPopulate="true" entity="gpt_Knowledge" field="TypeEnum" entityAlias="gpt_Knowledge" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="Code" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Code" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
            <availableField name="Description" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Description" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="EmbeddingModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="EmbeddingModelCode" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="IsHasQuestion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsHasQuestion" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
            <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
            <availableField name="KnowledgeGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="KnowledgeGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Knowledge" entityType="0" attributeType="日期与时间" />
            <availableField name="Name" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Name" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="SearchServicesEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
            <availableField name="SearchServicesParams" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesParams" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="SearchServicesURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SearchServicesURL" entity="gpt_Knowledge" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Knowledge" entityType="0" attributeType="Guid" />
            <availableField name="StatusEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StatusEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
            <availableField name="TypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="TypeEnum" entity="gpt_Knowledge" entityType="0" attributeType="整数" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Knowledge" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dc7312-af07-4841-88ff-bf4820f4abc7" id="08dc7306-ca78-4497-874c-1891cf5d86b7" name="gpt_Knowledge" primaryField="KnowledgeGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="true" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions>
                    <condition field="gpt_Knowledge.TypeEnum" operatorType="eq" id="e166dc06-93a5-43a9-acc4-037086a1afd6" dataType="number" valueType="0" leftValueType="field">
                        <Value xsi:type="xsd:long">1</Value>
                    </condition>
                </conditions>
                <resourceFilters>
                    <resourceFilter joinType="0" type="1" id="86074d4b-45ec-47a3-abf0-08dd6b6b7adc" name="vs_ResFilter_Space">
                        <onConditions />
                        <conditions />
                        <actions />
                    </resourceFilter>
                </resourceFilters>
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dc7312-af03-4ee6-8800-b4c0fb6df0d2" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dc7312-af03-4ef9-8b2f-d8c90d6114e6" title="基础信息" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="130px" width="50%" />
                            <cellStyle labelWidth="130px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="2967b0ec-080c-4d4c-b84c-d1816c9db133" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="知识库名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="知识库名称" field="Name" allowEdit="false" customizeReferenceable="false" id="e6e988be-63f4-4017-8fa7-6bf384a924ab" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textBox id="d046c412-ba0a-4495-9f6b-09b5287123ef" field="Name" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="c1f94d72-3bd0-463a-b6fc-b4ca90249197" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="知识库编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="编号" field="Code" allowEdit="false" customizeReferenceable="false" id="dafa82b7-f782-4ea3-85b7-108ccf7a55d3" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textBox id="137e197c-0d11-4036-83f6-8b4498e74e3b" field="Code" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                                <customProps />
                                                <events>
                                                    <event name="onvalidation" functionName="_appForm_code_validation" enabled="true" metadataStatus="Product" />
                                                </events>
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="c28daac1-366b-49ac-933d-125d58792d82" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="向量模型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="向量模型编号" field="EmbeddingModelCode" allowEdit="false" customizeReferenceable="false" id="84333228-835d-4b3f-a26b-483ce28fd326" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <comboBox id="8f42c77b-85e0-4624-bd1e-95a077c51e69" field="EmbeddingModelCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" redundancyField="" allowEmpty="true" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                                <customProps />
                                                <events />
                                                <options />
                                                <optionsDataSource>
                                                    <dataSource keyName="ModelGUID" entity="gpt_Model" withNoLock="true" mode="1">
                                                        <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AsgewCbgHQFEAPKAV2gEsMA7UaCq+WAQwGdYjSLrb74AaALAAoJKkw4wuAJIspAW0atYM+f2Gj02PDOABPFtBBzmbHfsNzBI5Jom5x4AMJaTsB2Gc4rG9/a1gAOSY5EFd3IJDvG193ABVdRFClOISQKLF/XFAAJwA3ckgQeMT8KhJjJRz8wuKQUvL02zxHbJAmQywAcQBVKQARVxa2jp7+xt8h9pAsCKS2SY7Z8cyF6djyEMHWqax1yPVozPdRgeT/E+W7cXIAM3Jpk7DsW/uu3r7LvGu76dmnrBev2CaQOGSuzx+uw2czcENee1CQgAYtkMMYfP5YABBNgYiSwWBCADqAAsQK1YAAKACMAF5qQBKIA==]]></command>
                                                        <fields>
                                                            <field name="InstanceName" allowPopulate="false" entity="gpt_ModelInstance" field="InstanceName" entityAlias="gpt_ModelInstance" metadataStatus="Product" />
                                                            <field name="InstanceCode" allowPopulate="false" entity="gpt_ModelInstance" field="InstanceCode" entityAlias="gpt_ModelInstance" metadataStatus="Product" />
                                                            <field name="ModelGUID" allowPopulate="false" entity="gpt_Model" field="ModelGUID" entityAlias="gpt_Model" metadataStatus="Product" />
                                                            <field name="InstanceGUID" allowPopulate="false" entity="gpt_ModelInstance" field="InstanceGUID" entityAlias="gpt_ModelInstance" metadataStatus="Product" />
                                                        </fields>
                                                        <availableFields>
                                                            <availableField name="ExecutionSetting" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExecutionSetting" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(1024)）" />
                                                            <availableField name="IsImg" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsImg" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="ModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelCode" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModelName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModelType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelType" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="ServiceTypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ServiceTypeEnum" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Model" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="ModelGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModelGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Model" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Model" entityType="0" attributeType="时间戳" />
                                                            <availableField name="ApiKey" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ApiKey" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="ClientID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ClientID" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="CustomModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CustomModelCode" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="DeploymentName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DeploymentName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="EnableCustomModel" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EnableCustomModel" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="EnableThinking" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EnableThinking" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="Endpoint" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Endpoint" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="InstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceCode" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="InstanceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="IsAvailable" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsAvailable" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="IsDefault" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsDefault" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="IsSupportTool" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsSupportTool" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="MaxConcurrencyQuantity" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="MaxConcurrencyQuantity" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
                                                            <availableField name="ModelGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="gpt_ModelInstance_ModelGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModelName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="gpt_ModelInstance_ModelName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="StrategyId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="StrategyId" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(64)）" />
                                                            <availableField name="Vendor" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Vendor" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_CreatedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_CreatedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_CreatedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="InstanceGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InstanceGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_ModifiedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_ModifiedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_ModifiedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="gpt_ModelInstance_VersionNumber" entity="gpt_ModelInstance" entityType="0" attributeType="时间戳" />
                                                        </availableFields>
                                                        <fixedSortings />
                                                        <summaries />
                                                        <diagrams>
                                                            <diagram xmlAttributeId="08dc971b-e01a-443a-81fd-a6c95c94ac31" id="08dc6593-e473-4a3c-866d-621c232bd94f" name="gpt_Model" primaryField="ModelGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                                                <conditions>
                                                                    <condition field="gpt_Model.ServiceTypeEnum" operatorType="eq" id="31fd9b47-34cc-43bd-b9fb-4686a54afaa9" dataType="number" valueType="0" leftValueType="field">
                                                                        <actions />
                                                                        <Value xsi:type="xsd:long">1</Value>
                                                                    </condition>
                                                                </conditions>
                                                                <resourceFilters />
                                                                <projectInterfaceFilters />
                                                            </diagram>
                                                            <diagram xmlAttributeId="08dc971b-e122-4a90-8901-7e80153e62da" isCurrent="true" id="08dc650d-2c1e-4b7c-8060-68c0fb5aa43d" name="gpt_ModelInstance" primaryField="InstanceGUID" parentId="08dc6593-e473-4a3c-866d-621c232bd94f" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="false" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                                                <conditions />
                                                                <resourceFilters />
                                                                <projectInterfaceFilters />
                                                                <diagramRelation primaryEntityName="gpt_Model" primaryFieldName="ModelGUID" entityName="gpt_ModelInstance" fieldName="ModelGUID" relation="gpt_Model.ModelGUID = gpt_ModelInstance.ModelGUID" Type="OneToMore" />
                                                            </diagram>
                                                        </diagrams>
                                                        <performanceOptimizeHints />
                                                    </dataSource>
                                                    <extendFields>
                                                        <extendField displayName="文本" name="InstanceName" entity="gpt_ModelInstance" isQueryField="true" mapType="Text" />
                                                        <extendField displayName="值" name="InstanceCode" entity="gpt_ModelInstance" isQueryField="false" mapType="Value" />
                                                    </extendFields>
                                                    <sortFields />
                                                </optionsDataSource>
                                            </comboBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="5dbb7664-a453-4490-a927-690a83169bb8" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="52044977-18af-4d03-b98e-8401f4894cf0" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="知识库说明" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="描述" field="Description" allowEdit="false" customizeReferenceable="false" id="69215c55-496e-45ca-8e2e-622b9886e3b8" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textArea id="136104f6-b232-49f2-a09d-3de8c39c02aa" field="Description" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                    <group id="8576955c-27e7-4a54-9b00-2d27827ae082" title="知识库配置" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="151px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="0bcb3aeb-0e84-4542-a6e8-006d643541cd" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="知识库类型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="知识库类型枚举" field="TypeEnum" allowEdit="false" customizeReferenceable="false" id="fd62ec66-c494-4edc-b65d-e357e0216cd8" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <radioButtonList id="e2827cfc-c2a8-4565-89df-33e28b10ab05" field="TypeEnum" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="true" metadataStatus="Product" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                                                <customProps />
                                                <events />
                                                <options>
                                                    <option value="1" text="文档知识库" isDefault="true" disabled="false" />
                                                    <option value="2" text="搜索知识库" isDefault="false" disabled="false" />
                                                </options>
                                            </radioButtonList>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="b710ad5c-50f3-4ee0-a58b-66ddfeb79bb5" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="937b370d-16c2-4ddc-a7e9-0f44b5006fcb" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="文档列表" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="相关列表" field="subGrid_gpt_KnowledgeFile_KnowledgeGUID" allowEdit="false" customizeReferenceable="false" id="a4f50513-e4de-475a-b072-4f17b410f979" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <subGrid id="9d331fa0-ce15-408a-b4e2-7b7c9b36ae09" field="subGrid_gpt_KnowledgeFile_KnowledgeGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" fullField="KnowledgeGUID" height="300">
                                                <customProps />
                                                <events />
                                                <grid field="gpt_KnowledgeFile.KnowledgeGUID" metadataId="08dc73fa-4a24-4158-8483-67517924ad2f" />
                                            </subGrid>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="122a0489-84f3-4e9a-88f9-3064f0e7ea16" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="搜索服务" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="搜索服务枚举" field="SearchServicesEnum" allowEdit="false" customizeReferenceable="false" id="68dd451b-c49e-4045-826f-519353f6c336" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>requirementLevel</props>
                                                <props>isHidden</props>
                                            </customProps>
                                            <radioButtonList id="e0cc4448-17a6-4f12-851e-fd9ac0326dc9" field="SearchServicesEnum" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                                                <customProps />
                                                <events />
                                                <options>
                                                    <option value="0" text="企业自定义" isDefault="true" disabled="false" />
                                                </options>
                                            </radioButtonList>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="6a31a248-776d-4520-be20-8f9c1dba276a" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="c3523adb-f5ad-4c88-8184-daaaeef8038d" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="搜索服务API" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="搜索服务地址" field="SearchServicesURL" allowEdit="false" customizeReferenceable="false" id="d3d69594-b39f-41c8-8166-ee1457ef7474" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>requirementLevel</props>
                                                <props>isHidden</props>
                                            </customProps>
                                            <textBox id="41b08966-4259-49cb-be60-105e3bfc30d2" field="SearchServicesURL" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="eb992dea-2df4-44a8-bb89-595345ea9e48" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="参数列表" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="相关列表" field="subGrid_gpt_KnowledgeParams_KnowledgeGUID" allowEdit="false" customizeReferenceable="false" id="35f18e4c-f581-45f5-9716-81d609c08c2d" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <subGrid id="74952914-9c51-40b1-8b6f-9fdf5fd12119" field="subGrid_gpt_KnowledgeParams_KnowledgeGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" fullField="KnowledgeGUID" height="300">
                                                <customProps />
                                                <events />
                                                <grid field="gpt_KnowledgeParams.KnowledgeGUID" metadataId="08dc7488-f473-44ef-8b3a-1522e3ad90c7" />
                                            </subGrid>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dc7312-af06-4e52-8ddf-457d66b7449c" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dc7312-af04-4108-8897-817289bd26be" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mCancel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc7312-af04-4092-85a0-b4e78a2e594b" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="cc0d4a1f-2603-4322-9c76-a3758d57cbce" title="返回" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240541419780" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58240541419780_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="92ef6067-0412-4daf-bbf2-d6726b5b1181" title="编辑" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58240541417432" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58240541417432_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appForm_load" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens>
            <hidden id="08dca723-9151-4b8e-8f72-088f6b79ecde" field="SpaceGUID" errorMode="default" readonlyMode="none" defaultValue="[query:SpaceGUID]" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false">
                <customProps />
                <events />
            </hidden>
        </hiddens>
        <langs />
        <rule>
            <configs>
                <config id="08dc7312-af04-411a-8ef1-b04056226b5a" title="默认规则" controlId="08dc7312-af04-4092-85a0-b4e78a2e594b" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dc7312-af04-4131-8e98-b115e4585f02" ruleId="08dc7312-af04-4143-83ab-11e0294deef8" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="a2102e2d-cd4e-4e42-9360-06d517bf3118" title="新规则" controlId="937b370d-16c2-4ddc-a7e9-0f44b5006fcb" controlType="cell" controlSubType="" controlProp="isHidden" controlName="subGrid_gpt_KnowledgeFile_KnowledgeGUID" metadataStatus="Product">
                    <handles>
                        <handle handleId="f34f6535-7c16-47de-8dec-b4817f97e1e3" ruleId="9ea7c421-2f0f-4fbd-95b6-0eec01372bed" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="fe795baf-a8b8-4e36-b9af-698ea59f5141" title="新规则" controlId="122a0489-84f3-4e9a-88f9-3064f0e7ea16" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="SearchServicesEnum" metadataStatus="Product">
                    <handles>
                        <handle handleId="0f25b43f-b7b3-44d2-accb-e83ffda1ec2e" ruleId="85fc2fae-d364-4519-a093-220c817f7169" action="required" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="9da37aeb-a984-4fb3-b4b5-0b5ab690b60e" title="新规则" controlId="122a0489-84f3-4e9a-88f9-3064f0e7ea16" controlType="cell" controlSubType="" controlProp="isHidden" controlName="SearchServicesEnum" metadataStatus="Product">
                    <handles>
                        <handle handleId="afee1e80-1404-4454-855b-a0fb65550dd3" ruleId="85fc2fae-d364-4519-a093-220c817f7169" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="d0a56c54-0949-4d40-aef2-1ba8ce0f51d9" title="新规则" controlId="c3523adb-f5ad-4c88-8184-daaaeef8038d" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="SearchServicesURL" metadataStatus="Product">
                    <handles>
                        <handle handleId="67a65b57-0c0a-4af7-8610-b94337d1f31f" ruleId="85fc2fae-d364-4519-a093-220c817f7169" action="required" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="f17a3fcf-6e35-40f8-af1b-cf467017ce81" title="新规则" controlId="c3523adb-f5ad-4c88-8184-daaaeef8038d" controlType="cell" controlSubType="" controlProp="isHidden" controlName="SearchServicesURL" metadataStatus="Product">
                    <handles>
                        <handle handleId="7ebc2846-1f58-4162-b71b-fe0f89b43755" ruleId="85fc2fae-d364-4519-a093-220c817f7169" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="f7973cb3-0e0b-4933-a2f9-8fb77f1805b9" title="新规则" controlId="eb992dea-2df4-44a8-bb89-595345ea9e48" controlType="cell" controlSubType="" controlProp="isHidden" controlName="subGrid_gpt_KnowledgeParams_KnowledgeGUID" metadataStatus="Product">
                    <handles>
                        <handle handleId="cf70ca94-2d05-470f-b451-2fc403a20ea7" ruleId="85fc2fae-d364-4519-a093-220c817f7169" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="7ed2c1c1-8a04-4169-9804-be68c3299454" title="新规则" controlId="8576955c-27e7-4a54-9b00-2d27827ae082" controlType="group" controlSubType="" controlProp="isHidden" controlName="8576955c-27e7-4a54-9b00-2d27827ae082" metadataStatus="Product">
                    <handles>
                        <handle handleId="b4e68c6d-1978-4ec8-a418-6e1689dc615c" ruleId="9ea7c421-2f0f-4fbd-95b6-0eec01372bed" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="ccdeec4f-d5ce-48f3-8a19-df8fe2f175a4" title="新规则" controlId="08dc7312-af04-4108-8897-817289bd26be" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mCancel" metadataStatus="Product">
                    <handles>
                        <handle handleId="341bf52e-abec-442d-bf51-90952a304369" ruleId="08dc7312-af04-4143-83ab-11e0294deef8" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="ab7d4a95-de56-4c27-ab2c-b4d394b77189" title="新规则" controlId="cc0d4a1f-2603-4322-9c76-a3758d57cbce" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58240541419780" metadataStatus="Product">
                    <handles>
                        <handle handleId="7c898bca-7651-445b-96dd-239168ade250" ruleId="08dc7312-af04-4143-83ab-11e0294deef8" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="985e58d9-735e-4788-b591-9b77bef5b007" title="新规则" controlId="92ef6067-0412-4daf-bbf2-d6726b5b1181" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58240541417432" metadataStatus="Product">
                    <handles>
                        <handle handleId="d01e3647-25bb-433b-b5ab-73e2ea031e6f" ruleId="08dc7312-af04-4143-83ab-11e0294deef8" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="9ea7c421-2f0f-4fbd-95b6-0eec01372bed" title="文档上传展示" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;operator&quot;:&quot;not_equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;TypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="08dc7312-af04-4143-83ab-11e0294deef8" title="查看模式" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
                <group id="85fc2fae-d364-4519-a093-220c817f7169" title="搜索知识库展示" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;TypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;2&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules>
        <rule id="483ad55b-b51e-4697-9083-3e7219ac5651" name="知识库名称重复性校验" promptMode="tip" errorMessage="知识库名称不能重复"><![CDATA[ISUNIQUE('Name') == true]]></rule>
        <rule id="547ea2f2-a613-494d-bfc5-56a0091f56ca" name="知识库编码重复性校验" promptMode="tip" errorMessage="知识库编码不能重复"><![CDATA[ISUNIQUE('Code') == true]]></rule>
    </checkRules>
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="18ba2919-c44d-4b7a-a2b4-3df1d825579f" controlId="08dc7312-af04-4092-85a0-b4e78a2e594b" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

/**
 *  数据提交前事件
 *  @example
 *  //当未满足某条件时，取消表单提交
 *  if($e.formData.name === null){
 *      $e.cancel = false
 *  }
 */
var formData = $e.formData;
var knowledgeCode = $e.formData.Code;
var spaceGUID = $e.formData.SpaceGUID;
var embeddingCode = $e.formData.EmbeddingModelCode;
var urlParams = $ctx.getUrlParams();
var mode = urlParams.mode;
if(mode != 1){
  return $api.knowledge.saveKnowledgeCheck({knowledgeCode:knowledgeCode,embeddingCode:embeddingCode})
.then(function(res){
  if(res != true){
    $notify.warning("已有生成好的文件，不支持切换向量模型");
    $e.cancel = false
    $page.refreshData();
  }
  else{
    $form.submit().then(function(res){
      $notify.success("保存成功");
      $page.redirect({mode:2,oid:res});
    });
  }
}
)
}
else{
    //重新获取编码
      $api.knowledge.getNewCode({spaceGUID:spaceGUID,code:knowledgeCode}).then(function (data2) {
          if (data2) {
            formData.Code = data2;
            $form.setData(formData);
            $form.submit().then(function(res){
            $notify.success("保存成功");
            $page.redirect({mode:2,oid:res});
            })
          } else {
            $notify.warning("编码生成失败!")
          }
          
      });
      

}

]]></script>
        </code>
        <code id="49a2a67f-55a4-401a-9588-8a8aa375977a" controlId="08dc7312-af04-4108-8897-817289bd26be" controlType="ToolbarItem" controlAction="_appForm_mCancel_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
$page.redirect('/std/42000401/08dc7307-68d5-4e9e-8689-0448e83eb085')]]></script>
        </code>
        <code id="7fd3be5a-9854-4fad-89e2-b8bea19e2df7" controlId="08dc7312-af06-4e1d-855f-3bc680e08ff4" controlType="Form" controlAction="_appForm_load">
            <script><![CDATA[/**
 *  数据加载完成事件
 *  @example
 *  // 数据加载完后，将表单设置成查看模式
 *  $form.setMode(3);
 */

var conn = $util.notification();
conn.start();
conn.subscribe('KNOWLEDGE_DOQUERY',function(data){
  var code = $form.getData("Code");
  if(code == data.Code)
  {
    var grid = $form.getGrid("subGrid_gpt_KnowledgeFile_KnowledgeGUID");
    grid.doQuery();
  }
})]]></script>
        </code>
        <code id="1c0090fe-7c36-46fb-886a-5a24b93542b1" controlId="137e197c-0d11-4036-83f6-8b4498e74e3b" controlType="CellControl" controlAction="_appForm_code_validation">
            <script><![CDATA[/**
 *  校验事件
 *  @example
 *  //$e.errorText 为错误提示语
 *  //$e.isValid 为判断当前输入内容是否通过校验
 *  //$e.value 为当前的输入内容
 *  //可以参考如下使用：
 *  //if($e.value > 2) {
 *  //  $e.errorText = "不可以大于2"
 *  //  $e.isValid = false
 *  //}
 */
$Utility.validateCode($e)]]></script>
        </code>
        <code id="8a140a30-27e4-483d-82ed-31c996dcec43" controlId="92ef6067-0412-4daf-bbf2-d6726b5b1181" controlType="ToolbarItem" controlAction="_appForm_button_58240541417432_click">
            <script><![CDATA[$Utility.enableEdit()]]></script>
        </code>
        <code id="ee02d817-66e9-4e7b-8c8d-b7c8ffcd5ef3" controlId="cc0d4a1f-2603-4322-9c76-a3758d57cbce" controlType="ToolbarItem" controlAction="_appForm_button_58240541419780_click">
            <script><![CDATA[$page.redirect('/std/42000401/08dc7307-68d5-4e9e-8689-0448e83eb085#')]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000401" service="knowledge" action="getNewCode" type="0" apiSourceType="" />
        <api functionCode="42000401" service="knowledge" action="saveKnowledgeCheck" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls>
        <dependentUrl value="/std/42000401/08dc7307-68d5-4e9e-8689-0448e83eb085" />
        <dependentUrl value="/std/42000401/08dc7307-68d5-4e9e-8689-0448e83eb085#" />
    </dependentUrls>
    <dependentLangs />
    <dependentResources />
    <components />
    <workflow enabled="false" />
</form>