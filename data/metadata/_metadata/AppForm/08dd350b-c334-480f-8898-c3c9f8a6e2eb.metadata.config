<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="郑越" createdOn="2025-01-15T02:24:39Z" modifiedBy="郑越" modifiedOn="2025-01-17T17:15:16.0244817+08:00" metadataStatus="Product" formId="08dd350b-c334-480f-8898-c3c9f8a6e2eb" name="敏感词表单控件" isSeparatedLayout="true" entityId="08dd3507-b550-4999-81d9-3c10df9d9027" functionPageId="08dd350b-c330-4750-8278-9331b7a763f7" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="SensitiveWordsGUID" entity="gpt_SensitiveWords" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_SensitiveWords.IsEnabled as IsEnabled,
gpt_SensitiveWords.Reply as Reply,
gpt_SensitiveWords.SensitiveWordCode as SensitiveWordCode,
gpt_SensitiveWords.SensitiveWordName as SensitiveWordName,
gpt_SensitiveWords.SensitiveWords as SensitiveWords,
gpt_SensitiveWords.SpaceGUID as SpaceGUID,
gpt_SensitiveWords.CreatedGUID as CreatedGUID,
gpt_SensitiveWords.CreatedName as CreatedName,
gpt_SensitiveWords.CreatedTime as CreatedTime,
gpt_SensitiveWords.ModifiedGUID as ModifiedGUID,
gpt_SensitiveWords.ModifiedName as ModifiedName,
gpt_SensitiveWords.ModifiedTime as ModifiedTime,
gpt_SensitiveWords.SensitiveWordsGUID as SensitiveWordsGUID 
From gpt_SensitiveWords As gpt_SensitiveWords 
Where gpt_SensitiveWords.SensitiveWordsGUID=@oid]]></command>
        <fields>
            <field name="IsEnabled" allowPopulate="true" entity="gpt_SensitiveWords" field="IsEnabled" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="Reply" allowPopulate="true" entity="gpt_SensitiveWords" field="Reply" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="SensitiveWordCode" allowPopulate="true" entity="gpt_SensitiveWords" field="SensitiveWordCode" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="SensitiveWordName" allowPopulate="true" entity="gpt_SensitiveWords" field="SensitiveWordName" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="SensitiveWords" allowPopulate="true" entity="gpt_SensitiveWords" field="SensitiveWords" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="SensitiveWordsGUID" allowPopulate="true" entity="gpt_SensitiveWords" field="SensitiveWordsGUID" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceGUID" allowPopulate="true" entity="gpt_SensitiveWords" field="SpaceGUID" entityAlias="gpt_SensitiveWords" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_SensitiveWords" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_SensitiveWords" entityType="0" attributeType="日期与时间" />
            <availableField name="IsEnabled" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsEnabled" entity="gpt_SensitiveWords" entityType="0" attributeType="整数" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_SensitiveWords" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_SensitiveWords" entityType="0" attributeType="日期与时间" />
            <availableField name="Reply" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Reply" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="SensitiveWordCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SensitiveWordCode" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="SensitiveWordName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SensitiveWordName" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="SensitiveWords" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SensitiveWords" entity="gpt_SensitiveWords" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="SensitiveWordsGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SensitiveWordsGUID" entity="gpt_SensitiveWords" entityType="0" attributeType="Guid" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_SensitiveWords" entityType="0" attributeType="Guid" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_SensitiveWords" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dd350b-c336-49ab-828d-de6f173f03ca" id="08dd3507-b550-4999-81d9-3c10df9d9027" name="gpt_SensitiveWords" primaryField="SensitiveWordsGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dd350b-c331-4038-897d-4c0f33a7776c" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dd350b-c331-404d-831b-74978eccdbc9" groupId="groupId1" title="新分组" disableStyle="true" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="137px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="a9187d27-69cc-4333-bafe-2d93e94bf844" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="SensitiveWordName" />
                                    </cell>
                                    <cell id="5a054a4c-e841-48ab-a79e-2691890708eb" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="SensitiveWordCode" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="611eae2d-5214-4db4-95c4-d24c4acc68be" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="IsEnabled" />
                                    </cell>
                                    <cell id="a51c4442-a3f0-4e80-b015-7f240c61e087" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="SpaceGUID" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="a006b6e5-f4f2-4292-999a-5e542c5d4479" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="SensitiveWords" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="6773b3b6-49c7-4369-afb1-672b9b5c04c0" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="Reply" />
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dd350b-c334-4835-8a24-7301bbd2e274" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dd350b-c331-42d7-87cb-4558786b0a3a" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000601/08dd3508-d90e-4a1e-8415-2d12008e41e3" id="08dd3508-d90e-4a1e-8415-2d12008e41e3" itemId="ce79ac18-8ac3-4e81-8211-2080c8424173" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params />
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dd350b-c331-4224-8146-31dbe351981a" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs>
                <config id="08dd350b-c331-42ec-8125-0cff4ba6d18f" title="默认规则" controlId="08dd350b-c331-4224-8146-31dbe351981a" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dd350b-c331-4306-87f8-298ea5e80386" ruleId="08dd350b-c331-4318-8d61-93ad9334260f" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dd350b-c331-4318-8d61-93ad9334260f" title="默认规则" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="1b6f60be-d030-46f8-bd7b-59b54454848a" controlId="08dd350b-c331-4224-8146-31dbe351981a" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[var params = $page.getParams();
$form.setData("SpaceGUID",params.spaceGUID);
return $form.submit({
  service: 'SensitiveWords',
  action: 'SaveSensitiveWords',
  validate: true
}).then(function (id) {
  $notify.success('保存成功')
  if (!$page.getParams().oid) {
    $page.redirect({ oid: id, mode: 2 })
    return
  }
})
]]></script>
        </code>
        <code id="a8ecbee6-d335-4766-8a92-173eb5857ea3" controlId="cc13ec1f-d268-4099-99c9-a1107ead0381" controlType="CellControl" controlAction="_appForm_sensitiveWordCode_validation">
            <script><![CDATA[if ($e.value.length  == 0) {
    $e.isValid = false
    $e.errorText = "编码不能为空"
    return 
  }
$Utility.validateCode($e)]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000601" service="SensitiveWords" action="SaveSensitiveWords" type="0" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <components>
        <component name="SensitiveWordCode" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="5a054a4c-e841-48ab-a79e-2691890708eb" allowUnRef="false" allowRef="false">
            <label title="编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="敏感词编码" field="SensitiveWordCode" allowEdit="false" customizeReferenceable="false" id="62b03468-130e-49b3-8746-44b1e4e2d195" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="cc13ec1f-d268-4099-99c9-a1107ead0381" field="SensitiveWordCode" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                    <customProps />
                    <events>
                        <event name="onvalidation" functionName="_appForm_sensitiveWordCode_validation" enabled="true" metadataStatus="Product" />
                    </events>
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="SensitiveWordName" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="a9187d27-69cc-4333-bafe-2d93e94bf844" allowUnRef="false" allowRef="false">
            <label title="名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="敏感词名称" field="SensitiveWordName" allowEdit="false" customizeReferenceable="false" id="9bc183f7-4afd-45f9-b4f5-5c740747e8c7" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="6db5f8e9-5caa-4fb9-a579-d70ac62e7fc4" field="SensitiveWordName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="IsEnabled" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="611eae2d-5214-4db4-95c4-d24c4acc68be" allowUnRef="false" allowRef="false">
            <label title="是否启用" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="是否启用" field="IsEnabled" allowEdit="false" customizeReferenceable="false" id="13074b0e-6285-41f1-bbb0-30d6a0715fb4" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <radioButtonList id="7526b882-68ab-4ea3-9fa2-4c7bc40f70ab" field="IsEnabled" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                    <customProps />
                    <events />
                    <options>
                        <option value="1" text="启用" isDefault="true" disabled="false" />
                        <option value="0" text="禁用" isDefault="false" disabled="false" />
                    </options>
                </radioButtonList>
                <behaviors />
            </column>
        </component>
        <component name="SensitiveWords" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="a006b6e5-f4f2-4292-999a-5e542c5d4479" allowUnRef="false" allowRef="false">
            <label title="敏感词" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="&lt;p&gt;词与词之间用换行符间隔，一个敏感词一行，每行最长100个字符&lt;/p&gt;" fieldTips="" requirementLevel="none" />
            <column title="敏感词" field="SensitiveWords" allowEdit="false" customizeReferenceable="false" id="e8ffd716-6881-4af7-a5bb-a140684cbdd1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textArea id="07f9cb3e-9d41-4d5f-8c9d-39a0f68d6339" field="SensitiveWords" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" height="470" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
        <component name="Reply" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="6773b3b6-49c7-4369-afb1-672b9b5c04c0" allowUnRef="false" allowRef="false">
            <label title="命中后回复" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="&lt;p&gt;系统检测到用户输入中包含敏感词后，统一使用此内容回复用户&lt;/p&gt;" fieldTips="" requirementLevel="none" />
            <column title="回复" field="Reply" allowEdit="false" customizeReferenceable="false" id="e592fcac-7e2d-4d07-a6f4-15ee3de3ba78" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textArea id="81937753-126a-4db8-aed4-f83bb8cfdca3" field="Reply" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" height="120" maxLength="200" autoHeight="false" minRows="2" maxRows="6">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
        <component name="SpaceGUID" scope="08dd350b-c331-4038-897d-4c0f33a7776c.08dd350b-c331-404d-831b-74978eccdbc9" defaultRefId="a51c4442-a3f0-4e80-b015-7f240c61e087" allowUnRef="false" allowRef="false">
            <label title="空间GUID" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="空间GUID" field="SpaceGUID" allowEdit="false" customizeReferenceable="false" id="296e3078-137c-4fdc-9943-c38eec391ac5" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="fbd12634-bfba-404b-95c5-a0604445e978" field="SpaceGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="true" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
    </components>
    <workflow enabled="false" />
</form>