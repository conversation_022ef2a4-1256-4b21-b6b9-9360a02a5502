<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-22T11:25:41Z" modifiedBy="黄浩翔" modifiedOn="2025-07-29T14:56:25.1138804+08:00" metadataStatus="Product" formId="08ddc912-7e0d-4dde-8409-aeeb659d8175" name="MCP服务表表单控件" isSeparatedLayout="true" entityId="08ddc5cc-4a78-45ba-8904-535992959ae5" functionPageId="08ddc912-7e0d-4506-8f35-55a3fd3af0ee" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="ServiceGUID" entity="gpt_McpService" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_McpService.CustomHeaders as CustomHeaders,
gpt_McpService.ServiceCode as ServiceCode,
gpt_McpService.ServiceDescription as ServiceDescription,
gpt_McpService.ServiceIcon as ServiceIcon,
gpt_McpService.ServiceName as ServiceName,
gpt_McpService.ServiceTransport as ServiceTransport,
gpt_McpService.ServiceURL as ServiceURL,
gpt_McpService.SpaceGUID as SpaceGUID,
gpt_McpService.Status as Status,
gpt_McpService.TimeoutSeconds as TimeoutSeconds,
gpt_McpService.CreatedGUID as CreatedGUID,
gpt_McpService.CreatedName as CreatedName,
gpt_McpService.CreatedTime as CreatedTime,
gpt_McpService.ModifiedGUID as ModifiedGUID,
gpt_McpService.ModifiedName as ModifiedName,
gpt_McpService.ModifiedTime as ModifiedTime,
gpt_McpService.ServiceGUID as ServiceGUID 
From gpt_McpService As gpt_McpService 
Where gpt_McpService.ServiceGUID=@oid]]></command>
        <fields>
            <field name="CustomHeaders" allowPopulate="true" entity="gpt_McpService" field="CustomHeaders" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceCode" allowPopulate="true" entity="gpt_McpService" field="ServiceCode" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceDescription" allowPopulate="true" entity="gpt_McpService" field="ServiceDescription" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceGUID" allowPopulate="true" entity="gpt_McpService" field="ServiceGUID" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceIcon" allowPopulate="true" entity="gpt_McpService" field="ServiceIcon" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceName" allowPopulate="true" entity="gpt_McpService" field="ServiceName" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceTransport" allowPopulate="true" entity="gpt_McpService" field="ServiceTransport" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="ServiceURL" allowPopulate="true" entity="gpt_McpService" field="ServiceURL" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceGUID" allowPopulate="true" entity="gpt_McpService" field="SpaceGUID" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="subGrid_gpt_McpServiceTool_ServiceGUID" allowPopulate="true" entity="gpt_McpService" field="ServiceGUID" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
            <field name="TimeoutSeconds" allowPopulate="true" entity="gpt_McpService" field="TimeoutSeconds" entityAlias="gpt_McpService" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_McpService" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_McpService" entityType="0" attributeType="日期与时间" />
            <availableField name="CustomHeaders" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CustomHeaders" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_McpService" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_McpService" entityType="0" attributeType="日期与时间" />
            <availableField name="ServiceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceCode" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ServiceDescription" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceDescription" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="ServiceGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceGUID" entity="gpt_McpService" entityType="0" attributeType="Guid" />
            <availableField name="ServiceIcon" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceIcon" entity="gpt_McpService" entityType="0" attributeType="图片" />
            <availableField name="ServiceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceName" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ServiceTransport" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceTransport" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="ServiceURL" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceURL" entity="gpt_McpService" entityType="0" attributeType="文本（nvarchar(1024)）" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_McpService" entityType="0" attributeType="Guid" />
            <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_McpService" entityType="0" attributeType="整数" />
            <availableField name="TimeoutSeconds" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TimeoutSeconds" entity="gpt_McpService" entityType="0" attributeType="整数" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_McpService" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08ddc912-7e0d-4fab-801d-7455476a28ea" id="08ddc5cc-4a78-45ba-8904-535992959ae5" name="gpt_McpService" primaryField="ServiceGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08ddc912-7e0d-4bfd-82dd-9b270997b7e0" groupId="groupId1" title="基础信息" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="2618548c-0533-457d-86bd-d7c873861a26" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceName" />
                                    </cell>
                                    <cell id="13a08e72-099e-4dd0-ac33-5a25468b7ce2" colSpan="2" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="4c47ee2d-0af1-4ae7-84a1-db0bfe737842" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceCode" />
                                    </cell>
                                    <cell id="77cb8aae-5a9f-4ad9-b287-9a39538b1789" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="1ff5689b-b6a8-4b8a-b5f2-e6f40ec4a6cc" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceDescription" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="afddc75a-8dea-49d7-95be-a6e66f340899" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceIcon" />
                                    </cell>
                                    <cell id="c8a00129-d66e-4ee2-9fdc-a8cb475df9ec" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="a9fce9d6-01ca-43da-9a90-c025568a4ceb" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceTransport" />
                                    </cell>
                                    <cell id="ae976d4f-cfe4-4299-ace1-e3d8c108be15" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="2cb036aa-a905-4699-a2ca-f4d022bec95e" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ServiceURL" />
                                    </cell>
                                    <cell id="43dba486-0192-4330-bd03-a28aa9921344" colSpan="2" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="63467396-ffaa-4b45-8a54-2fbdba2e4db8" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="CustomHeaders" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="5fa81e75-6caa-4f44-b2f2-6fab6c7b1ffe" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="TimeoutSeconds" />
                                    </cell>
                                    <cell id="9d5bf9d4-eff9-463e-9abe-9b4d43bbc766" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="865f1536-9ba7-4a30-8541-ffa04a1414a5" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="subGrid_gpt_McpServiceTool_ServiceGUID" />
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08ddc912-7e0d-4e00-82f0-d972279999a2" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08ddc912-7e0d-4c61-8940-9d9219c452f0" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000801/08ddc83c-57da-43d6-8a50-d1c59e60ce7f" id="08ddc83c-57da-43d6-8a50-d1c59e60ce7f" itemId="26876010-8ce9-4282-ad02-13b9cb97f3f7" metadataStatus="Product">
                                    <options>
                                        <option key="jumpMode" value="self" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params />
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="0afb43ab-518b-472e-bfe5-f1eaf9395e88" title="刷新工具列表" isHighlight="false" type="button" iconClassUrl="" id="button_58206501558694" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58206501558694_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08ddc912-7e0d-4c47-89da-2e7abf5363f5" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <attributes />
        <hiddens>
            <hidden id="08ddc5cd-d113-4b2d-8197-b1c709793973" field="SpaceGUID" errorMode="default" readonlyMode="none" defaultValue="[query:SpaceGUID]" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false">
                <customProps />
                <events />
            </hidden>
        </hiddens>
        <langs />
        <rule>
            <configs>
                <config id="57c7ac65-28b6-443b-8504-32b3fe386e6d" title="新规则" controlId="865f1536-9ba7-4a30-8541-ffa04a1414a5" controlType="cell" controlSubType="" controlProp="isHidden" controlName="subGrid_gpt_McpServiceTool_ServiceGUID" metadataStatus="Product">
                    <handles>
                        <handle handleId="711dd5f7-2b82-4808-b655-25d4744ddb68" ruleId="08ddc912-7e0d-4ca6-888e-67358a76825a" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="10a2f380-af89-41ec-81e9-9cb5ebe6073f" title="新规则" controlId="0afb43ab-518b-472e-bfe5-f1eaf9395e88" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58206501558694" metadataStatus="Product">
                    <handles>
                        <handle handleId="72787b57-8450-4484-abe7-3c0bf282c88b" ruleId="08ddc912-7e0d-4ca6-888e-67358a76825a" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08ddc912-7e0d-4ca6-888e-67358a76825a" title="编辑模式" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;2&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="fe844db5-1279-4fe9-b7dd-0e7ff7c7cd8d" controlId="08ddc912-7e0d-4c47-89da-2e7abf5363f5" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[

const isVal = $form.validate()
if (isVal) {
  // 准备数据，确保类型正确
  const formData = {
    "serviceGUID": $form.getData("ServiceGUID"),
    "serviceName": $form.getData("ServiceName"),
    "serviceCode": $form.getData("ServiceCode"),
    "serviceDescription": $form.getData("ServiceDescription"),
    "serviceURL": $form.getData("ServiceURL"),
    "serviceTransport": $form.getData("ServiceTransport"),
    "serviceIcon": $form.getData("ServiceIcon"),
    "customHeaders": $form.getData("CustomHeaders") || "",
    "timeoutSeconds": parseInt($form.getData("TimeoutSeconds")) || 30,
    "status": parseInt($form.getData("Status")) || 1,
    "spaceGUID": $form.getData("SpaceGUID")
  }

  console.log(formData)

  
  $api.mcpManager.saveMcpService({
    data: formData
  })
  .then(function(res){
    $notify.success('MCP服务保存成功！')
    
    var params = $page.getParams()
    if (res && !params.oid) {
      $page.redirect({ oid: res, mode:2})
    }
  })
}]]></script>
        </code>
        <code id="b94ec04c-70c7-49d4-8591-e89196def914" controlId="0afb43ab-518b-472e-bfe5-f1eaf9395e88" controlType="ToolbarItem" controlAction="_appForm_button_58206501558694_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

const formData = {
  "serviceGUID": $form.getData("ServiceGUID")
}

$page.loading.show()
$api.mcpManager.refreshMcpTools({
    data: formData
})
.then(function(res){
  $page.loading.hide()
  $notify.success('工具列表刷新成功！')
  $page.refreshData()
})

]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000801" service="mcpManager" action="refreshMcpTools" type="0" apiSourceType="" />
        <api functionCode="42000801" service="mcpManager" action="saveMcpService" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <components>
        <component name="ServiceName" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="2618548c-0533-457d-86bd-d7c873861a26" allowUnRef="false" allowRef="false">
            <label title="服务名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="服务名称" field="ServiceName" allowEdit="false" customizeReferenceable="false" id="a90c2c8c-190f-4423-9f7b-a8e694b0908e" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="af3c11d1-33a8-4bc3-9cbe-6786a4c303f8" field="ServiceName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="ServiceDescription" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="1ff5689b-b6a8-4b8a-b5f2-e6f40ec4a6cc" allowUnRef="false" allowRef="false">
            <label title="服务描述" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="服务描述" field="ServiceDescription" allowEdit="false" customizeReferenceable="false" id="751c6c48-1059-4226-ac72-43475a7a3416" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textArea id="9cb5c739-b004-4251-b11f-ff869d030cc7" field="ServiceDescription" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
        <component name="ServiceCode" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="4c47ee2d-0af1-4ae7-84a1-db0bfe737842" allowUnRef="false" allowRef="false">
            <label title="服务编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="服务编码" field="ServiceCode" allowEdit="false" customizeReferenceable="false" id="019e9dcd-e1df-42dd-99b3-639a45043f07" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="d68e4a32-6994-49a1-afeb-8262e8d17b24" field="ServiceCode" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="ServiceURL" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="2cb036aa-a905-4699-a2ca-f4d022bec95e" allowUnRef="false" allowRef="false">
            <label title="服务URL" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="服务URL地址" field="ServiceURL" allowEdit="false" customizeReferenceable="false" id="7b732784-edf3-40dd-a691-9491ac4de407" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="21b78afd-547b-4094-b6cd-6cda960bed2b" field="ServiceURL" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="1024" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="ServiceTransport" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="a9fce9d6-01ca-43da-9a90-c025568a4ceb" allowUnRef="false" allowRef="false">
            <label title="传输方式" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="服务传输方式" field="ServiceTransport" allowEdit="false" customizeReferenceable="false" id="41c83973-7714-46f4-ba70-2d9579d283f1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <radioButtonList id="76c8d438-2c67-49dc-aa19-3d6316fba12c" field="ServiceTransport" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionDataType="text" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                    <customProps />
                    <events />
                    <options>
                        <option value="StreamableHttp" text="StreamableHttp" isDefault="true" disabled="false" />
                        <option value="SSE" text="SSE" isDefault="false" disabled="false" />
                        <option value="STDIO" text="STDIO" isDefault="false" disabled="false" />
                    </options>
                </radioButtonList>
                <behaviors />
            </column>
        </component>
        <component name="ServiceIcon" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="afddc75a-8dea-49d7-95be-a6e66f340899" allowUnRef="false" allowRef="false">
            <label title="服务图标" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="&lt;p&gt;建议格式：PNG、SVG，建议尺寸：52x52px&lt;/p&gt;" requirementLevel="none" />
            <column title="服务图片" field="ServiceIcon" allowEdit="false" customizeReferenceable="false" id="7e737ac8-d36b-4846-a0de-4bd76395afde" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <imageUpload id="9c328516-695c-4e0b-82cf-73b8f41e8626" field="ServiceIcon" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" limitFileCount="1" limitSize="5" fileLimitSize="5" limitType="*.jpg;*.jpeg;*.bmp;*.gif;*.png" showUploadButton="true" enableCheck="false" checkMode="0">
                    <customProps />
                    <events />
                    <rightsAddRule />
                    <rightsDelRule />
                </imageUpload>
                <behaviors />
            </column>
        </component>
        <component name="TimeoutSeconds" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="5fa81e75-6caa-4f44-b2f2-6fab6c7b1ffe" allowUnRef="false" allowRef="false">
            <label title="超时时长" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="超时时长" field="TimeoutSeconds" allowEdit="false" customizeReferenceable="false" id="c4d7e303-ade7-4087-96bc-6a7af0042f87" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <spinner id="b0030dec-fdf2-426c-939d-02a678ca2306" field="TimeoutSeconds" errorMode="default" readonlyMode="none" defaultValue="10" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="false" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="秒" unitTextType="0" unitTextBizParam="gptbuilder_check_baidu_secret_key" minValue="10" maxValue="500" minOperatorType="ge" maxOperatorType="le">
                    <customProps />
                    <events />
                </spinner>
                <behaviors />
            </column>
        </component>
        <component name="CustomHeaders" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="63467396-ffaa-4b45-8a54-2fbdba2e4db8" allowUnRef="false" allowRef="false">
            <label title="请求头" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="自定义请求头" field="CustomHeaders" allowEdit="false" customizeReferenceable="false" id="083e40d2-ac8f-420f-b092-6821df2ad4ec" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textArea id="6bb82fac-8527-41ef-95aa-11b9a740099e" field="CustomHeaders" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
        <component name="subGrid_gpt_McpServiceTool_ServiceGUID" scope="08ddc912-7e0d-4be3-81ff-dc1f3a6a3811.08ddc912-7e0d-4bfd-82dd-9b270997b7e0" defaultRefId="865f1536-9ba7-4a30-8541-ffa04a1414a5" allowUnRef="false" allowRef="false">
            <label title="工具列表" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="相关列表" field="subGrid_gpt_McpServiceTool_ServiceGUID" allowEdit="false" customizeReferenceable="false" id="c14776aa-a520-43d3-b2b8-8a195d92a7a1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <subGrid id="594dcc5d-5d57-4b47-bd3e-479523196ca3" field="subGrid_gpt_McpServiceTool_ServiceGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="true" metadataStatus="Product" fullField="ServiceGUID" height="300">
                    <customProps />
                    <events />
                    <grid field="gpt_McpServiceTool.ServiceGUID" metadataId="08ddcb4b-94fa-422c-89fb-4ee683a4c929" />
                </subGrid>
                <behaviors />
            </column>
        </component>
    </components>
    <workflow enabled="false" />
</form>