<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-05-13T10:23:15Z" modifiedBy="万桥" modifiedOn="2024-07-24T15:41:01.3289715+08:00" metadataStatus="Product" formId="08dc7336-b31c-4b66-8801-03d1537c223a" name="技能基础表表单控件" isSeparatedLayout="false" entityId="08dc70ba-c30b-4a8d-893d-813f8d335ba6" functionPageId="08dc7336-b31c-43f8-87db-cf50a3677d87" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="SkillGUID" entity="gpt_Skill" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_Skill.Describe as Describe,
gpt_Skill.Guide as Guide,
gpt_Skill.Icon as Icon,
gpt_Skill.IsSystem as IsSystem,
gpt_Skill.Metadata as Metadata,
gpt_Skill.Mode as Mode,
gpt_Skill.ModelInstanceCode as ModelInstanceCode,
gpt_Skill.OpenDialogWindow as OpenDialogWindow,
gpt_Skill.SkillCode as SkillCode,
gpt_Skill.SkillName as SkillName,
gpt_Skill.SkillVersions as SkillVersions,
gpt_Skill.SpaceGUID as SpaceGUID,
gpt_Skill.StartUpPluginGUID as StartUpPluginGUID,
gpt_Skill.StartUpToolGUID as StartUpToolGUID,
gpt_Skill.Status as Status,
gpt_Skill.CreatedGUID as CreatedGUID,
gpt_Skill.CreatedName as CreatedName,
gpt_Skill.CreatedTime as CreatedTime,
gpt_Skill.ModifiedGUID as ModifiedGUID,
gpt_Skill.ModifiedName as ModifiedName,
gpt_Skill.ModifiedTime as ModifiedTime,
gpt_Skill.SkillGUID as SkillGUID 
From gpt_Skill As gpt_Skill 
Where gpt_Skill.SkillGUID=@oid]]></command>
        <fields>
            <field name="SkillCode" allowPopulate="true" entity="gpt_Skill" field="SkillCode" entityAlias="gpt_Skill" metadataStatus="Product">
                <fields />
            </field>
            <field name="SkillGUID" allowPopulate="true" entity="gpt_Skill" field="SkillGUID" entityAlias="gpt_Skill" metadataStatus="Product">
                <fields />
            </field>
            <field name="SkillName" allowPopulate="true" entity="gpt_Skill" field="SkillName" entityAlias="gpt_Skill" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
            <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="Guide" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Guide" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="Icon" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Icon" entity="gpt_Skill" entityType="0" attributeType="图片" />
            <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Skill" entityType="0" attributeType="整数" />
            <availableField name="Metadata" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Metadata" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="Mode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Mode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(16)）" />
            <availableField name="ModelInstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelInstanceCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Skill" entityType="0" attributeType="日期与时间" />
            <availableField name="OpenDialogWindow" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="OpenDialogWindow" entity="gpt_Skill" entityType="0" attributeType="整数" />
            <availableField name="SkillCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillCode" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="SkillGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SkillGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
            <availableField name="SkillName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillName" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="SkillVersions" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SkillVersions" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
            <availableField name="StartUpPluginGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpPluginGUID" entity="gpt_Skill" entityType="0" attributeType="Guid" />
            <availableField name="StartUpToolGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="StartUpToolGUID" entity="gpt_Skill" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Status" entity="gpt_Skill" entityType="0" attributeType="整数" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Skill" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dc7336-b31c-4f44-8d0a-24899a08e7b7" id="08dc70ba-c30b-4a8d-893d-813f8d335ba6" name="gpt_Skill" primaryField="SkillGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions>
                    <condition field="gpt_Skill.SkillGUID" operatorType="eq" id="122ab8d0-6129-4724-acec-6647a62f757a" dataType="string" valueType="1" leftValueType="field">
                        <actions />
                        <Value xsi:type="xsd:string">[query:oid]</Value>
                    </condition>
                </conditions>
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dc7336-b31c-46ce-88c5-370ffbcee99a" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dc7336-b31c-46e1-8b13-4480ab9c6312" title="新分组" disableStyle="true" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="132px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="c90e0ace-3e23-4b3c-9073-c1e514b1a30f" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="技能基础表主键" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="技能基础表主键" field="SkillGUID" allowEdit="false" customizeReferenceable="false" id="656fd53d-6227-4c5c-ab69-fe79a053ffd4" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="e9ca6f47-1d73-4b41-8e79-f7c8d0de01a8" field="SkillGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="true" metadataStatus="Product" isBold="false" maxLength="0">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="c7cc9117-0fe4-4b88-a41b-3ca91b9cebdf" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="技能名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="单行文本" field="newSkillName" allowEdit="false" customizeReferenceable="false" id="0104be4a-a2f3-4662-860c-ebfc3d011030" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="23e47957-9d35-47c4-a9c9-6b1ce446b596" field="newSkillName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="8ef56f4c-c37a-406a-97f6-b5bedce52e50" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="技能编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="单行文本" field="newSkillCode" allowEdit="false" customizeReferenceable="false" id="e77651a9-408f-45d0-9456-74112f64754c" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="0ecca71d-384d-40b9-b8f4-991b4b5d96a9" field="newSkillCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                                <customProps />
                                                <events>
                                                    <event name="onvalidation" functionName="_appForm_newSkillCode_validation" enabled="true" metadataStatus="Product" />
                                                </events>
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dc7336-b31c-4b89-8c52-ae3f9f3a135c" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dc7336-b31c-4774-8afa-a3a89ec48a32" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc7336-b31c-478a-8c03-489ad751c1ba" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mCancel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appForm_load" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens>
            <hidden id="08dc70ba-e82b-407d-8c11-c3a07457a5cc" field="SkillCode" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false">
                <customProps />
                <events />
            </hidden>
            <hidden id="08dc70ba-f604-46c0-8b13-b14c851d3db0" field="SkillName" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false">
                <customProps />
                <events />
            </hidden>
        </hiddens>
        <langs />
        <rule>
            <configs>
                <config id="08dc7336-b31c-479c-8314-171e84261099" title="默认规则" controlId="08dc7336-b31c-4774-8afa-a3a89ec48a32" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dc7336-b31c-47af-8ebd-2ae0af2428db" ruleId="08dc7336-b31c-47c1-8768-8a5bbf72fd94" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dc7336-b31c-47c1-8768-8a5bbf72fd94" title="默认规则" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="2e693664-adf6-4429-a2d5-5e93404cba91" controlId="08dc7336-b31c-4774-8afa-a3a89ec48a32" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[var isValidate = $form.validate();
if (!isValidate) {
  return
}
var data = $form.getData()
return $api.skill.copySkill({
  data: {
    copySkillGUID: data.SkillGUID,
    newSkillName: data.newSkillName,
    newSkillCode: data.newSkillCode
  }
}).then(function () {
  $notify.success('复制成功！')
  $form.resetChangeState()
  $page.close()
})]]></script>
        </code>
        <code id="61452cef-1c76-4ac8-b9b4-be3880611c6b" controlId="08dc7336-b31c-478a-8c03-489ad751c1ba" controlType="ToolbarItem" controlAction="_appForm_mCancel_click">
            <script><![CDATA[$form.resetChangeState()
$page.close()
]]></script>
        </code>
        <code id="8a5e536e-2f8e-4840-90cb-740b71ff951a" controlId="08dc7336-b31c-4b66-8801-03d1537c223a" controlType="Form" controlAction="_appForm_load">
            <script><![CDATA[var data = $form.getData()
$form.setData({
  newSkillName: data.SkillName,
  newSkillCode: data.SkillCode + '_copy'
})
$form.resetChangeState()]]></script>
        </code>
        <code id="a91f0ff0-2b70-42c5-ae3b-1b9dfd79685b" controlId="0ecca71d-384d-40b9-b8f4-991b4b5d96a9" controlType="CellControl" controlAction="_appForm_newSkillCode_validation">
            <script><![CDATA[$Utility.validateCode($e)]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000301" service="skill" action="copySkill" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls />
    <dependentLangs />
    <components />
</form>