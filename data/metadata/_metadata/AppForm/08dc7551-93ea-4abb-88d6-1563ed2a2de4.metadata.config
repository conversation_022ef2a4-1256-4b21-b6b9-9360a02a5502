<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-05-16T02:40:41Z" modifiedBy="夏娜" modifiedOn="2024-11-13T14:46:35.2491579+08:00" metadataStatus="Product" formId="08dc7551-93ea-4abb-88d6-1563ed2a2de4" name="督办表表单控件" isSeparatedLayout="false" entityId="08dc747f-3239-4635-8aa4-c839f01a4ef2" functionPageId="08dc7551-93b1-4434-81d9-dc02c387ec0a" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="SupervisionGUID" entity="gpt_Supervision" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_Supervision.AssociateTaskName as AssociateTaskName,
gpt_Supervision.DutyUserName as DutyUserName,
gpt_Supervision.ExpectedFinishDate as ExpectedFinishDate,
gpt_Supervision.Remark as Remark,
gpt_Supervision.CreatedGUID as CreatedGUID,
gpt_Supervision.CreatedName as CreatedName,
gpt_Supervision.CreatedTime as CreatedTime,
gpt_Supervision.ModifiedGUID as ModifiedGUID,
gpt_Supervision.ModifiedName as ModifiedName,
gpt_Supervision.ModifiedTime as ModifiedTime,
gpt_Supervision.SupervisionGUID as SupervisionGUID 
From gpt_Supervision As gpt_Supervision 
Where gpt_Supervision.SupervisionGUID=@oid]]></command>
        <fields>
            <field name="AssociateTaskName" allowPopulate="true" entity="gpt_Supervision" field="AssociateTaskName" entityAlias="gpt_Supervision" metadataStatus="Product">
                <fields />
            </field>
            <field name="DutyUserName" allowPopulate="true" entity="gpt_Supervision" field="DutyUserName" entityAlias="gpt_Supervision" metadataStatus="Product">
                <fields />
            </field>
            <field name="ExpectedFinishDate" allowPopulate="true" entity="gpt_Supervision" field="ExpectedFinishDate" entityAlias="gpt_Supervision" metadataStatus="Product">
                <fields />
            </field>
            <field name="Remark" allowPopulate="true" entity="gpt_Supervision" field="Remark" entityAlias="gpt_Supervision" metadataStatus="Product">
                <fields />
            </field>
            <field name="SupervisionGUID" allowPopulate="true" entity="gpt_Supervision" field="SupervisionGUID" entityAlias="gpt_Supervision" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="AssociateTaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="AssociateTaskName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
            <availableField name="DutyUserName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DutyUserName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ExpectedFinishDate" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExpectedFinishDate" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Supervision" entityType="0" attributeType="日期与时间" />
            <availableField name="Remark" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Remark" entity="gpt_Supervision" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="SupervisionGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SupervisionGUID" entity="gpt_Supervision" entityType="0" attributeType="Guid" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Supervision" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dc7551-93eb-49d1-8447-913cb14cfdd0" id="08dc747f-3239-4635-8aa4-c839f01a4ef2" name="gpt_Supervision" primaryField="SupervisionGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dc7551-93e5-4cd6-8b0f-6149b431cc98" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dc7551-93e5-4d02-8659-16117d4de73c" title="督办信息" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="58ce480d-c35e-49ed-9993-77d80998e084" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="督办标题" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="督办标题" field="AssociateTaskName" allowEdit="false" customizeReferenceable="false" id="6148be6e-8f64-49a8-af7e-ba12477d4de4" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="b2f3391b-9704-4315-afca-5a344ff62708" field="AssociateTaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="87867aea-ffbd-4cd8-b2a2-57293e83631a" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="主责人" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="主责人" field="DutyUserName" allowEdit="false" customizeReferenceable="false" id="8293abf1-d620-4ae5-bf10-eb15da280850" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="bfec9743-047a-4338-b0c3-f8595b7ff74e" field="DutyUserName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="55f84e05-63c1-4ecd-bf77-1f1eb44c3412" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="要求完成日期" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="要求完成日期" field="ExpectedFinishDate" allowEdit="false" customizeReferenceable="false" id="a9487fcf-b9a9-41bb-97a7-4fa3033ed4d1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="date" align="center" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <datePicker id="099f60b8-5fe3-4723-bc63-168be1687cf1" field="ExpectedFinishDate" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" format="yyyy-MM-dd" metadataStatus="Product" allowClear="false">
                                                <customProps />
                                                <events />
                                            </datePicker>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="fa13cd1b-81bc-479b-9d8d-6fb57505e940" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="188d1a2a-3409-41b1-90d1-bc1cffd4b2f4" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="事项说明" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="事项说明" field="Remark" allowEdit="false" customizeReferenceable="false" id="d0cfde75-ee84-4930-a5a1-9a1e360c0121" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textArea id="db66ca62-0d49-46ad-8a34-35a175cac73f" field="Remark" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" height="60" maxLength="512" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="eb14e9fe-ef1a-4596-a2f4-f20a7d82b667" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dc7551-93ea-4afe-8480-f1aab52ec148" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dc7551-93e5-4f7d-813c-b09cb5f62985" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.UI.Template.Form.defaultSaveAndBack()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions>
                                    <standardBehaviorOption key="saveSuccessTips" value="" />
                                </standardBehaviorOptions>
                            </item>
                            <item itemId="08dc7551-93e6-4012-8d6c-a7de1eb1e192" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="Mysoft.Map6.Utility.back()" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="89aab139-5ef8-45d3-a570-ea2a20ab4157" title="AI辅助录入" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58233020186903" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events />
                                <behavior target="self" targetDisplayType="fixedWidth" type="none" itemId="00e44ba5-f8e7-4f81-86dd-f1e1b71be069" metadataStatus="Product">
                                    <options>
                                        <option key="skillCode" value="test_815_1" />
                                    </options>
                                    <params />
                                    <events />
                                </behavior>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="42418971-1016-4aa5-84f3-dfeb5ca96c68" title="AI录入" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58227586086732" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58227586086732_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs>
                <config id="08dc7551-93e6-403a-8f53-279c7204b335" title="默认规则" controlId="08dc7551-93e5-4f7d-813c-b09cb5f62985" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dc7551-93e6-4070-87d6-0d73ea62312f" ruleId="08dc7551-93e6-40d0-88ff-8469777d9be8" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dc7551-93e6-40d0-88ff-8469777d9be8" title="默认规则" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="7b13b70c-b85e-478b-aedd-7a481f89a061" controlId="08dc7551-93e5-4f7d-813c-b09cb5f62985" controlType="ToolbarItem" controlAction="Mysoft.Map6.UI.Template.Form.defaultSaveAndBack()">
            <script><![CDATA[]]></script>
        </code>
        <code id="883129d3-ae58-4811-952c-41276cf49d9c" controlId="42418971-1016-4aa5-84f3-dfeb5ca96c68" controlType="ToolbarItem" controlAction="_appForm_button_58227586086732_click">
            <script><![CDATA[(function () {
  this._gpt.open({
    skills: ['scene_input_form_copy_1013'],
    defaultSkill: 'scene_input_form_copy_1013',
    onCardAction: function(e) {
      console.log(e)
      if (e.data && e.data.message && e.data.name === 'form-binding' && e.data.message.data) {
        var fData = e.data.message.data
        console.log(fData)
        $form.setData(fData)
      }
    }
  })
})()]]></script>
        </code>
    </codes>
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls>
        <dependentUrl xsi:nil="true" />
    </dependentUrls>
    <dependentLangs />
    <components />
</form>