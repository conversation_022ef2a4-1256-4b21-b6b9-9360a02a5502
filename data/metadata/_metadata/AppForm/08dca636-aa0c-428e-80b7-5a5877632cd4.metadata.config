<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="万桥" createdOn="2024-07-17T08:01:29Z" modifiedBy="万桥" modifiedOn="2025-01-06T18:24:52.2597072+08:00" metadataStatus="Product" formId="08dca636-aa0c-428e-80b7-5a5877632cd4" name="工作空间表单控件" isSeparatedLayout="false" entityId="08dca632-3947-4ff2-8e66-a765f55b144a" functionPageId="08dca636-aa05-49b6-827f-9803b2173cfa" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="SpaceGUID" entity="gpt_WorkSpace" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_WorkSpace.Describe as Describe,
gpt_WorkSpace.IsSystem as IsSystem,
gpt_WorkSpace.SpaceCode as SpaceCode,
gpt_WorkSpace.SpaceName as SpaceName,
gpt_WorkSpace.CreatedGUID as CreatedGUID,
gpt_WorkSpace.CreatedName as CreatedName,
gpt_WorkSpace.CreatedTime as CreatedTime,
gpt_WorkSpace.ModifiedGUID as ModifiedGUID,
gpt_WorkSpace.ModifiedName as ModifiedName,
gpt_WorkSpace.ModifiedTime as ModifiedTime,
gpt_WorkSpace.SpaceGUID as SpaceGUID 
From gpt_WorkSpace As gpt_WorkSpace 
Where gpt_WorkSpace.SpaceGUID=@oid]]></command>
        <fields>
            <field name="Describe" allowPopulate="true" entity="gpt_WorkSpace" field="Describe" entityAlias="gpt_WorkSpace" metadataStatus="Product">
                <fields />
            </field>
            <field name="IsSystem" allowPopulate="false" entity="gpt_WorkSpace" field="IsSystem" entityAlias="gpt_WorkSpace" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceCode" allowPopulate="true" entity="gpt_WorkSpace" field="SpaceCode" entityAlias="gpt_WorkSpace" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceGUID" allowPopulate="true" entity="gpt_WorkSpace" field="SpaceGUID" entityAlias="gpt_WorkSpace" metadataStatus="Product">
                <fields />
            </field>
            <field name="SpaceName" allowPopulate="true" entity="gpt_WorkSpace" field="SpaceName" entityAlias="gpt_WorkSpace" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_WorkSpace" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_WorkSpace" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_WorkSpace" entityType="0" attributeType="日期与时间" />
            <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_WorkSpace" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsSystem" entity="gpt_WorkSpace" entityType="0" attributeType="整数" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_WorkSpace" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_WorkSpace" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_WorkSpace" entityType="0" attributeType="日期与时间" />
            <availableField name="SpaceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceCode" entity="gpt_WorkSpace" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_WorkSpace" entityType="0" attributeType="Guid" />
            <availableField name="SpaceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="SpaceName" entity="gpt_WorkSpace" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_WorkSpace" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dca636-aa0f-4b91-86cf-911c5f866e20" id="08dca632-3947-4ff2-8e66-a765f55b144a" name="gpt_WorkSpace" primaryField="SpaceGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dca636-aa06-4b83-8be6-6f2af3bda180" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dca636-aa06-4bab-8670-4543f4d12f1f" title="新分组" disableStyle="true" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="100%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="e3e1150d-ff37-45c4-abbc-851968b3c195" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="空间名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="空间名称" field="SpaceName" allowEdit="false" customizeReferenceable="false" id="abf4b27c-02c0-4fa0-976e-cc168e7a3945" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>readonlyMode</props>
                                            </customProps>
                                            <textBox id="9e5b6f93-e366-4a94-affc-a13c413ff06b" field="SpaceName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="9fe8b964-ea43-4e65-80ef-a57b7152b0d7" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="空间编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="&lt;p&gt;默认添加 x_ 前缀&lt;/p&gt;" requirementLevel="none" />
                                        <column title="空间编码" field="SpaceCode" allowEdit="false" customizeReferenceable="false" id="70862b4f-5566-453b-ad55-50ca77128d44" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>readonlyMode</props>
                                            </customProps>
                                            <textBox id="0bdb5adb-075a-4e02-a3dd-6abd2a126385" field="SpaceCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="d291a83e-9adb-4c5f-b27f-b0cf9460c84a" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="关联角色" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="&lt;p&gt;配置关联角色的空间，只有对应角色可以访问空间知识库、技能、助手、插件&lt;br&gt;&lt;/p&gt;" requirementLevel="none" />
                                        <column title="弹出选择" field="role" allowEdit="false" customizeReferenceable="false" id="a932229a-5b08-4069-9157-d4f756994c31" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <lookPopup id="6c06a6b0-4daf-420a-afa7-2e7062e4b413" field="role" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" valueFromSelect="true" redundancyField="" multiSelect="true">
                                                <customProps />
                                                <events />
                                                <behavior target="dialog" targetDisplayType="fixedWidth" type="dataSelect" url="/std/42001301/08dca65f-ed68-4d24-8e78-0cea646dd466" id="08dca65f-ed68-4d24-8e78-0cea646dd466" itemId="e2bb426b-b041-44e9-aefc-9399a274391c" metadataStatus="Product">
                                                    <options>
                                                        <option key="width" value="450" />
                                                        <option key="height" value="600" />
                                                    </options>
                                                    <params />
                                                    <events />
                                                    <dataSelectRule id="4c32c724-6572-4e66-9fc9-651b4c25f188" metadataId="08dca65f-ed69-4cb6-84ab-c6d63d0caaf0" isAllowRepeat="false" isSaveParentFieldData="false">
                                                        <dataSelectFields>
                                                            <dataSelectField field="value" mapField="myStandardRoleId" isUnique="true" dataType="columnField" />
                                                            <dataSelectField field="text" mapField="standardRoleName" isUnique="false" dataType="columnField" />
                                                        </dataSelectFields>
                                                    </dataSelectRule>
                                                </behavior>
                                            </lookPopup>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="180a0beb-1872-4418-ae37-798db5faf047" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="空间描述" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="空间描述" field="Describe" allowEdit="false" customizeReferenceable="false" id="7285b419-1cf5-4d6e-935b-76581c1b08a3" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>readonlyMode</props>
                                            </customProps>
                                            <textArea id="c4c3c8a3-0520-4cbf-8e85-16775eeb8add" field="Describe" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="120" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dca636-aa0c-42db-8c57-0d549a9049a1" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dca636-aa06-4fa4-8b7a-06f9f90b7ad8" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mCancel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dca636-aa06-4ed8-8348-09f5116ab572" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appForm_load" enabled="true" metadataStatus="Product" />
            <event name="oninit" functionName="_appForm_init" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs>
                <config id="08dca636-aa06-4fc9-8bde-f5ab532a5043" title="默认规则" controlId="08dca636-aa06-4ed8-8348-09f5116ab572" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dca636-aa06-4ff9-83b0-520ee27e0d16" ruleId="08dca636-aa07-401c-82be-2c48a6b4fe03" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="76987828-b47c-40dd-94cc-9f00d9db4e06" title="新规则" controlId="e3e1150d-ff37-45c4-abbc-851968b3c195" controlType="cell" controlSubType="" controlProp="readonlyMode" controlName="SpaceName" metadataStatus="Product">
                    <handles>
                        <handle handleId="92e33871-6cfb-43c5-807a-ca32b44d50c9" ruleId="95725dac-94a7-4543-b28c-775513e3db6c" action="readonly" value="true" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="5cbcf8f8-a3d3-422f-bee3-5747db400d88" title="新规则" controlId="9fe8b964-ea43-4e65-80ef-a57b7152b0d7" controlType="cell" controlSubType="" controlProp="readonlyMode" controlName="SpaceCode" metadataStatus="Product">
                    <handles>
                        <handle handleId="97d8072b-fd77-4004-ac74-bce2f8cad9ed" ruleId="95725dac-94a7-4543-b28c-775513e3db6c" action="readonly" value="true" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="50383783-3fee-4c46-9835-633d46dbdc1f" title="新规则" controlId="180a0beb-1872-4418-ae37-798db5faf047" controlType="cell" controlSubType="" controlProp="readonlyMode" controlName="Describe" metadataStatus="Product">
                    <handles>
                        <handle handleId="8e6c84d5-ba3c-438a-b210-9c3d93be53e6" ruleId="95725dac-94a7-4543-b28c-775513e3db6c" action="readonly" value="true" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dca636-aa07-401c-82be-2c48a6b4fe03" title="默认规则" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
                <group id="95725dac-94a7-4543-b28c-775513e3db6c" title="是否系统级" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;IsSystem&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="63e4844e-d7bb-42bd-8b9d-c87eb85125ed" controlId="08dca636-aa06-4ed8-8348-09f5116ab572" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
const isVal = $form.validate()
if (isVal) {
    var formdata = $e.formData;
    console.log(formdata)
    return $api.workspace.saveAndUpdate({
      data: formdata
    }).then(function () {
      $notify.success('保存成功！')
      $form.resetChangeState()
      
      $page.close()
      $page.refreshRelativeData()
      $grid.reload()
    })
}
]]></script>
        </code>
        <code id="d9ac9c9f-76e2-4d61-a624-f73dfd1710a0" controlId="08dca636-aa06-4fa4-8b7a-06f9f90b7ad8" controlType="ToolbarItem" controlAction="_appForm_mCancel_click">
            <script><![CDATA[$form.resetChangeState()
$page.close()
]]></script>
        </code>
        <code id="2d3d1cf5-1bd9-4fa3-a522-c84398f7d261" controlId="08dca636-aa0c-428e-80b7-5a5877632cd4" controlType="Form" controlAction="_appForm_init">
            <script><![CDATA[/**
 *  表单初始化完成事件
 *  @example
 *  //设置表单控件属性
 *  $form.setProp('target','name','testName')
 */
var form = $form.getData();
console.info(form)
if (form.SpaceCode != null && form.SpaceCode != '') {
  console.info()
  var SpaceGUID = form.SpaceGUID;
  $api.workSpace.query({
      spaceGUID: SpaceGUID
    }).then(function (data) {
      console.info(data)
      form.role = data.role
      if (form.SpaceCode.startsWith("x_")) {
        form.SpaceCode = form.SpaceCode.substring("x_".length);
      }
      form.role__text = data.role__text
      $form.setData(form)
      //$form.setProp('role__text',data.role__text)
    })

}]]></script>
        </code>
        <code id="72422d9f-52af-48cf-8e6e-c4544869bf38" controlId="08dca636-aa0c-428e-80b7-5a5877632cd4" controlType="Form" controlAction="_appForm_load">
            <script><![CDATA[/**
 *  数据加载完成事件
 *  @example
 *  // 数据加载完后，将表单设置成查看模式
 *  $form.setMode(3);
 */]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42001301" service="workspace" action="saveAndUpdate" type="0" apiSourceType="" />
        <api functionCode="42001301" service="workSpace" action="query" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <components />
</form>