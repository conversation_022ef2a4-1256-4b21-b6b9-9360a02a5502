<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="黄浩翔" createdOn="2025-07-25T07:56:44Z" modifiedBy="黄浩翔" modifiedOn="2025-07-28T18:16:23.513738+08:00" metadataStatus="Product" formId="08ddcb50-cc9d-4734-8d9b-8c7926c04182" name="MCP服务工具表表单控件" isSeparatedLayout="true" entityId="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" functionPageId="08ddcb50-cc9c-4299-88e0-35c18e382fb7" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="ToolGUID" entity="gpt_McpServiceTool" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_McpServiceTool.InputSchema as InputSchema,
gpt_McpServiceTool.LastTestInput as LastTestInput,
gpt_McpServiceTool.LastTestOutput as LastTestOutput,
gpt_McpServiceTool.OutputSchema as OutputSchema,
gpt_McpServiceTool.ServiceGUID as ServiceGUID,
gpt_McpServiceTool.SpaceGUID as SpaceGUID,
gpt_McpServiceTool.Status as Status,
gpt_McpServiceTool.ToolDescription as ToolDescription,
gpt_McpServiceTool.ToolName as ToolName,
gpt_McpServiceTool.ToolTitle as ToolTitle,
gpt_McpServiceTool.CreatedGUID as CreatedGUID,
gpt_McpServiceTool.CreatedName as CreatedName,
gpt_McpServiceTool.CreatedTime as CreatedTime,
gpt_McpServiceTool.ModifiedGUID as ModifiedGUID,
gpt_McpServiceTool.ModifiedName as ModifiedName,
gpt_McpServiceTool.ModifiedTime as ModifiedTime,
gpt_McpServiceTool.ToolGUID as ToolGUID 
From gpt_McpServiceTool As gpt_McpServiceTool 
Where gpt_McpServiceTool.ToolGUID=@oid]]></command>
        <fields>
            <field name="LastTestOutput" allowPopulate="true" entity="gpt_McpServiceTool" field="LastTestOutput" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
            <field name="subGrid_MCP_TOOL_PARAMETER_toolGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
            <field name="ToolDescription" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolDescription" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
            <field name="ToolGUID" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolGUID" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
            <field name="ToolName" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolName" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
            <field name="ToolTitle" allowPopulate="true" entity="gpt_McpServiceTool" field="ToolTitle" entityAlias="gpt_McpServiceTool" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
            <availableField name="InputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="LastTestInput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestInput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="LastTestOutput" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastTestOutput" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_McpServiceTool" entityType="0" attributeType="日期与时间" />
            <availableField name="OutputSchema" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="OutputSchema" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="ServiceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ServiceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
            <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_McpServiceTool" entityType="0" attributeType="整数" />
            <availableField name="ToolDescription" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolDescription" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="ToolGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolGUID" entity="gpt_McpServiceTool" entityType="0" attributeType="Guid" />
            <availableField name="ToolName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolName" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ToolTitle" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ToolTitle" entity="gpt_McpServiceTool" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_McpServiceTool" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08ddcb50-cc9d-4b55-8a54-8e62bb822c6e" id="08ddc5d0-50ae-4af7-850d-93e3efa69f2f" name="gpt_McpServiceTool" primaryField="ToolGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" logicFormula="" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08ddcb50-cc9d-418e-8bea-38e823953d4d" title="工具校验" tabTitle="" disableStyle="false" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="a71ca692-0d10-4a83-b8d9-4b3da506ef82" groupId="group_58206499249300" title="新分组-2" disableStyle="true" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="9988461b-b34b-43a8-bb9e-ef02045d35e6" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ToolName" />
                                    </cell>
                                    <cell id="e5095313-5633-4675-afe1-ad50f55f03f0" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="046ea7b9-6862-43db-8c25-ab14f1c5f5b3" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ToolTitle" />
                                    </cell>
                                    <cell id="ea42e43c-8bac-43a0-8b1a-7c6a814f2024" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="75ff94cd-7f65-4d69-b44f-0c159c3d9c92" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ToolDescription" />
                                    </cell>
                                    <cell id="c311d96c-0ea2-4f46-86b5-1f2e554fb398" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="832556f2-fcdd-4977-adb8-25318fbe2031" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="subGrid_MCP_TOOL_PARAMETER_toolGUID" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="01d0e58f-c257-4679-9408-c0fb059ab66f" colSpan="3" rowSpan="1" metadataStatus="Product">
                                        <component ref="LastTestOutput" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="738bf4b4-adb0-4812-9e75-2f817de56177" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                    <cell id="03fb24ce-ff20-413d-87ab-9bffeb0b7061" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08ddcb50-cc9d-475f-8b3e-bb633f968b8e" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="oninit" functionName="_appForm_init" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens>
            <hidden id="08ddc5d0-50aa-4a70-87e1-c4ad80ebf571" field="ToolGUID" errorMode="default" readonlyMode="none" requirementLevel="none" isHidden="true" metadataStatus="Product" allowPopulate="false" isCustomField="false" />
        </hiddens>
        <langs />
        <rule>
            <configs />
            <groups>
                <group id="08ddcb50-cc9d-4525-8bb7-b3c40be1e8c4" title="默认规则" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;type&quot;:&quot;number&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="19bae61d-3734-4d8c-b01a-606c78842ec7" controlId="08ddcb50-cc9d-4734-8d9b-8c7926c04182" controlType="Form" controlAction="_appForm_init">
            <script><![CDATA[/**
 *  表单初始化完成事件
 *  @example
 *  //设置表单控件属性
 *  $form.setProp('target','name','testName')
 */

var params = $page.getParams();
if (params.toolGUID) {
  $api.mcpToolManager.getToolByGUID(
    {toolGUID:params.toolGUID})
    .then(function(res){
      console.log(res)

      $form.setData('ToolName', res.name)
      $form.setData('ToolTitle', res.title)
      $form.setData('ToolDescription', res.description)
      $form.setData('LastTestOutput', res.lastTestOutput)

      $form.setData('subGrid_MCP_TOOL_PARAMETER_toolGUID', params.toolGUID)

      $form.resetChangeState()

  })
}]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000801" service="mcpToolManager" action="getToolByGUID" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <components>
        <component name="ToolName" scope="08ddcb50-cc9d-418e-8bea-38e823953d4d.a71ca692-0d10-4a83-b8d9-4b3da506ef82" defaultRefId="9988461b-b34b-43a8-bb9e-ef02045d35e6" allowUnRef="false" allowRef="false">
            <label title="工具名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="工具名称" field="ToolName" allowEdit="false" customizeReferenceable="false" id="37985c5d-ab54-4bd0-9bcd-512fa8b9d47c" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="4f0ac4af-224c-4f45-a721-f12e5451e6a3" field="ToolName" errorMode="default" readonlyMode="all" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="ToolTitle" scope="08ddcb50-cc9d-418e-8bea-38e823953d4d.a71ca692-0d10-4a83-b8d9-4b3da506ef82" defaultRefId="046ea7b9-6862-43db-8c25-ab14f1c5f5b3" allowUnRef="false" allowRef="false">
            <label title="工具标题" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="工具标题" field="ToolTitle" allowEdit="false" customizeReferenceable="false" id="999cdbc1-59fb-43f9-922c-6b4a7ad2bc64" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="dddc9769-1a4a-4ccf-bd2d-1cead05a1436" field="ToolTitle" errorMode="default" readonlyMode="all" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="ToolDescription" scope="08ddcb50-cc9d-418e-8bea-38e823953d4d.a71ca692-0d10-4a83-b8d9-4b3da506ef82" defaultRefId="75ff94cd-7f65-4d69-b44f-0c159c3d9c92" allowUnRef="false" allowRef="false">
            <label title="工具介绍" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="工具介绍" field="ToolDescription" allowEdit="false" customizeReferenceable="false" id="cf6722bb-1126-45fb-99c3-af2ded57999b" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="36c5a391-e949-40ec-b46a-01775d87d08d" field="ToolDescription" errorMode="default" readonlyMode="all" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="LastTestOutput" scope="08ddcb50-cc9d-418e-8bea-38e823953d4d.a71ca692-0d10-4a83-b8d9-4b3da506ef82" defaultRefId="01d0e58f-c257-4679-9408-c0fb059ab66f" allowUnRef="false" allowRef="false">
            <label title="输出参数" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="最后一次测试的出参" field="LastTestOutput" allowEdit="false" customizeReferenceable="false" id="e4fd71d4-15ad-4973-bfd2-086c30f09c1b" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                <customProps />
                <textArea id="b25f45a7-7dc0-40ab-b497-3cd787a970b3" field="LastTestOutput" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="true" minRows="2" maxRows="10">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
        <component name="subGrid_MCP_TOOL_PARAMETER_toolGUID" scope="08ddcb50-cc9d-418e-8bea-38e823953d4d.a71ca692-0d10-4a83-b8d9-4b3da506ef82" defaultRefId="832556f2-fcdd-4977-adb8-25318fbe2031" allowUnRef="false" allowRef="false">
            <label title="参数列表" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="相关列表" field="subGrid_MCP_TOOL_PARAMETER_toolGUID" allowEdit="false" customizeReferenceable="false" id="73715715-ab5e-47ea-9600-a6a6d35d3e3d" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <subGrid id="15980627-5c5e-4d21-aa94-175c3797eb19" field="subGrid_MCP_TOOL_PARAMETER_toolGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" fullField="ToolGUID" height="300">
                    <customProps />
                    <events />
                    <grid field="MCP_TOOL_PARAMETER.toolGUID" metadataId="08ddcda2-651a-405d-81bb-44fdd234f877" />
                </subGrid>
            </column>
        </component>
    </components>
    <workflow enabled="false" />
</form>