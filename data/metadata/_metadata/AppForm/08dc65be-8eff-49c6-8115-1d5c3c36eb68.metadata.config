<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-04-26T07:00:29Z" modifiedBy="夏娜" modifiedOn="2024-04-28T11:31:07.4756441+08:00" metadataStatus="Product" formId="08dc65be-8eff-49c6-8115-1d5c3c36eb68" name="表单控件" isSeparatedLayout="false" entityId="00000000-0000-0000-0000-000000000000" functionPageId="00000000-0000-0000-0000-000000000000" isRevisedId="1" application="4200" htmlCache="default" componentGuid="08dc65be-8efd-4f4e-8617-efddfb7f697c" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="PrimaryKey" withNoLock="false" mode="3">
        <fields>
            <field name="assistantIcon" allowPopulate="false" field="assistantIcon" metadataStatus="Product">
                <fields />
            </field>
            <field name="assistantName" allowPopulate="false" field="assistantName" metadataStatus="Product">
                <fields />
            </field>
            <field name="greetingMessage" allowPopulate="false" field="greetingMessage" metadataStatus="Product">
                <fields />
            </field>
            <field name="modelSettings" allowPopulate="false" field="modelSettings" metadataStatus="Product">
                <fields />
            </field>
            <field name="prompt" allowPopulate="false" field="prompt" metadataStatus="Product">
                <fields />
            </field>
            <field name="selfIntroduction" allowPopulate="false" field="selfIntroduction" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields />
        <fixedSortings />
        <summaries />
        <diagrams />
        <customDataSource>
            <customFields>
                <customField name="PrimaryKey" displayName="主键" attributeType="guid" isRequired="true" isPrimaryKey="true" />
                <customField name="assistantName" displayName="助手名称" attributeType="string" isRequired="true" isPrimaryKey="false" />
                <customField name="selfIntroduction" displayName="自我介绍" attributeType="string" isRequired="false" isPrimaryKey="false" />
                <customField name="greetingMessage" displayName="引导语" attributeType="string" isRequired="false" isPrimaryKey="false" />
                <customField name="assistantIcon" displayName="助手图标" attributeType="图片" isRequired="false" isPrimaryKey="false" />
                <customField name="modelSettings" displayName="模型设置" attributeType="guid" isRequired="false" isPrimaryKey="false" />
                <customField name="prompt" displayName="提示词" attributeType="string" isRequired="false" isPrimaryKey="false" />
            </customFields>
        </customDataSource>
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dc65be-8eff-48a6-8059-61a1ee270eb7" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dc65be-8eff-48b8-8f52-7fc8a0f9ec3d" title="基础信息" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="e61b77d4-5b07-4c7a-9397-39eda5f96e9f" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="助手名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="助手名称" field="assistantName" allowEdit="false" customizeReferenceable="false" id="cfdeff23-bda7-4d08-984d-c0899c0dc36a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textBox id="8801af46-b18f-44bd-9161-f33f5cd64f51" field="assistantName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="07c89415-70cf-45ca-85f7-5de420f155d0" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="自我介绍" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="自我介绍" field="selfIntroduction" allowEdit="false" customizeReferenceable="false" id="aa50d070-b9d6-4996-8779-937ea13bb5ef" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textArea id="baa8b9a1-9d4c-4d21-8330-9eb97012ddc5" field="selfIntroduction" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="bf986a44-73f2-44dc-b992-910d60079e3e" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="引导语" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="引导语" field="greetingMessage" allowEdit="false" customizeReferenceable="false" id="56a80207-7369-40fc-a4e8-ad4bd5c83788" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textArea id="ae13cff0-1606-469f-9a97-d6f49a635d86" field="greetingMessage" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="ad0204cd-93e9-4d21-9785-671f336e24a7" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="助手图标" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="助手图标" field="assistantIcon" allowEdit="false" customizeReferenceable="false" id="9b72191b-55d6-4e3c-8182-8bb5608687c6" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <imageUpload id="8e3af3a5-5703-49c1-bc6c-41b08c539adf" field="assistantIcon" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" limitSize="5" fileLimitSize="-1" limitType="*.jpg;*.jpeg;*.bmp;*.gif;*.png" showUploadButton="true" enableCheck="false" checkMode="0">
                                                <customProps />
                                                <events />
                                                <rightsAddRule />
                                                <rightsDelRule />
                                            </imageUpload>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="8d3c8b29-4811-4ced-b6c7-641f3447080d" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="模型设置" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="模型设置" field="modelSettings" allowEdit="false" customizeReferenceable="false" id="d8b77315-a759-4838-88ab-6eefe41c5be8" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <comboBox id="b9a51587-40eb-47b4-8f32-c0ec8bed93d6" field="modelSettings" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                                <customProps />
                                                <events />
                                                <options />
                                                <optionsDataSource>
                                                    <dataSource keyName="InstanceGUID" entity="gpt_ModelInstance" withNoLock="true" mode="1">
                                                        <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AsgewCbgJIDsBnaAQ30hADoBBRASwGkQBPWEw2WxlgGgFgAUElSYcYAsTIVKAERCIwGZgFsQ+aADkSqthzkKlq9VtX8hydNjxFS5KnMKQATnQBGIXbAfO3IM8MsxCVtpAFF8LEQMOnVPcMjo9X8LUWtJO0pgqRAAYStPLLs8nGSRK3EbbMzKuxMPdlhCijrSwLSQqlSwAHEAVVwZTy6+gdaupqocpxASaBAsEcGGqZm5hf6ZMfKJyhXZ+brPPbWWwQDxmulj+YAVOh1l6f2sO9MzlO3LqgnFgq/FrZBL6UUR0ABmdHmvwaoIhUI2gPaVVhkKwhxh2HBqNO5jKQPS0hRt3u9Q4RJeJNgAgAYk4MMoEB98R1OBxzp8CR4qQB1AAWIGmsAAFABGAC8IoAlEA]]></command>
                                                        <fields>
                                                            <field name="InstanceName" allowPopulate="false" entity="gpt_ModelInstance" field="InstanceName" entityAlias="gpt_ModelInstance" metadataStatus="Product" />
                                                            <field name="InstanceGUID" allowPopulate="false" entity="gpt_ModelInstance" field="InstanceGUID" entityAlias="gpt_ModelInstance" metadataStatus="Product" />
                                                        </fields>
                                                        <availableFields>
                                                            <availableField name="ApiKey" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ApiKey" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="DeploymentName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DeploymentName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="Endpoint" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Endpoint" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
                                                            <availableField name="InstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceCode" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="InstanceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModelGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="InstanceGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InstanceGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_ModelInstance" entityType="0" attributeType="时间戳" />
                                                        </availableFields>
                                                        <fixedSortings />
                                                        <summaries />
                                                        <diagrams>
                                                            <diagram xmlAttributeId="08dc65bf-52f4-469b-8f1a-fb688b0402d1" id="08dc650d-2c1e-4b7c-8060-68c0fb5aa43d" name="gpt_ModelInstance" primaryField="InstanceGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                                                <conditions />
                                                                <resourceFilters />
                                                                <projectInterfaceFilters />
                                                            </diagram>
                                                        </diagrams>
                                                    </dataSource>
                                                    <extendFields>
                                                        <extendField displayName="文本" name="InstanceName" entity="gpt_ModelInstance" isQueryField="false" mapType="Text" />
                                                        <extendField displayName="值" name="InstanceGUID" entity="gpt_ModelInstance" isQueryField="false" mapType="Value" />
                                                    </extendFields>
                                                    <sortFields />
                                                </optionsDataSource>
                                            </comboBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                    <group id="3dbf184b-f329-40f0-b576-cded3798ae84" title="提示词" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="a814e630-5490-4152-bb5c-aafdb52c6d29" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="提示词" visible="true" titleShowStyle="hideArea" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="提示词" field="prompt" allowEdit="false" customizeReferenceable="false" id="bd374943-7dc4-4ca2-9e28-f41f75e42dc3" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product">
                                            <customProps />
                                            <textArea id="aa7760b5-3b8a-4ed5-a78e-312a6a98b9ea" field="prompt" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" placeholder="请输入系统提示词，用于定义助手的人设与限制，用户不可见" isHidden="false" metadataStatus="Product" height="240" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dc65be-8eff-49ea-8658-41fece6bcc71" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <components />
</form>