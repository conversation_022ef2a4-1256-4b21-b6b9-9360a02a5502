DELETE FROM gpt_assistant WHERE AssistantGUID='7a3f4310-125c-11ef-83d7-00155d822d63';
INSERT INTO gpt_assistant (AssistantCode, AssistantName, AssistantType, `Describe`, Icon, IsSystem, ModelInstanceCode, ModelInstanceGUID, ModelInstanceName, PromptCode, PromptContext, PromptGUID, SelfIntroduction, AssistantGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, AccessUrl, IntroductoryPhrase, SkillConfig, StandalonePage, SpaceGUID) VALUES ('system_assistant', 'AI智能助手', 0, 'AI智能助手', '', 1, null, null, null, null, null, null, 'AI智能助手', '7a3f4310-125c-11ef-83d7-00155d822d63', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-06-04 18:00:38', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-06-04 18:00:38', '2024-06-13 10:09:24', '/gptbuilder/assistant/index.html?id=7a3f4310-125c-11ef-83d7-00155d822d63', '你好，我是你的助手', 1, 1, '08dca713-3d6e-fbce-835e-6d2d2154296c');

-- 默认助手 - 【系统】智能检查
DELETE FROM gpt_assistant WHERE AssistantGUID='01bb58c9-8138-4f70-be1e-e5ee3d2ac669';
INSERT INTO gpt_assistant (AssistantGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, AssistantName, AssistantCode, IsSystem, `Describe`, SelfIntroduction, AssistantType, PromptGUID, ModelInstanceGUID, ModelInstanceCode, ModelInstanceName, PromptCode, PromptContext, IntroductoryPhrase, Icon, SkillConfig, StandalonePage, AccessUrl, OpenCheck, SpaceGUID, InitEvent, Visible) VALUES('01bb58c9-8138-4f70-be1e-e5ee3d2ac669', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:52:41', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 18:03:49', '2024-11-25 18:03:49', 'AI智能助手', 'system_plan', 0, '天际助手-智能检查', '', 1, NULL, NULL, NULL, NULL, NULL, NULL, '', '', 0, 1, '/gptbuilder/assistant/index.html?id=01bb58c9-8138-4f70-be1e-e5ee3d2ac669', 0, '08dca713-3d6e-fbce-835e-6d2d2154296c', '', 0);

DELETE FROM gpt_assistantskill WHERE AssistantGUID='01bb58c9-8138-4f70-be1e-e5ee3d2ac669';
INSERT INTO gpt_assistantskill (AssistantSkillGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, AssistantGUID, SkillGUID) VALUES('08dd0d38-7559-fa38-8342-236075c8fbfb', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 18:03:49', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 18:03:49', '2024-11-25 18:03:49', '01bb58c9-8138-4f70-be1e-e5ee3d2ac669', '23142564-1cf8-4f64-ab98-e49d33a156df');

-- 默认发布助手 - 【系统】智能检查
DELETE FROM gpt_application WHERE ApplicationGUID='08dd0d2e-f1b7-4ae9-8d79-0a96175787f6';
INSERT INTO gpt_application (ApplicationGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, ApplicationName, ApplicationCode, AssistantGUIDs, Description, Icon, SpaceGUID, EnableUserAuthorization) VALUES('08dd0d2e-f1b7-4ae9-8d79-0a96175787f6', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:55:43', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 17:55:51', '2024-11-25 17:55:51', '【系统】智能检查', 'system_plan', '01bb58c9-8138-4f70-be1e-e5ee3d2ac669', '', NULL, '08dca713-3d6e-fbce-835e-6d2d2154296c', 0);

DELETE FROM gpt_applicationrelease WHERE ApplicationGUID='08dd0d2e-f1b7-4ae9-8d79-0a96175787f6';
INSERT INTO gpt_applicationrelease (ApplicationReleaseGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, ReleaseName, ReleaseTypeEnum, ReleaseCode, ShowHistory, `Usage`, ApplicationGUID) VALUES('08dd0d2e-c20b-4317-8758-59200025617c', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:55:43', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:55:43', '2024-11-25 16:55:43', '流程侧边栏', 3, 'lg6a28kpNY4iBosHGFsGGcYmZrhdbg82', 0, '<script  src="{{baseUrl}}/gptbuilder/assistant/index.js"  id="skyline-gpt"  data-gpt-base-url="{{baseUrl}}"  data-gpt-share="gptn0defc6v6xfxdpi+lg6a28kpNY4iBosHGFsGGcYmZrhdbg82+{{baseInfo}}"  data-gpt-history="0"></script>', '08dd0d2e-f1b7-4ae9-8d79-0a96175787f6');

DELETE FROM gpt_applicationsite WHERE ApplicationGUID='08dd0d2e-f1b7-4ae9-8d79-0a96175787f6';
INSERT INTO gpt_applicationsite (ApplicationSiteGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, SiteName, SiteDomainName, ApplicationGUID) VALUES('08dd0d30-c4a0-47ea-8ca2-632c74ec2192', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 17:09:00', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 17:12:28', '2024-11-25 17:12:27', '所有站点', '*', '08dd0d2e-f1b7-4ae9-8d79-0a96175787f6');

DELETE FROM gpt_applicationsecurity WHERE ApplicationGUID='08dd0d2e-f1b7-4ae9-8d79-0a96175787f6';
INSERT INTO gpt_applicationsecurity (ApplicationSecurityGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, AppSecret, AppSecretName, ApplicationGUID, UserCheckUrl) VALUES('08dd0d2e-f1c3-4316-894b-0802566f6655', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:55:43', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 17:55:51', '2024-11-25 17:55:51', 'GIavaB4AAp9U9KSsXobqmwkpdyneyAFj', NULL, '08dd0d2e-f1b7-4ae9-8d79-0a96175787f6', '');

-- 默认助手 - AI智能客服
DELETE FROM gpt_assistant WHERE AssistantGUID='562bf3c9-cd73-11ef-b079-00155d822d63';
INSERT INTO gpt_assistant (AccessUrl, AssistantCode, AssistantName, AssistantType, `Describe`, Icon, InitEvent, IntroductoryPhrase, IsSystem, ModelInstanceCode, ModelInstanceGUID, ModelInstanceName, OpenCheck, PromptCode, PromptContext, PromptGUID, RecommendedSkill, SelfIntroduction, SkillConfig, SpaceGUID, StandalonePage, AssistantGUID, CreatedGUID, CreatedName, CreatedTime, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, RemoteAssistantConfigData, Status, Type, Visible) VALUES ('', 'system_kefu', 'AI智能客服', null, 'AI智能客服', '', '', 'AI智能客服', 0, null, null, null, 0, null, null, null, '', '', 0, '08dca713-3d6e-fbce-835e-6d2d2154296c', 1, '562bf3c9-cd73-11ef-b079-00155d822d63', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-11-25 16:52:41', '08dd2df1-a5d2-4c56-8c34-89a339741bd2', '林一一', '2025-01-22 15:27:35', '2025-01-22 15:33:30', '{"publishCode":"gptvii04uqqiqh961u","releaseCode":"w9nsEmfB5x6UWuIMgvfRKhOyQKcAkh3m","host":"https://ai.tj.mycyjg.com","tenantCode":"mycyjg"}', 1, '1', 1);
