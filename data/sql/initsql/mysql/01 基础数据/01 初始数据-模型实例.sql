
-- 通义千问 text-embedding-v2模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT '', '', '', '', 'https://dashscope.aliyuncs.com', 'default_text_embedding', '默认嵌入模型实例', 1, '5ec77603-3452-11ef-83d7-00155d822d63', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 20:34:08', '08dd2417-42ef-f9fe-fa6e-040a52c30cf8', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 20:34:11', '2024-12-24 20:34:11', 0, 0, null, ''
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd2417-42ef-f9fe-fa6e-040a52c30cf8'
    );

-- 通义千问 long模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, null, '', '通义千问 long', 'https://dashscope.aliyuncs.com/', 'default_doc_model', '默认文档模型实例', 1, '6be95155-3458-45c0-8a19-44e46fc14a3b', '', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-12-24 14:53:29', '08dcfef8-e2e5-42c6-845a-8ce67c3966f2', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-12-24 14:53:29', '2024-12-24 14:53:29', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dcfef8-e2e5-42c6-845a-8ce67c3966f2'
    );

-- 通义千问 plus模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, '', '', '通义千问 plus', 'https://dashscope.aliyuncs.com', 'default_text_generation', '默认文本生成模型实例', 1, '354d4bb5-22e8-11ef-83d7-00155d822d63', '', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-12-24 14:53:29', '08dd0f4c-e9b1-4681-844d-b1fdaeaafb2e', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 14:53:29', '2024-12-24 14:53:29', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd0f4c-e9b1-4681-844d-b1fdaeaafb2e'
    );

-- 通义千问 max模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, null, '', '通义千问 max', 'https://dashscope.aliyuncs.com/', 'text_generation_large', '大参数文本生成模型实例', 0, '4db4ca0f-22e8-11ef-83d7-00155d822d63', null, '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-12-24 14:53:29', '08dc89f1-f3ea-43f5-fa2e-cdb5896f96ea', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 14:53:29', '2024-12-24 14:53:29', 0, 10, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dc89f1-f3ea-43f5-fa2e-cdb5896f96ea'
    );

-- 通义千问多模态 vl-plus模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, '', 'qwen-vl-plus', '通义千问多模态 vl-plus', 'https://dashscope.aliyuncs.com', 'default_img_model', '默认图片识别多模态模型实例', 1, '6cf1ca0f-22e8-11ef-83d7-00155d822d63', '', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2024-12-24 14:53:29', '08dcc03b-ee49-fb7e-fea6-90243ac591c9', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 14:53:29', '2024-12-24 14:53:29', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dcc03b-ee49-fb7e-fea6-90243ac591c9'
    );

-- 通义千问多模态 vl-max模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, '', '', '通义千问多模态 vl-max', 'https://dashscope.aliyuncs.com', 'img_model_large', '大参数图片识别多模态模型实例', 0, '7bd2ca0f-22e8-11ef-83d7-00155d822d63', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 18:58:40', '08dd2409-ec9e-fe9b-fe2c-b74d199be3ca', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 18:58:41', '2024-12-24 18:58:41', 0, 0, null, ''
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd2409-ec9e-fe9b-fe2c-b74d199be3ca'
    );

-- 阿里 通用手写体模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, null, '', '', 'ocr-api.cn-hangzhou.aliyuncs.com', 'ocr_recognize_handwriting', 'OCR通用手写体服务实例', 1, '22c56393-6f1e-11ef-a63c-00155d822d63', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-11-06 11:11:00', '08dcfe10-a3cc-fcab-f85b-5f975bcfa160', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-09 18:11:55', '2024-12-09 18:11:55', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dcfe10-a3cc-fcab-f85b-5f975bcfa160'
    );

-- 阿里 增值税发票识别模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, null, '', '阿里 增值税发票识别', 'ocr-api.cn-hangzhou.aliyuncs.com', 'ocr_recognize_invoice', 'OCR增值税发票服务实例', 0, '22c56393-6f1e-11ef-a63c-00155d822d64', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-06 10:25:58', '08dd159d-51cf-fa35-865b-90af5f0063e6', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-06 16:24:16', '2024-12-06 16:24:16', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd159d-51cf-fa35-865b-90af5f0063e6'
    );

-- 阿里 全文识别高精版模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT null, '', '', '', 'ocr-api.cn-hangzhou.aliyuncs.com', 'ocr_recognize_advanced', 'OCR全文识别高精版服务实例', 0, '8ad089b9-6f20-11ef-a63c-00155d822d63', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-11-06 11:12:43', '08dcfe10-e11d-ff74-fed1-bee709d051e6', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-09 18:11:21', '2024-12-09 18:11:21', 0, 0, null, null
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dcfe10-e11d-ff74-fed1-bee709d051e6'
    );


-- 阿里 安全检查模型
INSERT INTO gpt_modelinstance (ApiKey, ClientID, DeploymentName, `Describe`, Endpoint, InstanceCode, InstanceName, IsDefault, ModelGUID, Vendor, CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, IsAvailable, MaxConcurrencyQuantity, ModelName, StrategyId)
SELECT '', '', '', '', 'https://green-cip.cn-shanghai.aliyuncs.com', 'text_content_review', '文本安全检查服务实例', 0, 'a5014c80-beb1-11ef-b079-00155d822d63', '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 19:06:34', '08dd240b-073a-4052-81de-6538a548c5bb', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-12-24 19:06:35', '2024-12-24 19:07:50', 0, 0, null, ''
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd240b-073a-4052-81de-6538a548c5bb'
    );

-- OpenAI deepseek-r1
INSERT INTO gpt_modelinstance (CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, ModelGUID, `Describe`, DeploymentName, Endpoint, ApiKey, InstanceName, InstanceCode, IsDefault, ClientID, Vendor, MaxConcurrencyQuantity, IsAvailable, ModelName, StrategyId, EnableCustomModel, CustomModelCode)
SELECT '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2025-02-08 18:04:38', '08dd4827-ff0d-4002-fca9-c30070606b60', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2025-02-10 16:44:21', '2025-02-11 17:49:54', '0d814593-e45f-11ef-b079-00155d822d63', '', '', 'https://dashscope.aliyuncs.com/compatible-mode/v1', '', '阿里云百炼DeepSeek R1', 'aliyun_deepseek_r1', 0, '', '', 0, 0, NULL, '', 1, 'deepseek-r1'
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd4827-ff0d-4002-fca9-c30070606b60'
    );

-- OpenAI deepseek-v3
INSERT INTO gpt_modelinstance (CreatedGUID, CreatedName, CreatedTime, InstanceGUID, ModifiedGUID, ModifiedName, ModifiedTime, VersionNumber, ModelGUID, `Describe`, DeploymentName, Endpoint, ApiKey, InstanceName, InstanceCode, IsDefault, ClientID, Vendor, MaxConcurrencyQuantity, IsAvailable, ModelName, StrategyId, EnableCustomModel, CustomModelCode)
SELECT '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2025-02-11 10:23:54', '08dd4a43-2142-40fb-fcfd-c6437e698cc4', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2025-02-11 10:23:58', '2025-02-11 17:49:54', '0d814593-e45f-11ef-b079-00155d822d63', '', '', 'https://dashscope.aliyuncs.com/compatible-mode/v1', '', '阿里云百炼DeepSeek V3', 'aliyun_deepseek_v3', 0, '', '', 0, 0, NULL, '', 1, 'deepseek-v3'
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '08dd4a43-2142-40fb-fcfd-c6437e698cc4'
    );

-- 阿里 多模态vl-ocr模型
INSERT INTO `gpt_modelinstance`(`ApiKey`, `ClientID`, `CustomModelCode`, `DeploymentName`, `Describe`, `EnableCustomModel`, `Endpoint`, `InstanceCode`, `InstanceName`, `IsAvailable`, `IsDefault`, `IsSupportTool`, `MaxConcurrencyQuantity`, `ModelGUID`, `ModelName`, `StrategyId`, `Vendor`, `CreatedGUID`, `CreatedName`, `CreatedTime`, `InstanceGUID`, `ModifiedGUID`, `ModifiedName`, `ModifiedTime`, `VersionNumber`)
SELECT '', '', 'qwen-vl-ocr', '', '', 1, 'https://dashscope.aliyuncs.com', 'ocr_recognize_vl', 'OCR多模态服务实例', 0, 0, 0, 0, '3a1a03b7-80f0-7c76-b94f-7cc164564856', NULL, NULL, '', '08dce9cd-5710-4f7d-8f03-b7a406a2529e', '系统管理员', '2024-11-06 11:12:43', '3a19feb7-e22b-825f-db63-41ec6312fd9c', '4230bc6e-69e6-46a9-a39e-b929a06a84e8', '系统管理员', '2025-05-22 18:30:05', '2025-05-22 18:30:12'
FROM dual
WHERE NOT EXISTS (
        SELECT 1
        FROM gpt_modelinstance
        WHERE InstanceGUID = '3a19feb7-e22b-825f-db63-41ec6312fd9c'
    );