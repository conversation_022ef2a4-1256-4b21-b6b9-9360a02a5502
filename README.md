# java产品模板仓库

## 目录结构
```
ci                     ci(环境部署)目录
    dev
        local             本地开发环境脚手架        
        server            服务器环境脚手架        
    resourcepackage
        dog           安装使用狗相关资源
        modelingplatform 安装建模平台相关资源
        mydoc         安装文档服务相关资源
        mysql         安装mysql相关资源
        nacos         安装nacos相关资源
        rabbitmq      安装rabbitmq相关资源
        redis         安装redis相关资源
        tenantservice 安装建模租户服务相关资源
        xxljob        安装xxljob相关资源         
data            数据目录
    metadata(元数据)
        _metadata(产品元数据)
        customize（二开元数据）
    mdc(数据模板)
    report(报表模板)
    sql(sql脚本)
        dm(达梦脚本)
            initsql(初始化脚本)
            updatesql(升级脚本)
        mysql(mysql脚本)
            initsql(初始化脚本)
            updatesql(升级脚本)
    statis(静态资源文件)
    task(调度任务)
    workflow()
src(产品代码目录)
│  ├─gptbuilder-dependencies # GPT平台版本依赖（GPT平台所有模块依赖版本集中维护位置）
│  ├─gptbuilder-rmi-starter # 售楼rmi启动模块（包含所有GPT平台对外开放的pub接口、model定义）
│  ├─gptbuilder-starter # GPT平台启动
│  │  ├─gptbuilder-common # 公共模块
│  │  │  ├─gptbuilder-common-AppServiceBase # AppService基类，封装调用引擎能力
│  │  ├─gptbuilder-common-model
│  │  ├─gptbuilder-common-util # 工具方法类库
│  │  │  ├─gptbuilder-common-util-HttpUtil # OKHttp工具类
│  ├─gptbuilder-container # java建模平台启动的入口
│  │  ├─gptbuilderApplication # spring boot
│  │  ├─bootstrap.yml # 配置文件
│  ├─gptbuilder-agent # GPT平台核心模块：提示词、模型、技能、知识库管理
│  │  ├─gptbuilder-agent-interfaces # GPT平台核心模块接口模块
│  │  ├─gptbuilder-agent-model # GPT平台核心模块model。定义dto、const、enum。模块内外均可使用
│  │  ├─gptbuilder-agent-rmi # GPT平台核心模块远程接口SDK
│  │  ├─gptbuilder-agent-service # GPT平台核心模块管理服务
│  │  │  ├─gptbuilder-agent-service-AssistantService # 助手管理
│  │  │  │  ├─gptbuilder-agent-service-AssistantService-StreamingChatCompletion # 流式对话完成
│  │  │  │  ├─gptbuilder-agent-service-AssistantService-ChatCompletion # 对话完成
│  │  │  ├─gptbuilder-agent-service-SkillService # 技能管理
│  │  │  ├─gptbuilder-agent-service-PluginService # 插件管理
│  │  │  ├─gptbuilder-agent-service-KnowledgeService # 知识库管理
│  │  │  ├─gptbuilder-agent-service-ModelInstanceService # 模型实例管理
│  │  └─pom.xml # 工程文件
│  │  ├─gptbuilder-agent.iml # 项目配置文件。构建时自动生成。无需提交到代码仓库
│  ├─gptbuilder-plugin # GPT平台业务插件
│  │  ├─gptbuilder-plugin-interfaces # GPT平台业务插件模块
│  │  ├─gptbuilder-plugin-model # GPT平台业务插件model。定义dto、const、enum。模块内外均可使用
│  │  ├─gptbuilder-plugin-rmi # GPT平台业务插件远程接口SDK
│  │  ├─gptbuilder-plugin-service # GPT平台业务插件服务
│  │  └─pom.xml # 工程文件
│  │  ├─gptbuilder-plugin.iml # 项目配置文件。构建时自动生成。无需提交到代码仓库

Application启动
│  │  ├─src/main/resource # 资源文件
│  │  │  ├─assembly
│  │  │  ├─assembly.xml # assembly打包插件配置
│  │  │  ├─bootstrap.yml # 配置文件
│  │  ├─pom.xml # mvn配置，需要在里面添加二开模块的依赖
│  │─gptbuilder-common # 项目公共类库 
│  ├─gptbuilder-agent # 项目核心，技能、模型管理
│  ├─gptbuilder-plugin # 插件能力
│  │─pom.xml # 二开mvn配置，需要引入产品的依赖
```

## 快速搭建本地开发环境
### 本地环境准备
参考文档：
https://docs.mingyuanyun.com/pages/viewpage.action?pageId=153229325

只需要看 "本地准备"章节


### 本机hosts修改


    {本机IP}  替换成本地的ip地址，不能是127.0.0.1    

    ************* dbhost
    ************* nacos
    ************* xxljobadmin
    ************* javaschedule
    ************* tenantservice
    ************* mydoc
    ************* moql
    
    {本机IP} modelingplatform
    {本机IP} javaservice
    {本机IP} jcsj
    {本机IP} gateway
    {本机IP} redis
    {本机IP} rabbitmq

### 本地基础服务启动
1.修改开发狗文件：

    文件路径：ci/local/config.evn
    参数：dogPath = {开发狗文件所在路径}
    例如：dogPath="/Users/<USER>/workspace/bin/mysoft"
    
2.拉取本地基础服务镜像，并启动镜像：

    # Windows 系统
    cd ci/local
    01-start-platform.cmd(拉取，启动镜像)

    # MacOS 系统
    cd ci/local
    sh 01-start-platform.sh(拉取，启动镜像)

3.启动java服务

参考文档：
https://docs.mingyuanyun.com/pages/viewpage.action?pageId=153229325

只需要看 "启动 Java 服务"章节

4.登录

    登录地址:http://localhost:9300
    企业编码：gpt
    账号：admin
    密码： Mysoft@95938


5.启动GPT引擎

    修改ci/local/docker-compose-gptengine.yml文件，找到local_gptengine，
    修改经镜像版本为最新版本，  

    重启运行01-start-gptengine.cmd(或者是01-start-gptengine.sh)文件




## 开发目录书面

#### container
启动工程包括项目启动的相关yml配置，及assembly打包工具，与一般工程不同的是这个模块中不含Springboot启动类，启动类由平台进行托管，在模块的pom 文件中可以配置我们选择启动的业务单元，所以这个模块也称为 “启动容器”


## 业务单元代码规范
业务单元下分包规则：分层结构参考阿里巴巴Java开发手册进行3层结构

#### controller
接口层，用来接收接口请求包括2个方面  
1）建模界面接口：用来和建模界面交互（可以参考建模api文档进行开发）  
2）开放接口：用来给其他服务或第三方提供的接口  
具体规则
1、controller不要包含业务逻辑代码，只能是参数的校验处理，业务逻辑通过调用service代码层方式实现  
2、controller接口一定要继承平台的基础Controller类。

#### service
业务逻辑层，相对具体的业务逻辑服务层

该包下严格按照定制接口和实现的方式进行开发,具体的包结构包括dto包、impl实现包、接口类

#### dao
数据访问层，与堵车数据库通讯

该包下包含entity包和对应的Dao，entity包是实体包  
实体定义规则：通过建模建立的实体，必须继承BaseEntity，自己定义的表不用继承相关的基类  
dao访问类 不要定义 xxxMapper  定义成 xxxDao继承BaseMapper，假如有相关批量插入或更新的场景继承平台提供的扩展类ExtensionBaseMapper

#### 其他功能组件包
按照相应功能建立相应的功能特性包，和controller、dao、service包平级。如下：

backgroundjob: 后台作业功能包
event: 业务事件功能包，该包下分事件发布者包（publisher）、订阅者包（subscriber）
remote: 远程调用的接口包， 包含相关接口实体
task: 调度任务的包
mip: 集成平台接口包

