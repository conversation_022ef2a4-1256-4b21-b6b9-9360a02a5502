<?xml version="1.0" encoding="utf-8"?>
<businessObjectApi xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" businessObjectApiId="08dd360a-aa81-4e81-8f1c-053d3136206e" metadataversion="6" metadataStatus="Product" application="4200" functionGUID="00000000-0000-0000-0000-000000000000" name="rulegroup" displayName="规则分组" remark="" apiType="GET" entityId="08dcd95a-5b0e-40c8-8dfb-a5ac6c705930" createdBy="李昂" CreatedOn="2025-01-16T16:49:19.4723296+08:00" modifiedBy="李昂" modifiedOn="2025-01-17T11:14:44.6504556+08:00" isPublic="true">
    <dataSource keyName="RuleGroupGUID" entity="gpt_planRuleGroup" withNoLock="true" mode="1">
        <command type="sql" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9RYCGA7ASgVwgHEAnAez0QDoAFTLQgVQEkARWDAZ1lu0dYBoAsACgkqdNnxEyFSlJAlyiAHIYAtiHZd5iiqo1DRyNHR0yqwUsTidYl64bEnJBBecoBhYiAzQQAEz42Wy8fP0DmFkdjCVxXXSpQ3wD9TRDvZP9U6PFTePck8IAVAEsNLVhCgNKDESdYsyVKAFlSfxKAMxKAoIrW9q6eyJznOOkm/s7urPU0rknBmdqjXJdx2QXpmrnYTeqykBGG/KbGil7bM8Re4QAxMjUEGLz1xFgAQS56l7clWGEAOoACxA3ieqzGv1kVyCAF4AAKkEr+IA==]]></command>
        <fields />
        <availableFields />
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dd360a-aa83-4ca1-89b1-13267e110bb6" id="08dcd95a-5b0e-40c8-8dfb-a5ac6c705930" name="gpt_planRuleGroup" primaryField="RuleGroupGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" logicFormula="" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions>
                    <condition field="gpt_planRuleGroup.RuleGroupGUID" operatorType="eq" id="4bbac95a-6d33-43e7-a811-5f6e738b03e5" dataType="string" valueType="1" leftValueType="field">
                        <actions />
                        <Value xsi:type="xsd:string">[query:id]</Value>
                    </condition>
                </conditions>
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <fieldSets>
        <field metadataStatus="Product" field="PlanGUID" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.PlanGUID" alias="PlanGUID" entityAlias="gpt_planRuleGroup" type="guid" />
        <field metadataStatus="Product" field="RuleGroupName" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.RuleGroupName" alias="RuleGroupName" entityAlias="gpt_planRuleGroup" type="string" />
        <field metadataStatus="Product" field="Sort" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.Sort" alias="Sort" entityAlias="gpt_planRuleGroup" type="int32" />
        <field metadataStatus="Product" field="CreatedGUID" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.CreatedGUID" alias="CreatedGUID" entityAlias="gpt_planRuleGroup" type="guid" />
        <field metadataStatus="Product" field="CreatedName" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.CreatedName" alias="CreatedName" entityAlias="gpt_planRuleGroup" type="string" />
        <field metadataStatus="Product" field="CreatedTime" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.CreatedTime" alias="CreatedTime" entityAlias="gpt_planRuleGroup" type="datetime" />
        <field metadataStatus="Product" field="ModifiedGUID" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.ModifiedGUID" alias="ModifiedGUID" entityAlias="gpt_planRuleGroup" type="guid" />
        <field metadataStatus="Product" field="ModifiedName" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.ModifiedName" alias="ModifiedName" entityAlias="gpt_planRuleGroup" type="string" />
        <field metadataStatus="Product" field="ModifiedTime" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.ModifiedTime" alias="ModifiedTime" entityAlias="gpt_planRuleGroup" type="datetime" />
        <field metadataStatus="Product" field="RuleGroupGUID" entityName="gpt_planRuleGroup" name="gpt_planRuleGroup.RuleGroupGUID" alias="RuleGroupGUID" entityAlias="gpt_planRuleGroup" type="guid" />
    </fieldSets>
    <parameters>
        <parameter name="id" displayName="参数ID" type="guid" required="true" in="path" defaultValue="" />
    </parameters>
    <pagination enabled="false" />
</businessObjectApi>