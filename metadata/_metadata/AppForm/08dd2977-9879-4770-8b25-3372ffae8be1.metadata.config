<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-12-31T08:46:19Z" modifiedBy="李昂" modifiedOn="2025-03-18T14:48:51.6491913+08:00" metadataStatus="Product" formId="08dd2977-9879-4770-8b25-3372ffae8be1" name="结果评测任务表表单控件" isSeparatedLayout="true" entityId="08dd2658-fb21-430e-8745-6d2d6d5ef5c4" functionPageId="08dd2977-9878-4e1b-8571-68497a23cfba" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="EvalTaskGUID" entity="gpt_EvalTask" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_EvalTask.Attachment as Attachment,
gpt_EvalTask.DatasetDocument as DatasetDocument,
gpt_EvalTask.Document as Document,
gpt_EvalTask.EvalMode as EvalMode,
gpt_EvalTask.EvalObject as EvalObject,
gpt_EvalTask.EvalObjectType as EvalObjectType,
gpt_EvalTask.EvalRule as EvalRule,
gpt_EvalTask.LastRecordGUID as LastRecordGUID,
gpt_EvalTask.ModelInstanceCode as ModelInstanceCode,
gpt_EvalTask.SpaceGUID as SpaceGUID,
gpt_EvalTask.Status as Status,
gpt_EvalTask.TaskName as TaskName,
gpt_EvalTask.CreatedGUID as CreatedGUID,
gpt_EvalTask.CreatedName as CreatedName,
gpt_EvalTask.CreatedTime as CreatedTime,
gpt_EvalTask.EvalTaskGUID as EvalTaskGUID,
gpt_EvalTask.ModifiedGUID as ModifiedGUID,
gpt_EvalTask.ModifiedName as ModifiedName,
gpt_EvalTask.ModifiedTime as ModifiedTime 
From gpt_EvalTask As gpt_EvalTask 
Where gpt_EvalTask.EvalTaskGUID=@oid]]></command>
        <fields>
            <field name="Document" allowPopulate="true" entity="gpt_EvalTask" field="Document" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="EvalMode" allowPopulate="true" entity="gpt_EvalTask" field="EvalMode" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="EvalObject" allowPopulate="true" entity="gpt_EvalTask" field="EvalObject" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="EvalObjectType" allowPopulate="true" entity="gpt_EvalTask" field="EvalObjectType" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="EvalRule" allowPopulate="true" entity="gpt_EvalTask" field="EvalRule" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="EvalTaskGUID" allowPopulate="true" entity="gpt_EvalTask" field="EvalTaskGUID" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="ModelInstanceCode" allowPopulate="true" entity="gpt_EvalTask" field="ModelInstanceCode" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" allowPopulate="true" entity="gpt_EvalTask" field="EvalTaskGUID" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
            <field name="TaskName" allowPopulate="true" entity="gpt_EvalTask" field="TaskName" entityAlias="gpt_EvalTask" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="Attachment" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Attachment" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_EvalTask" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_EvalTask" entityType="0" attributeType="日期与时间" />
            <availableField name="DatasetDocument" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="DatasetDocument" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="Document" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Document" entity="gpt_EvalTask" entityType="0" attributeType="附件" />
            <availableField name="EvalMode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalMode" entity="gpt_EvalTask" entityType="0" attributeType="整数" />
            <availableField name="EvalObject" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalObject" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="EvalObjectType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalObjectType" entity="gpt_EvalTask" entityType="0" attributeType="整数" />
            <availableField name="EvalRule" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalRule" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(max)）" />
            <availableField name="EvalTaskGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskGUID" entity="gpt_EvalTask" entityType="0" attributeType="Guid" />
            <availableField name="LastRecordGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="LastRecordGUID" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="ModelInstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModelInstanceCode" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_EvalTask" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_EvalTask" entityType="0" attributeType="日期与时间" />
            <availableField name="SpaceGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="SpaceGUID" entity="gpt_EvalTask" entityType="0" attributeType="Guid" />
            <availableField name="Status" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="Status" entity="gpt_EvalTask" entityType="0" attributeType="整数" />
            <availableField name="TaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskName" entity="gpt_EvalTask" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_EvalTask" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dd2977-9879-4b2e-82a6-771d4eda511d" id="08dd2658-fb21-430e-8745-6d2d6d5ef5c4" name="gpt_EvalTask" primaryField="EvalTaskGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dd2977-9878-4fb5-8fef-b5242d31311d" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dd2977-9878-4fed-8d70-eea077243e75" groupId="groupId1" title="新分组" disableStyle="true" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="23f8e931-cba6-44bb-b0c1-cf50e6d3ba4b" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="TaskName" />
                                    </cell>
                                    <cell id="08dd2977-9879-4044-8423-226008c90edc" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="9e3229c1-cb34-49f4-b2d1-807b30bf8c4b" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="EvalObjectType" />
                                    </cell>
                                    <cell id="d85bb165-5170-4332-ba21-a976f661ceef" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="EvalObject" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="d4917597-aa4e-4c67-ad09-628cc273e3a8" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="ModelInstanceCode" />
                                    </cell>
                                    <cell id="86b6ff90-2244-455c-af78-dec3c9ee5c7c" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="7a8061e2-4e6d-4e06-b810-cae34f30411c" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="Document" />
                                    </cell>
                                    <cell id="81308d17-008a-4070-b8f6-9a0aaf0c62d9" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="1246db7a-147a-4ee0-90a6-c7742e015045" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <component ref="EvalMode" />
                                    </cell>
                                    <cell id="9300336d-6c7d-432d-be91-9e2461aee26b" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="361b9540-eed7-4778-860c-cf4fdfcce920" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="EvalRule" />
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="ebe57910-5305-43cf-a34f-c788f865f60d" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <component ref="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" />
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dd2977-9879-47ac-8f95-c487148473d8" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="08dd2977-9879-40a7-83cb-d77093a68e3c" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mCancel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="30591ebc-4df5-4d45-a683-307d6cf9ddb2" title="下载评测集模版" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58225070659361" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58225070659361_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="8f7edc79-3c58-49ad-aca9-2b4801cd656e" title="执行任务" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58225070602428" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="ondialogclose" functionName="_appForm_button_58225070602428_dialogClose" enabled="true" metadataStatus="Product" />
                                    <event name="onbeforeopen" functionName="_appForm_button_58225070602428_beforeOpen" enabled="true" metadataStatus="Product" />
                                </events>
                                <behavior target="dialog" targetDisplayType="fixedWidth" type="page" url="/std/42000703/08dd297b-ea52-4234-8e55-d835ec20fbb6" id="08dd297b-ea52-4234-8e55-d835ec20fbb6" itemId="0ca99f82-8e01-4e96-b480-0612d8622518" metadataStatus="Product">
                                    <options>
                                        <option key="width" value="300" />
                                        <option key="height" value="200" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="text" key="mode" value="1" />
                                    </params>
                                    <events />
                                </behavior>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dd2977-9879-4077-872f-01df3490fd95" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appForm_load" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs>
                <config id="08dd2977-9879-40da-8848-d51867755589" title="默认规则" controlId="08dd2977-9879-4077-872f-01df3490fd95" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="08dd2977-9879-4112-8a78-44f310a4a5a9" ruleId="08dd2977-9879-413e-8b85-b97d89c82f7c" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="cc24e2d3-b4f7-4624-b184-901d7f527cfb" title="新规则" controlId="8f7edc79-3c58-49ad-aca9-2b4801cd656e" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58225070602428" metadataStatus="Product">
                    <handles>
                        <handle handleId="2857aa8a-a785-447d-9f7c-4372bf64f97a" ruleId="5d361f67-35d2-4d6c-866c-1e43384c37d2" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="ba7a3643-bcf6-4277-b3cf-72853e62e8c2" title="新规则" controlId="30591ebc-4df5-4d45-a683-307d6cf9ddb2" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58225070659361" metadataStatus="Product">
                    <handles>
                        <handle handleId="755bd314-dae6-46e7-a821-5acf48457924" ruleId="5d361f67-35d2-4d6c-866c-1e43384c37d2" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="33f848ae-95c7-45b5-a42c-daaeeb6457b4" title="新规则" controlId="ebe57910-5305-43cf-a34f-c788f865f60d" controlType="cell" controlSubType="" controlProp="isHidden" controlName="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" metadataStatus="Product">
                    <handles>
                        <handle handleId="50e11514-e543-408a-9700-7b3c09e50d31" ruleId="5d361f67-35d2-4d6c-866c-1e43384c37d2" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="*************-4816-8c38-bbd27ee91e8e" title="新规则" controlId="7a8061e2-4e6d-4e06-b810-cae34f30411c" controlType="cell" controlSubType="" controlProp="isHidden" controlName="Document" metadataStatus="Product">
                    <handles>
                        <handle handleId="5a2d2a45-4755-4404-829f-03b18362c984" ruleId="5d361f67-35d2-4d6c-866c-1e43384c37d2" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="7d75b607-cb7e-41c5-ba1e-c5e6a89b32fd" title="新规则" controlId="361b9540-eed7-4778-860c-cf4fdfcce920" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="EvalRule" metadataStatus="Product">
                    <handles>
                        <handle handleId="fcc49a5f-26c0-408e-ab95-bcfc1c1fb5c4" ruleId="5a4c746d-3a5a-4789-ad56-a824dc310d04" action="required" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="cc99dbd2-7163-4d30-bcac-fea7bff0d2ed" title="新规则" controlId="361b9540-eed7-4778-860c-cf4fdfcce920" controlType="cell" controlSubType="" controlProp="isHidden" controlName="EvalRule" metadataStatus="Product">
                    <handles>
                        <handle handleId="77609e5a-4f6e-401a-90ff-29ea4b488053" ruleId="5a4c746d-3a5a-4789-ad56-a824dc310d04" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dd2977-9879-413e-8b85-b97d89c82f7c" title="查看" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;urlParam&quot;,&quot;field&quot;:&quot;mode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
                <group id="5d361f67-35d2-4d6c-866c-1e43384c37d2" title="新增" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;urlParam&quot;,&quot;field&quot;:&quot;mode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;1&quot;}]}" metadataStatus="Product" />
                <group id="5a4c746d-3a5a-4789-ad56-a824dc310d04" title="评判模式-语义" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;EvalMode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules />
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="7c23f11b-6a5e-490e-94f4-dba4dd8af014" controlId="08dd2977-9879-4077-872f-01df3490fd95" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */
$form.resetChangeState()
const isVal = $form.validate()
var params = $page.getParams()
if (isVal) {

  return $api.evalTask.save({
    data: {
      "evalTaskGUID": params.oid,
      "document": $form.getData("Document"),
      "evalObject": $form.getData("EvalObject"),
      "evalObjectType": $form.getData("EvalObjectType"),
      "modelInstanceCode": $form.getData("ModelInstanceCode"),
      "spaceGUID": params.SpaceGUID,
      "taskName": $form.getData("TaskName"),
      "evalMode": $form.getData("EvalMode"),
      "evalRule": $form.getData("EvalRule")
    }
  }).then(function(res){
    $notify.success('保存成功！');
    if (!params.oid) {
      $page.redirect({mode:2, oid: res});
    }
  })
}]]></script>
        </code>
        <code id="26662310-d206-4d4b-80d0-35c6f98afd9d" controlId="08dd2977-9879-40a7-83cb-d77093a68e3c" controlType="ToolbarItem" controlAction="_appForm_mCancel_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

$page.redirect('/std/42000703/08dd2975-c65d-4437-86dd-7b8984f78c8d#')]]></script>
        </code>
        <code id="16187e22-f30f-4364-b989-1af202258bf7" controlId="08dd2977-9879-4770-8b25-3372ffae8be1" controlType="Form" controlAction="_appForm_load">
            <script><![CDATA[/**
 *  数据加载完成事件
 *  @example
 *  // 数据加载完后，将表单设置成查看模式
 *  $form.setMode(3);
 */
var params = $page.getParams()
var evalObjectType = $form.getData("EvalObjectType") || 1;
$api.evalTask.evalObjectList({data: {"evalObjectType":evalObjectType,"spaceGUID": params.SpaceGUID,}}).then(function(res){
    $form.setProp("EvalObject","data", res, false)
    $form.resetChangeState()
})

if (!$form.getData("EvalMode")) {
  $form.setData({"EvalMode": 0})
}


var conn = $util.notification();
conn.start();
conn.subscribe('EVAL_TASK_RECORD_DOQUERY',function(data){
  var oid = params.oid;
  if(oid == data.evalTaskGUID)
  {
    var grid = $form.getGrid("subGrid_gpt_EvalTaskRecord_EvalTaskGUID");
    grid.doQuery();
  }
})]]></script>
        </code>
        <code id="e455f313-4b20-4549-aad6-ba4efa29fd18" controlId="30591ebc-4df5-4d45-a683-307d6cf9ddb2" controlType="ToolbarItem" controlAction="_appForm_button_58225070659361_click">
            <script><![CDATA[/**
 *  @example
 *  列表场景
 *  1、操作栏按钮：
 *  $e.selecteds //为当前选中行的数据
 *  2、操作栏开关按钮：
 *  $e.checked //为当前开关选中状态
 *  3、行操作按钮
 *  $e.selecteds //为当前行的数据
 *  4、对话框按钮
 *  $e.selecteds //为当前选中行的数据
 *  
 *  表单场景
 *  $e.formData //为当前表单的数据
 *
 *  超链接
 *  $e.item //为点击链接备选项属性
 */

var evalObject = $form.getData("EvalObject");
var evalObjectType = $form.getData("EvalObjectType");
if (!evalObject || !evalObjectType) {
  $notify("请选择评测对象");
  return ;
}
$page.open("/api/42000703/evalTask/downloadDataset",
{"evalObjectType":evalObjectType,"evalObjectGUID": evalObject})]]></script>
        </code>
        <code id="d2b3a33a-f850-4c8b-b543-41f210356dc7" controlId="663a70b7-47ba-4c8f-838e-f01cf33862cc" controlType="CellControl" controlAction="_appForm_evalObjectType_valueChanged">
            <script><![CDATA[/**
 *  值改变后事件
 *  @example
 *  //$e.value为当前改变的值
 *  //在列表中$e还包含：
 *  //1. oldValue（修改前的值）
 *  //2. 当前修改的row与column对象
 */

var params = $page.getParams()
var evalObjectType = $e.value;
$api.evalTask.evalObjectList({data: {"evalObjectType":evalObjectType,"spaceGUID": params.SpaceGUID,}}).then(function(res){
    $form.setProp("EvalObject","data", res, false)
    $form.resetChangeState()
})]]></script>
        </code>
        <code id="a7088e46-f3bc-465d-a76a-022a2d6dcb12" controlId="8f7edc79-3c58-49ad-aca9-2b4801cd656e" controlType="ToolbarItem" controlAction="_appForm_button_58225070602428_beforeOpen">
            <script><![CDATA[/**
 *  页面打开前事件
 *  @example
 *  //阻止弹出框弹出
 *  $e.cancel = true 
 * 
 *  //用来给弹出页面传递数据
 *  //在弹出页面中可以通过$page.getDialogPageData来进行获取
 *  $e.options.pageData = {} 
 * 
 *  //传递页面参数
 *  $e.params = {businessCode:'0001'}
 */
var params = $page.getParams()
var documentFile = $form.getData("Document")
var modelInstanceCode = $form.getData("ModelInstanceCode")



if (!documentFile) {
  $notify.warning('请先上传数据集！')
  $e.cancel = true 
  return;
}


if (!params.oid) {
  $notify.warning('请先保存任务！')
  $e.cancel = true 
  return;
}

 $e.options.pageData = {oid:params.oid, document: documentFile, modelInstanceCode: modelInstanceCode}]]></script>
        </code>
        <code id="7570d444-cd18-4503-9a49-311af3d6966e" controlId="8f7edc79-3c58-49ad-aca9-2b4801cd656e" controlType="ToolbarItem" controlAction="_appForm_button_58225070602428_dialogClose">
            <script><![CDATA[/**
 *  对话框关闭后事件
 *  @example
 *  //可用来获取关闭的弹出框通过$page.close(data)传过来的数据
 *  console.log($e.data)
 */

var grid = $form.getGrid("subGrid_gpt_EvalTaskRecord_EvalTaskGUID")
grid.reload()
]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000703" service="evalTask" action="evalObjectList" type="0" apiSourceType="" />
        <api functionCode="42000703" service="evalTask" action="save" type="0" apiSourceType="" />
    </apis>
    <flows />
    <dependentScripts />
    <dependentUrls>
        <dependentUrl value="/std/42000703/08dd2975-c65d-4437-86dd-7b8984f78c8d#" />
    </dependentUrls>
    <dependentLangs />
    <dependentResources />
    <components>
        <component name="TaskName" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="23f8e931-cba6-44bb-b0c1-cf50e6d3ba4b" allowUnRef="false" allowRef="false">
            <label title="任务名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="任务名称" field="TaskName" allowEdit="false" customizeReferenceable="false" id="ead34d07-0242-4c37-9612-e6147f4f5738" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <textBox id="85a286cd-617f-428c-b0b3-ba1504070dca" field="TaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                    <customProps />
                    <events />
                </textBox>
                <behaviors />
            </column>
        </component>
        <component name="EvalObjectType" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="9e3229c1-cb34-49f4-b2d1-807b30bf8c4b" allowUnRef="false" allowRef="false">
            <label title="评测对象类型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="评测对象类型" field="EvalObjectType" allowEdit="false" customizeReferenceable="false" id="9a4f247a-bc42-4683-993f-b6fe7d99145a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <radioButtonList id="663a70b7-47ba-4c8f-838e-f01cf33862cc" field="EvalObjectType" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                    <customProps />
                    <events>
                        <event name="onvaluechanged" functionName="_appForm_evalObjectType_valueChanged" enabled="true" metadataStatus="Product" />
                    </events>
                    <options>
                        <option value="1" text="智能检查方案" isDefault="true" disabled="false" />
                        <option value="2" text="技能" isDefault="false" disabled="false" />
                        <option value="3" text="提示词" isDefault="false" disabled="false" />
                    </options>
                </radioButtonList>
                <behaviors />
            </column>
        </component>
        <component name="EvalObject" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="d85bb165-5170-4332-ba21-a976f661ceef" allowUnRef="false" allowRef="false">
            <label title="评测对象" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="评测对象" field="EvalObject" allowEdit="false" customizeReferenceable="false" id="d7008173-c59f-423c-b18c-9dbc43fd37d0" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <comboBox id="03629fb4-8b5c-423b-b551-e8b3b205c575" field="EvalObject" errorMode="default" readonlyMode="modify" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                    <customProps />
                    <events />
                    <options />
                </comboBox>
                <behaviors />
            </column>
        </component>
        <component name="ModelInstanceCode" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="d4917597-aa4e-4c67-ad09-628cc273e3a8" allowUnRef="false" allowRef="false">
            <label title="选择模型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="模型实例" field="ModelInstanceCode" allowEdit="false" customizeReferenceable="false" id="ecbf4cdc-3696-476b-a899-0a9e10c78c29" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <comboBox id="ec0f3a35-966e-49fd-bc1c-d3fd32310598" field="ModelInstanceCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="true" metadataStatus="Product" optionsType="sql" redundancyField="" allowEmpty="true" emptyIsDefault="true" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                    <customProps />
                    <events />
                    <sql showNumber="0">
                        <moql>
                            <content><![CDATA[select InstanceCode As `value`, InstanceName As `text`,ExecutionSetting  AS `ExecutionSetting`  from gpt_modelinstance  gmi left join gpt_model gm on gmi.ModelGUID = gm.ModelGUID where gm.ServiceTypeEnum in (0,3,4)]]></content>
                            <orderBy />
                        </moql>
                        <databaseSqls />
                    </sql>
                    <options />
                </comboBox>
                <behaviors />
            </column>
        </component>
        <component name="Document" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="7a8061e2-4e6d-4e06-b810-cae34f30411c" allowUnRef="false" allowRef="false">
            <label title="数据集" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="数据集文件" field="Document" allowEdit="false" customizeReferenceable="false" id="4bac30d9-a8aa-4f66-92b4-d6d08772509a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="true" hasExportIgnoreRule="false" printIgnore="true" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <fileUpload id="a4ad68a4-505b-49e9-a9e6-323341a7ebed" field="Document" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" limitFileCount="1" limitSize="50" fileLimitSize="20" limitType="*.xls;*.xlsx,customizeFileType" customType="zip" enableAllDownloads="false" showTips="false" showDragArea="false">
                    <customProps />
                    <events />
                    <rightsAddRule />
                    <rightsDelRule />
                </fileUpload>
                <behaviors />
            </column>
        </component>
        <component name="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="ebe57910-5305-43cf-a34f-c788f865f60d" allowUnRef="false" allowRef="false">
            <label title="任务执行记录" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
            <column title="相关列表" field="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" allowEdit="false" customizeReferenceable="false" id="b3485a2b-39fa-46d3-9ffa-0690e2b92074" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <subGrid id="0e7c072a-c4f8-4b29-870a-61dc544a5ca7" field="subGrid_gpt_EvalTaskRecord_EvalTaskGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" fullField="EvalTaskGUID" height="300">
                    <customProps />
                    <events />
                    <grid field="gpt_EvalTaskRecord.EvalTaskGUID" metadataId="08dd2978-3131-4c57-8d70-239920e39ae5" />
                </subGrid>
                <behaviors />
            </column>
        </component>
        <component name="EvalMode" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="1246db7a-147a-4ee0-90a6-c7742e015045" allowUnRef="false" allowRef="false">
            <label title="评判模型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="评判模型" field="EvalMode" allowEdit="false" customizeReferenceable="false" id="cca976be-3d9a-44f4-bc1b-6989de69fb89" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps />
                <radioButtonList id="a0f72e75-5a07-4437-9400-29b1e65c0ea1" field="EvalMode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                    <customProps />
                    <events />
                    <options>
                        <option value="1" text="语义模式" isDefault="true" disabled="false" />
                        <option value="0" text="精准模式" isDefault="false" disabled="false" />
                    </options>
                </radioButtonList>
                <behaviors />
            </column>
        </component>
        <component name="EvalRule" scope="08dd2977-9878-4fb5-8fef-b5242d31311d.08dd2977-9878-4fed-8d70-eea077243e75" defaultRefId="361b9540-eed7-4778-860c-cf4fdfcce920" allowUnRef="false" allowRef="false">
            <label title="评判规则" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
            <column title="评判规则" field="EvalRule" allowEdit="false" customizeReferenceable="false" id="a2d1e65f-68ba-4188-bf4b-957f05c95ed2" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                <customProps>
                    <props>requirementLevel</props>
                    <props>isHidden</props>
                </customProps>
                <textArea id="caf609c0-be13-428f-9942-5bff2e9e70e3" field="EvalRule" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="true" minRows="6" maxRows="6">
                    <customProps />
                    <events />
                </textArea>
                <behaviors />
            </column>
        </component>
    </components>
    <workflow enabled="false" />
</form>