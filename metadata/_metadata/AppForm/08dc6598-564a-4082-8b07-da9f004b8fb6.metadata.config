<?xml version="1.0" encoding="utf-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="夏娜" createdOn="2024-04-26T02:26:53Z" modifiedBy="黄诚" modifiedOn="2025-05-22T17:25:04.2626841+08:00" metadataStatus="Product" formId="08dc6598-564a-4082-8b07-da9f004b8fb6" name="模型实例表单控件" isSeparatedLayout="false" entityId="08dc650d-2c1e-4b7c-8060-68c0fb5aa43d" functionPageId="08dc6598-55f4-41b0-89fa-4bc909ec8a76" isRevisedId="1" application="4200" htmlCache="default" inheritFrom="00000000-0000-0000-0000-000000000000" metaDataExtendType="None" enableInherited="false">
    <dataSource keyName="InstanceGUID" entity="gpt_ModelInstance" withNoLock="true" mode="1">
        <command type="" queryDb=""><![CDATA[Select gpt_ModelInstance.ApiKey as ApiKey,
gpt_ModelInstance.ClientID as ClientID,
gpt_ModelInstance.CustomModelCode as CustomModelCode,
gpt_ModelInstance.DeploymentName as DeploymentName,
gpt_ModelInstance.Describe as Describe,
gpt_ModelInstance.EnableCustomModel as EnableCustomModel,
gpt_ModelInstance.Endpoint as Endpoint,
gpt_ModelInstance.InstanceCode as InstanceCode,
gpt_ModelInstance.InstanceName as InstanceName,
gpt_ModelInstance.IsAvailable as IsAvailable,
gpt_ModelInstance.IsDefault as IsDefault,
gpt_ModelInstance.IsSupportTool as IsSupportTool,
gpt_ModelInstance.MaxConcurrencyQuantity as MaxConcurrencyQuantity,
gpt_ModelInstance.ModelGUID as ModelGUID,
gpt_ModelInstance.ModelName as ModelName,
gpt_ModelInstance.StrategyId as StrategyId,
gpt_ModelInstance.Vendor as Vendor,
gpt_ModelInstance.CreatedGUID as CreatedGUID,
gpt_ModelInstance.CreatedName as CreatedName,
gpt_ModelInstance.CreatedTime as CreatedTime,
gpt_ModelInstance.InstanceGUID as InstanceGUID,
gpt_ModelInstance.ModifiedGUID as ModifiedGUID,
gpt_ModelInstance.ModifiedName as ModifiedName,
gpt_ModelInstance.ModifiedTime as ModifiedTime 
From gpt_ModelInstance As gpt_ModelInstance 
Where gpt_ModelInstance.InstanceGUID=@oid]]></command>
        <fields>
            <field name="ApiKey" allowPopulate="true" entity="gpt_ModelInstance" field="ApiKey" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="ClientID" allowPopulate="true" entity="gpt_ModelInstance" field="ClientID" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="CustomModelCode" allowPopulate="true" entity="gpt_ModelInstance" field="CustomModelCode" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="DeploymentName" allowPopulate="true" entity="gpt_ModelInstance" field="DeploymentName" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="Describe" allowPopulate="true" entity="gpt_ModelInstance" field="Describe" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="EnableCustomModel" allowPopulate="true" entity="gpt_ModelInstance" field="EnableCustomModel" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="Endpoint" allowPopulate="true" entity="gpt_ModelInstance" field="Endpoint" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="InstanceCode" allowPopulate="true" entity="gpt_ModelInstance" field="InstanceCode" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="InstanceGUID" allowPopulate="true" entity="gpt_ModelInstance" field="InstanceGUID" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="InstanceName" allowPopulate="true" entity="gpt_ModelInstance" field="InstanceName" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="IsAvailable" allowPopulate="true" entity="gpt_ModelInstance" field="IsAvailable" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="IsDefault" allowPopulate="true" entity="gpt_ModelInstance" field="IsDefault" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="IsSupportTool" allowPopulate="true" entity="gpt_ModelInstance" field="IsSupportTool" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="MaxConcurrencyQuantity" allowPopulate="true" entity="gpt_ModelInstance" field="MaxConcurrencyQuantity" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="ModelGUID" allowPopulate="true" entity="gpt_ModelInstance" field="ModelGUID" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="ModelName" allowPopulate="false" entity="gpt_ModelInstance" field="ModelName" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="StrategyId" allowPopulate="true" entity="gpt_ModelInstance" field="StrategyId" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
            <field name="Vendor" allowPopulate="true" entity="gpt_ModelInstance" field="Vendor" entityAlias="gpt_ModelInstance" metadataStatus="Product">
                <fields />
            </field>
        </fields>
        <availableFields>
            <availableField name="ApiKey" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ApiKey" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="ClientID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ClientID" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
            <availableField name="CustomModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CustomModelCode" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="DeploymentName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="DeploymentName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="Describe" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Describe" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="EnableCustomModel" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EnableCustomModel" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
            <availableField name="Endpoint" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Endpoint" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="InstanceCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceCode" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="InstanceGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="InstanceGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
            <availableField name="InstanceName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="InstanceName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="IsAvailable" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsAvailable" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
            <availableField name="IsDefault" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsDefault" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
            <availableField name="IsSupportTool" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="IsSupportTool" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
            <availableField name="MaxConcurrencyQuantity" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="MaxConcurrencyQuantity" entity="gpt_ModelInstance" entityType="0" attributeType="整数" />
            <availableField name="ModelGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
            <availableField name="ModelName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_ModelInstance" entityType="0" attributeType="Guid" />
            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(128)）" />
            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_ModelInstance" entityType="0" attributeType="日期与时间" />
            <availableField name="StrategyId" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="StrategyId" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(64)）" />
            <availableField name="Vendor" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="Vendor" entity="gpt_ModelInstance" entityType="0" attributeType="文本（nvarchar(512)）" />
            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_ModelInstance" entityType="0" attributeType="时间戳" />
        </availableFields>
        <fixedSortings />
        <summaries />
        <diagrams>
            <diagram xmlAttributeId="08dc6598-564c-441c-83d8-20f4c0c5756d" id="08dc650d-2c1e-4b7c-8060-68c0fb5aa43d" name="gpt_ModelInstance" primaryField="InstanceGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                <conditions />
                <resourceFilters />
                <projectInterfaceFilters />
            </diagram>
        </diagrams>
        <performanceOptimizeHints />
    </dataSource>
    <layout concurrencyDetect="false" doubleToolBar="false" asyncRender="true" templateStyle="default" showNavigation="false" tabPosition="top" freeTab="false" groupStyle="unfolded">
        <regions>
            <region regionId="region1" id="08dc6598-5647-42ec-867b-d2bf611980e3" title="新分区" tabTitle="" disableStyle="true" isHidden="false" metadataStatus="Product">
                <events />
                <groups>
                    <group id="08dc6598-**************-ad371985420a" title="基础信息" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="cd97c830-7e31-46c9-a226-9ee0edd7047b" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="模型厂商" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="下拉框" field="ModelTypeEnum" allowEdit="false" customizeReferenceable="false" id="9519eb30-0647-4de9-b9cc-55ea76e0adce" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <comboBox id="3fee97c4-8067-494f-9740-44cae00603d1" field="ModelTypeEnum" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="true" metadataStatus="Product" optionsType="enum" redundancyField="" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" allowClear="false" filterable="false" operatorType="like" allowDeselectDisabledOption="true">
                                                <customProps />
                                                <events />
                                                <options />
                                                <enum enumTypeName="com.mysoft.gptbuilder.common.model.enums.ModelTypeEnum" defaultValue="" />
                                            </comboBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="a9c697e7-8c2e-4c88-81a1-242219c59c08" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="服务类型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="数字" field="ServiceTypeEnum" allowEdit="false" customizeReferenceable="false" id="4af06a4f-5137-4bb3-bc27-671c0a7f0512" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" dataSourceType="Normal" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <spinner id="098347d5-4f57-414e-a7bd-20817ff228cd" field="ServiceTypeEnum" errorMode="default" readonlyMode="none" defaultValue="0.00" requirementLevel="none" templateStyle="" isHidden="true" metadataStatus="Product" precision="0" precisionType="0" rounding="-1" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                                <customProps />
                                                <events>
                                                    <event name="onvaluechanged" functionName="_appForm_serviceTypeEnum_valueChanged" enabled="true" metadataStatus="Product" />
                                                </events>
                                            </spinner>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="920269a7-ee32-4371-801b-a50d885dd7a7" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="实例名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="模型名称" field="InstanceName" allowEdit="false" customizeReferenceable="false" id="44ac7ad4-2931-4bd5-9205-6c73f35263a9" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textBox id="e7a93897-934a-43fe-a12f-76ec9962e692" field="InstanceName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="f4ce0916-b471-4887-8156-221bfb395ef1" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="实例编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="模型编码" field="InstanceCode" allowEdit="false" customizeReferenceable="false" id="93b2a7c0-5961-4580-bd3f-6e180844cd94" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textBox id="a05bc03b-1731-49cf-8c04-3cdf48b42a59" field="InstanceCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events>
                                                    <event name="onvalidation" functionName="_appForm_instanceCode_validation" enabled="true" metadataStatus="Product" />
                                                </events>
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="e5e68c54-0dc5-4e3a-803f-01993a907ee7" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="所属服务" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="模型GUID" field="ModelGUID" allowEdit="false" customizeReferenceable="false" id="565c80fc-bd1e-4088-bd7d-d7f13b2cb718" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <lookPopup id="1985d928-dc94-42a6-a6ef-2350740f75c6" field="ModelGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionsType="optionsDataSource" valueFromSelect="true" redundancyField="ModelName" multiSelect="false">
                                                <customProps />
                                                <events>
                                                    <event name="onvaluechanged" functionName="_appForm_modelGUID_valueChanged" enabled="true" metadataStatus="Product" />
                                                </events>
                                                <optionsDataSource>
                                                    <dataSource keyName="ModelGUID" entity="gpt_Model" withNoLock="true" mode="1">
                                                        <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9AsgewCbgHQFEAPKAV2gEsMA7UaCq+WAQwGdYjSLrb74AaALAAoJKkw4wuAJIspAW0atYM+f2Gj02PDOABPFtBBzmbHfsNzBI5Jom5x4AMJaTsB2Gc4rG9/a1gAOSY5EFd3IJDvG193ABVdRFClOISQKLF/XFAAJwA3ckgQeMT8KhJjJRz8wuKQUvL02zxHbJAmQywAcQBVKQARVxa2jp7+xt8h9pAsCKS2SY7Z8cyF6djyEMHWqax1yPVozPdRgeT/E+W7cXIAM3Jpk7DsW/uu3r7LvGu76dmnrBev2CaQOGSuzx+uw2czcENee1CQgAYtkMMYfP5YABBNgYiSwWBCADqAAsQK1YAAKACMAF5qQBKIA==]]></command>
                                                        <fields>
                                                            <field name="ModelName" allowPopulate="false" entity="gpt_Model" field="ModelName" entityAlias="gpt_Model" metadataStatus="Product" />
                                                            <field name="ModelGUID" allowPopulate="false" entity="gpt_Model" field="ModelGUID" entityAlias="gpt_Model" metadataStatus="Product" />
                                                            <field name="ServiceTypeEnum" allowPopulate="false" entity="gpt_Model" field="ServiceTypeEnum" entityAlias="gpt_Model" metadataStatus="Product" />
                                                            <field name="ModelType" allowPopulate="false" entity="gpt_Model" field="ModelType" entityAlias="gpt_Model" metadataStatus="Product" />
                                                        </fields>
                                                        <availableFields>
                                                            <availableField name="ExecutionSetting" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ExecutionSetting" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(1024)）" />
                                                            <availableField name="IsImg" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsImg" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="IsSystem" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="IsSystem" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="ModelCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelCode" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModelName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModelType" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ModelType" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="ServiceTypeEnum" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="4" aliasName="ServiceTypeEnum" entity="gpt_Model" entityType="0" attributeType="整数" />
                                                            <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_Model" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="ModelGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModelGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_Model" entityType="0" attributeType="Guid" />
                                                            <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_Model" entityType="0" attributeType="文本（nvarchar(128)）" />
                                                            <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_Model" entityType="0" attributeType="日期与时间" />
                                                            <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_Model" entityType="0" attributeType="时间戳" />
                                                        </availableFields>
                                                        <fixedSortings />
                                                        <summaries />
                                                        <diagrams>
                                                            <diagram xmlAttributeId="08dcd188-4d46-4f7c-8a60-23ed797921b6" id="08dc6593-e473-4a3c-866d-621c232bd94f" name="gpt_Model" primaryField="ModelGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                                                                <conditions />
                                                                <resourceFilters />
                                                                <projectInterfaceFilters />
                                                            </diagram>
                                                        </diagrams>
                                                        <performanceOptimizeHints />
                                                    </dataSource>
                                                    <extendFields>
                                                        <extendField displayName="文本" name="ModelName" entity="gpt_Model" isQueryField="false" mapType="Text" />
                                                        <extendField displayName="值" name="ModelGUID" entity="gpt_Model" isQueryField="false" mapType="Value" />
                                                        <extendField displayName="扩展字段" name="ServiceTypeEnum" entity="gpt_Model" isQueryField="false" mapType="Extend" />
                                                        <extendField displayName="扩展字段" name="ModelType" entity="gpt_Model" isQueryField="false" mapType="Extend" />
                                                    </extendFields>
                                                    <sortFields />
                                                </optionsDataSource>
                                                <behavior target="dialog" targetDisplayType="fixedWidth" type="dataSelect" url="/std/42000501/08dcd148-190b-437e-83a1-a3cb257c5afb" id="08dcd148-190b-437e-83a1-a3cb257c5afb" itemId="76b42071-3565-4fdc-b4a2-1eb381f3a311" metadataStatus="Product">
                                                    <options>
                                                        <option key="width" value="960" />
                                                        <option key="height" value="600" />
                                                    </options>
                                                    <params />
                                                    <events />
                                                    <dataSelectRule id="19605512-d49a-4f4f-aee0-495193b91da3" metadataId="08dcd148-190d-4f68-86fe-cb641bbc30c4" isAllowRepeat="false" isSaveParentFieldData="false">
                                                        <dataSelectFields>
                                                            <dataSelectField field="value" mapField="ModelGUID" isUnique="true" dataType="columnField" />
                                                            <dataSelectField field="text" mapField="ModelName" isUnique="false" dataType="columnField" />
                                                        </dataSelectFields>
                                                    </dataSelectRule>
                                                </behavior>
                                            </lookPopup>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="f13bcb00-4893-4f7e-87b4-cf7ec71e16aa" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="最大并发数" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="最大并发数" field="MaxConcurrencyQuantity" allowEdit="false" customizeReferenceable="false" id="a1ded8c1-95ce-4562-96d8-edf85b4dbe06" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <spinner id="8012e332-c4a5-44a6-a4ba-941967a62732" field="MaxConcurrencyQuantity" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="false" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="0" maxValue="1000" minOperatorType="ge" maxOperatorType="le">
                                                <customProps />
                                                <events />
                                            </spinner>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="854a3263-77a3-4bd7-991b-94d4413b6b70" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="自定义模型" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="&lt;div&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&lt;p&gt;开启后可以自定义模型名称，适用于配置模型快照版本等场景；&lt;/p&gt;&lt;/div&gt;&lt;/div&gt;" requirementLevel="none" />
                                        <column title="自定义模型" field="EnableCustomModel" allowEdit="false" customizeReferenceable="false" id="9c8049c4-160a-4d28-9d4d-f1eed7daec2a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <ToggleSwitch id="b4e66a89-eb88-477e-8c05-f1b794b4396a" field="EnableCustomModel" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" isHidden="false" metadataStatus="Product" trueValue="启用" falseValue="禁用">
                                                <customProps />
                                                <events>
                                                    <event name="onvaluechanged" functionName="_appForm_enableCustomModel_valueChanged" enabled="true" metadataStatus="Product" />
                                                </events>
                                            </ToggleSwitch>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="a79e911b-2e54-4146-979e-dd39ddcfa9bf" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="模型编码" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="自定义模型编码" field="CustomModelCode" allowEdit="false" customizeReferenceable="false" id="9577326f-e7e9-43ff-827a-a3e31da2bc4d" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>requirementLevel</props>
                                                <props>isHidden</props>
                                            </customProps>
                                            <textBox id="9408ab58-0dab-4a24-b9c8-7781e1a9bbd7" field="CustomModelCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="1b24c48c-3a69-43ff-a1c1-7851f591d710" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="是否默认" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldDescription="" fieldTips="&lt;p&gt;默认模型：助手全局默认使用的模型，提示词、技能也可以指定默认模型&lt;br&gt;&lt;/p&gt;" requirementLevel="none" />
                                        <column title="是否默认" field="IsDefault" allowEdit="false" customizeReferenceable="false" id="aaa0cf56-b778-457b-be4d-bf3c55466456" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <radioButtonList id="070f830d-c903-486b-a8dc-5ddb0beb0b68" field="IsDefault" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                                                <customProps />
                                                <events />
                                                <options>
                                                    <option value="0" text="否" isDefault="true" disabled="false" />
                                                    <option value="1" text="是" isDefault="false" disabled="false" />
                                                </options>
                                            </radioButtonList>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="14e5cdf7-aa27-4759-87d6-61390f142871" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="是否可用" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="是否可用" field="IsAvailable" allowEdit="false" customizeReferenceable="false" id="89ce0b6e-1ba5-43ab-bfc3-a7c78aa46c65" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <radioButtonList id="1401262b-8a47-4b8f-84fe-fc66e79d14fd" field="IsAvailable" errorMode="default" readonlyMode="all" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                                                <customProps />
                                                <events />
                                                <options>
                                                    <option value="0" text="否" isDefault="true" disabled="false" />
                                                    <option value="1" text="是" isDefault="false" disabled="false" />
                                                </options>
                                            </radioButtonList>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="30ce6442-56a7-492a-b1f0-f17b77524477" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="是否支持工具" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="&lt;p&gt;是否支持工具：&lt;span&gt;对于支持 FunctionCall的模型，在连接测试时，需要开启相应开关以确保能够正常调用插件&lt;/span&gt;&lt;/p&gt;" requirementLevel="none" />
                                        <column title="是否支持工具" field="IsSupportTool" allowEdit="false" customizeReferenceable="false" id="78d619f5-0687-46c2-a05b-324067d61937" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <radioButtonList id="e07eb99f-8ced-49fc-8f38-69d4cb9c7290" field="IsSupportTool" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" redundancyField="" disableFolderSelect="false" showLongText="false" valueFromSelect="false" allowEmpty="false">
                                                <customProps />
                                                <events />
                                                <options>
                                                    <option value="0" text="否" isDefault="true" disabled="false" />
                                                    <option value="1" text="是" isDefault="false" disabled="false" />
                                                </options>
                                            </radioButtonList>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="666e88ab-238c-49b9-a50b-d9ad2ebebfdd" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="939a4856-9027-4ac1-9fd2-8d3649baa641" colSpan="2" rowSpan="1" metadataStatus="Product">
                                        <label title="实例说明" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="模型说明" field="Describe" allowEdit="false" customizeReferenceable="false" id="fa805a81-291a-4583-a6b0-970b36b1f6c1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textArea id="1cc49490-2dae-4167-b723-32797b786507" field="Describe" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="84" maxLength="512" autoHeight="false" minRows="2" maxRows="6">
                                                <customProps />
                                                <events />
                                            </textArea>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                        </rows>
                    </group>
                    <group id="8a2a6d94-24c2-48b9-8562-121a2b1e7d5c" title="服务配置" disableStyle="false" isHidden="false" tipsType="0" metadataStatus="Product">
                        <cellStyles>
                            <cellStyle labelWidth="110px" width="50%" />
                            <cellStyle labelWidth="110px" width="50%" />
                        </cellStyles>
                        <rows>
                            <row>
                                <cells>
                                    <cell id="67ffa8c4-6cda-4d5c-8f0f-ee0d966f5208" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="终结点" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="终结点" field="Endpoint" allowEdit="false" customizeReferenceable="false" id="a5c18915-03fb-4659-8292-37b97833fed1" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <textBox id="d6a89d43-0835-40c1-8a65-5d54b6d11415" field="Endpoint" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="3fc4486a-0903-4c43-b879-9ee99be90481" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="部署名称" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="部署名称" field="DeploymentName" allowEdit="false" customizeReferenceable="false" id="1d959c63-a436-426c-b64f-85264b0b8ed7" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>requirementLevel</props>
                                                <props>isHidden</props>
                                            </customProps>
                                            <textBox id="aacfbc1a-f860-41ae-9b38-710c42000834" field="DeploymentName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="3816282c-a700-4e76-8662-32fc2af141ca" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="ClientID" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="ClientID" field="ClientID" allowEdit="false" customizeReferenceable="false" id="83d5aac8-28bb-4b3b-926a-71f820bbe1a9" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                                <props>requirementLevel</props>
                                            </customProps>
                                            <password id="8759a797-8db4-4f86-9814-b9eac9908b5b" field="ClientID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" maxLength="512" isBold="false">
                                                <customProps />
                                                <events />
                                            </password>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="340bd381-fcd2-438e-bd7a-74596b15714e" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="SecretKey" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="API秘钥" field="ApiKey" allowEdit="false" customizeReferenceable="false" id="7ddd73fe-6ce1-4457-8d2b-a8c63ec323c4" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps />
                                            <password id="70133d36-1aa6-4117-b14c-069b41722943" field="ApiKey" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="required" isHidden="false" metadataStatus="Product" maxLength="512" isBold="false">
                                                <customProps />
                                                <events />
                                            </password>
                                            <behaviors />
                                        </column>
                                    </cell>
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="2cd9e3c7-79cb-44c7-af5d-e1d34aa343d6" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="产商" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" requirementLevel="none" />
                                        <column title="产商" field="Vendor" allowEdit="false" customizeReferenceable="false" id="3b814727-5469-4fce-ae9c-1f8af3db509d" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <textBox id="34f0357d-8637-43cd-8ddb-2b3bfa5ba738" field="Vendor" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="512" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="fcc9f6ec-0ab8-47fa-bb6c-15cdf2855546" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                            <row>
                                <cells>
                                    <cell id="38f57822-d1e8-46a7-8c9c-4b2cb6126a3b" colSpan="1" rowSpan="1" metadataStatus="Product">
                                        <label title="策略ID" visible="true" titleShowStyle="show" isMoreCondition="false" align="left" tips="" fieldTips="" requirementLevel="none" />
                                        <column title="审核策略ID" field="StrategyId" allowEdit="false" customizeReferenceable="false" id="185e3709-9f0c-4dec-b832-eee36f42c3a2" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" metadataStatus="Product" enableAI="false">
                                            <customProps>
                                                <props>isHidden</props>
                                            </customProps>
                                            <textBox id="2ac5637f-df4d-4700-97ac-b48abd5f7e19" field="StrategyId" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                                <customProps />
                                                <events />
                                            </textBox>
                                            <behaviors />
                                        </column>
                                    </cell>
                                    <cell id="54652fd9-59c4-4b52-92b0-af854ff26f4e" colSpan="1" rowSpan="1" metadataStatus="Product" />
                                </cells>
                            </row>
                        </rows>
                    </group>
                </groups>
            </region>
        </regions>
        <toolbars>
            <toolbar toolbarId="08dc6598-564a-40b3-88d7-d57c48f13f3b" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items>
                            <item itemId="cc34be27-79b2-427d-b3a9-71ac827196c0" title="返回" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="button_58240541268407" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58240541268407_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc6598-5647-44db-8d38-026953c6fb7e" title="取消" isHighlight="false" type="button" id="mCancel" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mCancel_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="a095e98f-9059-41a3-9a46-28fe59b288c3" title="连接测试" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58246328488413" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="true" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58246328488413_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="08dc6598-5647-4473-81a6-0ef382e505fe" title="保存" isHighlight="true" type="button" id="mSave" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_mSave_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                            <item itemId="0580b43c-bf78-42be-a081-148d7339c573" title="编辑" isHighlight="true" type="button" iconClassUrl="" iconClass="" id="button_58240541199456" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items />
                                <events>
                                    <event name="onclick" functionName="_appForm_button_58240541199456_click" enabled="true" metadataStatus="Product" />
                                </events>
                                <customProps>
                                    <props>isHidden</props>
                                </customProps>
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events>
            <event name="onload" functionName="_appForm_load" enabled="true" metadataStatus="Product" />
        </events>
        <attributes />
        <hiddens />
        <langs />
        <rule>
            <configs>
                <config id="2f268b27-10b0-454b-b3f6-abc305a7b676" title="新规则" controlId="08dc6598-5647-44db-8d38-026953c6fb7e" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mCancel" metadataStatus="Product">
                    <handles>
                        <handle handleId="12e55f1b-34ae-41ce-8e55-454c115ccab4" ruleId="078d398b-d2c9-4225-8328-5d0642b02829" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="9f94032e-9558-4df1-8706-6594b7e14e20" title="新规则" controlId="cc34be27-79b2-427d-b3a9-71ac827196c0" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58240541268407" metadataStatus="Product">
                    <handles>
                        <handle handleId="414c8a85-cdf9-4667-aa96-070f5e058afa" ruleId="078d398b-d2c9-4225-8328-5d0642b02829" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="0b8de7ae-dc1a-4ab6-b5c5-9deedad8adf0" title="新规则" controlId="0580b43c-bf78-42be-a081-148d7339c573" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="button_58240541199456" metadataStatus="Product">
                    <handles>
                        <handle handleId="5190f4b9-6ba5-44fa-bd0a-735b8deeac9a" ruleId="078d398b-d2c9-4225-8328-5d0642b02829" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="6b32e422-4318-49a6-af0a-ab89187d9f20" title="新规则" controlId="08dc6598-5647-4473-81a6-0ef382e505fe" controlType="toolbaritem" controlSubType="global" controlProp="isHidden" controlName="mSave" metadataStatus="Product">
                    <handles>
                        <handle handleId="139a5c53-4f51-47a6-8263-0893a3e07c52" ruleId="078d398b-d2c9-4225-8328-5d0642b02829" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="224a225b-7230-41f4-bfef-64b5a56aca16" title="新规则" controlId="3fc4486a-0903-4c43-b879-9ee99be90481" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="DeploymentName" metadataStatus="Product">
                    <handles>
                        <handle handleId="37ba7237-664c-4137-85ae-31c4b94e3e18" ruleId="c0885131-a201-40d9-b43a-9d23b96a572b" action="optional" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="0e578fdf-d366-4980-acfa-0238e78a87ca" title="新规则" controlId="3fc4486a-0903-4c43-b879-9ee99be90481" controlType="cell" controlSubType="" controlProp="isHidden" controlName="DeploymentName" metadataStatus="Product">
                    <handles>
                        <handle handleId="5bd068f7-91fa-4ee6-8b6e-fbab24529f23" ruleId="c0885131-a201-40d9-b43a-9d23b96a572b" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="f2fb609c-c6e3-4c13-8cf4-3145ba712810" title="新规则" controlId="2cd9e3c7-79cb-44c7-af5d-e1d34aa343d6" controlType="cell" controlSubType="" controlProp="isHidden" controlName="Vendor" metadataStatus="Product">
                    <handles>
                        <handle handleId="06713dac-a6f8-49d0-a921-622ff36b4934" ruleId="d61b6b3d-ad9e-4081-b31e-6de426cb323b" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="b9f5e3e0-21ae-4a2e-a977-9d245b335a69" title="新规则" controlId="3816282c-a700-4e76-8662-32fc2af141ca" controlType="cell" controlSubType="" controlProp="isHidden" controlName="ClientID" metadataStatus="Product">
                    <handles>
                        <handle handleId="db3ae433-8ccc-437c-ab95-1b9fffa129da" ruleId="08dc6598-5647-4524-8f58-df9514b4cb7b" action="show" value="" metadataStatus="Product" />
                        <handle handleId="58429c1f-db6e-4de3-92c8-a5576122e413" ruleId="82afd0b5-970e-4718-a4bc-e6e11c3b8670" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="063466a0-958b-4005-8ca3-759019144404" title="新规则" controlId="14e5cdf7-aa27-4759-87d6-61390f142871" controlType="cell" controlSubType="" controlProp="isHidden" controlName="IsAvailable" metadataStatus="Product">
                    <handles>
                        <handle handleId="200eb938-478b-40a6-b1cf-7be2acd38898" ruleId="7e094478-1c16-4f2e-bbee-49e3f9167f94" action="hide" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="db48b1c6-a6c7-4d7e-a68b-b800e16ed3f4" title="新规则" controlId="38f57822-d1e8-46a7-8c9c-4b2cb6126a3b" controlType="cell" controlSubType="" controlProp="isHidden" controlName="StrategyId" metadataStatus="Product">
                    <handles>
                        <handle handleId="956a89e2-717d-48c1-92e7-c7fc4b3456a5" ruleId="25e0a40b-5e85-4c40-9689-f7fe218dcfaa" action="show" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="e47ee363-a2c4-4295-9e7b-8b7bd3aef1f1" title="新规则" controlId="3816282c-a700-4e76-8662-32fc2af141ca" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="ClientID" metadataStatus="Product">
                    <handles>
                        <handle handleId="2ac20436-d5bd-4437-8f87-1a5862c0f829" ruleId="08dc6598-5647-4524-8f58-df9514b4cb7b" action="required" value="" metadataStatus="Product" />
                        <handle handleId="b4d501e7-1172-4396-9239-93b4fe2f596c" ruleId="82afd0b5-970e-4718-a4bc-e6e11c3b8670" action="required" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="a7615d06-77b2-46a4-a534-d5a75dac2df7" title="新规则" controlId="a79e911b-2e54-4146-979e-dd39ddcfa9bf" controlType="cell" controlSubType="" controlProp="requirementLevel" controlName="CustomModelCode" metadataStatus="Product">
                    <handles>
                        <handle handleId="c6e46510-cf9f-435e-a967-0bb7a0643522" ruleId="9eb88400-50a1-499d-a60a-8582ab92aa07" action="required" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="161428b0-0068-4bca-a391-8cb0ad2b34cf" title="新规则" controlId="a79e911b-2e54-4146-979e-dd39ddcfa9bf" controlType="cell" controlSubType="" controlProp="isHidden" controlName="CustomModelCode" metadataStatus="Product">
                    <handles>
                        <handle handleId="82c2ce73-77ae-4d4f-998c-248c5586cb58" ruleId="796a2541-323b-4368-b4c6-f746b56078b4" action="hideAndEmpty" value="" metadataStatus="Product" />
                        <handle handleId="7b6ad08d-e2ba-43f6-8daf-d21913dab60c" ruleId="39d51906-169e-4d14-96fe-0684525921dd" action="hideAndEmpty" value="" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="68caadd1-f6ba-4c6d-9cc8-b0060b7ae2a0" title="新规则" controlId="854a3263-77a3-4bd7-991b-94d4413b6b70" controlType="cell" controlSubType="" controlProp="isHidden" controlName="EnableCustomModel" metadataStatus="Product">
                    <handles>
                        <handle handleId="af2e236b-f883-4eab-b7b1-1bf3a8fa05ad" ruleId="39d51906-169e-4d14-96fe-0684525921dd" action="hideAndEmpty" value="" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="08dc6598-5647-4524-8f58-df9514b4cb7b" title="ClientId显示规则" rule="{&quot;condition&quot;:&quot;OR&quot;,&quot;rules&quot;:[{&quot;condition&quot;:&quot;OR&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ModelTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;6&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ModelTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;7&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]},{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ServiceTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;2&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;InstanceCode&quot;,&quot;operator&quot;:&quot;not_equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;ocr_recognize_vl&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}]}" metadataStatus="Product" />
                <group id="82afd0b5-970e-4718-a4bc-e6e11c3b8670" title="阿里安全审查" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ServiceTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;5&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="39d51906-169e-4d14-96fe-0684525921dd" title="隐藏自定义模型" rule="{&quot;condition&quot;:&quot;OR&quot;,&quot;rules&quot;:[{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ServiceTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;2&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;InstanceCode&quot;,&quot;operator&quot;:&quot;not_equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;ocr_recognize_vl&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ServiceTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;5&quot;,&quot;isEdit&quot;:false,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="796a2541-323b-4368-b4c6-f746b56078b4" title="未启用自定义模型" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;EnableCustomModel&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;0&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="25e0a40b-5e85-4c40-9689-f7fe218dcfaa" title="百度安全审查" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ServiceTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;5&quot;,&quot;isEdit&quot;:false,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="078d398b-d2c9-4225-8328-5d0642b02829" title="表单查看模式" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;editMode&quot;,&quot;field&quot;:&quot;_editMode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;3&quot;}]}" metadataStatus="Product" />
                <group id="c0885131-a201-40d9-b43a-9d23b96a572b" title="部署名称隐藏" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ModelTypeEnum&quot;,&quot;operator&quot;:&quot;not_equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;0&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;},{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ModelTypeEnum&quot;,&quot;operator&quot;:&quot;not_equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;7&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="d61b6b3d-ad9e-4081-b31e-6de426cb323b" title="明源模型" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;ModelTypeEnum&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;7&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="7e094478-1c16-4f2e-bbee-49e3f9167f94" title="表单新增" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;urlParam&quot;,&quot;field&quot;:&quot;mode&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;1&quot;}]}" metadataStatus="Product" />
                <group id="9eb88400-50a1-499d-a60a-8582ab92aa07" title="启用自定义模型" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;EnableCustomModel&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;number&quot;,&quot;value&quot;:&quot;1&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
    </layout>
    <checkRules />
    <linkageRules>
        <linkage name="所属服务联动" id="fd2880d7-d6c3-45c1-b733-670657e3dd5d" field="ModelGUID" type="valuechanged" columnType="" columnField="" metadatastatus="Product">
            <actions>
                <action id="f23504f9-2477-486a-a30d-168230dc19b1" subControlLinkField="" field="ModelTypeEnum" type="value" prop="ModelType" source="ModelGUID" metadataStatus="Product" />
                <action id="e8561dc7-33b3-4f6f-9e9b-2d6310d84f4b" subControlLinkField="" field="ServiceTypeEnum" type="value" prop="ServiceTypeEnum" source="ModelGUID" metadataStatus="Product" />
            </actions>
        </linkage>
    </linkageRules>
    <capitalizeAmountLinkageRules />
    <codes>
        <code id="845de9c8-1ef8-4948-8dcc-e66048d64cc5" controlId="0580b43c-bf78-42be-a081-148d7339c573" controlType="ToolbarItem" controlAction="_appForm_button_58240541199456_click">
            <script><![CDATA[$Utility.enableEdit()]]></script>
        </code>
        <code id="4412ddfe-e436-4810-859e-febeb9b9cbab" controlId="08dc6598-5647-4473-81a6-0ef382e505fe" controlType="ToolbarItem" controlAction="_appForm_mSave_click">
            <script><![CDATA[return $form.submit({
  service: 'ModelInstance',
  action: 'SaveModelInstance',
  validate: true
}).then(function (id) {
  $notify.success('模型保存成功')
  if (!$page.getParams().oid) {
    $util.locationStorage.set('ModelTest', '1')
    $page.redirect({ oid: id, mode: 2 })
    return
  }
  $form.submit({
    service: 'ModelInstance',
    action: 'TestConnection',
    validate: true
  }).then(function(){
    $notify.success("连接测试成功")
    $form.setData('IsAvailable', '1')
  }, function (e) {
    var restest = $_.parseJSON(e.responseText);
    $form.setData('IsAvailable', restest.isBusinessLogicError ? '0' : '1')
  })
})
]]></script>
        </code>
        <code id="c62d9252-5e1d-439c-ac1f-db0bff923b84" controlId="08dc6598-5647-44db-8d38-026953c6fb7e" controlType="ToolbarItem" controlAction="_appForm_mCancel_click">
            <script><![CDATA[$page.redirect('/std/42000501/08dc6598-55f3-4e3b-838b-f55241c4f5ae')]]></script>
        </code>
        <code id="7e8b7993-467d-47a2-9228-f2f40bb9fda6" controlId="08dc6598-564a-4082-8b07-da9f004b8fb6" controlType="Form" controlAction="_appForm_load">
            <script><![CDATA[var mode = $util.locationStorage.get('ModelTest')
if (mode === '1') {
  $form.submit({
    service: 'ModelInstance',
    action: 'TestConnection',
    validate: true
  }).then(function(){
    $notify.success("连接测试成功")
  }, function (e) {
    var restest = $_.parseJSON(e.responseText);
    $form.setData('IsAvailable', restest.isBusinessLogicError ? '0' : '1')
  })
}

if ($form.getData("ModelGUID") == "0d814593-e45f-11ef-b079-00155d822d63") {
  $form.setMode(['EnableCustomModel'], 3)
}
$util.locationStorage.del('ModelTest')]]></script>
        </code>
        <code id="fe4d4f7f-f1c7-467c-9111-dcdf138e5d1e" controlId="098347d5-4f57-414e-a7bd-20817ff228cd" controlType="CellControl" controlAction="_appForm_serviceTypeEnum_valueChanged">
            <script><![CDATA[/**
 *  值改变后事件
 *  @example
 *  //$e.value为当前改变的值
 *  //在列表中$e还包含：
 *  //1. oldValue（修改前的值）
 *  //2. 当前修改的row与column对象
 */
if ($e.value != "2" && $e.value != "5" && $form.getData('ModelGUID') == "0d814593-e45f-11ef-b079-00155d822d63") {
  $form.show('EnableCustomModel')
  $form.setData({"EnableCustomModel":1})
  $form.setMode(['EnableCustomModel'], 3)
}]]></script>
        </code>
        <code id="f87a74b3-55cb-4b45-8555-47300ee7046e" controlId="1985d928-dc94-42a6-a6ef-2350740f75c6" controlType="CellControl" controlAction="_appForm_modelGUID_valueChanged">
            <script><![CDATA[/**
 *  值改变后事件
 *  @example
 *  //$e.value为当前改变的值
 *  //在列表中$e还包含：
 *  //1. oldValue（修改前的值）
 *  //2. 当前修改的row与column对象
 */
var val = $form.getData("EnableCustomModel");
if(val == 1){
  var modelGUID = $form.getData("ModelGUID");
  $api.ModelInstance.getModelCodeByModelGUID({modelGUID:modelGUID}).then(function(res){
    $form.setData({"CustomModelCode":res})
  })
}
else{
  $form.setData({"CustomModelCode":""})
}


if ($e.value == "0d814593-e45f-11ef-b079-00155d822d63") {
  $form.show('EnableCustomModel')
  $form.setData({"EnableCustomModel":1})
  $form.setMode(['EnableCustomModel'], 3)
  
}else {
  $form.setMode(['EnableCustomModel'], 2)
}]]></script>
        </code>
        <code id="b43f28a5-a18e-4219-bea1-7c92f96b017c" controlId="a05bc03b-1731-49cf-8c04-3cdf48b42a59" controlType="CellControl" controlAction="_appForm_instanceCode_validation">
            <script><![CDATA[// $Utility.validateCode($e)]]></script>
        </code>
        <code id="0d1f7dcf-9592-4298-9c8f-a59d4ce137d1" controlId="a095e98f-9059-41a3-9a46-28fe59b288c3" controlType="ToolbarItem" controlAction="_appForm_button_58246328488413_click">
            <script><![CDATA[return $form.submit({
  service: 'ModelInstance',
  action: 'TestConnection',
  validate: true
}).then(function() {
  $notify.success('连接成功！')
})]]></script>
        </code>
        <code id="2920cf78-00de-455f-ad49-c5a00d340a30" controlId="b4e66a89-eb88-477e-8c05-f1b794b4396a" controlType="CellControl" controlAction="_appForm_enableCustomModel_valueChanged">
            <script><![CDATA[/**
 *  值改变后事件
 *  @example
 *  //$e.value为当前改变的值
 *  //在列表中$e还包含：
 *  //1. oldValue（修改前的值）
 *  //2. 当前修改的row与column对象
 */
if($e.value == 1){
  var modelGUID = $form.getData("ModelGUID");
  $api.ModelInstance.getModelCodeByModelGUID({modelGUID:modelGUID}).then(function(res){
    $form.setData({"CustomModelCode":res})
  })
}
else{
  $form.setData({"CustomModelCode":""})
}]]></script>
        </code>
        <code id="c7751c98-37d4-4e49-8a07-1af73a6ff7ac" controlId="cc34be27-79b2-427d-b3a9-71ac827196c0" controlType="ToolbarItem" controlAction="_appForm_button_58240541268407_click">
            <script><![CDATA[$page.redirect('/std/42000501/08dc6598-55f3-4e3b-838b-f55241c4f5ae')]]></script>
        </code>
    </codes>
    <apis>
        <api functionCode="42000501" service="ModelInstance" action="getModelCodeByModelGUID" type="0" apiSourceType="" />
        <api functionCode="42000501" service="ModelInstance" action="SaveModelInstance" type="0" />
        <api functionCode="42000501" service="ModelInstance" action="TestConnection" type="0" />
    </apis>
    <flows />
    <dependentScripts>
        <dependentScript scriptGuid="08dc7169-ed5f-449b-8d94-281697aa1ce2" dependentId="$ModelContext" />
        <dependentScript scriptGuid="08dc7878-4a0c-44ff-8101-c2b05c38c359" dependentId="$Utility" />
    </dependentScripts>
    <dependentUrls>
        <dependentUrl value="/std/42000501/08dc6598-55f3-4e3b-838b-f55241c4f5ae" />
    </dependentUrls>
    <dependentLangs />
    <dependentResources />
    <components />
    <workflow enabled="false" />
</form>