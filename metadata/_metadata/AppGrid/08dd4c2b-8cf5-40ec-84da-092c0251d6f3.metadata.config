<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2025-02-13T12:40:08Z" modifiedBy="李昂" modifiedOn="2025-03-18T14:46:41.3170326+08:00" metadataStatus="Product" name="提示词评测结果列表控件" functionPageId="08dd4c2b-8cf3-40b2-8632-adebc3b4173f" description="" htmlCache="default" enableUserSettings="false" gridId="08dd4c2b-8cf5-40ec-84da-092c0251d6f3" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" showTitle="false" entityName="gpt_PromptEvalResult" entityId="08dd4c26-3bb7-4724-89ea-4879e39c666d" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd4c2b-8cf5-48ab-8e3f-a59f676aa873" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dd4c2b-8cf4-4b0d-89ee-1c04b45545db" title=" 更多操作" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="mMore" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="08dd4c2b-8cf4-4b71-87dc-f3ac82d4e025" title="打印" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mPrint" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.print(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="08dd4c2b-8cf4-4ba9-8fce-0e2cd9ac706c" title="导出" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mExport" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.exportExcel(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd4c2b-8cf5-48d7-8af3-726e3b6b561b" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dd4c2b-8cf5-48ff-8c26-a22138778b86" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition isCurrent="true" id="f094a819-05d9-4b72-9770-640662221a2f" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="0d8448b8-5812-42ab-aed3-8755f79ea85b" ref="EvalConclusion" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
            </conditions>
            <components>
                <component id="0d8448b8-5812-42ab-aed3-8755f79ea85b" name="EvalConclusion" metadataStatus="Product">
                    <label title="评测结论" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="EvalConclusion" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="true" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <options>
                            <option value="通过" text="通过" isDefault="false" disabled="false" />
                            <option value="不通过" text="不通过" isDefault="false" disabled="false" />
                        </options>
                    </comboBox>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs />
            <groups />
        </rule>
        <views>
            <view xmlAttributeId="08dd4c2b-8cf4-4f11-86f3-9531b0d30809" viewId="08dd4c2b-8cf5-4172-8370-63ff05b9506a" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dd4c26-3bb7-4724-89ea-4879e39c666d" isLookup="false">
                <dataSource keyName="PromptEvalResultGUID" entity="gpt_PromptEvalResult" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9ACgJwPYFtkFEA3AQzACUQBnAVzGgDoBBGa0imu2Yy2Z6V8lVrQANAFgAUElSZcBEoI4MAQsWiQAFgDksXHqvXas4qcnTY80ImyF16AYQ1qA4gFUAkgBE9sRy4+eJtLmclYK7ML01mD2WAB2kGDUlACW8T7RsQlJqfFBZrKW0RF20QAq3ADWbl4ZChWU1QH5MhbyNkpR9VUUkFgYACY13tyw5T1Q/UPNksGF7YqR+AAeUNTQaXFlKTggGauQ65vbuy0hReG2DCuIUNAgAyVwozd3D09n82EdkV+xA3tRn8sADPm1vos7F8tMRdj5obCQGDQsUrvQGpUYXDRhisUjZgVwajOmUsJUQFssNBSD5SeTKdSwMiLj87PYMCA1A9hj52Zz7tMvMyFk8HByuQM8bzxQK8cKIaK+RKToCeEqBSr5cTIgBZEEpABmKW5AR8eoGhuNgsCBNaKMunXNloeUtGTqNLsRWoduv1HoGKrNfqtmtt5xFaK+2roPKBRJ9MdNEgAYhYEIT7ay4IweHN41nYLAJAB1DQgDmwAAUAEYALzVgCUQA=]]></command>
                    <fields>
                        <field name="ActualResult" allowPopulate="true" entity="gpt_PromptEvalResult" field="ActualResult" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="BatchNo" allowPopulate="true" entity="gpt_PromptEvalResult" field="BatchNo" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ChatGUID" allowPopulate="true" entity="gpt_PromptEvalResult" field="ChatGUID" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="CreatedName" allowPopulate="false" entity="gpt_PromptEvalResult" field="CreatedName" entityAlias="gpt_PromptEvalResult" metadataStatus="Product" />
                        <field name="CreatedTime" allowPopulate="false" entity="gpt_PromptEvalResult" field="CreatedTime" entityAlias="gpt_PromptEvalResult" metadataStatus="Product" />
                        <field name="EvalConclusion" allowPopulate="true" entity="gpt_PromptEvalResult" field="EvalConclusion" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExecutionTime" allowPopulate="true" entity="gpt_PromptEvalResult" field="ExecutionTime" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExpectedResult" allowPopulate="true" entity="gpt_PromptEvalResult" field="ExpectedResult" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PromptCode" allowPopulate="true" entity="gpt_PromptEvalResult" field="PromptCode" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PromptEvalResultGUID" allowPopulate="true" entity="gpt_PromptEvalResult" field="PromptEvalResultGUID" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PromptName" allowPopulate="true" entity="gpt_PromptEvalResult" field="PromptName" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TaskName" allowPopulate="true" entity="gpt_PromptEvalResult" field="TaskName" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TokenTotal" allowPopulate="true" entity="gpt_PromptEvalResult" field="TokenTotal" entityAlias="gpt_PromptEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="ActualResult" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ActualResult" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="BatchNo" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="BatchNo" entity="gpt_PromptEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="ChatGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ChatGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_PromptEvalResult" entityType="0" attributeType="日期与时间" />
                        <availableField name="EvalConclusion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalConclusion" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(32)）" />
                        <availableField name="EvalTaskGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="EvalTaskRecordGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskRecordGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="ExecutionTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ExecutionTime" entity="gpt_PromptEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="ExpectedResult" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ExpectedResult" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_PromptEvalResult" entityType="0" attributeType="日期与时间" />
                        <availableField name="PromptCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PromptCode" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="PromptEvalResultGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PromptEvalResultGUID" entity="gpt_PromptEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="PromptName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PromptName" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="TaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskName" entity="gpt_PromptEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="TokenTotal" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TokenTotal" entity="gpt_PromptEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_PromptEvalResult" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd4c2b-8cf6-414b-84fb-11d1d004d92b" id="08dd4c26-3bb7-4724-89ea-4879e39c666d" name="gpt_PromptEvalResult" primaryField="PromptEvalResultGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_PromptEvalResult.EvalTaskRecordGUID" operatorType="eq" id="91e5d86c-7a79-467e-a358-819939f88848" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:oid]</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="TaskName" allowUnRef="false" allowRef="false">
                        <column title="任务名称" width="120" field="TaskName" allowEdit="false" customizeReferenceable="false" id="79609511-4b51-4f3e-b430-fbdecc81beff" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="7c61e5a6-3f88-4277-a524-c26b433af0b4" field="TaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="BatchNo" allowUnRef="false" allowRef="false">
                        <column title="执行批次" width="120" field="BatchNo" allowEdit="false" customizeReferenceable="false" id="5b7510c0-f1aa-4683-8b83-a564f1eed390" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="6e12cb5d-8cb0-4f35-80cc-aed9c2f958ce" field="BatchNo" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </component>
                    <component name="PromptCode" allowUnRef="false" allowRef="false">
                        <column title="提示词编码" width="120" field="PromptCode" allowEdit="false" customizeReferenceable="false" id="9bf0e5e1-67b8-4d75-b97a-735e50522e8a" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="f88cbbd8-3612-41c3-b21a-cfb44e15aad5" field="PromptCode" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="PromptName" allowUnRef="false" allowRef="false">
                        <column title="提示词名称" width="120" field="PromptName" allowEdit="false" customizeReferenceable="false" id="dbff362b-5b25-4ee8-b2b0-1d31bd310849" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="8496c1a5-2ada-41eb-8f9f-13c67c0a34a4" field="PromptName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ExpectedResult" allowUnRef="false" allowRef="false">
                        <column title="预期输出" width="120" field="ExpectedResult" allowEdit="false" customizeReferenceable="false" id="31b07f81-f16f-444a-a087-f74f19c3953b" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="098b70c3-06e6-4ac4-8738-78ab323bf12f" field="ExpectedResult" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ActualResult" allowUnRef="false" allowRef="false">
                        <column title="实际输出" width="120" field="ActualResult" allowEdit="false" customizeReferenceable="false" id="b46048f8-649a-4e07-8b6d-23f475b5f929" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="1f32c9d0-a275-4acb-9c3d-e205c6585891" field="ActualResult" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="EvalConclusion" allowUnRef="false" allowRef="false">
                        <column title="评测结果" width="120" field="EvalConclusion" allowEdit="false" customizeReferenceable="false" id="570f9c58-f1c3-4378-abcd-e142690ca1a5" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="14b29258-5d8e-4151-ab64-4153cfd3141d" field="EvalConclusion" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="32" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ChatGUID" allowUnRef="false" allowRef="false">
                        <column title="会话ID" width="120" field="ChatGUID" allowEdit="false" customizeReferenceable="false" id="196fa3b4-7d14-4b4c-a6de-e16ce57bde17" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="792b6daf-7ac2-4ea8-855c-82c51b97ec95" field="ChatGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000701/08dc9657-e048-48d3-87ea-5e10b589949d" id="08dc9657-e048-48d3-87ea-5e10b589949d" itemId="2f17c71f-f573-41cf-80ca-0867206c9362" metadataStatus="Product">
                                    <options>
                                        <option key="pageType" value="page" />
                                        <option key="jumpMode" value="blank" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="chat_guid" value="ChatGUID" />
                                        <param type="text" key="assistant_guid" value="7a3f4310-125c-11ef-83d7-00155d822d63" />
                                        <param type="text" key="skill_guid" value="7ae7118b-41d6-46a4-8809-5e8d88225d57" />
                                        <param type="data" key="chat_time" value="CreatedTime" />
                                        <param type="data" key="chat_user" value="CreatedName" />
                                    </params>
                                    <events />
                                </behavior>
                            </behaviors>
                        </column>
                    </component>
                    <component name="ExecutionTime" allowUnRef="false" allowRef="false">
                        <column title="执行耗时" width="120" field="ExecutionTime" allowEdit="false" customizeReferenceable="false" id="5afa6871-5182-4c0d-bab3-d27692b3b5f9" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="f65a6e5f-1d30-490d-a266-958494536ddf" field="ExecutionTime" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </component>
                    <component name="TokenTotal" allowUnRef="false" allowRef="false">
                        <column title="消耗Token总数" width="120" field="TokenTotal" allowEdit="false" customizeReferenceable="false" id="70464b4a-57fb-4c88-b3de-ec80d32c204e" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="12153e0f-ff4a-41d0-b986-e06d0c08d807" field="TokenTotal" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="PromptEvalResultGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="TaskName" width="120" />
                        <component ref="BatchNo" width="120" />
                        <component ref="PromptCode" width="120" />
                        <component ref="PromptName" width="120" />
                        <component ref="ExpectedResult" width="120" />
                        <component ref="ActualResult" width="120" />
                        <component ref="EvalConclusion" width="120" />
                        <component ref="ChatGUID" width="120" />
                        <component ref="ExecutionTime" width="120" />
                        <component ref="TokenTotal" width="120" />
                    </columnRefs>
                    <sorts />
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="de6047e7-6d62-4062-8096-5cc99d09d508" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>