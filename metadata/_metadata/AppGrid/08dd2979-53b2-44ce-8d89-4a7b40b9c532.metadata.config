<?xml version="1.0" encoding="utf-8"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" metadataversion="3.0" createdBy="李昂" createdOn="2024-12-31T08:58:42Z" modifiedBy="李昂" modifiedOn="2025-03-18T14:48:06.8555188+08:00" metadataStatus="Product" name="检查方案测评结果列表控件" functionPageId="08dd2979-53b1-4fc2-898a-0610aa10d20d" description="" htmlCache="default" enableUserSettings="false" gridId="08dd2979-53b2-44ce-8d89-4a7b40b9c532" application="4200" isSeparatedLayout="true" inheritFrom="00000000-0000-0000-0000-000000000000" enableInherited="false" metaDataExtendType="None">
    <codes />
    <apis />
    <flows />
    <dependentScripts />
    <dependentUrls />
    <dependentLangs />
    <dependentResources />
    <layout autoLoad="true" templateStyle="default" pageStyle="default" pageSize="20" totalCountingMode="0" showListHeader="true" searchResultHighlight="false" title="" showTitle="false" entityName="gpt_PlanEvalResult" entityId="08dd265b-613e-43b1-8d8f-b3b60aef791d" toolBarLeftItemDisplayMode="gridItemSelected" globalUseDataRights="false" rowUseDataRights="false" rowButtonStyle="0" isDisableWildcard="false" isUnionSearch="false" listHeaderCategory="Advanced" viewListDisplayType="Menu" autoHeight="true" virtualScroll="false" projectFilter="false" projectFilterType="1" projectDisplayType="5" projectFilterLabel="项目" projectFilterMethod="4" projectFilterInherit="0" projectAllowSearch="true" projectEnableLinkage="false" secondProjectDisplayType="0" fixedTableHead="false" rowSortable="false" tipsType="0" enableCustomSearchLayout="true" showVerticalLine="false" showViewSummaryData="false" borderStyle="Default">
        <toolbars>
            <toolbar toolbarId="08dd2979-53b2-4545-8a5d-2a7d60d8ead6" type="global" templateStyle="default">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items>
                            <item itemId="08dd2979-53b2-4130-80de-4d3e128e858b" title=" 更多操作" isHighlight="false" type="menu" iconClassUrl="" iconClass="" id="mMore" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                <items>
                                    <item itemId="08dd2979-53b2-4189-810e-c2301c952a3f" title="打印" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mPrint" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.print(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                    <item itemId="08dd2979-53b2-41bf-8ca3-3259184e644d" title="导出" isHighlight="false" type="button" iconClassUrl="" iconClass="" id="mExport" rowButtonStyle="2" isMenuButton="false" switchState="false" isHidden="false" isDisabled="false" metadataStatus="Product" addRowPosition="Default" colorType="default">
                                        <items />
                                        <events>
                                            <event name="onclick" functionName="Mysoft.Map6.UI.Template.Grid.exportExcel(e)" enabled="true" metadataStatus="Product" />
                                        </events>
                                        <customProps />
                                        <standardBehaviorOptions />
                                    </item>
                                </items>
                                <events />
                                <customProps />
                                <standardBehaviorOptions />
                            </item>
                        </items>
                    </group>
                </groups>
                <events />
            </toolbar>
            <toolbar toolbarId="08dd2979-53b2-456b-8901-6b030fff8773" type="row" templateStyle="row">
                <groups>
                    <group align="left">
                        <items />
                    </group>
                    <group align="center">
                        <items />
                    </group>
                    <group align="right">
                        <items />
                    </group>
                </groups>
                <events />
            </toolbar>
        </toolbars>
        <events />
        <filter filterId="08dd2979-53b2-4591-82bc-abf88f74eb98" searchType="0" enableCriteriaSave="true" enableCriteriaDisplay="true" isExpandable="true" autoExpand="false" quickIsExpandable="true" quickAutoExpand="false" hideSaveSearchBtn="false" enableMemory="false">
            <style labelWidth="91" />
            <conditions>
                <condition isCurrent="true" id="11171416-0bf3-4ce2-addd-abd4d7304a6d" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="1" metadataStatus="Product">
                    <component id="4c7df828-6abe-45d0-aec2-2e1874e27ca8" ref="EvalConclusion" />
                    <template><![CDATA[]]></template>
                    <searchType>eq</searchType>
                </condition>
                <condition id="de0d4fbe-7b27-4d6a-9bd5-c534fed65534" showAllItems="false" isEncryption="false" unencryptionSide="0" layout="horizontal" unencryptedLength="0" visible="true" disableUserHide="0" metadataStatus="Product">
                    <component id="ec5922c4-f727-4b2e-bb5f-5f14aa73ffed" ref="RuleName" />
                    <template><![CDATA[]]></template>
                    <searchType>like</searchType>
                </condition>
            </conditions>
            <components>
                <component id="4c7df828-6abe-45d0-aec2-2e1874e27ca8" name="EvalConclusion" metadataStatus="Product">
                    <label title="评测结论" visible="true" titleShowStyle="show" isMoreCondition="false" conditionType="" requirementLevel="none" />
                    <comboBox field="EvalConclusion" errorMode="default" readonlyMode="none" requirementLevel="none" templateStyle="flat" placeholder="" isHidden="false" metadataStatus="Product" optionDataType="textValue" optionsType="options" allowEmpty="false" emptyIsDefault="false" valueFromSelect="false" showLongText="false" disableFolderSelect="false" multiSelect="false" allowClear="true" filterable="false" operatorType="eq" allowDeselectDisabledOption="false">
                        <customProps />
                        <events />
                        <options>
                            <option value="通过" text="通过" isDefault="false" disabled="false" />
                            <option value="结论不通过" text="结论不通过" isDefault="false" disabled="false" />
                            <option value="详情不通过" text="详情不通过" isDefault="false" disabled="false" />
                        </options>
                    </comboBox>
                </component>
                <component id="ec5922c4-f727-4b2e-bb5f-5f14aa73ffed" name="RuleName" metadataStatus="Product">
                    <label title="规则名称" visible="true" titleShowStyle="show" isMoreCondition="false" requirementLevel="none" />
                    <textBox field="RuleName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" templateStyle="flat" isHidden="false" metadataStatus="Product" isBold="false" maxLength="128" showMaxLength="false">
                        <customProps />
                        <events />
                    </textBox>
                </component>
            </components>
            <events />
        </filter>
        <quickFinds />
        <langs />
        <rule>
            <configs>
                <config id="be6535e7-d700-49de-9aaa-55ef9d21d231" title="新规则" controlId="2e54aa8d-3237-4a0b-9304-3398a5aecea5" controlType="column" controlSubType="" controlProp="fontColor" controlName="ExpectedResultAmount" metadataStatus="Product">
                    <handles>
                        <handle handleId="74c2de92-4c82-4dfd-8109-4a95d756e2b3" ruleId="322089e2-3c79-4215-837a-1ce90d26e029" action="setcolor" value="#F86161" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="008693a9-30b4-4420-96eb-bce7fa77c12f" title="新规则" controlId="5587f96e-fba3-4140-b888-18e89a058921" controlType="column" controlSubType="" controlProp="fontColor" controlName="ActualResultStatus" metadataStatus="Product">
                    <handles>
                        <handle handleId="77f11ee3-af26-458c-9b12-6c7e2a43f428" ruleId="322089e2-3c79-4215-837a-1ce90d26e029" action="setcolor" value="#F86161" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="bf4f12c2-fe15-41a4-9e01-3f8d794da9b0" title="新规则" controlId="2b440f96-042e-4206-a5da-6ca0f65a6a45" controlType="column" controlSubType="" controlProp="fontColor" controlName="ExpectedFailResult" metadataStatus="Product">
                    <handles>
                        <handle handleId="54946f23-9197-4de3-93bf-0e4016c3aa2e" ruleId="f3d411b5-bfbd-457b-8024-7a0eec432774" action="setcolor" value="#F86161" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="fda2f92e-117b-4313-b66d-f5c7289a740a" title="新规则" controlId="11a9b851-e30d-45b6-9f5a-c611b9084ffd" controlType="column" controlSubType="" controlProp="fontColor" controlName="ActualNotPassResult" metadataStatus="Product">
                    <handles>
                        <handle handleId="c4e543c3-feab-4386-ac3e-e0919d4bb13d" ruleId="f3d411b5-bfbd-457b-8024-7a0eec432774" action="setcolor" value="#F86161" metadataStatus="Product" />
                    </handles>
                </config>
                <config id="503deccc-149a-432a-a9a3-fe47142e29c8" title="新规则" controlId="722351dc-3cfd-49fe-85aa-713a92c6164a" controlType="column" controlSubType="" controlProp="fontColor" controlName="EvalConclusion" metadataStatus="Product">
                    <handles>
                        <handle handleId="7fc7c387-b701-454c-a886-ca87d203c071" ruleId="322089e2-3c79-4215-837a-1ce90d26e029" action="setcolor" value="#F86161" metadataStatus="Product" />
                        <handle handleId="2fe29a6c-4d6f-42a7-8ecf-4127d57b171d" ruleId="f3d411b5-bfbd-457b-8024-7a0eec432774" action="setcolor" value="#EAA000" metadataStatus="Product" />
                    </handles>
                </config>
            </configs>
            <groups>
                <group id="322089e2-3c79-4215-837a-1ce90d26e029" title="结论不通过" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;EvalConclusion&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;结论不通过&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
                <group id="f3d411b5-bfbd-457b-8024-7a0eec432774" title="详情不通过" rule="{&quot;condition&quot;:&quot;AND&quot;,&quot;rules&quot;:[{&quot;action&quot;:&quot;field&quot;,&quot;field&quot;:&quot;EvalConclusion&quot;,&quot;operator&quot;:&quot;equal&quot;,&quot;type&quot;:&quot;string&quot;,&quot;value&quot;:&quot;详情不通过&quot;,&quot;isEdit&quot;:true,&quot;valueType&quot;:&quot;text&quot;}]}" metadataStatus="Product" />
            </groups>
        </rule>
        <views>
            <view xmlAttributeId="08dd2979-53b2-4331-8564-5e79dc2a1522" viewId="08dd2979-53b2-44f7-8879-f4cef40e4de2" name="所有数据" isDefault="false" isHidden="false" templateStyle="default" metadataStatus="Product" entityId="08dd265b-613e-43b1-8d8f-b3b60aef791d" isLookup="false">
                <dataSource keyName="PlanEvalResultGUID" entity="gpt_PlanEvalResult" withNoLock="true" mode="1">
                    <command type="" queryDb=""><![CDATA[MoUwNiDGAuAEDmAHaB9ACmAhgOwKIDdMwAlEAZwFcxoA6AQRgqIDkB7aNTMs0y62LrAbQmYNhy49yVaABoAsACgkqDDgJFeM+o03TqwaJhFkBp4aK0GjJhcuToseQiX20AQscgALNmdie0D5sdiqO6i5WtADC3sYA4gCqAJIAIv6xCSmpoQ5qznp8tBpg0azYkGAUZACW5f4lZRVVteW5qk4lUTQlACpcANZJaQ0u/WRD2e3hBa5FPWODpJCsAE4AJsPpgn1LUGubU0ph+V1uPQAeUBTQddi9NQC2IA1XkDd3D8/Tp5HnuBdEFBoCB1gAxTA1OYyV5AmCgiFQqI/Tp/eYAuEg9ZROiPVgUbBwHaA4GgnF4glyY55VGFbT5MrrF6CBmsJkoiJ06g0fLMTDPfy8/kgDmzbrEKggPkCwQSiDSkXUjqc6Hc8YDBX+dUK0Vnea9VgDED3dhELWG40GoxgXVo7TRVYgYygrYZR3Ow5pW1cmLurGawQOp3+4Xe1W+4Ogr7M0xBj3RsPdACybJqADMai7sv4U+t05nPTklTM9dpc/nQQHTOWM5XQ8Xfj6aDWC9Gc6na+sEw3aeGeb2oq6WQO3K7FGDVqxHggaSqokJTCcR0VYLBFAB1bwgR2wAAUAEYALz7gCUQA]]></command>
                    <fields>
                        <field name="ActualNotPassResult" allowPopulate="true" entity="gpt_PlanEvalResult" field="ActualNotPassResult" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ActualResultStatus" allowPopulate="true" entity="gpt_PlanEvalResult" field="ActualResultStatus" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="BatchNo" allowPopulate="true" entity="gpt_PlanEvalResult" field="BatchNo" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ChatGUID" allowPopulate="true" entity="gpt_PlanEvalResult" field="ChatGUID" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="CreatedName" allowPopulate="false" entity="gpt_PlanEvalResult" field="CreatedName" entityAlias="gpt_PlanEvalResult" metadataStatus="Product" />
                        <field name="CreatedTime" allowPopulate="false" entity="gpt_PlanEvalResult" field="CreatedTime" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="EvalConclusion" allowPopulate="true" entity="gpt_PlanEvalResult" field="EvalConclusion" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExecutionTime" allowPopulate="false" entity="gpt_PlanEvalResult" field="ExecutionTime" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExpectedFailResult" allowPopulate="true" entity="gpt_PlanEvalResult" field="ExpectedFailResult" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="ExpectedResultAmount" allowPopulate="true" entity="gpt_PlanEvalResult" field="ExpectedResultAmount" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PlanEvalResultGUID" allowPopulate="true" entity="gpt_PlanEvalResult" field="PlanEvalResultGUID" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="PlanName" allowPopulate="true" entity="gpt_PlanEvalResult" field="PlanName" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="RuleName" allowPopulate="true" entity="gpt_PlanEvalResult" field="RuleName" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TaskName" allowPopulate="true" entity="gpt_PlanEvalResult" field="TaskName" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                        <field name="TokenTotal" allowPopulate="true" entity="gpt_PlanEvalResult" field="TokenTotal" entityAlias="gpt_PlanEvalResult" metadataStatus="Product">
                            <fields />
                        </field>
                    </fields>
                    <availableFields>
                        <availableField name="ActualNotPassResult" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ActualNotPassResult" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ActualResultStatus" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ActualResultStatus" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="BatchNo" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="BatchNo" entity="gpt_PlanEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="ChatGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ChatGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="CreatedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedName" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="CreatedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="CreatedTime" entity="gpt_PlanEvalResult" entityType="0" attributeType="日期与时间" />
                        <availableField name="EvalConclusion" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalConclusion" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="EvalTaskGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="EvalTaskRecordGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="EvalTaskRecordGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="ExecutionTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ExecutionTime" entity="gpt_PlanEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="ExpectedFailResult" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ExpectedFailResult" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ExpectedResultAmount" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ExpectedResultAmount" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(max)）" />
                        <availableField name="ModifiedGUID" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="ModifiedName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedName" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(128)）" />
                        <availableField name="ModifiedTime" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="ModifiedTime" entity="gpt_PlanEvalResult" entityType="0" attributeType="日期与时间" />
                        <availableField name="PlanCode" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PlanCode" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="PlanEvalResultGUID" isRedundance="false" isPrimaryAttribute="true" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PlanEvalResultGUID" entity="gpt_PlanEvalResult" entityType="0" attributeType="Guid" />
                        <availableField name="PlanName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="PlanName" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="RuleName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="RuleName" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="TaskName" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TaskName" entity="gpt_PlanEvalResult" entityType="0" attributeType="文本（nvarchar(64)）" />
                        <availableField name="TokenTotal" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="TokenTotal" entity="gpt_PlanEvalResult" entityType="0" attributeType="整数" />
                        <availableField name="VersionNumber" isRedundance="false" isPrimaryAttribute="false" isEncryption="false" unencryptionSide="0" unencryptedLength="0" aliasName="VersionNumber" entity="gpt_PlanEvalResult" entityType="0" attributeType="时间戳" />
                    </availableFields>
                    <fixedSortings />
                    <summaries />
                    <diagrams>
                        <diagram xmlAttributeId="08dd2979-53b2-4663-8a3c-39f716abbf3a" id="08dd265b-613e-43b1-8d8f-b3b60aef791d" name="gpt_PlanEvalResult" primaryField="PlanEvalResultGUID" authControlType="0" projFilter="false" projViewFilter="false" projInterfaceFilter="false" projectFilterType="0" resFilter="false" type="0" isMaster="true" joinType="0" conditionType="0" isUsed="false" metadataStatus="Product" enableGroup="false">
                            <conditions>
                                <condition field="gpt_PlanEvalResult.EvalTaskRecordGUID" operatorType="eq" id="c79bc403-4eaf-424a-ad57-434a120db8cf" dataType="string" valueType="1" leftValueType="field">
                                    <actions />
                                    <Value xsi:type="xsd:string">[query:oid]</Value>
                                </condition>
                            </conditions>
                            <resourceFilters />
                            <projectInterfaceFilters />
                        </diagram>
                    </diagrams>
                    <performanceOptimizeHints />
                </dataSource>
                <components>
                    <component name="TaskName" allowUnRef="false" allowRef="false">
                        <column title="任务名称" width="120" field="TaskName" allowEdit="false" customizeReferenceable="false" id="5846ff9e-4888-4b0f-98da-ca5892e21a48" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="92fc781b-20e1-4318-ad59-192efe7840f7" field="TaskName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="PlanName" allowUnRef="false" allowRef="false">
                        <column title="检查方案名称" width="120" field="PlanName" allowEdit="false" customizeReferenceable="false" id="05951092-f400-4dbf-967a-caa9ff050ca1" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="7d0c74cc-7846-4bb3-a857-91ac8f23e914" field="PlanName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="RuleName" allowUnRef="false" allowRef="false">
                        <column title="检查规则名称" width="120" field="RuleName" allowEdit="false" customizeReferenceable="false" id="8da181fd-a659-48cd-b6fc-6482c55ea947" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="4b24e10c-307a-4399-aba4-9db2f388ece0" field="RuleName" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="64" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ExpectedResultAmount" allowUnRef="false" allowRef="false">
                        <column title="预期通过结果" width="120" field="ExpectedResultAmount" allowEdit="false" customizeReferenceable="false" id="2e54aa8d-3237-4a0b-9304-3398a5aecea5" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="611ebd49-9749-4417-b788-9df1e0f6a0f6" field="ExpectedResultAmount" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ActualResultStatus" allowUnRef="false" allowRef="false">
                        <column title="实际通过结果" width="120" field="ActualResultStatus" allowEdit="false" customizeReferenceable="false" id="5587f96e-fba3-4140-b888-18e89a058921" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="1569e521-3ff7-4cec-869b-a9048e5d7bcc" field="ActualResultStatus" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ExpectedFailResult" allowUnRef="false" allowRef="false">
                        <column title="预期不通过结果" width="120" field="ExpectedFailResult" allowEdit="false" customizeReferenceable="false" id="2b440f96-042e-4206-a5da-6ca0f65a6a45" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="cd64fe2f-bf0e-40a8-8f12-81c58069e0b4" field="ExpectedFailResult" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ActualNotPassResult" allowUnRef="false" allowRef="false">
                        <column title="实际不通过结果" width="120" field="ActualNotPassResult" allowEdit="false" customizeReferenceable="false" id="11a9b851-e30d-45b6-9f5a-c611b9084ffd" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="bb4c94a7-f23e-4e48-ac2f-d1e189d72761" field="ActualNotPassResult" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="EvalConclusion" allowUnRef="false" allowRef="false">
                        <column title="评测结论" width="120" field="EvalConclusion" allowEdit="false" customizeReferenceable="false" id="722351dc-3cfd-49fe-85aa-713a92c6164a" isHidden="false" disableUserHide="0" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textArea id="bba186dc-0d29-4795-a22f-77abd9125feb" field="EvalConclusion" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" height="60" maxLength="0" autoHeight="false" minRows="2" maxRows="6">
                                <customProps />
                                <events />
                            </textArea>
                            <behaviors />
                        </column>
                    </component>
                    <component name="ChatGUID" allowUnRef="false" allowRef="false">
                        <column title="会话ID" width="120" field="ChatGUID" allowEdit="false" customizeReferenceable="false" id="96f4a2e4-5693-410a-9720-ebe938475554" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="text" align="left" dataSourceType="Field" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <textBox id="4c52c997-2cf0-446f-8e4a-6eee34d5bf71" field="ChatGUID" errorMode="default" readonlyMode="none" defaultValue="" requirementLevel="none" isHidden="false" metadataStatus="Product" isBold="false" maxLength="0" showMaxLength="false">
                                <customProps />
                                <events />
                            </textBox>
                            <behaviors>
                                <behavior target="self" targetDisplayType="fixedWidth" type="page" url="/std/42000701/08dc9657-e048-48d3-87ea-5e10b589949d" id="08dc9657-e048-48d3-87ea-5e10b589949d" itemId="cec6f979-5e71-445c-9af8-10e15ef2be87" metadataStatus="Product">
                                    <options>
                                        <option key="pageType" value="page" />
                                        <option key="jumpMode" value="blank" />
                                        <option key="slipTitleMode" value="show" />
                                        <option key="slipTitleContent" value="" />
                                    </options>
                                    <params>
                                        <param type="data" key="chat_guid" value="ChatGUID" />
                                        <param type="text" key="assistant_guid" value="7a3f4310-125c-11ef-83d7-00155d822d63" />
                                        <param type="text" key="skill_guid" value="23142564-1cf8-4f64-ab98-e49d33a156df" />
                                        <param type="data" key="chat_time" value="CreatedTime" />
                                        <param type="data" key="chat_user" value="CreatedName" />
                                    </params>
                                    <events />
                                </behavior>
                            </behaviors>
                        </column>
                    </component>
                    <component name="ExecutionTimes" allowUnRef="false" allowRef="false">
                        <column title="执行时长" width="120" field="ExecutionTimes" allowEdit="false" customizeReferenceable="false" id="dff660b2-f28c-4492-841c-e1762692e98d" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" dataSourceType="CalculateColumn" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="136e9972-b4b2-4449-a2af-96e250305aa9" field="ExecutionTimes" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" unitTextBizParam="" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <subControlFormula text="ROUND(gpt_PlanEvalResult.ExecutionTime/1000,3) &amp; 's'" category="Simple" originalSqlExpression="CONCAT((CONCAT((ROUND((((gpt_PlanEvalResult.ExecutionTime)/(1000))),3)),'')),('s'))" fieldSqlExpression="CONCAT((CONCAT((ROUND((((gpt_PlanEvalResult.ExecutionTime)/(1000))),3)),'')),('s'))" aggreSqlExpression="CONCAT((ROUND((((gpt_PlanEvalResult.ExecutionTime)/(1000))),3)),())" fontExpression="f.connect(f.tostring(f.round(f.divide(ExecutionTime,1000),3)),&quot;s&quot;)" queryMode="Subquery" tableName="gpt_PlanEvalResult" expressionValueClrType="System.String" fieldName="ExecutionTimes">
                                <Id>08dd2efb-eb02-46db-814e-81497e40b643</Id>
                                <RelationTables>
                                    <RelationTable name="gpt_PlanEvalResult" id="08dd265b-613e-43b1-8d8f-b3b60aef791d">
                                        <RelationFields>
                                            <RelationField name="ExecutionTime" id="08dd2efa-f269-41e2-8290-322dfe9d9247" aliasName="ExecutionTime" type="int32" />
                                        </RelationFields>
                                    </RelationTable>
                                </RelationTables>
                                <databaseSqls />
                            </subControlFormula>
                            <behaviors />
                        </column>
                    </component>
                    <component name="BatchNo" allowUnRef="false" allowRef="false">
                        <column title="批次号" width="120" field="BatchNo" allowEdit="false" customizeReferenceable="false" id="7ef6bb54-e987-4a28-b8d0-d479f0a16e6c" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="left" allowSort="true" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="e30d0949-1965-4812-9152-6c6af0078862" field="BatchNo" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </component>
                    <component name="TokenTotal" allowUnRef="false" allowRef="false">
                        <column title="消耗Token数" width="120" field="TokenTotal" allowEdit="false" customizeReferenceable="false" id="84136689-9c33-4853-ba8f-accf3da837bc" isHidden="false" disableUserHide="1" fontColor="#222222" enableRollUp="false" enableUpdateRollUp="false" exportIgnore="false" hasExportIgnoreRule="false" printIgnore="false" dataType="number" align="right" allowSort="false" isSummaryColumn="false" isBold="false" tips="" metadataStatus="Product" enableAI="false">
                            <customProps />
                            <spinner id="027e8850-35f1-4f77-9736-25fce8f305c5" field="TokenTotal" errorMode="default" readonlyMode="none" defaultValue="0" requirementLevel="none" templateStyle="" isHidden="false" metadataStatus="Product" precision="0" precisionType="0" rounding="0" roundingType="0" showThousandths="true" isBold="false" allowZero="true" allowEmpty="false" showPercentage="false" unitText="" unitTextType="0" minValue="-99999999999.99" maxValue="99999999999.99" minOperatorType="ge" maxOperatorType="le">
                                <customProps />
                                <events />
                            </spinner>
                            <behaviors />
                        </column>
                    </component>
                </components>
                <linkageRules />
                <layout hideColumnHeader="false" hideRefreshColumn="false" hideToolbar="false" rowToolbarWidth="0" frozenToolbar="true" idField="PlanEvalResultGUID" multiSelect="false" showIndexColumn="true" isSimulationEditing="false" allowEdit="false" fixedColumns="0" maxWrapRow="0" editMode="0" autoInsertRow="false" allowHeaderWrap="false" isKeepSelectedRecordOnPage="false">
                    <columnRefs>
                        <component ref="TaskName" width="120" />
                        <component ref="BatchNo" width="120" />
                        <component ref="PlanName" width="120" />
                        <component ref="RuleName" width="120" />
                        <component ref="ExpectedResultAmount" width="120" />
                        <component ref="ActualResultStatus" width="120" />
                        <component ref="ExpectedFailResult" width="120" />
                        <component ref="ActualNotPassResult" width="120" />
                        <component ref="EvalConclusion" width="120" />
                        <component ref="ChatGUID" width="120" />
                        <component ref="ExecutionTimes" width="120" />
                        <component ref="TokenTotal" width="120" />
                    </columnRefs>
                    <sorts>
                        <sort field="BatchNo" defaultDirection="asc" isDefault="true" metadataStatus="Product" />
                        <sort field="CreatedTime" defaultDirection="asc" isDefault="false" metadataStatus="Product" />
                        <sort field="ExecutionTime" defaultDirection="desc" isDefault="false" metadataStatus="Product" />
                    </sorts>
                    <fixedFilterConditions />
                    <summaries />
                    <summaryLayout id="8f9fc4a0-b298-4918-8a5a-86434e74d3c5" isHidden="false" metadataStatus="Product" />
                    <events />
                    <attributes />
                    <hiddens />
                </layout>
            </view>
        </views>
    </layout>
</grid>