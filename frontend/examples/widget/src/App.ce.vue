<script setup lang="ts">
import { ref } from 'vue'
import { WidgetProvider, useWidget, type WidgetProps } from '@skyline-ai/widget/vue'
import Next from './components/Next.vue'
import { jsonrepair } from 'jsonrepair'

const { disabled } = defineProps<WidgetProps>()
const data = ref<any>([])
const enableEdit = ref(false)

useWidget({
  onStreamOpen: async ({ stream }) => {
    text.value = ''
    for await (const element of stream) {
      text.value = JSON.stringify(element)
      data.value = element as any
    }
  },
})

const layoutData = ref<{
  layout?: {
    regions: {
      id: string
      name: string
      groups: {
        id: string
        groupName: string
        columnCount: number
        rows: {
          cells: {
            id: string
            colSpan: string
            title: string
            field: string
          }[]
        }[]
      }[]
    }[]
  }
}>({})
watch(data, (v) => {
  if (v.output && typeof v.output === 'string') {
    const i = v.output.indexOf('{')
    if (i > -1) {
      const json = v.output.substring(i, v.output.length - 1)
      const text = jsonrepair(json)
      try {
        const res = JSON.parse(text)
        if (res?.layout) {
          layoutData.value = res
        }
      } catch (e) {}
      console.log(layoutData.value)
    }
  }
})

const text = ref('')
</script>

<template>
  <WidgetProvider class="flex flex-col space-y-1">
    <div class="flex space-x-1">
      <span>I am widget, disabled is {{ disabled }}</span>
      <Next :data="data" />
    </div>
    <div @click="enableEdit = !enableEdit" class="cursor-pointer">
      {{ enableEdit ? '禁用编辑' : '启用编辑' }}
    </div>
    <div v-if="layoutData?.layout?.regions">
      <div v-for="(region, index) in layoutData.layout.regions" :key="index">
        <div class="flex flex-col">
          <div>{{ region.name }}</div>
          <div class="flex flex-col">
            <div v-for="(group, index) in region.groups" :key="index">
              <div class="flex">{{ group.groupName }}</div>
              <div v-for="row in group.rows" class="flex flex-row">
                <div
                  v-for="cell in row.cells"
                  class="box-border flex-[1_1_50%] px-[6px] py-[0] leading-[24px]"
                >
                  <span v-if="cell.title">{{ cell.title }}（{{ cell.field }}）</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--本地mock场景-->
    <div class="flex flex-col">
      <input
        class="my-[4px] ml-[6px] box-border w-[calc(100%_-_6px)] rounded-[4px] border-[1px] border-solid
          border-[#c3c3c3] leading-[24px]"
        v-if="enableEdit && data.chatGUID"
        v-model="data.chatGUID"
      />
      <div v-else>{{ data?.chatGUID }}</div>
      <div class="flex flex-row flex-wrap">
        <div
          v-for="(arr, i) in data?.arguments"
          :key="i"
          class="box-border flex-[1_1_50%] px-[6px] py-[0] leading-[24px]"
        >
          <input
            class="mx-[0] my-[4px] w-full rounded-[4px] border-[1px] border-solid border-[#c3c3c3] leading-[24px]"
            v-if="enableEdit"
            v-model="arr.key"
          />
          <template v-else> {{ arr?.key }}（{{ arr.value }}） </template>
        </div>
      </div>
    </div>
    <!-- <div class="break-words">
      {{ text }}
    </div> -->
  </WidgetProvider>
</template>

<style>
@tailwind base;
@tailwind utilities;
</style>
