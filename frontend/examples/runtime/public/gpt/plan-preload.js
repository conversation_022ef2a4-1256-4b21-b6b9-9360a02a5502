;(function () {
  var script = document.createElement('script')
  script.type = 'text/javascript'
  script.id = 'skyline-gpt'
  script.setAttribute(
    'data-gpt-share',
    'gptm0hpixf84qojx25+xHTg5Yk9BseLX6hfchhvffm6Q6gZ6Ct0+sgi99qbsdkyA8BpCc8JvR1wTK8s59r9Mrk4xBOB2cOembJ4hKMBCeCb0VXROSnKnUGlyQLEVoVBvpT6ZFw1+VVHdPZn5nLhzbM0Yrkx0K2owa2uH/f6I2TKvZkovkdX8',
  )
  script.setAttribute('data-gpt-history', '1')
  script.setAttribute('data-gpt-base-url', 'http://gptengine:5001')
  script.src = ''
  document.body.appendChild(script)
  ;(function () {
    var origin = 'http://localhost:9501'
    
    var i;
    i = document.createElement("script");
    i.setAttribute("type", "module");
    i.setAttribute("crossorigin", "");
    i.setAttribute(
      "src",
      origin + "/gptbuilder/assistant/assets/index-CyTlwYSm.js"
    );
    document.head.appendChild(i);
    i = document.createElement("link");
    i.setAttribute("rel", "modulepreload");
    i.setAttribute("crossorigin", "");
    i.setAttribute(
      "href",
      origin + "/gptbuilder/assistant/assets/vendor-BaYbWmVn.js"
    );
    document.head.appendChild(i);
    i = document.createElement("link");
    i.setAttribute("rel", "stylesheet");
    i.setAttribute("crossorigin", "");
    i.setAttribute(
      "href",
      origin + "/gptbuilder/assistant/assets/vendor-DTI811Ny.css"
    );
    document.head.appendChild(i);
    i = document.createElement("link");
    i.setAttribute("rel", "stylesheet");
    i.setAttribute("crossorigin", "");
    i.setAttribute(
      "href",
      origin + "/gptbuilder/assistant/assets/index-yITzoKcx.css"
    );
    document.head.appendChild(i);
  })()
})()
