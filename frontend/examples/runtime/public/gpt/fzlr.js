(function () {
  var script = document.createElement('script');
  script.type = "text/javascript";
  script.id = "skyline-gpt";
  script.setAttribute("data-gpt-share", "gptlmi74u2juzsssuu+jdkc3peTAAIGZV4gTBx7R275mW0gkBtD+sgi99qbsdkyA8BpCc8JvR1wTK8s59r9Mrk4xBOB2cOembJ4hKMBCeCb0VXROSnKnTP9m2a+oBYqbDGpGDKfH+vxRGkoFOFk7/U98ZSi0EUHfsd6tWW9NWF9yAh2Mt304");
  script.setAttribute("data-gpt-history", "1")
  script.setAttribute("data-gpt-base-url", "http://gptengine:5000")
  script.src = 'http://gptengine:5000/gptbuilder/assistant/index.js?v='.concat(Date.now());
  document.body.appendChild(script);
})();
