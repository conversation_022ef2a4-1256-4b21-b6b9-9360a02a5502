<script lang="ts" setup>
import { ref } from 'vue'
const isFrame = ref(!!window.frameElement || window.parent !== window)
</script>

<template>
  <div class="router" v-if="!isFrame">
    <router-link to="/initToken">init-token</router-link>
    <router-link to="/page">独立页面</router-link>
    <router-link to="/iframe">嵌入页面</router-link>
    <router-link to="/sidebar">侧边栏</router-link>
    <router-link to="/gzt">辅助录入(主页面)</router-link>
    <router-link to="/gztOpen">辅助录入(弹出框)</router-link>
    <router-link to="/callback">辅助录入(超级工作台-主页面)</router-link>
    <router-link to="/callbackTwo">辅助录入(超级工作台-弹框页面)</router-link>
    <router-link to="/otherPage">iframe挂载助手</router-link>
  </div>
  <div class="router-view">
    <router-view />
  </div>
</template>

<style>
*,
body,
html {
  margin: 0;
  padding: 0;
}
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}
.router a {
  margin: 8px 6px;
  font-size: 16px;
  color: #333;
  display: inline-block;
}
.router-view {
  position: absolute;
  top: 60px;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}
</style>
