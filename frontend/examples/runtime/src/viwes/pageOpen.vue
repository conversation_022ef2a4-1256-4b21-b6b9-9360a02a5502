<template>
  <div style="border: 1px solid #999; height: 500px">
    pageOpen
    <iframe
      src="/gzt"
      style="
        width: 100%;
        height: 100%;
        border: none;
        background-color: transparent;
        pointer-events: auto !important;
      "
      allow="*"
    ></iframe>
  </div>
</template>

<script lang="ts">
import GPT from '@skyline/ai'
export default {
  name: 'CallbackPage',
  props: {
    url: {
      type: String,
      default: '',
    },
  },
  setup() {
    // 地址 http://modelingplatform:9300/std/42000101/08dcaa29-4e1c-441e-88fa-a27853a5bc12?mode=3&jumpMode=self&autoTitle=true&_mp=crumbs&title=%E5%8F%91%E5%B8%83&SpaceGUID=08dcab16-02b9-fed5-f934-d4d7d64041c7&oid=08dd1a92-867f-4bb7-8e4c-67169b1357ca&_ct=%E5%8A%A9%E6%89%8B%E5%8F%91%E5%B8%83&_t=1737368731724&_hid=e00bc3b0-7eb9-4cc3-ab25-d54ddc73ae7b
    const init = () => {
      GPT.init('http://localhost:6678/gpt/preload.js').then(() => {
        const context = {
          userGuid: '08dd261f-5856-4a75-8e2c-5afc6d2c652d',
          userCode: 'lin11',
          userName: 'lin11',
          mobile: '15625342622',
          // mock 数据
          customerId: '46b55e4c-810a-4c0d-82a0-b039389cf725',
          customerName: '天际开发者社区',
        }
        GPT.setAccessToken(
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MzczNjY3MzUsIlVzZXJHVUlEIjoiMDhkZDI2MWYtNTg1Ni00YTc1LThlMmMtNWFmYzZkMmM2NTJkIiwiVXNlckNvZGUiOiJsaW4xMSIsIlVzZXJOYW1lIjoibGluMTEiLCJNb2JpbGUiOiIxMzA1ODEyMjk3NyIsIkFwcFR5cGUiOm51bGwsIkFwcENvZGUiOm51bGwsIkFwcE5hbWUiOm51bGwsIkFwcFZlcnNpb24iOm51bGwsIk1vZHVsZUNvZGUiOm51bGwsIk1vZHVsZU5hbWUiOm51bGwsIlBhZ2VOYW1lIjpudWxsLCJQYWdlVXJsIjpudWxsLCJFcnBWZXJzaW9uIjpudWxsLCJNZXRhZGF0YSI6bnVsbCwiQ3VzdG9tZXJHVUlEIjpudWxsLCJDdXN0b21lck5hbWUiOm51bGwsIlRlbmFudENvZGUiOm51bGwsIlRlbmFudE5hbWUiOm51bGx9.bPHCMvr984iXlHb-ExbcuxAAKAChnBoKvWYcXlHwD9M',
        )
        GPT.setContext(context)
        GPT.show()
      })
    }
    const isFrame = !!window.frameElement || window.parent !== window
    if (!isFrame) {
      init()
    }
    return {}
  },
}
</script>
