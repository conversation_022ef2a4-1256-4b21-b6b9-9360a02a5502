<script lang="ts">
import GPT from '@skyline/ai'

export default {
  mounted: () => {
    const token =
      'eyJzaGFyZUNvZGUiOiJzeXN0ZW1fcGxhbitsZzZhMjhrcE5ZNGlCb3NIR0ZzR0djWW1acmhkYmc4MitzZ2k5OXFic2RreUE4QnBDYzhKdlIxd1RLOHM1OXI5TXJrNHhCT0IyY09lbWJKNGhLTUJDZUNiMFZYUk9TbktuVFA5bTJhK29CWXFiREdwR0RLZkgrdnhSR2tvRk9GazcvVTk4WlNpMEVVRis4YVJYMjM4S0Y2UTFtSE15czVQNSIsImJhc2VVcmwiOiJodHRwOi8vZ3B0ZW5naW5lOjUwMDAiLCJqc1VybCI6Imh0dHA6Ly9ncHRlbmdpbmU6NTAwMC9ncHRidWlsZGVyL2Fzc2lzdGFudC9pbmRleC5qcyIsImFjY2Vzc1Rva2VuIjoiZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFjMlZ5UjNWcFpDSTZiblZzYkN3aWRYTmxja052WkdVaU9pSXhJaXdpZFhObGNrNWhiV1VpT2lJeElpd2liVzlpYVd4bElqcHVkV3hzTENKaGNIQlVlWEJsSWpwdWRXeHNMQ0poY0hCRGIyUmxJanB1ZFd4c0xDSmhjSEJPWVcxbElqcHVkV3hzTENKaGNIQldaWEp6YVc5dUlqcHVkV3hzTENKdGIyUjFiR1ZEYjJSbElqcHVkV3hzTENKdGIyUjFiR1ZPWVcxbElqcHVkV3hzTENKd1lXZGxUbUZ0WlNJNmJuVnNiQ3dpY0dGblpWVnliQ0k2Ym5Wc2JDd2laWEp3Vm1WeWMybHZiaUk2Ym5Wc2JDd2liV1YwWVdSaGRHRWlPbTUxYkd3c0ltTjFjM1J2YldWeVIzVnBaQ0k2Ym5Wc2JDd2lZM1Z6ZEc5dFpYSk9ZVzFsSWpwdWRXeHNMQ0owWlc1aGJuUkRiMlJsSWpwdWRXeHNMQ0owWlc1aGJuUk9ZVzFsSWpwdWRXeHNMQ0psZUhBaU9qRTNNek14TXpFek56SjkuWXQ4dDhjRTZQUkZCMjdwUm96clJqUjMyZU8wekZfTkFiQmgyUG9Mc2VmOCIsImhpc3RvcnkiOjF9'
    GPT.init({
      token,
      container: '#gpt',
    }).then(() => {
      console.log('NotifyMounted')
      setTimeout(() => {
        // GPT.plan.query('1cd9e945-c560-4c96-b65b-2eec6ae656c0')
        GPT.plan.query({
          instanceId: '1cd9e945-c560-4c96-b65b-2eec6ae656c0',
          onClose: () => {
            console.log('close')
          },
        })
      }, 1000)
      // GPT.open({
      //   skills: ['stream_callback'],
      //   defaultSkill: 'stream_callback',
      //   // auto: true,
      //   context: {
      //     input: '123',
      //   },
      //   onSkillRunning: (e) => {
      //     console.log(e.message.content)
      //     console.log(JSON.parse(JSON.stringify(e)))
      //   },
      //   onClose: () => {
      //     console.log('close')
      //   },
      // })
    })
  },
}
</script>

<template>
  <div id="gpt" style="width: 400px; height: 800px"></div>
  <div>init-token</div>
</template>
