<template>
  侧边栏
  <div @click="handleClick">智能检查</div>
</template>

<script lang="ts">
import GPT from '@skyline/ai'
const appInfo = {
  app: {
    // 应用id
    appid: 'gptplbck50vee7mqas',
    // 密钥
    appsecret: 'zHct8rdjoZKpZ05ttSguBUh0FumeCbkv',
    // 租户
    tenantCode: 'mysoft',
  },
  userInfo: {
    userGuid: '08dc2616-c1fb-43c2-8a2b-65984ca0a769',
    userCode: 'lin11',
    userName: '林一一',
    mobile: '15625342622',
  },
  // 应用访问地址
  url: 'http://**********:5001/gptbuilder/assistant/index.html?share=gptplbck50vee7mqas+9d2HgXPpe8ROtXdpdeTD8xm6kvtXMovN+sgi99qbsdkyA8BpCc8JvR1wTK8s59r9Mrk4xBOB2cOembJ4hKMBCeCb0VXROSnKnTP9m2a+oBYqbDGpGDKfH+vxRGkoFOFk7/U98ZSi0EUGdMRRgz/bDifJLdakbRRm+&history=1',
}
export default {
  name: 'SidebarPage',
  mounted() {
    fetch('http://**********:5001/Login/GetToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payload: appInfo.userInfo,
        ...appInfo.app,
      }),
    }).then((res) => {
      res.json().then((data) => {
        if (data.success) {
          GPT.init('/gpt/plan-preload.js').then(() => {
            const context = {
              userGuid: '08dc2616-c1fb-43c2-8a2b-65984ca0a769',
              userCode: 'lin11',
              userName: '林一一',
              mobile: '15625342622',
              // mock 数据
              customerId: '46b55e4c-810a-4c0d-82a0-b039389cf725',
              customerName: '天际开发者社区',
            }
            GPT.setAccessToken(data.data.accessToken)
            GPT.setContext(context)
            GPT.show()
          })
        } else {
          alert(data.message)
        }
      })
    })
  },
  methods: {
    handleClick() {
      GPT.plan.query('295ccf70-5d0d-4026-94c6-b2a9b06f7fff')
      // GPT.plan.query({instanceId: '295ccf70-5d0d-4026-94c6-b2a9b06f7fff'})
      // GPT.plan.query({businessId: '295ccf70-5d0d-4026-94c6-b2a9b06f7fff'})
      // GPT.plan.query({instanceId: '295ccf70-5d0d-4026-94c6-b2a9b06f7fff', businessId: '225ccf70-5d0d-4026-94c6-b2a9b06f7fff'})
      // GPT.plan.query()
    },
  },
}
</script>
