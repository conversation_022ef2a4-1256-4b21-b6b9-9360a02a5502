<template>
  <div style="border: 1px solid #999; height: 500px">
    <iframe
      src="http://localhost:8080/gzt"
      style="
        width: 100%;
        height: 100%;
        border: none;
        background-color: transparent;
        pointer-events: auto !important;
      "
      allow="*"
    ></iframe>
  </div>
</template>

<script lang="ts">
import GPT from '@skyline/ai'
export default {
  name: 'CallbackPage',
  props: {
    url: {
      type: String,
      default: '',
    },
  },
  setup() {
    const init = () => {
      GPT.init('/gpt/fzlr.js').then(() => {
        const context = {
          userGuid: '08dc2616-c1fb-43c2-8a2b-65984ca0a769',
          userCode: 'lin11',
          userName: '林一一',
          mobile: '15625342622',
          // mock 数据
          customerId: '46b55e4c-810a-4c0d-82a0-b039389cf725',
          customerName: '天际开发者社区',
        }
        GPT.setContext(context)
        GPT.show()
      })
    }
    const isFrame = !!window.frameElement || window.parent !== window
    if (!isFrame) {
      init()
    }
    return {}
  },
}
</script>
