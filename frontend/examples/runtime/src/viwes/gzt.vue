<template>
  {{ dataList }}
  <div class="btn" @click="handleClick">打开GPT助手</div>
  <div class="btn" @click="handleClose">收起侧滑</div>
  <div class="btn" @click="handleViewChat">查看会话</div>
  <div class="btn" @click="gptSdk.hide">隐藏图标</div>
  <div class="btn" @click="gptSdk.show">显示图标</div>
  <div class="btn" @click="handleMsg">发送消息</div>
  <div class="btn" @click="handleOpen">打开页面</div>
  <div class="btn" @click="handleClosePage">关闭页面</div>
  <div
    class="btn"
    @click="
      () =>
        gptSdk.media.preview(
          'http://mydoc:9070/api/v2/GetFile?documentId=46548c45-9ce5-409b-9c25-b7a31279acdf&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI0NjU0OGM0NS05Y2U1LTQwOWItOWMyNS1iN2EzMTI3OWFjZGYiLCJUZW5hbnRDb2RlIjoibXlzb2Z0IiwiZXhwIjoxNzUyNTY0ODU4LCJpYXQiOjE3MzcwMDkyNTgsImp0aSI6IjE5ZjhjNDkzLWMzYTItNDE5Zi05MGMwLTA3OTA1MzU3MjA4MSJ9.vqOdG6EjaXtDhtpGdpP32pWSCqjEy2h0br-YyBDHBFc',
        )
    "
  >
    打开预览图片URL
  </div>
  <div class="btn" @click="handleMessage">发送通知消息</div>
  <div class="btn" @click="handleScreenshot">screenshot</div>
  <div class="btn" @click="handlePlanQuery">智能检查</div>
</template>

<script lang="ts" setup>
import GPT from '@skyline/ai'
import { ref } from 'vue'

const dataList = ref({})
const handleClick = () => {
  console.log('open')
  GPT.open({
    defaultSkill: '',
    onCardAction: function (e) {
      if (e.data && e.data.message) {
        try {
          var data = e.data.message?.data?.aaa || []
          var list = []
          data.forEach(function (item) {
            list.push({
              x_Phone: item.Phone,
              x_Fzr: item.Fzr,
              x_CompanyName: item.CompanyName,
              x_Project: item.Project,
            })
          })
          dataList.value = list
          console.log(list)
        } catch (err) {
          console.log(err)
        }
      }
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (window?.gptSuccess) {
            resolve(true)
          } else {
            reject(false)
          }
        }, 1000)
      })
    },
    onError: function (e) {
      console.log(e)
    },
    onChat: function (e) {
      console.log(e)
    },
    onSkillStop: function () {
      console.log('stop')
    },
  })
}
const handleClose = () => {
  GPT.close()
}
const handleViewChat = () => {
  GPT.open({
    chatId: '68bc0e60-b6a5-47e3-a983-8ceb5b97c996',
    onChat: function (e) {
      console.log(e)
    },
  })
}
const handleMsg = () => {
  GPT.notification({ title: 'notification', body: '消息内容' })
}
const handleOpen = () => {
  GPT.page.open({ url: '/gzt' })
}
const handleClosePage = () => {
  GPT.page.close()
}

const handleScreenshot = () => {
  GPT.media.screenshot().then((data) => {
    console.log(data)
  })
}
const handleMessage = () => {
  GPT.media.notification({ title: '标题', body: '内容' })
}

const handlePlanQuery = () => {
  GPT.plan.query('95deca10-6343-4b00-b604-78c484b8e2f4')
}

const gptSdk = GPT
</script>

<style>
.btn {
  cursor: pointer;
  float: left;
  width: 100%;
  text-align: left;
}
</style>
