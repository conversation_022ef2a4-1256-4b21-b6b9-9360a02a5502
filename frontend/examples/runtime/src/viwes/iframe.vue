<template>
  <iframe
    :src="frameUrl"
    style="
      width: 100%;
      height: 100%;
      border: none;
      background-color: transparent;
      pointer-events: auto !important;
    "
    allow="*"
  ></iframe>
</template>

<script lang="ts">
const appInfo = {
  app: {
    // 应用id
    appid: 'gptplbck50vee7mqas',
    // 密钥
    appsecret: 'zHct8rdjoZKpZ05ttSguBUh0FumeCbkv',
    // 租户
    tenantCode: 'mysoft',
  },
  userInfo: {
    userGuid: '08dc2616-c1fb-43c2-8a2b-65984ca0a769',
    userCode: 'lin11',
    userName: '林一一',
    mobile: '15625342622',
  },
  // 应用访问地址
  url: 'http://**********:5001/gptbuilder/assistant/index.html?share=gptplbck50vee7mqas+9d2HgXPpe8ROtXdpdeTD8xm6kvtXMovN+sgi99qbsdkyA8BpCc8JvR1wTK8s59r9Mrk4xBOB2cOembJ4hKMBCeCb0VXROSnKnTP9m2a+oBYqbDGpGDKfH+vxRGkoFOFk7/U98ZSi0EUGdMRRgz/bDifJLdakbRRm+&history=1',
}
export default {
  name: 'FramePage',
  data() {
    return {
      frameUrl: '',
    }
  },
  mounted() {
    fetch('http://**********:5001/Login/GetToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payload: appInfo.userInfo,
        ...appInfo.app,
      }),
    }).then((res) => {
      res.json().then((data) => {
        console.log(data)
        if (data.success) {
          this.frameUrl = `${appInfo.url}&access_token=${data.data.accessToken}`
        } else {
          alert(data.message)
        }
      })
    })
  },
}
</script>
