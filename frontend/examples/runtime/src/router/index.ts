// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import CallbackPage from '../viwes/callback.vue'
import CallbackTwo from '../viwes/callbackTwo.vue'
import GptFrame from '../viwes/gptFrame.vue'
import GztPage from '../viwes/gzt.vue'
import Iframe from '../viwes/iframe.vue'
import InitToken from '../viwes/initToken.vue'
import OtherPage from '../viwes/other.vue'
import Page from '../viwes/page.vue'
import GztOpen from '../viwes/pageOpen.vue'
import Sidebar from '../viwes/sidebar.vue'

// 定义路由
const routes = [
  {
    path: '/page',
    name: 'page',
    component: Page,
  },
  {
    path: '/iframe',
    name: 'iframe',
    component: Iframe,
  },
  {
    path: '/sidebar',
    name: 'sidebar',
    component: Sidebar,
  },
  {
    path: '/gzt',
    name: 'gzt',
    component: GztPage,
  },
  {
    path: '/gztOpen',
    name: 'gztOpen',
    component: GztOpen,
  },
  {
    path: '/callback',
    name: 'callback',
    component: CallbackPage,
  },
  {
    path: '/callbackTwo',
    name: 'callbackTwo',
    component: CallbackTwo,
  },
  {
    path: '/gptFrame',
    name: 'gptFrame',
    component: GptFrame,
  },
  {
    path: '/otherPage',
    name: 'otherPage',
    component: OtherPage,
  },
  {
    path: '/initToken',
    name: 'initToken',
    component: InitToken,
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
