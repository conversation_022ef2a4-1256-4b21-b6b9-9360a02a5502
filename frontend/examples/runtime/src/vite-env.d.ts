/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_ERP_PAGE_URL: string

  // 云客服
  readonly VITE_APP_GPT_YUNKF_SHARE_CODE: string
  readonly VITE_APP_GPT_YUNKF_SHARE_JS: string
  readonly VITE_APP_GPT_YUNKF_PRELOAD_JS: string

  // 开发者工具
  readonly VITE_APP_GPT_MYSTUDIO_SHARE_CODE: string
  readonly VITE_APP_GPT_MYSTUDIO_SHARE_JS: string
  readonly VITE_APP_GPT_MYSTUDIO_APP_ID: string
  readonly VITE_APP_GPT_MYSTUDIO_APP_SECRET: string

  // 建模应用认证
  readonly VITE_APP_PLATFORM_APP_ID: string
  readonly VITE_APP_PLATFORM_APP_KEY: string
  readonly VITE_APP_PLATFORM_TENANT_CODE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
