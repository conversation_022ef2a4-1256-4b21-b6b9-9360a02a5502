import gpt from '@skyline/ai'

export const initPlan = async () => {
  //   const token = await getPlanToken()
  // await gpt.init('http://localhost:9501/gptbuilder/launcher/index.js?share=gptl7p3mxhoo0k4l3k+ltk6nn86eqG12uYFnNiY3RzG7IIVxhPc+sgi99qbsdkyA8BpCc8JvR1wTK8s59r9Mrk4xBOB2cOembJ4hKMBCeCb0VXROSnKnTP9m2a+oBYqbDGpGDKfH+vxRGkoFOFk7/U98ZSi0EUGjP6pp2jgFGkI9907qiix6&history=1&baseUrl=http://***********:5001')
  // gpt.setAccessToken('eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyR3VpZCI6bnVsbCwidXNlckNvZGUiOiJodXh0IiwidXNlck5hbWUiOiIiLCJtb2JpbGUiOm51bGwsImFwcFR5cGUiOm51bGwsImFwcENvZGUiOm51bGwsImFwcE5hbWUiOm51bGwsImFwcFZlcnNpb24iOm51bGwsIm1vZHVsZUNvZGUiOm51bGwsIm1vZHVsZU5hbWUiOm51bGwsInBhZ2VOYW1lIjpudWxsLCJwYWdlVXJsIjpudWxsLCJlcnBWZXJzaW9uIjpudWxsLCJtZXRhZGF0YSI6bnVsbCwiY3VzdG9tZXJHdWlkIjpudWxsLCJjdXN0b21lck5hbWUiOm51bGwsInRlbmFudENvZGUiOm51bGwsInRlbmFudE5hbWUiOm51bGwsImV4cCI6MTczNzQyODUxNn0.Okmlt4gXX7sGxwBNVUzPlIvyW60GB9nH0iJX9XEvrYY')
  // gpt.setContext({})
  // return

  const token =
    'eyJzaGFyZUNvZGUiOiJzeXN0ZW1fcGxhbitsZzZhMjhrcE5ZNGlCb3NIR0ZzR0djWW1acmhkYmc4MitzZ2k5OXFic2RreUE4QnBDYzhKdlIxd1RLOHM1OXI5TXJrNHhCT0IyY09lbWJKNGhLTUJDZUNiMFZYUk9TbktuVUdseVFMRVZvVkJ2cFQ2WkZ3MStWVkhkUFpuNW5MaHpiTTBZcmt4MEsycVhBUnk2VFdtWG4zM1BUR1lXL2Z2SCIsImJhc2VVcmwiOiJodHRwOi8vZ3B0ZW5naW5lOjUwMDAiLCJqc1VybCI6Imh0dHA6Ly9ncHRlbmdpbmU6NTAwMC9ncHRidWlsZGVyL2Fzc2lzdGFudC9pbmRleC5qcyIsImFjY2Vzc1Rva2VuIjoiZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFjMlZ5UjNWcFpDSTZJakE0WkdReU5qRm1MVFU0TlRZdE5HRTNOUzA0WlRKakxUVmhabU0yWkRKak5qVXlaQ0lzSW5WelpYSkRiMlJsSWpvaWJHbHVNVEVpTENKMWMyVnlUbUZ0WlNJNklpSXNJbTF2WW1sc1pTSTZiblZzYkN3aVlYQndWSGx3WlNJNmJuVnNiQ3dpWVhCd1EyOWtaU0k2Ym5Wc2JDd2lZWEJ3VG1GdFpTSTZiblZzYkN3aVlYQndWbVZ5YzJsdmJpSTZiblZzYkN3aWJXOWtkV3hsUTI5a1pTSTZiblZzYkN3aWJXOWtkV3hsVG1GdFpTSTZiblZzYkN3aWNHRm5aVTVoYldVaU9tNTFiR3dzSW5CaFoyVlZjbXdpT201MWJHd3NJbVZ5Y0ZabGNuTnBiMjRpT201MWJHd3NJbTFsZEdGa1lYUmhJanB1ZFd4c0xDSmpkWE4wYjIxbGNrZDFhV1FpT201MWJHd3NJbU4xYzNSdmJXVnlUbUZ0WlNJNmJuVnNiQ3dpZEdWdVlXNTBRMjlrWlNJNmJuVnNiQ3dpZEdWdVlXNTBUbUZ0WlNJNmJuVnNiQ3dpWlhod0lqb3hOek0zTlRNNE9UTTVmUS42X1hpcmdfT0hoYzBUdjlVQkhJRF9sQTNhbGRqWmQ0UXlKckVFd3diM2xvIiwiaGlzdG9yeSI6MH0='
  await gpt.init({ token, container: '.plan-container' })
  // query 测试用例
  // gpt.plan.query('838e1fc3-8550-4b22-8523-6dee1fae8fe2')
  // gpt.plan.query({instanceId: '838e1fc3-8550-4b22-8523-6dee1fae8fe2', data: {test: 123}, onClose: () => {
  //   console.log('close')
  // }})
  // gpt.plan.query({businessId: '838e1fc3-8550-4b22-8523-6dee1fae8fe2', data: {test: 123}})

  // invoke 测试用例
  // gpt.plan.invoke('67629fe7-92a0-4b94-8a5a-ad317e82a7b5')
  gpt.plan.invoke({
    id: '67629fe7-92a0-4b94-8a5a-ad317e82a7b5',
    data: { test: 123 },
    onClose: () => {
      console.log('close')
    },
  })

  // gpt.show()
  // gpt.open({
  //   skills: ['system_plan'],
  //   defaultSkill: 'system_plan',
  // })
  // gpt.plan.query('838e1fc3-8550-4b22-8523-6dee1fae8fe2')

  // setTimeout(() => {
  //   gpt.plan.query('838e1fc3-8550-4b22-8523-6dee1fae8fe2')
  // }, 3000)
}
