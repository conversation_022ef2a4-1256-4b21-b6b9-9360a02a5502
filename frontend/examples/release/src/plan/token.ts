// 这个 api 浏览器跨域调不了
// const appId = import.meta.env.VITE_APP_PLATFORM_APP_ID
// const appSecret = import.meta.env.VITE_APP_PLATFORM_APP_KEY
// const tenantCode = import.meta.env.VITE_APP_PLATFORM_TENANT_CODE

// export const getPlanToken = async () => {
//   const res = await fetch(
//     'https://apaas.xchzmyha7.mingyuanyun.com:8443/pub/42000101/gpt/assistant/getToken',
//     {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//         appid: appId,
//         appsecret: appSecret,
//         tenantCode: tenantCode,
//       },
//       body: JSON.stringify({
//         publishCode: 'system_plan',
//         payload: {
//           // userGuid: '用户GUID', // 可选，userid
//           // mobile: '用户手机号码', // 可选，获取的用户手机号码
//           userCode: 'foo', // 必须，获取的 usercode
//           userName: 'Foo', // 必须，获取的 username
//         },
//       }),
//     },
//   )
//   const data = await res.json()

//   return data.data.token
// }
