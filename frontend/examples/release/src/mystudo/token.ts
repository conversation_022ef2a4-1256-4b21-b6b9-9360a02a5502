const appId = import.meta.env.VITE_APP_GPT_MYSTUDIO_APP_ID
const appSecret = import.meta.env.VITE_APP_GPT_MYSTUDIO_APP_SECRET

const tenantCode = 'ai'

export const getMyStudioToken = async () => {
  const res = await fetch('https://gptengine.apaas.mypaas.com/Login/GetToken', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      appid: appId,
      appsecret: appSecret,
      tenantCode: tenantCode,
      payload: {
        // userGuid: '用户GUID', // 可选，userid
        // mobile: '用户手机号码', // 可选，获取的用户手机号码
        userCode: 'foo', // 必须，获取的 usercode
        userName: 'Foo', // 必须，获取的 username
      },
    }),
  })
  const data = await res.json()

  return data.data.accessToken
}
