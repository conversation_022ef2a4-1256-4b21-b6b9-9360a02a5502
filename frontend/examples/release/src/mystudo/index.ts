import gpt from '@skyline/ai'
import { createPreloadUrl } from '../preload'
import { getMyStudioToken } from './token'

// 开发者工具
const code = `${import.meta.env.VITE_APP_GPT_MYSTUDIO_SHARE_CODE}`
const js = `${import.meta.env.VITE_APP_GPT_MYSTUDIO_SHARE_JS}`

const url = createPreloadUrl({ code, js })

export const initMyStudio = async () => {
  await gpt.init(url)

  const token = await getMyStudioToken()
  gpt.setAccessToken(token)
  gpt.setContext({ userGuid: 'huxt' })
  gpt.show()
}
