const codeToUrl = (code: string) => {
  const blob = new Blob([code], { type: 'text/javascript' })
  return URL.createObjectURL(blob)
}

export const createPreloadUrl = ({
  code,
  js,
}: {
  code: string
  js: string
}) => {
  const url = js.startsWith('blob:') ? js : `${js}?v=${Date.now()}`

  const script = `(function () {
  var script = document.createElement('script');
  script.type = "text/javascript";
  script.id = "skyline-gpt";
  script.setAttribute("data-gpt-base-url", 'https://gptengine.apaas.mypaas.com');
  script.setAttribute("data-gpt-share", '${code}');
  script.setAttribute("data-gpt-history", "1")
  script.src = '${url}';
  document.body.appendChild(script);
})();`

  return codeToUrl(script)
}

export const createAssistantResource = () => {
  const code = `;(function () {
  var i
  i = document.createElement('script')
  i.setAttribute('type', 'module')
  i.setAttribute('crossorigin', '')
  i.setAttribute(
    'src',
    'https://gptengine.apaas.mypaas.com/gptbuilder/assistant/assets/index-CSNh0dTQ.js'
  )
  document.head.appendChild(i)
  i = document.createElement('link')
  i.setAttribute('rel', 'modulepreload')
  i.setAttribute('crossorigin', '')
  i.setAttribute(
    'href',
    'https://gptengine.apaas.mypaas.com/gptbuilder/assistant/assets/vendor-Be2-acjr.js'
  )
  document.head.appendChild(i)
  i = document.createElement('link')
  i.setAttribute('rel', 'stylesheet')
  i.setAttribute('crossorigin', '')
  i.setAttribute(
    'href',
    'https://gptengine.apaas.mypaas.com/gptbuilder/assistant/assets/vendor-BCYvaggc.css'
  )
  document.head.appendChild(i)
  i = document.createElement('link')
  i.setAttribute('rel', 'stylesheet')
  i.setAttribute('crossorigin', '')
  i.setAttribute(
    'href',
    'https://gptengine.apaas.mypaas.com/gptbuilder/assistant/assets/index-uYWT4UpV.css'
  )
  document.head.appendChild(i)
})()`
  return codeToUrl(code)
}
