import gpt from '@skyline/ai'
import { SystemContext } from '@skyline/ai/internal'

const context: SystemContext = {
  userGuid: '08da6e18-8f8b-4821-80af-c8843c59db30',
  userCode: '何彬英',
  mobile: 'mobile',
  appType: 'erp',
  appCode: '0011',
  appName: '系统管理',
  appVersion: '',
  erpVersion: 'V5.0',
  customerId: 'cc3ce97e-d996-4c76-9238-e458f7dfc4f4',
  customerName: '广州市绿浪展览策划有限公司',
  tenantCode: 'cyjsbetauatone',
  tenantName: '广州市绿浪展览策划有限公司',
  metadata:
    '[{"name":"企业名","value":""},{"name":"客户端","value":"PC"},{"name":"操作系统","value":"Windows 10"},{"name":"浏览器","value":"chrome 126.0.0.0"}]',
}

export const initYunkf = async () => {
  // 本地环境
  // const code = import.meta.env.VITE_APP_GPT_YUNKF_SHARE_CODE
  // const js = import.meta.env.VITE_APP_GPT_YUNKF_SHARE_JS
  // const url = createPreloadUrl({ code, js })

  // 智能客服，生产环境
  const url = 'https://kfmerge.fdcyun.com/js/gpt/preload.js'

  console.log(url)
  await gpt.init(url)

  gpt.setContext(context)
  gpt.show()
}
