import { onBeforeUnmount, onMounted, onUnmounted } from 'vue'
import { EventChannel } from '../../core'
import { useNamespace } from './namespace'

export const useWidgetProvider = () => {
  const namespace = useNamespace()

  const channel = new EventChannel(namespace)
  onBeforeUnmount(() => {
    channel.dispose()
  })

  onMounted(() => {
    channel.emit('mounted')
  })
  onUnmounted(() => {
    channel.emit('unmounted')
  })
}
