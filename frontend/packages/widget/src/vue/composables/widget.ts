import { onBeforeUnmount } from 'vue'
import { EventChannel, Logger } from '../../core'
import { useNamespace } from './namespace'

export type WidgetData = Record<string, unknown>

type WidgetStreamEvent<T> = {
  type: 'open' | 'close'
  stream: AsyncGenerator<T>
}

type UseWidgetOptions<T> = {
  onStreamOpen?: ({ stream }: { stream: AsyncGenerator<T> }) => Promise<void>
  onStreamClose?: () => Promise<void>
}

type WidgetNextOptions<T extends WidgetData> = {
  data?: T
}

export const useWidget = <T extends WidgetData = WidgetData>({
  onStreamOpen,
  onStreamClose,
}: UseWidgetOptions<T> = {}) => {
  const namespace = useNamespace()

  const channel = new EventChannel(namespace)
  onBeforeUnmount(() => {
    channel.dispose()
  })

  const logger = new Logger(namespace)

  if (typeof onStreamOpen === 'function') {
    channel.on('stream', async (e) => {
      const s = (e as CustomEvent<WidgetStreamEvent<T>>).detail
      if (s.type === 'open') {
        logger.success(`stream:client:open`)
        await onStreamOpen(s)
        logger.success(`stream:client:done`)
      } else {
        if (typeof onStreamClose === 'function') {
          await onStreamClose()
        }
        logger.success(`stream:client:close`)
      }
    })
  }

  const next = <T extends Record<string, unknown> = any>({ data }: WidgetNextOptions<T> = {}) => {
    channel.emit('next', { data })
  }

  return {
    next,
  }
}
