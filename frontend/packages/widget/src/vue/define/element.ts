import { type Component, type DefineComponent } from 'vue'
import { NAMESPACE_KEY } from '../constants'

export type WidgetProps = {
  disabled?: boolean
  data: {
    id: string
  }
}

export const defineCustomElement = async (name: string, component: Component) => {
  let Widget = customElements.get(name)
  if (Widget) {
    return Widget
  }

  const { defineCustomElement } = await import('vue')
  Widget = defineCustomElement(component as DefineComponent, {
    configureApp(app) {
      const namespace = name.toLowerCase().replace(/-/g, ':')
      app.provide(NAMESPACE_KEY, namespace)
    },
  })
  customElements.define(name, Widget)

  return Widget
}
