import { type Component } from 'vue'
import { defineCustomElement } from './element'
import { getStateFromUrl } from './state'

type WidgetFactoryReturn = {
  meta: { url: string }
  component: Component
}

type WidgetFactory = () => Promise<WidgetFactoryReturn>

export const defineWidget = async (factory: WidgetFactory) => {
  const r = await factory()
  if (!r) {
    return
  }
  const { meta, component } = r

  let data = await getStateFromUrl(meta.url)

  if (!data) {
    if (!import.meta.env.DEV) {
      return
    }
    data = {
      name: 'skyline-ai-widget-nonce',
      event: '',
    }
  }
  const Widget = await defineCustomElement(data.name, component)

  if (data.event) {
    const event = new CustomEvent(data.event, {
      detail: data,
      bubbles: true,
      cancelable: true,
    })
    document.dispatchEvent(event)
  } else {
    const el = new Widget()
    document.body.appendChild(el)
  }
}
