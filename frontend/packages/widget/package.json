{"name": "@skyline-ai/widget", "version": "1.0.3", "license": "MIT", "type": "module", "exports": {"./core": {"import": "./dist/core.js", "types": "./dist/core.d.ts", "default": "./dist/core.js"}, "./vite": {"import": "./dist/vite.js", "types": "./dist/vite.d.ts", "default": "./dist/vite.js"}, "./vue": {"import": "./dist/vue.js", "types": "./dist/vue.d.ts", "default": "./dist/vue.js"}, "./server": {"import": "./dist/server.js", "types": "./dist/server.d.ts", "default": "./dist/server.js"}, "./ipc": {"import": "./dist/ipc.js", "types": "./dist/ipc.d.ts", "default": "./dist/ipc.js"}}, "typesVersions": {"*": {"core": ["./dist/vite.d.ts"], "vite": ["./dist/vite.d.ts"], "vue": ["./dist/vue.d.ts"], "server": ["./dist/server.d.ts"], "ipc": ["./dist/ipc.d.ts"]}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "prepare": "npm run build", "prepublishOnly": "npm run build"}, "dependencies": {"js-base64": "^3.7.7", "nanoid": "^5.1.2", "path-to-regexp": "^8.2.0"}, "devDependencies": {"vite": "^5.4.14", "vue": "^3.5.13"}, "peerDependencies": {"vite": ">=5", "vue": ">=3"}, "peerDependenciesMeta": {"vite": {"optional": true}, "vue": {"optional": true}}, "publishConfig": {"registry": "https://npm.mingyuanyun.com/"}}