import { J<PERSON><PERSON> } from 'jsdom'
import { writeFileSync } from 'node:fs'
import path from 'node:path'
import type { Plugin, ResolvedConfig } from 'vite'

interface TagDescriptor {
  tag: string
  attrs: Record<string, string>
  children: string
  injectTo: 'body' | 'head'
  html: string
}

//读取HTML。转化成TagDescriptor结构
const includesTag = ['link', 'script']

const getHtmlTagDescriptor = (el: HTMLCollection | undefined, injectTo: 'body' | 'head') => {
  const result: TagDescriptor[] = []
  if (el) {
    for (let index = 0; index < el.length; index++) {
      const element = el.item(index)
      if (element) {
        if (!includesTag.includes(element.tagName.toLocaleLowerCase())) {
          continue
        }
        const attrs = {} as Record<string, string>
        for (let i = 0; i < element.attributes.length; i++) {
          const attr = element.attributes[i]
          attrs[attr.name] = attr.value
        }
        if (attrs.rel !== 'icon' && !attrs.skip) {
          result.push({
            tag: element.tagName.toLowerCase(),
            attrs,
            children: element.innerHTML,
            injectTo,
            html: element.outerHTML,
          })
        }
      }
    }
  }
  return result
}

type InjectContentOptions = {
  preBuild?: () => string
  attrValue?: (key: string, value: string) => string
}
type InjectJsPluginOptions = {
  index?: InjectContentOptions
  loader?: InjectContentOptions
}

const resolveModuleParams = (html: string, list: TagDescriptor[]) => {
  list.forEach((item) => {
    // 为所有脚本添加参数
    if (item.tag === 'script' && item.attrs.src) {
      html = html.replace(`"${item.attrs.src}"`, `"${item.attrs.src}?source=loader"`)
    }
  })

  return html
}

const createDefaultContent = (list: TagDescriptor[], options: InjectContentOptions) => {
  if (typeof options.preBuild !== 'function') {
    options.preBuild = () => ''
  }
  if (typeof options.attrValue !== 'function') {
    options.attrValue = (key: string, value: string) => `'${value}'`
  }

  const contents: string[] = [`;(function(){`, options.preBuild(), 'var i;']
  list.forEach((item) => {
    contents.push(`i = document.createElement('${item.tag}');`)
    for (const key in item.attrs) {
      if (Object.prototype.hasOwnProperty.call(item.attrs, key)) {
        const value = item.attrs[key]
        contents.push(`i.setAttribute('${key}',${options.attrValue(key, value)});`)
      }
    }
    if (item.children) {
      contents.push(`i.innerHTML="${item.children.replaceAll('"', '\\"')}";`)
    }
    contents.push(`document.${item.injectTo}.appendChild(i);`)
  })
  contents.push('})();')
  return contents.join('')
}

//TODO 临时方案从HTML读取生成文件, 后续可以更换成直接读取manifest.json
export default function injectJsPlugin(options: InjectJsPluginOptions = {}): Plugin {
  let config: ResolvedConfig
  if (!options.index) {
    options.index = {}
  }

  return {
    name: 'vite-plugin-inject-js',
    enforce: 'pre',
    configResolved(resolvedConfig) {
      config = resolvedConfig
    },
    transformIndexHtml: {
      order: 'post',
      async handler(html, ctx) {
        // 仅在构建时触发
        if (config.mode !== 'production') {
          return
        }
        //读取HTML中的使用方式
        const { window } = new JSDOM(html)
        const { document } = window
        const headerChildren = document.querySelector('head')?.children
        const bodyChildren = document.querySelector('body')?.children
        const list: TagDescriptor[] = []
        list.push(...getHtmlTagDescriptor(headerChildren, 'head'))
        list.push(...getHtmlTagDescriptor(bodyChildren, 'body'))
        // 开始写清单文件
        const listContent = JSON.stringify(list, null, 2)
        // 输出一个空行 因为运行插件中的日志不会换行
        // 开始进行输出
        const cwd = path.resolve(config.root, config.build.outDir)
        const htmlFilePath = path.join(cwd, 'list.json')
        writeFileSync(htmlFilePath, listContent, { encoding: 'utf-8' })

        if (options.index) {
          const content = createDefaultContent(list, options.index)
          writeFileSync(path.join(cwd, 'index.js'), content, {
            encoding: 'utf-8',
          })
        }

        if (options.loader) {
          const content = createDefaultContent(list, options.loader)
          writeFileSync(path.join(cwd, 'loader.js'), content, {
            encoding: 'utf-8',
          })
          return resolveModuleParams(html, list)
        }
      },
    },
  }
}
