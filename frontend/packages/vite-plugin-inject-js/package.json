{"name": "@acme/vite-plugin-inject-js", "version": "1.0.0", "private": true, "license": "ISC", "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "prepare": "npm run build", "prepublishOnly": "npm run build"}, "dependencies": {"jsdom": "^24.1.3", "vite": "^5.4.14"}, "devDependencies": {"@types/jsdom": "^21.1.7"}}