import minimist from 'minimist'
import prompts from 'prompts'
import { green } from './colors'
import { prompt } from './prompt'
import { resolveProject } from './resolve'
import { formatTargetDir } from './utils'

const argv = minimist<{
  template?: string
  help?: boolean
  overwrite?: boolean
}>(process.argv.slice(2), {
  default: { help: false, template: 'vue' },
  alias: { h: 'help', t: 'template' },
  string: ['_'],
})
const cwd = process.cwd()

// prettier-ignore
const helpMessage = `\
Usage: @skyline-ai-widget/create [OPTION]... [DIRECTORY]

Create a new Widget project in TypeScript.
With no arguments, start the CLI in interactive mode.

Options:
  -t, --template NAME        use a specific template

Available templates:
${green     ('vue         vue'      )}`

async function init() {
  const argTargetDir = formatTargetDir(argv._[0])
  const argTemplate = argv.template || argv.t

  const help = argv.help
  if (help) {
    console.log(helpMessage)
    return
  }

  let targetDir = ''

  let result: prompts.Answers<'projectName' | 'overwrite' | 'packageName'>

  prompts.override({
    overwrite: argv.overwrite,
  })

  try {
    const r = await prompt({
      overwrite: argv.overwrite,
      target: argTargetDir,
    })
    result = r.result
    targetDir = r.targetDir
  } catch (cancelled: any) {
    console.log(cancelled.message)
    return
  }
  console.log(targetDir)

  // user choice associated with prompts
  const { overwrite, packageName, projectName } = result
  console.log(result)

  const name = packageName || projectName

  resolveProject({ cwd, overwrite, targetDir, name, template: argTemplate })
}

init().catch((e) => {
  console.error(e)
})
