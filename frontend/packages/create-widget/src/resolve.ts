import { existsSync, mkdirSync } from 'node:fs'
import path from 'node:path'
import { renameProjectName } from './template'
import { emptyDir, pkgFromUserAgent } from './utils'

export const resolveProject = ({
  targetDir,
  cwd,
  overwrite,
  name,
  template,
}: {
  name: string
  targetDir: string
  cwd: string
  overwrite?: string
  template: string
}) => {
  const root = path.join(cwd, targetDir)

  if (overwrite === 'yes') {
    emptyDir(root)
  } else if (!existsSync(root)) {
    mkdirSync(root, { recursive: true })
  }

  console.log(`\nScaffolding project in "${root}"...`)

  renameProjectName({
    name,
    root,
    template,
  })

  const cdProjectName = path.relative(cwd, root)
  console.log(`\nDone. Now run:\n`)
  if (root !== cwd) {
    console.log(`  cd ${cdProjectName.includes(' ') ? `"${cdProjectName}"` : cdProjectName}`)
  }

  const pkgInfo = pkgFromUserAgent(process.env.npm_config_user_agent)

  // const pkgManager = pkgInfo ? pkgInfo.name : 'npm'

  // switch (pkgManager) {
  //   case 'yarn':
  //     console.log('  yarn')
  //     console.log('  yarn dev')
  //     break
  //   default:
  //     console.log(`  ${pkgManager} install`)
  //     console.log(`  ${pkgManager} run dev`)
  //     break
  // }
  // console.log()
}
