import { existsSync } from 'node:fs'
import path from 'node:path'
import prompts from 'prompts'
import { red, reset } from './colors'
import { formatTargetDir, isEmpty, isValidPackageName, toValidPackageName } from './utils'

const defaultTargetDir = 'ai-widget-project'

export const prompt = async ({
  overwrite,
  target,
}: { overwrite?: boolean; target?: string } = {}) => {
  let result: prompts.Answers<'projectName' | 'overwrite' | 'packageName'>

  prompts.override({
    overwrite,
  })

  let targetDir = target || defaultTargetDir

  const getProjectName = () => path.basename(path.resolve(targetDir))

  result = await prompts(
    [
      {
        type: target ? null : 'text',
        name: 'projectName',
        message: reset('Project name:'),
        initial: defaultTargetDir,
        onState: (state) => {
          targetDir = formatTargetDir(state.value) || defaultTargetDir
        },
      },
      {
        type: () => (!existsSync(targetDir) || isEmpty(targetDir) ? null : 'select'),
        name: 'overwrite',
        message: () =>
          (targetDir === '.' ? 'Current directory' : `Target directory "${targetDir}"`) +
          ` is not empty. Please choose how to proceed:`,
        initial: 0,
        choices: [
          {
            title: 'Cancel operation',
            value: 'no',
          },
          {
            title: 'Remove existing files and continue',
            value: 'yes',
          },
          {
            title: 'Ignore files and continue',
            value: 'ignore',
          },
        ],
      },
      {
        type: (_, { overwrite }: { overwrite?: string }) => {
          if (overwrite === 'no') {
            throw new Error(red('✖') + ' Operation cancelled')
          }
          return null
        },
        name: 'overwriteChecker',
      },
      {
        type: () => (isValidPackageName(getProjectName()) ? null : 'text'),
        name: 'packageName',
        message: reset('Package name:'),
        initial: () => toValidPackageName(getProjectName()),
        validate: (dir) => isValidPackageName(dir) || 'Invalid package.json name',
      },
    ],
    {
      onCancel: () => {
        throw new Error(red('✖') + ' Operation cancelled')
      },
    },
  )

  return {
    result,
    targetDir,
  }
}
