<script setup lang="ts">
import { ref } from 'vue'
import {
  WidgetProvider,
  useWidget,
  type WidgetProps,
  type WidgetData,
} from '@skyline-ai/widget/vue'

const { disabled } = defineProps<WidgetProps>()

const data = ref<WidgetData>({})

const { next } = useWidget({
  onStreamOpen: async ({ stream }) => {
    for await (const element of stream) {
      data.value = element
    }
  },
})

const onClick = () => {
  // data 编辑完成后，可以调用 next 方法提交数据到助手
  next({ data: data.value })
}
</script>

<template>
  <WidgetProvider class="flex flex-col space-y-1">
    <div class="flex space-x-1">
      <span>I am widget, disabled is {{ disabled }}</span>
      <button @click="onClick">Next</button>
    </div>
    <div>{{ data }}</div>
  </WidgetProvider>
</template>

<style>
@tailwind base;
@tailwind utilities;
</style>
