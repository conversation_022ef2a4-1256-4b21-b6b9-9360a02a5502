import widget from '@skyline-ai/widget/vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import autoprefixer from 'autoprefixer'
import tailwindcss from 'tailwindcss'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    AutoImport({
      dts: '.vite/auto-imports.d.ts',
      imports: ['vue'],
    }),
    Components({
      dts: '.vite/components.d.ts',
    }),
    widget(),
  ],
  server: {
    host: '0.0.0.0',
    port: 11001,
    cors: true,
  },
  css: {
    postcss: {
      plugins: [tailwindcss, autoprefixer],
    },
  },
})
