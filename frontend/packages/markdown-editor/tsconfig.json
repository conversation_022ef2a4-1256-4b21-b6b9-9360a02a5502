{
  "compilerOptions": {
    "declaration": true, // 生成 .d.ts 文件
    "emitDeclarationOnly": true, // 只生成类型文件，不生成 JavaScript 文件
    "outDir": "types", // 输出目录
    "noEmit": true,
    "module": "ESNext",
    "moduleResolution": "Node", // 模块解析方式
    "strict": true, // 启用严格模式
    "esModuleInterop": true, // 兼容 CommonJS 和 ES 模块
    "skipLibCheck": true, // 跳过库文件检查
  },
  "include": ["src/**/*.ts", "src/**/*.vue"],
  "exclude": ["node_modules", "types"]
}
