import {
  Decoration,
  EditorView,
  keymap,
  MatchDecorator,
  ViewPlugin,
  ViewUpdate,
  WidgetType,
  type DecorationSet,
} from '@codemirror/view'
import { DisposableStack } from '@whatwg-node/disposablestack'
export { type EditorView }

type PlaceholderCustomOptions = {
  name: string
  container: Element
}

export type PlaceholderCustom = (e: PlaceholderCustomOptions) => Disposable

type PlaceholderWidgetOptions = {
  name: string
  custom: PlaceholderCustom
}

class PlaceholderWidget extends WidgetType {
  name: string
  id: string

  // 自定义元素
  custom: PlaceholderCustom

  // 确保销毁时可删除自定义实例
  disposables = new DisposableStack()

  constructor({ name, custom }: PlaceholderWidgetOptions) {
    super()
    this.id = new Date().getTime().toString()
    this.name = name
    this.custom = custom
  }

  override eq(other: PlaceholderWidget) {
    return this.id === other.id
  }

  override toDOM(): HTMLElement {
    const el = document.createElement('span')
    el.id = this.id
    el.style.display = 'inline-flex'
    el.textContent = this.name

    if (typeof this.custom === 'function') {
      this.disposables.use(this.custom({ name: this.name, container: el }))
    }
    return el
  }

  override ignoreEvent(event: Event): boolean {
    return true
  }

  override destroy(dom: HTMLElement): void {
    this.disposables.dispose()
  }
}

type PlaceholderConfig = {
  custom?: PlaceholderCustom
}

const runPlaceholderSelection = (view: EditorView) => {
  if (!view.state.selection.main.empty) {
    return false
  }
  const cursorPos = view.state.selection.main.head
  // 获取全部装饰器
  const decorations = view.state.facet(EditorView.decorations)

  for (const decoration of decorations) {
    let decoSet = decoration as DecorationSet

    // 一部分装饰器是函数
    if (typeof decoration === 'function') {
      decoSet = decoration(view)
    }

    let found = false
    decoSet.between(0, view.state.doc.length, (from, to, deco) => {
      if (deco.spec.widget instanceof PlaceholderWidget) {
        if (from < cursorPos && to >= cursorPos) {
          found = true
          // 选中微件
          view.dispatch({
            selection: { anchor: to, head: from },
            userEvent: 'select',
          })
          return false
        }
      }
    })
    if (found) {
      return true
    }
  }

  return false
}

const createDecorator = (custom: PlaceholderCustom) =>
  new MatchDecorator({
    regexp: /\{\{(.+?)\}\}/g,
    decoration: (match, view, pos) => {
      const name = match[1]

      return Decoration.replace({
        widget: new PlaceholderWidget({ name, custom }),
      })
    },
  })

export function placeholder(config: PlaceholderConfig = {}) {
  const placeholderMatcher = createDecorator(config.custom!)

  return [
    keymap.of([
      {
        key: 'Backspace',
        run: runPlaceholderSelection,
      },
    ]),
    ViewPlugin.fromClass(
      class {
        placeholders: DecorationSet
        constructor(view: EditorView) {
          this.placeholders = placeholderMatcher.createDeco(view)
        }
        update(update: ViewUpdate) {
          if (
            update.docChanged ||
            update.viewportChanged ||
            update.changes.length > 0
          ) {
            this.placeholders = placeholderMatcher.updateDeco(
              update,
              this.placeholders,
            )
          }
        }
        destroy() {
          this.placeholders = Decoration.none
        }
      },
      {
        decorations: (instance) => instance.placeholders,
        provide: (plugin) =>
          EditorView.atomicRanges.of((view) => {
            return view.plugin(plugin)?.placeholders || Decoration.none
          }),
      },
    ),
  ]
}
