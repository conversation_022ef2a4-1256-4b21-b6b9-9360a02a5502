import { defaultKeymap, history, historyKeymap } from '@codemirror/commands'
import { markdown } from '@codemirror/lang-markdown'
import { HighlightStyle, syntaxHighlighting } from '@codemirror/language'
import type { Extension } from '@codemirror/state'
import {
  drawSelection,
  EditorView,
  highlightSpecialChars,
  keymap,
} from '@codemirror/view'
import { tags } from '@lezer/highlight'
import type { StyleSpec } from 'style-mod'

export { EditorView }

const highlightStyle = /*@__PURE__*/ HighlightStyle.define([
  { tag: tags.meta, color: '#404740' },
  { tag: tags.link, textDecoration: 'underline' },
  {
    tag: tags.heading,
    textDecoration: 'none',
    fontWeight: 'bold',
    color: '#2CB8C5',
  },
  { tag: tags.emphasis, fontStyle: 'italic' },
  { tag: tags.strong, fontWeight: 'bold' },
  { tag: tags.strikethrough, textDecoration: 'line-through' },
  { tag: tags.keyword, color: '#708' },
  {
    tag: [
      tags.atom,
      tags.bool,
      tags.url,
      tags.contentSeparator,
      tags.labelName,
    ],
    color: '#219',
  },
  { tag: [tags.literal, tags.inserted], color: '#164' },
  { tag: [tags.string, tags.deleted], color: '#a11' },
  {
    tag: [tags.regexp, tags.escape, /*@__PURE__*/ tags.special(tags.string)],
    color: '#e40',
  },
  { tag: /*@__PURE__*/ tags.definition(tags.variableName), color: '#00f' },
  { tag: /*@__PURE__*/ tags.local(tags.variableName), color: '#30a' },
  { tag: [tags.typeName, tags.namespace], color: '#085' },
  { tag: tags.className, color: '#167' },
  {
    tag: [/*@__PURE__*/ tags.special(tags.variableName), tags.macroName],
    color: '#256',
  },
  { tag: /*@__PURE__*/ tags.definition(tags.propertyName), color: '#00c' },
  { tag: tags.comment, color: '#940' },
  { tag: tags.invalid, color: '#f00' },
])

export const createTheme = (
  spec: Record<string, StyleSpec>,
  options?: { dark?: boolean },
) => {
  return EditorView.theme(spec, options)
}

export const createEditor = ({
  content,
  parent,
  extensions = [],
}: {
  content: string
  parent: HTMLElement
  extensions?: Extension[]
}) => {
  return new EditorView({
    doc: content,
    parent,
    extensions: [
      ...extensions,
      markdown(),
      highlightSpecialChars(),
      history(),
      drawSelection(),
      EditorView.lineWrapping,
      syntaxHighlighting(highlightStyle, { fallback: true }),
      keymap.of([...defaultKeymap, ...historyKeymap]),
    ],
  })
}
