import { createTheme } from '../editor'

interface Params {
  customStyle?: object
  minHeight?: string
  maxHeight?: string
}

export const themeLight = ({minHeight, maxHeight}: Params) => createTheme({
    '.cm-content': {
      'min-height': minHeight || '',
      'max-height': maxHeight || '',
      'max-width': '100%',
    },
    '.cm-line': {
      'max-width': '100%',
    },
    '&.cm-focused': {
      outline: 'none',
    },
    '&.cm-focused .cm-selectionBackground, .cm-line::selection, .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection':
      {
        background: '#EBF1FF',
      },
    '.cm-selectionBackground': {
      background: '#EBF1FF',
    },
    '&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground': {
      background: '#EBF1FF',
    },
    '.cm-cursor': {
      borderLeft: '1px solid #000000', // 设置光标颜色
    },
})

export const themeDark = ({minHeight, maxHeight}: Params) => createTheme(
  {
    '.cm-content': {
      'white-space': 'pre-wrap', /* 保留空格和换行符 */
      'min-height': minHeight || '',
      'max-height': maxHeight || '',
      'max-width': '100%',
    },
    '.cm-line': {
      'max-width': '100%',
    },
    '&.cm-focused': {
      outline: 'none',
    },
    '&.cm-focused .cm-selectionBackground, .cm-line::selection, .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection':
      {
        background: '#0057FF4D',
      },
    '.cm-selectionBackground': {
      background: '#0057FF4D',
    },
    '&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground':
      {
        background: '#0057FF4D',
      },
    '.cm-cursor': {
      borderLeft: '1px solid #FFFFFF', // 设置光标颜色
    },
  },
  { dark: true }
)
