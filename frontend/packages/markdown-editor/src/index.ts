import { createApp } from 'vue'
import { themeDark, themeLight } from './common/theme'
import { createEditor, EditorView, placeholder } from './editor'
import { PlaceholderComponent, PlaceholderType } from './placeholder'

interface SourceMap {
  name?: string
  type?: PlaceholderType
  label?: string
  [key: string]: string | undefined
}
// 暴露到建模给的到业务组件使用
const createMarkdownEditor = (
  el: HTMLElement,
  content: string,
  params: {
    sourceMap: { [key: string]: SourceMap }
    disabled: boolean
    update?: Function
    minHeight?: string
    maxHeight?: string
  },
) => {
  let markdownEditorView: EditorView
  let currentSourceMap: { [key: string]: SourceMap } = params.sourceMap
  const select = (dom: Element) => {
    const [from, to] = rangeAtDom(dom)

    markdownEditorView?.dispatch({
      selection: { anchor: from, head: to },
    })
  }

  const remove = (dom: Element) => {
    const [from, to] = rangeAtDom(dom)

    markdownEditorView?.dispatch({
      changes: { from, to, insert: '' },
    })
  }

  // 在当前光标下插入字段、函数、插件等
  const addFieldToCurrent = (field: string) => {
    const { state } = markdownEditorView || {}
    if (state) {
      const insertText = `{{$${field}}}` // 插入的文本
      const from = state.selection.main.head // 当前光标位置
      const to = from + insertText.length // 插入文本后的光标位置
      const docLength = state.doc.length // 文档长度

      const transaction = state.update({
        changes: {
          from: state.selection.main.head, // 光标位置
          to: state.selection.main.head, // 插入点
          insert: insertText, // 插入的文本
        },
        selection: { anchor: to, head: to }, // 移动光标到插入文本的末尾
        scrollIntoView: true, // 确保插入后视图滚动到光标位置
      })
      markdownEditorView?.dispatch(transaction) // 应用事务

      // 如果是从后面加入的就聚焦一下，不然就不聚焦了
      markdownEditorView?.focus()
    }
  }

  const specialTags = placeholder({
    custom({ name, container }: { name: string; container: Element }) {
      const type = currentSourceMap[name]?.type ?? PlaceholderType.UNKNOWN
      const label = currentSourceMap[name]?.label ?? name
      const app = createApp(PlaceholderComponent, {
        type,
        name: label,
        id: name,
        onRemove: () => {
          remove(container)
        },
        onClick: () => {
          select(container)
        },
      })
      app.mount(container)

      return {
        [Symbol.dispose]() {
          app.unmount()
        },
      }
    },
  })

  const refreshPlaceholders = (
    newMap: { [key: string]: SourceMap },
    needUpdate: boolean = false,
  ) => {
    currentSourceMap = newMap
    // 刷新当前的content
    if (needUpdate) {
      const selection = markdownEditorView.state.selection.main
      const anchor = selection.anchor
      const head = selection.head
      const current = markdownEditorView?.state.doc.toString()
      markdownEditorView?.dispatch({
        changes: {
          from: 0,
          to: markdownEditorView.state.doc.length,
          insert: current,
        },
        selection: { anchor, head },
        scrollIntoView: true,
      })
    }
  }

  const setContent = (content: string) => {
    const current = markdownEditorView?.state.doc.toString()
    if (content === current) {
      return
    }
    markdownEditorView?.dispatch({
      changes: {
        from: 0,
        to: current?.length,
        insert: content,
      },
    })
  }

  const rangeAtDom = (dom: Element) => {
    const pos = markdownEditorView?.posAtDOM(dom, 0)
    const filed = dom.children[0].getAttribute('data-id') || dom.textContent

    const from = pos ? pos : 0
    const to = pos ? pos + (filed?.length || 0) + 4 : (filed?.length || 0) + 4
    return [from, to]
  }

  const selfExtensions: any[] = []
  if (params.disabled) {
    selfExtensions.push(EditorView.editable.of(false))
  }
  markdownEditorView = createEditor({
    parent: el,
    content: content,
    extensions: [
      ...selfExtensions,
      themeLight({ minHeight: params.minHeight, maxHeight: params.maxHeight }),
      themeDark({ minHeight: params.minHeight, maxHeight: params.maxHeight }),
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          if (params.update) {
            params.update(update.state.doc.toString())
          }
        }
      }),
      specialTags,
    ],
  })

  return {
    markdownEditorView,
    setContent,
    addFieldToCurrent,
    refreshPlaceholders,
  }
}

export default {
  createMarkdownEditor,
  PlaceholderType,
}

export { createMarkdownEditor, PlaceholderType }
