<template>
  <span
    class="cursor-pointer"
    :style="style"
    :data-id="id"
    @click="onClick"
  >
    <svg-icon  v-if="isSvgIconsRegistered" :name="icon" size="16px" />
    <span 
      v-else 
      v-html="defaultIcon" 
      :style="`display: flex;`"
    ></span>
  
    <span>{{props.name}}</span>
  </span>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { PlaceholderType } from './index'

const defaultIcon :string =  `
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
      <path d="M8.22778 0.96228C8.64541 0.96235 9.04576 1.12889 9.34106 1.42419L11.7014 3.78357C11.9967 4.07893 12.1624 4.48014 12.1624 4.89783V11.6371C12.1624 12.5069 11.457 13.2123 10.5872 13.2123H3.23755C2.3677 13.2123 1.66235 12.5069 1.66235 11.6371V2.53748C1.66235 1.66763 2.3677 0.96228 3.23755 0.96228H8.22778ZM3.23755 2.01208C2.9476 2.01208 2.71216 2.24753 2.71216 2.53748V11.6371C2.71216 11.927 2.9476 12.1625 3.23755 12.1625H10.5872C10.8771 12.1625 11.1125 11.927 11.1125 11.6371V5.33728H9.36255C8.4927 5.33728 7.78735 4.63193 7.78735 3.76208V2.01208H3.23755ZM6.73755 9.36267C7.02725 9.36277 7.2617 9.59742 7.26196 9.88708C7.26196 10.177 7.02741 10.4124 6.73755 10.4125H4.28735C3.99741 10.4125 3.76196 10.177 3.76196 9.88708C3.76222 9.59736 3.99757 9.36267 4.28735 9.36267H6.73755ZM9.53735 6.73767C9.82714 6.73767 10.0625 6.97236 10.0627 7.26208C10.0627 7.55203 9.8273 7.78748 9.53735 7.78748H4.28735C3.99741 7.78747 3.76196 7.55203 3.76196 7.26208C3.76222 6.97236 3.99757 6.73767 4.28735 6.73767H9.53735ZM8.83716 3.76208C8.83716 4.05203 9.0726 4.28748 9.36255 4.28748H10.72L8.83716 2.40466V3.76208Z" fill="#266EFF"/>
    </svg>
  `

const typeIcons: Record<PlaceholderType, string> = {
  [PlaceholderType.VARIABLE]: `${PlaceholderType.VARIABLE}-icon`,
  [PlaceholderType.FUNCTION]: `${PlaceholderType.VARIABLE}-icon`,
  [PlaceholderType.PARAMETER]: `${PlaceholderType.VARIABLE}-icon`,
  [PlaceholderType.REFERENCE]: `${PlaceholderType.VARIABLE}-icon`,
  [PlaceholderType.UNKNOWN]: `${PlaceholderType.VARIABLE}-icon`,
}

// 微件类型颜色映射
const typeColors: Record<PlaceholderType, string> = {
  [PlaceholderType.VARIABLE]: '#266EFF',
  [PlaceholderType.FUNCTION]: '#50b83c',
  [PlaceholderType.PARAMETER]: '#f49342',
  [PlaceholderType.REFERENCE]: '#9c6ade',
  [PlaceholderType.UNKNOWN]: '#266EFF',
}

const props = defineProps<{
  type: PlaceholderType;
  name: string;
  id?: string;
}>();

const icon = ref(typeIcons[props.type])
const color = ref(typeColors[props.type])

const isSvgIconsRegistered = computed(() => {
  return !!document.getElementById('__svg__icons__dom__gpt__designer')
})

const style = computed(() => {
  return {
    '--tw-ring-color': color.value,
    '--color-zinc-100': color.value,
    '--color-zinc-50': `${color.value}30`,
    '--color-zinc-200': `${color.value}40`,
    'background-color': `${color.value}30`,
    'border-color': `${color.value}40`,
    'color': color.value,
    'margin': '2px 4px 2px 0',
    'border-radius': '4px',
    'font-weight': '600',
    'display': 'flex',
    'align-items': 'center',
    'padding': '0 6px',
    'line-height': '22px',
  }
})
const emit = defineEmits(['click'])
const onClick = () => {
  emit('click')
}

</script>
