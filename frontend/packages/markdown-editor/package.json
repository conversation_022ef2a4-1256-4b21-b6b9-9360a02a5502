{"name": "@acme/markdown-editor", "version": "0.0.0", "private": true, "type": "module", "types": "./types/index.d.ts", "exports": {".": {"import": "./src/index.ts"}}, "typesVersions": {"*": {"*": ["./src/index.ts"]}}, "scripts": {"test": "bun test", "build:types": "tsc --build"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.5", "@lezer/highlight": "^1.2.1", "@whatwg-node/disposablestack": "^0.0.6", "style-mod": "^4.1.2"}, "devDependencies": {"@types/node": "^22.13.5", "vue": "^3.5.13"}}