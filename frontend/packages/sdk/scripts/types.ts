import fs from 'node:fs'
import path from 'node:path'
import ts, { NamedExports } from 'typescript'

const distDir = path.resolve(__dirname, '../dist')

fs.readdirSync(distDir).forEach((file) => {
  if (file.endsWith('.d.ts') || file.endsWith('.d.cts')) {
    const filePath = path.join(distDir, file)
    // 读取文件内容并生成 AST
    const sourceFile = ts.createSourceFile(
      filePath,
      fs.readFileSync(filePath, 'utf8'),
      ts.ScriptTarget.Latest,
      true,
    )

    // 定义 AST 转换逻辑
    const transformer = (context: ts.TransformationContext) => {
      const visit = (node: ts.Node) => {
        if (ts.isExportDeclaration(node) && node.exportClause) {
          const newElements = (node.exportClause as NamedExports).elements.map((element) => {
            if (ts.isExportSpecifier(element) && element.isTypeOnly) {
              // 移除 type 关键字
              return ts.factory.createExportSpecifier(false, element.propertyName, element.name)
            }
            // 非类型导出保持不变
            return element
          })
          // 更新 ExportDeclaration 节点
          return ts.factory.updateExportDeclaration(
            node,
            node.modifiers,
            false, // isTypeOnly 设置为 false
            ts.factory.createNamedExports(newElements),
            node.moduleSpecifier,
            node.attributes,
          )
        }
        // 递归遍历子节点
        return ts.visitEachChild(node, visit, context)
      }
      return (node: ts.Node) => ts.visitNode(node, visit)
    }

    // 应用转换
    const result = ts.transform(sourceFile, [transformer])
    const transformedSourceFile = result.transformed[0] as ts.SourceFile

    // 将修改后的 AST 转换为代码
    const printer = ts.createPrinter()
    const newContent = printer.printFile(transformedSourceFile)

    fs.writeFileSync(filePath, newContent, 'utf8')
  }
})
