import { ScreenshotResult, SIGNAL, signal, signalListener } from './internal'

export type AIScreenshotResult = ScreenshotResult
/**
 * 发送通知消息
 */
export const screenshot = (): Promise<AIScreenshotResult> => {
  signal(SIGNAL.ActionMediaScreenshot)
  return new Promise((resolve) => {
    signalListener(
      SIGNAL.ActionMediaScreenshotResult,
      (data) => {
        resolve(data)
      },
      true,
      window,
    )
  })
}
