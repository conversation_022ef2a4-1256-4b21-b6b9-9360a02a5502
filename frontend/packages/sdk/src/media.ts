import { SIGNAL, signal } from './internal'
import { notification } from './notification'
import { screenshot } from './screenshot'

/**
 * 打开预览图片URL
 * @param string url
 */
const preview = (urls: string[] | string) => signal(SIGNAL.ActionMediaPreview, urls)

export default {
  preview,
  /**
   * 发送通知消息
   * @deprecated 使用 从 gpt.media.notification 迁移到 gpt.notification
   */
  notification,
  screenshot,
}
