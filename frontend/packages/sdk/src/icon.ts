import { CreateIconOptions } from './internal'

export type AICreateIconOptions = CreateIconOptions

export const createIcon = (options: CreateIconOptions) => {
  let el: HTMLElement
  if (typeof options.el === 'string') {
    el = document.querySelector(options.el)
  } else {
    el = options.el
  }
  if (el) {
    el.addEventListener('click', options.onClick)
    if (options.title) {
      el.title = options.title
    }
  }
}
