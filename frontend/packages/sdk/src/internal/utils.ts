export const createUrl = (src: string, cache = false) => {
  const url = new URL(src)

  if (src.startsWith('blob:')) {
    url.search = ''
  } else if (cache) {
    url.searchParams.set('t', `${Date.now()}`)
  }
  return url
}

export const createScript = (src: string, attrs: Record<string, string>, cache = true) => {
  const url = createUrl(src, cache)
  src = url.toString()

  const script = document.createElement('script')
  script.type = 'module'
  script.async = true

  Object.keys(attrs).forEach((key) => {
    const value = attrs[key]
    script.setAttribute(key, value)
  })
  script.src = src
  document.body.appendChild(script)
  return script
}

export const isUrl = (url: unknown): url is string => {
  return (
    typeof url === 'string' &&
    (url.startsWith('http') || url.startsWith('/') || url.startsWith('blob:'))
  )
}
