import { SystemContext } from './context'

export type CardActionMessage = {
  id: string
  title?: string
  content?: string
  data?: Record<string, unknown>
}

export type CardActionEventData = {
  name: 'card-confirm' | 'form-binding' | 'protocol'
  message: CardActionMessage
}

export type CardActionEvent = {
  sender: 'button' | 'node' | 'link'
  event: 'click' | 'execute'
  data: CardActionEventData
}

export type SkillRunningMessage = CardActionMessage

export type SkillRunningEvent = {
  status: 'running' | 'done'
  message: SkillRunningMessage
}

export type ErrorEvent = {
  sender: 'skill'
  code: string
  message: string
  type: string
}

type OnCardAction = (event: CardActionEvent) => void
type OnError = (event: ErrorEvent) => void
type OnChat = (event: { id: string }) => void
type OnSkillRunning = (event: SkillRunningEvent) => void
type OnClose = () => void
type onSkillStop = () => void

export type OpenOptionsContext = {
  input?: string
  files?: string[] | { id: string; name: string }[]
  images?: string[]
} & SystemContext

export type OpenOptions = {
  id?: string
  feature?: string
  //chart  id
  chatId?: string
  // 技能列表，显示在聊天界面左下角的技能列表，不指定则使用 defaultSkill 指定
  skills?: string[]
  // 默认选中技能
  defaultSkill?: string
  data?: Record<string, string | number>
  auto?: boolean
  // 上下文参数
  context?: OpenOptionsContext
  interactive?: boolean
  width?: number

  // 回调方法
  onCardAction?: OnCardAction
  onError?: OnError
  onChat?: OnChat
  onSkillRunning?: OnSkillRunning
  onClose?: OnClose
  onSkillStop?: onSkillStop
}

export interface OpenOptionsForSignal extends OpenOptions {
  hasOnCardAction?: boolean
  hasOnError?: boolean
  hasOnSkillRunning?: boolean
  hasOnClose?: boolean
  hasOnChat?: boolean
  hasOnSkillStop?: boolean
}
