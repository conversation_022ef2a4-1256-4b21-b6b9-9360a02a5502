import { SIGNAL_MAP } from './signal'

// biome-ignore lint/complexity/noBannedTypes: off
const callbackCache = new Map<keyof SIGNAL_MAP, Map<Function, Function>>()

export const setCallbackCache = (type: keyof SIGNAL_MAP, key: Function, value: Function) => {
  let cache = callbackCache.get(type)
  if (!cache) {
    callbackCache.set(type, new Map())
    cache = callbackCache.get(type)
  }
  cache.set(key, value)
}

export const getCallbackCache = (type: keyof SIGNAL_MAP, key: Function) => {
  const cache = callbackCache.get(type)
  if (!cache) {
    return
  }
  const v = cache.get(key)
  if (!v) {
    return
  }
  cache.delete(key)
  return v
}

export const clearCallbackCache = (callback: (value: Function, key: Function) => void) => {
  callbackCache.forEach((value, t) => {
    value.forEach(callback)
  })
  callbackCache.clear()
}
