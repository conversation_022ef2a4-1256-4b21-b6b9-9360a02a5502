export type SystemContext = {
  // 信息开始用户
  // 用户唯一标识。
  userGuid?: string
  // 用户代码。
  userCode?: string
  // 用户名称。
  userName?: string
  // 用户手机号码。
  mobile?: string

  //页面信息
  // 页面名称。
  pageName?: string
  // 页面URL。
  pageUrl?: string
  // 应用类型，可能是'cloud'或'erp'。
  appType?: 'cloud' | 'erp'
  // 应用代码。
  appCode?: string
  // 应用名称。
  appName?: string
  // 应用版本号。
  appVersion?: string
  // 模块代码。
  moduleCode?: string
  // 模块名称。
  moduleName?: string

  //特殊信息
  // ERP版本号。
  erpVersion?: string
  // 扩展数据，用于存储额外的信息。
  metadata?: string

  //客户信息
  // 客户唯一标识。
  customerId?: string
  // 客户名称。
  customerName?: string

  //租户信息
  // 租户代码。
  tenantCode?: string
  // 租户名称。
  tenantName?: string
}
