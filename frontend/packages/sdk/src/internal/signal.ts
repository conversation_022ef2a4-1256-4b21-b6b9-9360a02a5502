import { clearCallbackCache, getCallbackCache, setCallbackCache } from './cache'
import { type ChatSyncOptions } from './chat'
import { type ConfigSetOptions } from './config'
import { type NotificationOptions } from './notification'
import {
  type CardActionEvent,
  type ErrorEvent,
  type OpenOptionsForSignal,
  type SkillRunningEvent,
} from './open'
import { type PageOpenOptions } from './page'
import { type PlanInvokeParams, type PlanQueryParams } from './plan'
import { type ScreenshotResult } from './screenshot'
import { type SkillShortcutsOptions } from './skill'
import { type ToolInvokeOptions } from './tool'

const CALLBACK_SUCCESS_SUFFIX = '_success_callback'
const CALLBACK_FAIL_SUFFIX = '_fail_callback'

const ACTION_OPEN_CARD_ACTION = 'skyline_gpt_action_chat_open_card_action'

//定义信号类型和信号类型对应的匹配
export enum SIGNAL {
  // Config
  ActionConfigSet = 'skyline_gpt_action_config_set',
  ActionConfigShow = 'skyline_gpt_action_config_show',
  // Page
  ActionPageOpen = 'skyline_gpt_action_page_open', // 打开
  ActionPageClose = 'skyline_gpt_action_page_close', // 关闭
  // Chat
  ActionChatClose = 'skyline_gpt_action_chat_close', // 关闭
  ActionOpen = 'skyline_gpt_action_chat_open', // 打开
  ActionClose = 'skyline_gpt_action_close',
  ActionOpenCardAction = ACTION_OPEN_CARD_ACTION, // CardAction 触发
  ActionOpenCardActionSuccessCallback = ACTION_OPEN_CARD_ACTION + CALLBACK_SUCCESS_SUFFIX, // CardAction 执行成功触发
  ActionOpenCardActionFailCallback = ACTION_OPEN_CARD_ACTION + CALLBACK_FAIL_SUFFIX, // CardAction 执行失败触发
  ActionOpenErrorCallback = 'skyline_gpt_action_chat_open_error_callback',
  ActionOpenSkillRunning = 'skyline_gpt_action_chat_open_skill_running',
  ActionOpenCloseCallback = 'skyline_gpt_action_chat_open_close_callback',
  ActionOpenChatCallback = 'skyline_gpt_action_chat_open_chat_callback',
  ActionOpenChatStopCallback = 'skyline_gpt_action_chat_open_chat_stop_callback',
  ActionChatOpen = 'skyline_gpt_action_chat_page_open', // 打开
  ActionChatMessageSend = 'skyline_gpt_action_chat_message_send', // 发送
  ActionChatMessageSync = 'skyline_gpt_action_chat_message_sync', // 发送
  ActionChatWelcomeMessageSend = 'skyline_gpt_action_welcome_message_send', // 欢迎消息

  //  通知类型 Notify
  NotifyMounted = 'skyline_gpt_notify_mounted',
  NotifyMountError = 'skyline_gpt_notify_mount_error',
  // config初始化完成
  NotifyConfigMounted = 'skyline_gpt_notify_config_mounted',
  NotifyConfigError = 'skyline_gpt_notify_config_error',

  //   媒体 预览
  ActionMediaPreview = 'skyline_gpt_media_preview',
  //   媒体 发送通知
  ActionMediaNotification = 'skyline_gpt_media_notification',
  //   媒体 截屏
  ActionMediaScreenshot = 'skyline_gpt_media_screenshot',
  ActionMediaScreenshotResult = 'skyline_gpt_media_screenshot_result',

  ActionSetToken = 'skyline_gpt_set_token',
  //工具类型
  ActionToolInvoke = 'skyline_gpt_tool_invoke',
  //工具类型
  ActionToolInvokeResult = 'skyline_gpt_tool_invoke_result',

  //技能快捷指令
  ActionShortcuts = 'skyline_gpt_shortcuts',

  //使用方案实例ID查询方案结果
  ActionPlanQuery = 'skyline_gpt_plan_query',
  //执行方案
  ActionPlanInvoke = 'skyline_gpt_plan_invoke',
}
export type SIGNAL_MAP = {
  //打开/关闭侧边栏
  [SIGNAL.ActionPageOpen]: PageOpenOptions
  [SIGNAL.ActionPageClose]: undefined

  //打开聊天
  [SIGNAL.ActionOpen]: OpenOptionsForSignal | undefined
  //打开聊天
  [SIGNAL.ActionClose]: undefined

  [SIGNAL.ActionOpenCardAction]: CardActionEvent
  [SIGNAL.ActionOpenCardActionSuccessCallback]: { id: string }
  [SIGNAL.ActionOpenCardActionFailCallback]: { id: string }
  [SIGNAL.ActionOpenErrorCallback]: ErrorEvent
  [SIGNAL.ActionOpenSkillRunning]: SkillRunningEvent
  [SIGNAL.ActionOpenCloseCallback]: undefined
  [SIGNAL.ActionOpenChatCallback]: { id: string }
  [SIGNAL.ActionOpenChatStopCallback]: undefined

  //关闭弹出的对话框
  [SIGNAL.ActionChatClose]: undefined

  //发送消息
  [SIGNAL.ActionChatMessageSync]: ChatSyncOptions
  [SIGNAL.ActionChatMessageSend]: string
  [SIGNAL.ActionChatWelcomeMessageSend]: string

  //配置&初始化相关相关
  [SIGNAL.ActionConfigSet]: ConfigSetOptions
  [SIGNAL.ActionConfigShow]: undefined

  //GPT给出的通知
  [SIGNAL.NotifyMounted]: undefined
  [SIGNAL.NotifyMountError]: undefined
  [SIGNAL.NotifyConfigMounted]: undefined
  [SIGNAL.NotifyConfigError]: undefined

  //媒体类型
  [SIGNAL.ActionMediaPreview]: string[] | string

  [SIGNAL.ActionMediaNotification]: NotificationOptions
  [SIGNAL.ActionMediaScreenshot]: undefined
  [SIGNAL.ActionMediaScreenshotResult]: ScreenshotResult

  [SIGNAL.ActionSetToken]: string

  [SIGNAL.ActionToolInvoke]: ToolInvokeOptions
  // biome-ignore lint/suspicious/noExplicitAny: off
  [SIGNAL.ActionToolInvokeResult]: any

  [SIGNAL.ActionShortcuts]: SkillShortcutsOptions
  [SIGNAL.ActionChatOpen]: PageOpenOptions

  [SIGNAL.ActionPlanQuery]: string | PlanQueryParams
  [SIGNAL.ActionPlanInvoke]: string | PlanInvokeParams
}

//强制
export const SIGNAL_FORCE_PARENT: SIGNAL[] = [SIGNAL.NotifyMounted, SIGNAL.NotifyMountError]
//发送信号
export const signal = <K extends keyof SIGNAL_MAP>(
  type: K,
  data?: SIGNAL_MAP[K],
  host?: Window,
) => {
  let h = host || getHost()
  if (SIGNAL_FORCE_PARENT.includes(type)) {
    h = host || getHost(window.parent)
  }
  const senders: Window[] = [window]
  //不然逐级发
  try {
    if (host && senders.every((w) => w !== host)) {
      senders.push(host)
    }
    if (senders.every((w) => w !== window.parent)) {
      senders.push(window.parent)
    }
    if (senders.every((w) => w !== window.top)) {
      senders.push(window.top)
    }
    //TODO 现在是临时方案，后续需要改成递归  2级
    if (senders.every((w) => w !== window.parent.parent)) {
      senders.push(window.parent.parent)
    }
    //TODO 现在是临时方案，后续需要改成递归  3级
    if (senders.every((w) => w !== window.parent.parent.parent)) {
      senders.push(window.parent.parent.parent)
    }
  } catch (error) {
    if (error) {
      console.warn(`GPT: Error sending signal ${type}:`, error.message)
    }
  } finally {
    senders.forEach((w) =>
      w.postMessage(
        {
          type,
          data,
        },
        '*',
      ),
    )
  }
}

const getHost = (def: Window = window): Window => {
  try {
    if (typeof window.parent.addEventListener === 'function') {
      return window.parent
    }
    return def
  } catch (error) {
    return def
  }
}

//开始监听信号函数
export const signalListener = <K extends keyof SIGNAL_MAP>(
  type: K,
  callback: (data: SIGNAL_MAP[K], source: Window) => void,
  once = false,
  host: Window = getHost(),
) => {
  // 因为事件会进行监听和释放，所以我们需要缓存一下
  const eventFunction = (event) => {
    if (event.data.type === type) {
      const eventData = event.data.data
      const res: any = callback(eventData, event.source)
      // 如果是promise，需要捕获到异步执行成功或失败的回调
      if (eventData?.data?.name === 'card-confirm' && eventData?.data?.message?.id) {
        if (typeof res === 'object' && typeof res.then === 'function') {
          res.then(
            () => {
              event.source.postMessage({
                type: type + CALLBACK_SUCCESS_SUFFIX,
                data: { id: eventData.data.message.id },
              })
            },
            () => {
              event.source.postMessage({
                type: type + CALLBACK_FAIL_SUFFIX,
                data: { id: eventData.data.message.id },
              })
            },
          )
        } else {
          event.source.postMessage({
            type: type + CALLBACK_SUCCESS_SUFFIX,
            data: { id: eventData.data.message.id },
          })
        }
      }
      if (once) {
        signalListenerClear(type, eventFunction)
      }
    }
  }

  setCallbackCache(type, callback, eventFunction)

  host.addEventListener('message', eventFunction)
}

//清除某个监听
export const signalListenerClear = <K extends keyof SIGNAL_MAP>(
  type: K,
  callback: (data: SIGNAL_MAP[K], source: Window) => void,
  win: Window = getHost(),
) => {
  const eventFunction = getCallbackCache(type, callback)
  if (eventFunction) {
    // biome-ignore lint/suspicious/noExplicitAny: off
    win.removeEventListener('message', eventFunction as any)
  }
}

//清除所有监听
export const signalListenerClearAll = () => {
  clearCallbackCache((value) => {
    // biome-ignore lint/suspicious/noExplicitAny: off
    getHost().removeEventListener('message', value as any)
  })
}
