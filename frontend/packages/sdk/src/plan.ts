import {
  PlanInvokeParams,
  PlanQueryParams,
  SIGNAL,
  signal,
  signalListener,
  signalListenerClear,
} from './internal'

export type AIPlanInvokeParams = PlanInvokeParams | string

// 智能检查执行规则
const invoke = (options: AIPlanInvokeParams) => {
  if (typeof options === 'string') {
    onExecute({ id: options }, SIGNAL.ActionPlanInvoke)
  } else {
    onExecute(options, SIGNAL.ActionPlanInvoke)
  }
}

export type AIPlanQueryParams = PlanQueryParams | string

// 使用方案实例ID查询方案结果
const query = (params: string | PlanQueryParams) => {
  onExecute(params, SIGNAL.ActionPlanQuery)
}

let openCardCloseCallback = () => {}
const onExecute = (params: string | PlanQueryParams | PlanInvokeParams, key: SIGNAL) => {
  signalListenerClear(SIGNAL.ActionOpenCloseCallback, openCardCloseCallback, window)
  const newOptions = params as string | PlanQueryParams
  if (typeof newOptions === 'object' && newOptions.onClose) {
    openCardCloseCallback = newOptions.onClose
    newOptions.onClose = undefined
    newOptions.hasOnClose = true
  }
  signal(key, newOptions)
  signalListener(SIGNAL.ActionOpenCloseCallback, openCardCloseCallback, false, window)
}

export default {
  invoke,
  query,
}
