import { SIGNAL, signal, signalListener, SystemContext } from './internal'

export type AIContext = SystemContext

/**
 * 设置助手上下文
 * @param ctx 助手上下文
 */
export const setContext = (context: AIContext = {}): Promise<void> => {
  signal(SIGNAL.ActionConfigSet, { context })
  return new Promise<void>((resolve, reject) => {
    signalListener(SIGNAL.NotifyConfigMounted, resolve)
    signalListener(SIGNAL.NotifyConfigError, reject)
  })
}
