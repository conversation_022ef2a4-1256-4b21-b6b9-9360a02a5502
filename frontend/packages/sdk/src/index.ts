import { default as chat } from './chat'
import { default as kefu } from './kefu'
import { default as media } from './media'
import { default as page } from './page'
import { default as plan } from './plan'
import { default as skill } from './skill'
import { default as tool } from './tool'

import { setContext } from './context'
import { createIcon } from './icon'
import { init } from './init'
import { notification } from './notification'
import { open } from './open'
import { setAccessToken } from './token'
import { version } from './version'
import { close, hide, show } from './workbench'

// 部分类型暂时不公开
// export { AIChatOpenOptions, AIChatSyncOptions } from './chat'
// export { AICreateIconOptions, createIcon } from './icon'
// export { AINotificationOptions, notification } from './notification'
// export { AIPageOpenOptions } from './page'
// export { AIScreenshotResult } from './screenshot'
// export { AISkillShortcutsOptions } from './skill'
// export { AIToolInvokeParams } from './tool'

export { AIContext, setContext } from './context'
export { AIInitOptions, init } from './init'
export { AIKefuOpenOptions } from './kefu'
export { AIOpenOptions, open } from './open'
export { AIPlanInvokeParams, AIPlanQueryParams } from './plan'
export { setAccessToken } from './token'
export { close, hide, show } from './workbench'

// 名字空间方法
export { chat, kefu, media, page, plan, skill, tool, version }

export default {
  //根方法
  setAccessToken,
  open,
  show,
  hide,
  close,
  setContext,
  init,
  createIcon,
  notification,
  //子类方法
  page,
  chat,
  media,
  version,
  tool,
  skill,
  plan,
  kefu,
}
