import { decode } from 'js-base64'
import { setContext } from './context'
import { createScript, isUrl, SIGNAL, signalListener } from './internal'
import { setAccessToken } from './token'

type InitOptions = {
  token?: string
  container?: string
  feature?: string
  share?: string
  history?: boolean
  accessToken?: string
  url?: string
  visible?: boolean
}

export type AIInitOptions = string | InitOptions | undefined

/**
 * 设置助手上下文
 * @param ctx 函数初始化
 */
export const init = (options: AIInitOptions = {}) => {
  if (typeof options === 'object') {
    const keys = Object.keys(options)
    // 没有给选项
    const isEmptyOptions = !keys.length
    const isFeatureOptions = options.feature
    if (isEmptyOptions || isFeatureOptions) {
      return initLauncher(options)
    }
  }

  if (isUrl(options)) {
    return initUrlWithStatus(options)
  }

  return initOptions(options)
}

// 在建模站点内启动的场景
const initLauncher = (options: InitOptions = {}) => {
  // 使用 AI 平台内启动器资源
  const url = new URL(location.origin)

  url.pathname = '/gptbuilder/launcher/index.js'

  // 若提供了 feature 参数，则拼接到 js 后
  if (options.feature) {
    url.searchParams.set('feature', options.feature)
  }
  // 通过 url 方式加载
  return initUrlWithUrl(url.toString())
}

export const initUrlWithUrl = (url: string) => {
  let script = document.querySelector(`[src='${url}']`)
  if (script) {
    return Promise.resolve()
  }
  if (typeof url !== 'string') {
    console.warn('请检查资源地址是否正确')
    return
  }
  script = createScript(url, {})
  return new Promise<void>((resolve, reject) => {
    signalListener(SIGNAL.NotifyMounted, () => {
      resolve()
    })
    signalListener(SIGNAL.NotifyMountError, reject)
  })
}

export const initUrlWithStatus = (url: string) => {
  let script = document.querySelector('[data-gpt-status]')
  if (script) {
    return Promise.resolve()
  }
  if (typeof url !== 'string') {
    console.warn('请检查资源地址是否正确')
    return
  }
  script = createScript(url, { 'data-gpt-status': 'loading' })
  return new Promise<void>((resolve, reject) => {
    signalListener(SIGNAL.NotifyMounted, () => {
      script.setAttribute('data-gpt-status', 'loaded')
      resolve()
    })
    signalListener(SIGNAL.NotifyMountError, reject)
  })
}

export const initOptions = (options: AIInitOptions = {}) => {
  try {
    let {
      token,
      container,
      share,
      url,
      history = false,
      accessToken,
      visible,
    } = options as InitOptions

    // 存在 token
    if (token) {
      const data = JSON.parse(decode(token || '')) as {
        shareCode: string
        jsUrl: string
        history: string | number | boolean
        accessToken: string
      }
      if (data.shareCode) {
        share = data.shareCode
      }
      if (data.jsUrl) {
        url = data.jsUrl
      }
      if (data.accessToken) {
        accessToken = data.accessToken
      }
      if (typeof data.history !== undefined) {
        // 文本/数值/布尔都转换为文本对比
        const v = String(data.history)
        history = v === '1' || v === 'true'
      }
    }

    // 必须存在 url
    if (!url) {
      return
    }

    const attrs = {
      id: 'skyline-gpt',
      'data-gpt-status': 'loading',
    }
    const u = new URL(url)
    if (share) {
      u.searchParams.set('share', share)
    }

    if (typeof history === 'boolean') {
      u.searchParams.set('history', history === true ? '1' : '0')
    }

    if (typeof visible === 'boolean') {
      u.searchParams.set('visible', visible === true ? '1' : '0')
    }

    if (!container) {
      if (u.searchParams.get('container')) {
        container = u.searchParams.get('container')
      }
    }

    if (container) {
      u.searchParams.set('container', container)
      // 兼容老逻辑，正式上线后移除
      attrs['data-gpt-container'] = container
    }
    if (accessToken) {
      u.searchParams.set('access_token', accessToken)
    }
    url = u.toString()

    const script = createScript(url, attrs)

    return new Promise<void>((resolve, reject) => {
      signalListener(SIGNAL.NotifyMounted, () => {
        script.setAttribute('data-gpt-status', 'loaded')
        if (accessToken) {
          setAccessToken(accessToken)
        }
        setContext().then(resolve).catch(reject)
      })
      signalListener(SIGNAL.NotifyMountError, reject)
    })
  } catch (err) {
    console.log(err)
  }
}
