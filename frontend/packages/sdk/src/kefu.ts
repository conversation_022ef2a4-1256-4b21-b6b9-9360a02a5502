import { AIContext } from './context'
import { initUrlWithStatus } from './init'
import { open as openOrigin } from './open'

const launcher = 'https://aiengine.tj.mycyjg.com/gptbuilder/launcher/kf.js'

// 初始化客服
const init = () => {
  return initUrlWithStatus(launcher)
}

interface KefuOpenOptions {
  context: AIContext
}

export type AIKefuOpenOptions = KefuOpenOptions

// 打开客服窗口
const open = (options: AIKefuOpenOptions) => {
  return openOrigin({
    feature: 'kefu',
    context: options.context,
  })
}

export default {
  init,
  open,
}
