import {
  CardActionEvent,
  ErrorEvent,
  OpenOptions,
  OpenOptionsForSignal,
  signal,
  SIGNAL,
  signalListener,
  signalListenerClear,
  SkillRunningEvent,
} from './internal'

let openCardActionCallback = (e: CardActionEvent) => {}

let openCardErrorCallback = (e: ErrorEvent) => {}

let openCardSkillRunningCallback = (e: SkillRunningEvent) => {}

let openCardCloseCallback = () => {}

let openCardChatCallback = (e: { id: string }) => {}

let openCardChatStopCallback = () => {}

export type AIOpenOptions = OpenOptions

/**
 * 打开聊天窗口
 */
export const open = (options?: AIOpenOptions) => {
  signalListenerClear(SIGNAL.ActionOpenCardAction, openCardActionCallback, window)
  signalListenerClear(SIGNAL.ActionOpenErrorCallback, openCardErrorCallback, window)
  signalListenerClear(SIGNAL.ActionOpenSkillRunning, openCardSkillRunningCallback, window)
  signalListenerClear(SIGNAL.ActionOpenCloseCallback, openCardCloseCallback, window)
  signalListenerClear(SIGNAL.ActionOpenChatCallback, openCardChatCallback, window)
  signalListenerClear(SIGNAL.ActionOpenChatStopCallback, openCardChatStopCallback, window)
  const newOptions = options as OpenOptionsForSignal
  if (newOptions && newOptions.onCardAction) {
    //暂存回调函数
    openCardActionCallback = newOptions.onCardAction
    newOptions.onCardAction = undefined
    newOptions.hasOnCardAction = true
  }
  if (newOptions && newOptions.onError) {
    //暂存回调函数
    openCardErrorCallback = newOptions.onError
    newOptions.onError = undefined
    newOptions.hasOnError = true
  }
  if (newOptions && newOptions.onSkillRunning) {
    openCardSkillRunningCallback = newOptions.onSkillRunning
    newOptions.onSkillRunning = undefined
    newOptions.hasOnSkillRunning = true
  }
  if (newOptions && newOptions.onClose) {
    openCardCloseCallback = newOptions.onClose
    newOptions.onClose = undefined
    newOptions.hasOnClose = true
  }
  if (newOptions && newOptions.onChat) {
    openCardChatCallback = newOptions.onChat
    newOptions.onChat = undefined
    newOptions.hasOnChat = true
  }
  if (newOptions && newOptions.onSkillStop) {
    openCardChatStopCallback = newOptions.onSkillStop
    newOptions.onSkillStop = undefined
    newOptions.hasOnSkillStop = true
  }
  signal(SIGNAL.ActionOpen, options)
  //这里锁定死是自己的window
  signalListener(SIGNAL.ActionOpenCardAction, openCardActionCallback, false, window)
  signalListener(SIGNAL.ActionOpenErrorCallback, openCardErrorCallback, false, window)
  signalListener(SIGNAL.ActionOpenSkillRunning, openCardSkillRunningCallback, false, window)
  signalListener(SIGNAL.ActionOpenCloseCallback, openCardCloseCallback, false, window)
  signalListener(SIGNAL.ActionOpenChatCallback, openCardChatCallback, false, window)
  signalListener(SIGNAL.ActionOpenChatStopCallback, openCardChatStopCallback, false, window)
}
