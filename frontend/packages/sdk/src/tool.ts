import { SIGNAL, signal, signalListener, signalListenerClear } from './internal'

export type AIToolInvokeParams = Record<string, unknown>
/**
 * 打开页面
 * @param string id
 * @param any params
 */
export const invoke = (id: string, params: AIToolInvokeParams) => {
  signal(SIGNAL.ActionToolInvoke, {
    id,
    params,
  })
  // biome-ignore lint/suspicious/noExplicitAny: off
  return new Promise<any>((resolve, reject) => {
    const func = (data, source) => {
      signalListenerClear(SIGNAL.ActionToolInvokeResult, func)
      resolve(data)
    }
    signalListener(SIGNAL.ActionToolInvokeResult, func)
  })
}

export default {
  invoke,
}
