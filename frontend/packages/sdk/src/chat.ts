import { ChatOpenOptions, ChatSyncOptions, SIGNAL, signal } from './internal'

export type AIChatSyncOptions = ChatSyncOptions

/**
 * 同步聊天消息
 * @param {AIChatSyncOptions} options
 * @returns
 */
const sync = (options: AIChatSyncOptions) => signal(SIGNAL.ActionChatMessageSync, options)

/**
 * 发送聊天消息
 * @param {String} message 消息
 */
const send = (message: string) => {
  signal(SIGNAL.ActionChatMessageSend, message)
}

export type AIChatOpenOptions = ChatOpenOptions

/**
 * 在聊天界面打开页面
 * @param options
 * @returns
 */
const open = (options: AIChatOpenOptions) => signal(SIGNAL.ActionChatOpen, options)

/**
 * 欢迎语
 * @param {String} message 消息内容
 */
const welcome = (message: string) => signal(SIGNAL.ActionChatWelcomeMessageSend, message)

export default {
  open,
  welcome,
  send,
  sync,
}
