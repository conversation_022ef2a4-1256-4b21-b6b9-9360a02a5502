import { defineConfig } from 'tsup'

export default defineConfig([
  {
    entry: ['src/index.ts'],
    format: ['cjs', 'esm'],
    dts: true,
    clean: true,
    target: 'es5',
    splitting: false,
    sourcemap: false,
  },
  {
    entry: ['src/internal/index.ts'],
    format: ['cjs', 'esm'],
    outDir: 'internal/dist',
    target: 'esnext',
    dts: true,
    clean: true,
    splitting: false,
    sourcemap: false,
  },
])
