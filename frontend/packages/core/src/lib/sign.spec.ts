import 'bun'
import { expect, test } from 'bun:test'
import { decrypt, seed } from './sign'

// 加密函数，不打包，对应的解密在 header.ts 里
const encrypt = (input: string, seed: number) => {
  let bytes = Array.from(input, (char) => char.charCodeAt(0)) // 将输入转为字节数组

  // 三轮加密
  for (let round = 0; round < 3; round++) {
    let temp = bytes.slice() // 复制当前字节数组
    for (let i = 0; i < temp.length; i++) {
      let val = temp[i]
      // 生成混淆值，依赖种子、位置和轮次
      let mix = ((seed >>> i % 32) ^ round) & 0xff

      // 第一步：异或
      val ^= mix
      // 第二步：循环左移 3 位
      val = ((val << 3) | (val >>> 5)) & 0xff
      // 第三步：加法混淆
      val = (val + (mix & 0x7f)) & 0xff
      // 第四步：再次异或（基于位置）
      val ^= i & 0xff

      bytes[i] = val
    }
  }

  // 转为十六进制字符串输出
  return bytes.map((b) => b.toString(16).padStart(2, '0')).join('')
}

// pnpm test 运行单测结果

test('sign crypto', () => {
  // 原始文本
  const original = 'ZD3MZOlQkLbaG0Ecs1S171mQQDEvmsKExJXfkxoYPgQ='

  const encrypted = encrypt(original, seed)

  const encrypted2 =
    '2b66fd3f544fc6b94fac1c91fd2f2147a7bc7e1e0b392b5ab52d7ef8ef09a0e0404de0cd82bb99e0a8b1e931'

  expect(encrypted).toBe(encrypted2)

  const decrypted = decrypt(encrypted, seed)
  expect(decrypted).toBe(original)
})
