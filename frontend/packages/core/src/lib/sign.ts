export const seed = 0x5a7c9e3f // 相同的固定种子

// 解密函数
export const decrypt = (hexInput: string, seed: number): string => {
  let bytes: number[] = []
  // 将十六进制字符串转为字节数组
  for (let i = 0; i < hexInput.length; i += 2) {
    bytes.push(parseInt(hexInput.slice(i, i + 2), 16))
  }

  // 三轮解密（逆序）
  for (let round = 2; round >= 0; round--) {
    let temp = bytes.slice() // 复制当前字节数组
    for (let i = 0; i < temp.length; i++) {
      let val = temp[i]
      // 生成相同的混淆值
      let mix = ((seed >>> i % 32) ^ round) & 0xff

      // 逆向操作
      val ^= i & 0xff // 还原第四步：异或位置
      val = (val - (mix & 0x7f)) & 0xff // 还原第三步：减法
      val = ((val >>> 3) | (val << 5)) & 0xff // 还原第二步：循环右移 3 位
      val ^= mix // 还原第一步：异或

      bytes[i] = val
    }
  }

  // 将字节数组转为字符串
  return String.fromCharCode(...bytes)
}
