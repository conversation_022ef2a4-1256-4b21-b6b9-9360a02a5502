import dayjs from 'dayjs'
import { round } from 'number-precision'
import type { CardParam, CardParams } from './types'

export type CardParamValue =
  | string
  | number
  | boolean
  | null
  | CardParamObject
  | CardParamArray
  | Date
export type CardParamObject = { [key: string]: CardParamValue }
export type CardParamArray = CardParamValue[]

const getArrayValue = (value: CardParamValue) => {
  try {
    if (typeof value === 'string') {
      value = JSON.parse(value || '[]')
    } else if (Array.isArray(value)) {
      //
    } else {
      value = []
    }
  } catch (error) {
    value = []
  }
  return value as CardParamArray
}

const getObjectValue = (value: CardParamValue) => {
  if (value && typeof value === 'string') {
    try {
      value = JSON.parse(value)
    } catch (error) {
      value = {}
    }
  }
  if (typeof value === 'undefined') {
    value = {}
  }
  return value as CardParamObject
}

const getNumberValue = (value: CardParamValue) => {
  if (typeof value === 'undefined' || value === '' || value === null) {
    value = null
  } else {
    const v = round(value as string, 6)
    if (isNaN(value as number)) {
      value = 0
    }
    // 避免超出安全整数范围显示异常
    if (v >= Number.MIN_SAFE_INTEGER && v <= Number.MAX_SAFE_INTEGER) {
      value = v
    }
  }
  return value as number | null
}

const getDateValue = (value: CardParamValue) => {
  if (value) {
    const val = dayjs(value as string)
    if (val.isValid()) {
      value = val.toDate()
    } else {
      value = null
    }
  }
  // 日期值不存在(undefined、空文本等])统一设置为null
  if (!value) {
    value = null
  }
  return value as Date | null
}

export const getCardParamValue = (param: CardParam) => {
  let value = param.literalValue as CardParamValue

  if (value && typeof value === 'string') {
    value = value.trim()
  }

  if (['array<object>', 'array<string>', 'array<number>'].includes(param.type)) {
    value = getArrayValue(value)
  }
  if (param.type === 'object') {
    value = getObjectValue(value)
  }
  if (param.type === 'number') {
    value = getNumberValue(value)
  }
  if (param.type === 'date') {
    value = getDateValue(value)
  }
  return value
}

// 是否引用其他节点的参数
const isReferenceParam = (param: CardParam) => {
  if (typeof param.value === 'object') {
    return param.value.type === 'ref' && !!param.value.content
  }
  return false
}

export const walkCardParams = (
  params: CardParams,
  iterator: (param: CardParam, key: string, reference: boolean) => void,
) => {
  Object.keys(params).forEach((key) => {
    // 获取参数
    const param = params[key]
    const reference = isReferenceParam(param)

    iterator(param, key, reference)
  })
}

// 将参数数组转换为对象
export const getCardParams = (
  params: CardParam[] = [],
  // 迭代器
  iterator?: (v: CardParam, reference: boolean) => CardParam | void,
): CardParams => {
  return params.reduce((r, param) => {
    if (typeof iterator === 'function') {
      const reference = isReferenceParam(param)
      const c = iterator(param, reference)
      // 修改参数
      if (typeof c !== 'undefined' && param.code === c.code) {
        param = c
      }
    }
    r[param.code] = param
    return r
  }, {} as CardParams)
}

export const formatCardParamValue = (value: CardParamValue, type: string) => {
  if (!value) {
    return ''
  }
  if (type === 'date') {
    const val = dayjs(value as string)
    if (val.isValid()) {
      return val.format('YYYY-MM-DD')
    }
  }
  if (typeof value === 'string') {
    return value
  }
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  if (typeof value === 'number') {
    return value.toString()
  }
  return String(value)
}

// 获取卡片原始参数数据
export const getCardOriginalParams = (
  data: CardParamObject,
  outputs: CardParams,
  nodeCode: string,
) => {
  const args: Record<string, string> = {}
  Object.keys(outputs).forEach((key) => {
    if (key in data) {
      // 仅更新输出参数配置存在数据
      const code = `NodeOutput_${nodeCode}_${key}`
      const param: CardParam = outputs[key]
      const value = data[key]
      args[code] = formatCardParamValue(value, param.type || '')
    }
  })

  return args
}
