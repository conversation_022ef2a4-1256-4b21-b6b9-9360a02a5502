import type { CardData, CardParam, CardProp } from '..'

export type FormDataValue = string | { text: string; value: string } | any

export type FormDataMap = {
  code: string
  key: string
  value: FormDataValue
  type: string
}

// 表单字段列
export type FormColumn = {
  name: string
  code: string
  type: string
  required: boolean
  readonly: boolean
  columns?: FormColumn[]
  options?: { text: string; value: string }[]
  thousands?: boolean
}

export interface FormEditorProb {
  minProb: number
  maxProb: number
  color: string
  text: string
}

export type ProbRule = { maxProb: number; minProb: number; priority: number }

export type ProbMap = Record<string, number | undefined | Record<string, number | undefined>[]>

interface FromCardProp extends CardProp {}

// 表单卡片参数
interface FormCardParam extends CardParam {
  // 将表单字段列，临时存储到参数上
  config?: FormColumn
}

// 表单卡片数据
export interface FormCardData extends CardData {
  outputs: FormCardParam[]
  title: string

  data: FormDataMap[]

  props: FromCardProp[]

  probRules: ProbRule[]
  probResult: Record<string, number>
}

export interface FormEditorProb {
  minProb: number
  maxProb: number
  color: string
  text: string
}
