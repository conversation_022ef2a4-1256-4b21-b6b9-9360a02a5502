export type CardParam = {
  code: string
  name: string
  type: string
  required: boolean
  description?: string | null
  defaultValue?: string | null
  value: { content: string; type: string }
  literalValue?: string
  literalCode?: string
  schema?: CardParam[] | null

  // config?: FormColumn
}

export type CardParams = Record<string, CardParam>

export interface CardData {
  // 节点编码
  nodeCode: string
  // 输入参数
  inputs: CardParam[]
  // 输出参数
  outputs: CardParam[]

  title?: string
  content?: string
}

export type CardPropValue = { code: string; name: string; description?: string }[]

export type CardProp = {
  name: string
  value: string | CardPropValue
}

// 将数组属性转换为对象属性
export interface CardProps extends Record<string, unknown> {}
