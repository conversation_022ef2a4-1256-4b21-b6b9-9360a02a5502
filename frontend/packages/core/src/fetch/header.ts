import md5 from 'md5'
import { decrypt, seed } from '../lib/sign'

// 已加密key
const decrypted =
  '2b66fd3f544fc6b94fac1c91fd2f2147a7bc7e1e0b392b5ab52d7ef8ef09a0e0404de0cd82bb99e0a8b1e931'

// 解密 key
const key = decrypt(decrypted, seed)

const tsKey = 'x-ai-ts'
const signKey = 'x-ai-sign'

// 添加 sign 头
export const withFetchSignature = (options: RequestInit) => {
  if (typeof options.headers === 'undefined') {
    options.headers = {} as HeadersInit
  }

  const ts = Math.ceil(new Date().getTime() / 1e3).toString()
  const body = typeof options?.body === 'string' ? options.body : ''

  const sign = md5(`${key}${ts}${body}`)

  if (options.headers instanceof Headers) {
    options.headers.set(tsKey, ts)
    options.headers.set(signKey, sign)
  } else {
    Object.assign(options.headers, { [tsKey]: ts, [signKey]: sign })
  }

  return options
}
