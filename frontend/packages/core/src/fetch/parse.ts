import { getCardProps } from '../card'
import { StreamDataParseError } from './error'
import {
  MessageType,
  type StreamResult,
  type StreamResultCard,
  type StreamResultDone,
  type StreamResultError,
  type StreamResultErrorInfo,
  type StreamResultProcess,
  type StreamResultReplace,
} from './types'

const parseText = (text: string): StreamResult => {
  return {
    text: text.replace(/\|n/g, '\n'),
  }
}
const parseReasoning = (text: string): StreamResult => {
  return {
    reasoning: text.replace(/\|n/g, '\n'),
  }
}

const parseHeartbeat = (text: string): StreamResult => {
  return {
    heartbeat: text,
  }
}

const parseProcess = (text: string): StreamResult => {
  return {
    process: JSON.parse(text) as StreamResultProcess,
  }
}

export const parseError = (text: string): StreamResult => {
  const info: StreamResultErrorInfo = JSON.parse(text || '{}')
  return {
    error: {
      error: info.UserMessage || '',
      code: info.ErrorCode || '',
      message: info.ErrorMessage || '',
      type: info.Type,
      link: info.Link || '',
    } as StreamResultError,
  }
}

const parseData = (text: string): StreamResult => {
  const data = (JSON.parse(text) as StreamResultCard[]) || []
  data.forEach((d) => {
    d.status = d.type === 'plan' ? 'waiting' : 'done'
    if (d.type === 'card' || d.type === 'page') {
      // 转换props为key-value形式
      d.data.props = getCardProps(d.data?.props)
      // 修正指令类型的卡片类型（嵌入卡片-页面打开、消息内容）
      const { mount } = d.data.props
      // 该模式为指令方式，无需渲染真实dom
      if (mount === 'chat' || mount === 'page') {
        d.command = 'page'
      }
    }
    if (d.type === 'form-binding') {
      d.command = 'action'
    }
  })

  return {
    data,
  }
}

const parseDone = (text: string): StreamResult => {
  return {
    done: {
      id: text,
    } as StreamResultDone,
  }
}

const parseReplace = (text: string): StreamResult => {
  return {
    replace: JSON.parse(text) as StreamResultReplace[],
  }
}

const parseRecovery = (text: string): StreamResult => {
  return {
    recovery: !!text ? text : 'recovery',
  }
}

const parseStream = (text: string): StreamResult => {
  return {
    stream: JSON.parse(text),
  }
}

//数据转化
export const parseResponse = (responseString: string): StreamResult => {
  if (!responseString) {
    return { text: '' }
  }
  let firstSeparatorIndex = responseString.indexOf(':')

  if (firstSeparatorIndex === -1 || firstSeparatorIndex > 2) {
    return {
      text: responseString,
    }
  }

  const typeStr = responseString.slice(0, firstSeparatorIndex)
  const dataStr = responseString.slice(firstSeparatorIndex + 1)
  try {
    const typeNumber = parseInt(typeStr, 10)
    switch (typeNumber) {
      // 返回文本消息
      case MessageType.text:
        return parseText(dataStr)

      // 当前的处理过程
      case MessageType.process:
        return parseProcess(dataStr)

      // 返回错误
      case MessageType.error:
        return parseError(dataStr)

      // 返回卡片
      case MessageType.data:
        return parseData(dataStr)

      // 完成
      case MessageType.done:
        return parseDone(dataStr)

      // 替换符
      case MessageType.replace:
        return parseReplace(dataStr)

      // 异常回收
      case MessageType.recovery:
        return parseRecovery(dataStr)

      // 流式数据
      case MessageType.stream:
        return parseStream(dataStr)

      // 推理过程
      case MessageType.reasoning:
        return parseReasoning(dataStr)

      // 心跳
      case MessageType.heartbeat:
        return parseHeartbeat(dataStr)
      default:
        return {}
    }
  } catch (e) {
    throw new StreamDataParseError('编排过程数据转化异常:' + responseString)
  }
}

export const LINE_SPLIT = ''
