import { type ChatMessageCard } from '../chat'
import { type StreamPipeline } from '../stream'

export enum MessageType {
  text = 0,
  process = 1,
  error = 2,
  data = 3,
  done = 4,
  replace = 5,
  recovery = 6,
  stream = 7,
  reasoning = 8,
  heartbeat = 9,
}

export interface StreamResultProcessTool {
  title?: string;
  name?: string;
  arguments?: string;
  result?: string;
}

export interface StreamResultProcess {
  id: string
  text: string
  icon?: string
  type?: 'text' | 'tool'
  tool?: StreamResultProcessTool
}
export interface StreamResultError {
  error: string
  code: string
  message: string
  type: string
  link?: string
}
export interface StreamResultErrorInfo {
  ErrorCode: string
  UserMessage: string
  ErrorMessage: string
  Type: string
  Link?: string
}

export interface StreamResultCard extends ChatMessageCard {}

export interface StreamResultDone {
  id: string
}
export type StreamResultStream = Record<string, any>

export interface StreamResult {
  text?: string
  process?: StreamResultProcess
  error?: StreamResultError
  data?: StreamResultCard[]
  done?: StreamResultDone
  replace?: StreamResultReplace[]
  recovery?: string
  stream?: StreamResultStream
  pipeline?: StreamPipeline
  reasoning?: string
  heartbeat?: string
}

export interface StreamResultReplace {
  key: string
  value: string
}
