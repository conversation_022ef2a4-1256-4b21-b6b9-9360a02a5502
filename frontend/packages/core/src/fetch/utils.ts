//这里按照规范自定义
export const NEWLINE = '\n'.charCodeAt(0)

export function isNewline(char: number) {
  return char === NEWLINE
}

export function concatChunks(chunks: Uint8Array[], totalLength: number) {
  const concatenatedChunks = new Uint8Array(totalLength)

  let offset = 0
  for (const chunk of chunks) {
    concatenatedChunks.set(chunk, offset)
    offset += chunk.length
  }
  chunks.length = 0

  return concatenatedChunks
}
