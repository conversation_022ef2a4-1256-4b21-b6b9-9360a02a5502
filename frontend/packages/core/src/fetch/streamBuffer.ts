import { type StreamResult } from './types'

export class StreamBuffer {
  running: boolean
  private pendingBuffer: StreamResult[]
  private debounceTimer: any
  private readonly DEBOUNCE_TIME: number
  constructor() {
    this.debounceTimer = null
    this.pendingBuffer = []
    this.DEBOUNCE_TIME = 300
    this.running = false
  }
  push(arr: StreamResult[]) {
    let hasRecovery = false
    let isDone = false
    arr.forEach((item) => {
      this.pendingBuffer.push(item)
      if (item.recovery) {
        this.pendingBuffer = [item]
        hasRecovery = true
      }
      if (item.done) {
        isDone = true
      }
    })
    // 流式输出结束直接退出
    if (isDone) {
      this.running = false
      if (this.debounceTimer) clearTimeout(this.debounceTimer)
    }
    // 存在撤回
    if (hasRecovery) {
      this.running = true
      if (this.debounceTimer) clearTimeout(this.debounceTimer)
      this.debounceTimer = setTimeout(() => {
        this.running = false
      }, this.DEBOUNCE_TIME)
    }
  }

  async *[Symbol.asyncIterator]() {
    if (this.running) {
      yield []
    } else {
      const arr = this.pendingBuffer
      this.pendingBuffer = []
      yield arr
    }
  }
}
