import { sleep } from 'radash'
import { StreamPipeline, tokenizeSplit } from '../stream'
import { NetworkError, NotSupportedError } from './error'
import { parseResponse } from './parse'
import { StreamBuffer } from './streamBuffer'
import { concatChunks, isNewline } from './utils'

export async function* fetchAsStream(url: string, requestInit: RequestInit, fetchFunc = fetch) {
  try {
    const response = await fetchFunc(url, requestInit)
    if (!response.ok) {
      // 提供状态码等更多信息
      throw new NetworkError(`网络连接错误，状态码：${response.status}`)
    }
    if (!response.body) {
      throw new NotSupportedError('当前浏览器不支持EventStream')
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    const chunks: Uint8Array[] = []
    let totalLength = 0

    const pipeline = new StreamPipeline()
    const streamBuffer = new StreamBuffer()

    while (true) {
      const { value } = await reader.read()

      if (value) {
        chunks.push(value)
        totalLength += value.length
        if (!isNewline(value[value.length - 1])) {
          // if the last character is not a newline, we have not read the whole JSON value
          continue
        }
      }

      if (chunks.length === 0) {
        break // we have reached the end of the stream
      }

      const concatenatedChunks = concatChunks(chunks, totalLength)
      totalLength = 0

      // const streamParts = decoder
      //   .decode(concatenatedChunks, { stream: true })
      //   .split('\n')
      //   .filter((line) => line !== '') // splitting leaves an empty string at the end
      //   .map(parseResponse)
      const streamParts = decoder
        .decode(concatenatedChunks, { stream: true })
        .split('\n')
        .filter((line) => line !== '')
        .map(parseResponse)

      streamBuffer.push(streamParts)

      for await (const streamParts of streamBuffer) {
        for (const streamPart of streamParts) {
          // 消息流管道
          streamPart.pipeline = pipeline
          // 文字流式
          if (streamPart.text) {
            if (streamPart.text.length > 40) {
              yield streamPart
            } else {
              const chars = tokenizeSplit(streamPart.text)
              const duration = 20

              for (const char of chars) {
                yield {
                  text: char,
                }
                await sleep(duration)
              }
            }
          } else if (streamPart.reasoning) {
            const chars = tokenizeSplit(streamPart.reasoning)
            const duration = 20

            for (const char of chars) {
              yield {
                reasoning: char,
              }
              await sleep(duration)
            }
          } else if (streamPart.heartbeat) {
            yield streamPart
          } else if (streamPart.stream) {
            pipeline.push(streamPart.stream)
            yield streamPart
          } else if (streamPart.done) {
            // 单独处理 done
            for await (const r of pipeline) {
              yield streamPart
            }
          } else {
            yield streamPart
          }
        }
      }
    }
  } catch (error: any) {
    if (error && error.name && error.name === 'AbortError') {
    } else {
      // 对于整体流程的异常，也确保资源释放
      // 在这里主要针对fetch操作异常，因为reader在成功获取响应后才会创建
      console.log('获取流失败', error)
      throw error // 保持原函数的错误向外抛出行为不变
    }
  }
}
