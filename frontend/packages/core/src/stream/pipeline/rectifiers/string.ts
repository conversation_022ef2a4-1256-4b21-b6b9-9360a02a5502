import { sleep } from 'radash'
import { tokenizeSplit } from '../../utils'
import { StreamRectifier } from './rectifier'
import { streamChars } from './utils'

export class StreamStringRectifier<T = string> extends StreamRectifier<T> {
  private chars: string[] = []
  private cache: string[] = []
  private origin: string = ''

  private timer: any = null
  private idle = true

  get running() {
    return this.cache.length > 0
  }

  get value() {
    return this.chars.join('').replace(/\|n/g, '\n') as T
  }

  push(value = '') {
    if (this.disposed) {
      return
    }
    if (!value) {
      value = ''
    }
    if (this.override) {
      if (value) {
        const text = this.origin
        const _v = `${value}`
        if (_v.startsWith(text)) {
          value = _v.substring(text.length)
        } else {
          // 数据格式错误
          throw new Error('text error')
        }
      }
    }
    // 新推的文本为空则不处理
    if (!value.length) {
      return
    }
    this.origin += value
    this.cache.push(...tokenizeSplit(value))
    this.run()
  }

  private run() {
    if (!this.idle) {
      return
    }
    this.idle = false
    this.timer = setTimeout(() => {
      Promise.all([
        new Promise<void>(async (resolve) => {
          for await (const char of streamChars(this.cache)) {
            this.chars.push(char)
          }
          // 处理完成即空闲
          this.idle = true
          resolve()

          // 延迟 500ms 再判断的是否需要关闭
          await sleep(500)
          if (!this.cache.length) {
            this.stop()
          }
        }),
      ])
    }, 1)
  }

  private stop() {
    this.idle = true
    clearTimeout(this.timer)
    this.timer = null
  }

  dispose() {
    this.stop()
    super.dispose()
  }
}
