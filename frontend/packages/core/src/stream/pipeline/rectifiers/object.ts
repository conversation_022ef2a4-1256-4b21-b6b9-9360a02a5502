import { reactive } from 'vue'
import { StreamArrayRectifier } from './array'
import { StreamBooleanRectifier } from './boolean'
import { StreamNumberRectifier } from './number'
import { StreamRectifier } from './rectifier'
import { StreamStringRectifier } from './string'
import type { LiteralValue, ObjectValue } from './types'

export class StreamObjectRectifier extends StreamRectifier {
  private rectifiers: Record<string, StreamRectifier> = reactive({})

  get running(): boolean {
    return Object.values(this.rectifiers).some((v) => v.running)
  }

  get value() {
    const data: Record<string, string | object[]> = {}
    for (const [key, value] of Object.entries(this.rectifiers)) {
      data[key] = value.value
    }
    return reactive(data)
  }

  get(key: string) {
    return this.rectifiers[key]?.value ?? ''
  }

  set(key: string, value: LiteralValue) {
    // null 值不要
    if (value === null) {
      return
    }
    let override = this.override
    let ctor
    if (typeof value === 'object') {
      // array
      ctor = StreamArrayRectifier
      override = true
    } else {
      // string, number, boolean, date
      if (typeof value === 'string') {
        ctor = StreamStringRectifier
      } else if (typeof value === 'number') {
        ctor = StreamNumberRectifier
      } else if (typeof value === 'boolean') {
        ctor = StreamBooleanRectifier
      }
    }
    if (!ctor) {
      throw new Error('unsupported type')
    }
    if (!this.rectifiers[key]) {
      this.rectifiers[key] = new ctor({ override })
    }
    this.rectifiers[key].push(value)
  }

  push(chunk: ObjectValue) {
    if (this.disposed) {
      return
    }
    for (const [key, value] of Object.entries(chunk)) {
      this.set(key, value)
    }
  }

  dispose() {
    for (const [_, value] of Object.entries(this.rectifiers)) {
      if (typeof value.dispose === 'function') {
        value.dispose()
      }
    }
    super.dispose()
  }
}
