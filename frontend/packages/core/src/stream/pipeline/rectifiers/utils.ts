import { sleep } from 'radash'

export async function* streamChars(chars: string[], duration = 20) {
  while (chars.length) {
    const char = chars.shift()
    if (typeof char !== 'undefined') {
      yield char
      await sleep(duration)
    } else {
      break
    }
  }
}

// export async function* streamText(text: string, duration = 20) {
//   const chars = text.split('')
//   return streamChars(chars, duration)
// }
