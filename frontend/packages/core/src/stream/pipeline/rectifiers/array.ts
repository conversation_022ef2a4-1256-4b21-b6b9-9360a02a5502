import { StreamObjectRectifier } from './object'
import { StreamRectifier } from './rectifier'
import type { ObjectValue } from './types'

export class StreamArrayRectifier<T = []> extends StreamRectifier {
  private rectifiers: StreamObjectRectifier[] = []

  get running(): boolean {
    return this.rectifiers.some((v) => v.running)
  }

  push(r: { index: number; value: ObjectValue }) {
    if (this.disposed) {
      return
    }
    if (!this.rectifiers[r.index]) {
      this.rectifiers[r.index] = new StreamObjectRectifier({
        override: this.override,
      })
    }
    this.rectifiers[r.index].push(r.value)
  }

  get value() {
    return this.rectifiers.map((v) => v.value)
  }

  get() {
    return this.value
  }
  dispose() {
    for (const value of this.rectifiers) {
      if (typeof value.dispose === 'function') {
        value.dispose()
      }
    }
    super.dispose()
  }
}
