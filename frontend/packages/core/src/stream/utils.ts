export const tokenizeSplit = (text: string) => {
  // 定义正则表达式：
  // 1. 匹配 Markdown 符号（如 ###, ##, ```）
  // 2. 匹配单词（包括字母、数字、下划线）
  // 3. 匹配中文字符
  // 4. 匹配空白字符（空格、换行符等）
  // 5. 匹配其他字符（如标点符号）
  const regex = /(#{1,6}\s|```|\b\w+\b|[\u4e00-\u9fa5]|\s+|[^\w\s])/g

  // 使用 match 方法匹配所有符合规则的片段
  const segments = text.match(regex)

  if (!segments) {
    return []
  }

  // 合并空白字符与前一个片段
  const result: string[] = []
  for (let i = 0; i < segments.length; i++) {
    if (/\s+/.test(segments[i]) && result.length > 0) {
      // 将空白字符附加到前一个片段
      result[result.length - 1] += segments[i]
    } else {
      // 否则直接添加到结果中
      result.push(segments[i])
    }
  }

  return result
}
