import { z } from 'zod'
import { EventSchema, type Event } from './event'
import { Expression } from './expression'
import { nanoid } from './utils'

export interface Slot {
  slot?: string
  content?: Element[] | string
}

export interface Element {
  type?: string
  props?: Record<string, any>
  events?: Event[]
  modal?: string
  // slots or text
  content?: Slot[] | string
}

const SlotSchemaLazy: z.ZodType<Slot> = z.lazy(() => SlotSchema)

const AnyOrExpression = z.preprocess((value) => Expression.from(value), z.any())

const ElementSchema = z.object({
  type: z.string().optional(),
  props: z.record(AnyOrExpression).optional(),
  events: z.array(EventSchema).optional(),
  modal: z.string().optional(),
  content: z.union([z.array(SlotSchemaLazy), z.string()]).optional(),
})

const SlotSchema = z.object({
  slot: z.string().optional().default('default'),
  content: z.array(ElementSchema).or(z.string()),
})

const VariableEditorSelectSchema = z.object({
  type: z.literal('Select'),
  props: z.object({
    options: z.array(
      z.object({
        text: z.string(),
        value: z.string(),
      }),
    ),
  }),
})

const VariableEditorCheckboxSchema = z.object({
  type: z.literal('Checkbox'),
  props: z.object({
    options: z.array(
      z.object({
        text: z.string(),
        value: z.boolean(),
      }),
    ),
  }),
})

const VariableEditorRadioSchema = z.object({
  type: z.literal('Radio'),
  props: z.object({
    options: z.array(
      z.object({
        text: z.string(),
        value: z.union([z.string(), z.number(), z.boolean()]),
      }),
    ),
  }),
})
const VariableEditorFieldFillingSchema = z.object({
  type: z.literal('field-filling'),
  props: z.object({}).optional(),
})

const VariableEditorTemplateSchema = z.object({
  type: z.literal('Template'),
  props: z.object({}).optional(),
})

const VariableEditorFieldsSchema = z.object({
  type: z.literal('Fields'),
  props: z.object({}).optional(),
})

const VariableEditorActionsSchema = z.object({
  type: z.literal('Actions'),
  props: z.object({}).optional(),
})

const VariableSchema = z.object({
  id: z.string().optional().default(nanoid(12)),
  label: z.string().optional(),
  name: z.string(),
  type: z.string(),
  defaultValue: z
    .union([z.string(), z.number(), z.boolean(), z.array(z.unknown())])
    .optional(),
  events: z.array(EventSchema).optional(),
  editor: z
    .union([
      VariableEditorSelectSchema,
      VariableEditorCheckboxSchema,
      VariableEditorTemplateSchema,
      VariableEditorRadioSchema,
      VariableEditorFieldsSchema,
      VariableEditorActionsSchema,
      VariableEditorFieldFillingSchema
    ])
    .optional(),
  advancedconfig: z.boolean().optional(),
})

const MetadataSchema = z.object({
  elements: z.array(ElementSchema),
  variables: z.array(VariableSchema).optional(),
})

export type Metadata = z.infer<typeof MetadataSchema>

export const defineMetadata = (metadata: Metadata) => {
  return MetadataSchema.safeParse(metadata).data as Metadata
}
