import { z } from 'zod'

type Variables = Record<string, unknown>

const ExpressionSchema = z.object({
  type: z.literal('expression'),
  content: z.string(),
})

export class Expression {
  private type = 'expression'
  constructor(private content: string) {}

  static from(value: unknown) {
    const v = ExpressionSchema.safeParse(value)
    if (v.success) {
      return new Expression(v.data.content)
    }
    return value
  }

  static is(value: unknown): value is Expression {
    return value instanceof Expression
  }

  static with(value: unknown, variables: Variables) {
    return Expression.is(value) ? value.toString(variables) : value
  }

  toJSON() {
    return {
      type: this.type,
      content: this.content,
    }
  }

  toString(variables: Variables) {
    return this.content.replace(
      /{{\$(.*?)}}/g,
      (_, key) => (variables[key.trim()] ?? '') as string,
    )
  }
}
