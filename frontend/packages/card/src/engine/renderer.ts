import { type Element, type Slot } from './metadata'

type RenderFunction<Ele = any, VNode = any> = (
  element: Ele,
  children: VNode,
) => VNode

type RendererOptions<Ele = any, VNode = any> = {
  h: RenderFunction<Ele, VNode>
}

export class Renderer<VNode extends any> {
  private h: RenderFunction<Element, VNode>

  constructor(options: RendererOptions<Element, VNode>) {
    this.h = options.h
  }

  private renderSlot(slot: Slot) {
    return () => {
      if (typeof slot.content === 'string') {
        return slot.content as string
      }
      return this.renderElements(slot.content)
    }
  }

  private renderSlots(slots: Slot[]) {
    return slots.reduce(
      (x, slot) => {
        const name = slot.slot ?? 'default'
        if (x[name]) {
          throw new Error(`Slot ${name} already exists`)
        }
        x[name] = this.renderSlot(slot)
        return x
      },
      {} as Record<string, Function>,
    )
  }

  private renderElement(element: Element) {
    if (!element) {
      return null
    }
    element = { ...element }

    if (typeof element.content === 'string') {
      element.content = [
        {
          slot: 'default',
          content: element.content as string,
        },
      ]
    }

    let children: any
    if (typeof element.content !== 'undefined') {
      if (Array.isArray(element.content)) {
        children = this.renderSlots(element.content)
      } else {
        throw new Error('Invalid block content')
      }
    }

    return this.h(element, children)
  }

  private renderElements(elements: Element[]) {
    return elements.map((element) => this.renderElement(element))
  }

  render(element: Element | Element[]) {
    if (!Array.isArray(element)) {
      element = [element]
    }
    return this.renderElements(element)
  }
}

export const createRenderer = () => {}
