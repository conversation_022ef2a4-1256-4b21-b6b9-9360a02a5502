import { type Metadata } from '../engine'

export const form: Metadata = {
  elements: [],
  variables: [
    {
      id: 'gU27AC82K385al98',
      name: 'fields',
      label: '表单字段',
      type: 'array',
      defaultValue: [],
      editor: {
        type: 'Fields',
      },
    },
    // {
    //   id: 'XS29ZDWMzPDNrcMQ',
    //   name: 'editable',
    //   label: '开启编辑',
    //   type: 'boolean',
    //   defaultValue: true,
    //   editor: {
    //     type: 'Checkbox',
    //   },
    //   events: [
    //     {
    //       name: 'change',
    //       type: 'script',
    //       callback: `((value, formData, config) => { if (!value) { formData.mode = 'view'; } config['mode'].hide = !value; })(value, formData, config)`,
    //     },
    //   ],
    // },
    {
      id: 'c3Jc41GIRqAJAxs3',
      name: 'editable',
      label: '表单模式',
      type: 'boolean',
      defaultValue: true,
      editor: {
        type: 'Radio',
        props: {
          options: [
            { text: '编辑模式', value: true },
            { text: '查看模式', value: false },
          ],
        },
      },
    },
    {
      id: 'kL43LC82K385jk11',
      name: 'hiddenFields',
      label: '隐藏字段',
      type: 'array',
      defaultValue: [],
      editor: {
        type: 'Fields',
      },
    },
    {
      id: 'kL43LC82K385jk12',
      name: 'actions',
      label: '按钮标题',
      type: 'array',
      defaultValue: [],
      advancedconfig: true,
      editor: {
        type: 'Actions',
      },
    },
  ],
}
