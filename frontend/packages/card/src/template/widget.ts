import { type Metadata } from '../engine'

export const widget: Metadata = {
  elements: [
    {
      type: 'Page',
      content: [
        {
          slot: 'default',
          content: [
            {
              type: 'Widget',
              props: {
                url: {
                  type: 'expression',
                  content: '{{$url}}',
                },
                visible: true,
                fullscreen: true,
              },
            },
          ],
        },
      ],
    },
  ],
  variables: [
    {
      id: 'NlZrsruKCfgQtODM',
      name: 'mount',
      label: '显示方式',
      type: 'string',
      defaultValue: 'message',
      editor: {
        type: 'Select',
        props: {
          options: [
            { text: '消息内容', value: 'message' },
            { text: '页面打开', value: 'page' },
            { text: '聊天区域', value: 'chat' },
          ],
        },
      },
    },
    {
      id: 'lMKwcgBCgGWWPAwm',
      name: 'url',
      label: '卡片地址',
      type: 'string',
      editor: {
        type: 'field-filling',
      },
    },
    {
      id: 'FuHexzsFYWEtQNCh',
      name: 'visible',
      label: '默认显示',
      type: 'boolean',
      defaultValue: true,
      editor: {
        type: 'Checkbox',
        props: {
          options: [
            { text: '显示', value: true },
            { text: '隐藏', value: false },
          ],
        },
      },
    },
  ],
}
