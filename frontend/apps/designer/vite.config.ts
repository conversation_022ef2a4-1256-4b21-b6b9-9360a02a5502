import injectJs from '@acme/vite-plugin-inject-js'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import autoprefixer from 'autoprefixer'
import { fileURLToPath, URL } from 'node:url'
import path from 'path'
import tailwindcss from 'tailwindcss'
import AutoImport from 'unplugin-auto-import/vite'
import { ArcoResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'
import mockDevServerPlugin from 'vite-plugin-mock-dev-server'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    mockDevServerPlugin(),
    createSvgIconsPlugin({
      customDomId: '__svg__icons__dom__gpt__designer',
      iconDirs: [path.resolve(process.cwd(), 'src/assets/svg')],
      symbolId: 'svg-[name]',
    }),

    vueJsx(),
    //为了方便建模调用，这里我们读取Html把所有的JS和CSS全部打包到一个JS中，方便引入
    injectJs(),
    Components({
      dts: '.vite/components.d.ts',
      resolvers: [
        ArcoResolver({
          sideEffect: true,
        }),
      ],
    }),
    AutoImport({
      dts: '.vite/auto-imports.d.ts',
      imports: ['vue'],
    }),
  ],
  //打包目录共用资源
  base: '/gptbuilder/designer',
  build: {
    emptyOutDir: true,
    outDir: '../../../data/statis/gptbuilder/designer/',
    manifest: 'manifest.json',
    target: 'esnext', // 设置目标环境为不支持模块的ES版本
    minify: true, // 关闭代码压缩
    rollupOptions: {
      output: {
        // 打包输出的配置
        chunkFileNames: (chunk) => {
          return chunk.facadeModuleId?.includes('markdown-editor') ?  'assets/markdownEditor.js' : 'assets/[name]-[hash].js'
        },
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // 因为 node_modules 中的依赖通常是不会改变的
            // 所以直接单独打包出去
            // 这个return 的值就是打包的名称
            return 'vendor'
          }
        },
      },
    },
  },
  server: {
    //启动全域服务
    host: '0.0.0.0',
    //写这个可以在建模中直接访问IP，让所有的静态资源变成IP地址
    origin: 'http://***********:5173',
    proxy: {
      '/api': {
        target: 'http://**********:9300',
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/api/, "")
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    postcss: {
      plugins: [tailwindcss, autoprefixer],
    },
  },
})
