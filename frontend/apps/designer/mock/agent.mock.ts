import { defineMock } from 'vite-plugin-mock-dev-server'

const text = `总结：
1. 应用发包的流程包括临时版本验证和本地验证，在本地验证中，如果测试正常就点击测试通过，反之则退回开发继续修改。[图片1]、[图片2]、[图片3]、[图片4]
2. 在测试通过后，进入版本发布阶段，在该阶段需要填写版本号和应用发布信息，并参考指定的格式进行发布。[图片5]
3. 在进行应用发包之前，需要登录开发云平台，并进行分支创建操作，其中需要切换项目为建模 JAVASDK，在分支管理中创建建模 JAVASDK 分支。[图片6]、[图片7]`

export default defineMock({
  url: '/api/Agent/StreamingChatCompletion/:id',
  method: 'POST',
  delay: [1, 3000],
  response(req, res, next) {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      Connection: 'keep-alive',
      'Cache-Control': 'no-cache',
    })
    const data = [
      { code: '1', body: JSON.stringify({ text: '正在解析语义' }), time: 1000 },
      {
        code: '1',
        body: JSON.stringify({ text: '正在匹配知识库' }),
        time: 1000,
      },
      {
        code: '1',
        body: JSON.stringify({ text: '正在帮您总结数据' }),
        time: 1000,
      },
    ]
    text.split('').forEach((c) => {
      data.push({
        code: '0',
        body: c,
        time: 10,
      })
    })
    data.push({
      code: '3',
      body: JSON.stringify([
        {
          type: 'score',
          data: {
            value: 10,
            max: 100,
            text: '因为什么，所以什么，因为什么，所以什么',
          },
        },
      ]),
      time: 100,
    })
    data.push({
      code: '1',
      body: JSON.stringify({ text: '正在结束步骤：完成执行动作' }),
      time: 0,
    })

    data.push({ code: '4', body: new Date().getTime().toString(), time: 0 })
    const pushData = () => {
      const item = data.shift()
      if (item) {
        res.write(`${item.code}:${item.body}\n`)
        setTimeout(pushData, item.time)
      } else {
        res.end()
      }
    }
    pushData()
  },
})
