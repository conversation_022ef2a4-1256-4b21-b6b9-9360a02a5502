import { defineMock } from 'vite-plugin-mock-dev-server'
import {
  defineAgentDetailResponseMock,
  defineAgentSystemParamsResponseMock,
  defineModelsResponseMock,
} from './config'

export default defineMock([
  {
    url: '/api/42000301/skill/systemParam',
    method: 'POST',
    delay: 0,
    response: defineAgentSystemParamsResponseMock,
  },
  {
    url: '/api/42000301/skill/detail',
    method: 'POST',
    delay: 0,
    response: defineAgentDetailResponseMock,
  },
  {
    url: '/api/42000501/ModelInstance/reasoningList',
    method: 'POST',
    delay: 0,
    response: defineModelsResponseMock,
  },
])
