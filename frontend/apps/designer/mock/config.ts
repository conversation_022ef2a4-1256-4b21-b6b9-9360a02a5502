import { MockHttpItem } from 'vite-plugin-mock-dev-server'

const SystemParams = [
  {
    code: 'system',
    name: '会话变量',
    children: [
      {
        code: 'System_Input',
        name: '用户输入',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_DateTime',
        name: '当前时间',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentDocument',
        name: '当前文档',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentDocumentName',
        name: '当前文档名称',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentDocumentSize',
        name: '当前文档尺寸',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentImage',
        name: '当前图片',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentImageName',
        name: '当前图片名称',
        groupCode: 'system',
        groupName: '会话变量',
      },
      {
        code: 'System_Keyword_CurrentImageSize',
        name: '当前图片尺寸',
        groupCode: 'system',
        groupName: '会话变量',
      },
    ],
  },
  {
    code: 'page',
    name: '页面变量',
    children: [
      {
        code: 'System_Keyword_PageName',
        name: '页面名称',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_PageUrl',
        name: '页面地址',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_AppType',
        name: '系统类型',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_AppCode',
        name: '系统编码',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_AppName',
        name: '系统名称',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_AppVersion',
        name: '系统版本',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_ModuleCode',
        name: '模块编码',
        groupCode: 'page',
        groupName: '页面变量',
      },
      {
        code: 'System_Keyword_ModuleName',
        name: '模块名称',
        groupCode: 'page',
        groupName: '页面变量',
      },
    ],
  },
  {
    code: 'env',
    name: '环境变量',
    children: [
      {
        code: 'System_Keyword_ErpVersion',
        name: 'Erp版本',
        groupCode: 'env',
        groupName: '环境变量',
      },
      {
        code: 'System_Keyword_Metadata',
        name: '扩展数据',
        groupCode: 'env',
        groupName: '环境变量',
      },
      {
        code: 'System_Keyword_CustomerGUID',
        name: '客户GUID',
        groupCode: 'env',
        groupName: '环境变量',
      },
      {
        code: 'System_Keyword_CustomerName',
        name: '客户名称',
        groupCode: 'env',
        groupName: '环境变量',
      },
      {
        code: 'System_Keyword_TenantCode',
        name: '租户编码',
        groupCode: 'env',
        groupName: '环境变量',
      },
      {
        code: 'System_Keyword_TenantName',
        name: '租户名称',
        groupCode: 'env',
        groupName: '环境变量',
      },
    ],
  },
  {
    code: 'user',
    name: '用户变量',
    children: [
      {
        code: 'System_Keyword_UserGUID',
        name: '用户GUID',
        groupCode: 'user',
        groupName: '用户变量',
      },
      {
        code: 'System_Keyword_UserCode',
        name: '用户编码',
        groupCode: 'user',
        groupName: '用户变量',
      },
      {
        code: 'System_Keyword_UserName',
        name: '用户名称',
        groupCode: 'user',
        groupName: '用户变量',
      },
      {
        code: 'System_Keyword_Mobile',
        name: '用户手机号码',
        groupCode: 'user',
        groupName: '用户变量',
      },
    ],
  },
]

export const defineAgentSystemParamsResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: SystemParams,
    }),
  )
}

export const defineAgentDetailResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        id: '6f88c440-8288-4f3d-bb2a-ff78d5ee7487',
        code: 'system_huxt',
        name: '胡新涛测试',
        openDialogWindow: 0,
        icon: '',
        status: '1',
        modelInstanceCode: '',
        description: '',
        mode: 'agent',
        questions: [],
        guide: null,
        welcome: '',
        spaceGUID: '08dca713-3d6e-fbce-835e-6d2d2154296c',
        startup: null,
        metaDataVersion: null,
        examples: [],
        flow: null,
        agent: null,
        saveMode: 1,
        initEvent: '',
        skillCategoryGUIDs: null,
        orchestrations: null,
      },
    }),
  )
}

export const defineModelsResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [
        {
          modelInstanceGUID: '08dc89f1-f3ea-43f5-fa2e-cdb5896f96ea',
          modelInstanceCode: 'text_generation_large',
          modelInstanceName: '大参数文本生成模型实例',
          modelGUID: '4db4ca0f-22e8-11ef-83d7-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com/',
          deploymentName: '',
          apiKey: '******',
          clientId: null,
          vendor: null,
          fileId: null,
          isImg: 0,
          strategyId: null,
          enableCustomModel: 0,
          customModelCode: '',
          supportDeepThink: 1,
          executionSetting:
            '{"config":{"temperature":{"name":"温度","tips":"<b>temperature: </b>调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与”<b>Top p</b>“同时调整。","minValue":0,"maxValue":2,"rounding":2},"top_p":{"name":"top_p","tips":"<b>Top p 为累计概率:<b/> 模型在生成输出时会从概率最高的词汇开始选择，直到这些词汇的总概率累积达到Top p 值。这样可以限制模型只选择这些高概率的词汇，从而控制输出内容的多样性。建议不要与“生成随机性”同时调整。","minValue":0,"maxValue":1,"rounding":1},"max_tokens":{"name":"最大Tokens","tips":"控制模型输出的Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。","minValue":0,"maxValue":8192,"rounding":0,"defaultValue":8192}},"generationDiversity":{"precise":{"temperature":0.1,"top_p":0.8},"balance":{"temperature":0.85,"top_p":0.8},"creative":{"temperature":0.95,"top_p":0.8}}}',
        },
        {
          modelInstanceGUID: '08dcc03b-ee49-fb7e-fea6-90243ac591c9',
          modelInstanceCode: 'default_img_model',
          modelInstanceName: '默认图片识别多模态模型实例',
          modelGUID: '6cf1ca0f-22e8-11ef-83d7-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com',
          deploymentName: 'qwen-vl-plus',
          apiKey: '******',
          clientId: '',
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: null,
          enableCustomModel: 1,
          customModelCode: 'qwen-vl-ocr-latest',
          supportDeepThink: 0,
          executionSetting: null,
        },
        {
          modelInstanceGUID: '08dcfef8-e2e5-42c6-845a-8ce67c3966f2',
          modelInstanceCode: 'default_doc_model',
          modelInstanceName: '默认文档模型实例',
          modelGUID: '6be95155-3458-45c0-8a19-44e46fc14a3b',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com/',
          deploymentName: '',
          apiKey: '******',
          clientId: null,
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: null,
          enableCustomModel: 0,
          customModelCode: '',
          supportDeepThink: 0,
          executionSetting: null,
        },
        {
          modelInstanceGUID: '08dd0f4c-e9b1-4681-844d-b1fdaeaafb2e',
          modelInstanceCode: 'default_text_generation',
          modelInstanceName: '默认文本生成模型实例',
          modelGUID: '354d4bb5-22e8-11ef-83d7-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com',
          deploymentName: '',
          apiKey: '******',
          clientId: '',
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: null,
          enableCustomModel: 1,
          customModelCode: 'qwen-plus-latest',
          supportDeepThink: 1,
          executionSetting:
            '{"config":{"temperature":{"name":"温度","tips":"<b>temperature: </b>调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与”<b>Top p</b>“同时调整。","minValue":0,"maxValue":2,"rounding":2},"top_p":{"name":"top_p","tips":"<b>Top p 为累计概率:<b/> 模型在生成输出时会从概率最高的词汇开始选择，直到这些词汇的总概率累积达到Top p 值。这样可以限制模型只选择这些高概率的词汇，从而控制输出内容的多样性。建议不要与“生成随机性”同时调整。","minValue":0,"maxValue":1,"rounding":1},"max_tokens":{"name":"最大Tokens","tips":"控制模型输出的Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。","minValue":0,"maxValue":8192,"rounding":0,"defaultValue":8192}},"generationDiversity":{"precise":{"temperature":0.1,"top_p":0.8},"balance":{"temperature":0.85,"top_p":0.8},"creative":{"temperature":0.95,"top_p":0.8}}}',
        },
        {
          modelInstanceGUID: '08dd2409-ec9e-fe9b-fe2c-b74d199be3ca',
          modelInstanceCode: 'img_model_large',
          modelInstanceName: '大参数图片识别多模态模型实例',
          modelGUID: '7bd2ca0f-22e8-11ef-83d7-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com',
          deploymentName: '',
          apiKey: '******',
          clientId: '',
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: '',
          enableCustomModel: 0,
          customModelCode: '',
          supportDeepThink: 0,
          executionSetting: null,
        },
        {
          modelInstanceGUID: '08dd4827-ff0d-4002-fca9-c30070606b60',
          modelInstanceCode: 'aliyun_deepseek_r1',
          modelInstanceName: '阿里云百炼DeepSeek R1',
          modelGUID: '0d814593-e45f-11ef-b079-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
          deploymentName: '',
          apiKey: '******',
          clientId: '',
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: '',
          enableCustomModel: 1,
          customModelCode: 'deepseek-r1-0528',
          supportDeepThink: 1,
          executionSetting: '',
        },
        {
          modelInstanceGUID: '08dd4a43-2142-40fb-fcfd-c6437e698cc4',
          modelInstanceCode: 'aliyun_deepseek_v3',
          modelInstanceName: '阿里云百炼DeepSeek V3',
          modelGUID: '0d814593-e45f-11ef-b079-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
          deploymentName: '',
          apiKey: '******',
          clientId: '',
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: '',
          enableCustomModel: 1,
          customModelCode: 'deepseek-v3',
          supportDeepThink: 0,
          executionSetting: '',
        },
        {
          modelInstanceGUID: '08ddad85-7f6d-47ed-850e-9ad46e781a0d',
          modelInstanceCode: 'text',
          modelInstanceName: '测试文本实例-千问3',
          modelGUID: '4db4ca0f-22e8-11ef-83d7-00155d822d63',
          modelType: 0,
          endpoint: 'https://dashscope.aliyuncs.com',
          deploymentName: '',
          apiKey: '******',
          clientId: null,
          vendor: '',
          fileId: null,
          isImg: 0,
          strategyId: '',
          enableCustomModel: 1,
          customModelCode: 'qwen-plus-latest',
          supportDeepThink: 1,
          executionSetting:
            '{"config":{"temperature":{"name":"温度","tips":"<b>temperature: </b>调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与”<b>Top p</b>“同时调整。","minValue":0,"maxValue":2,"rounding":2},"top_p":{"name":"top_p","tips":"<b>Top p 为累计概率:<b/> 模型在生成输出时会从概率最高的词汇开始选择，直到这些词汇的总概率累积达到Top p 值。这样可以限制模型只选择这些高概率的词汇，从而控制输出内容的多样性。建议不要与“生成随机性”同时调整。","minValue":0,"maxValue":1,"rounding":1},"max_tokens":{"name":"最大Tokens","tips":"控制模型输出的Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。","minValue":0,"maxValue":8192,"rounding":0,"defaultValue":8192}},"generationDiversity":{"precise":{"temperature":0.1,"top_p":0.8},"balance":{"temperature":0.85,"top_p":0.8},"creative":{"temperature":0.95,"top_p":0.8}}}',
        },
      ],
    }),
  )
}
