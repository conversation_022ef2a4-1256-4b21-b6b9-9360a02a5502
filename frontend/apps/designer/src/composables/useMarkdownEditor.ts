import { onMounted, shallowRef, useTemplateRef, type Ref } from 'vue'

export enum PlaceholderType {
  VARIABLE = 'variable',
  FUNCTION = 'function',
  PARAMETER = 'parameter',
  REFERENCE = 'reference',
  UNKNOWN = 'unknown',
}

type Placeholder = {
  type?: PlaceholderType
  label?: string
}

type UseEditorOptions = {
  content: Ref<string>
  data: Ref<Record<string, Placeholder>>
  elementId: string
  disabled?: boolean
  minHeight?: string
  maxHeight?: string
  update?: (content: string) => void
}

export const useMarkdownEditor = ({
  content,
  data,
  elementId,
  disabled,
  minHeight,
  maxHeight,
  update,
}: UseEditorOptions) => {
  const markdownEditorRef = useTemplateRef<HTMLElement>(elementId)
  const markdownEditorView: Ref<any | null> = shallowRef(null)
  const awaitQueue: Ref<Function[]> = shallowRef([])
  let superAddFieldToCurrent: any
  let superRefreshPlaceholders: any

  const setContent = (content: string) => {
    const current = markdownEditorView.value?.state.doc.toString()
    if (content === current) {
      return
    }
    markdownEditorView.value?.dispatch({
      changes: {
        from: 0,
        to: current?.length,
        insert: content,
      },
    })
  }

  const removeDeletedFields = () => {
    // 在同步完字段列表的时候 查询当前内容中是否有已经被删除的内容，删除变更的字段
    if (!markdownEditorView.value) return

    const doc = markdownEditorView.value.state.doc
    const changes = []

    for (let i = 0; i < doc.lines; i++) {
      const line = doc.line(i + 1)
      const lineText = line.text

      const regex = /\{\{\$(\w+)\}\}/g
      let match
      while ((match = regex.exec(lineText)) !== null) {
        const fieldName = match[1]
        if (!data.value[`$${fieldName}`]) {
          changes.push({
            from: line.from + match.index,
            to: line.from + match.index + match[0].length,
          })
        }
      }
    }

    if (changes.length > 0) {
      markdownEditorView.value.dispatch({
        changes,
      })
    }
  }

  onMounted(async () => {
    if (!markdownEditorRef.value) {
      return
    }

    // 动态导入 createMarkdownEditor
    const { createMarkdownEditor } = await import(
      /* webpackChunkName: "markdown-editor" */ '@acme/markdown-editor'
    )
    const {
      markdownEditorView: markdownEditor,
      addFieldToCurrent: addFieldToCurrentFn,
      refreshPlaceholders: refreshPlaceholdersFn,
    } = createMarkdownEditor(markdownEditorRef.value, content.value, {
      sourceMap: data.value,
      disabled: !!disabled,
      minHeight,
      maxHeight,
      update,
    })

    markdownEditorView.value = markdownEditor
    superAddFieldToCurrent = addFieldToCurrentFn
    superRefreshPlaceholders = refreshPlaceholdersFn

    // 切换节点初始化延迟处理数据更新
    if (awaitQueue.value && awaitQueue.value.length > 0) {
      awaitQueue.value.forEach((fn) => fn())
      awaitQueue.value = []
    }
  })

  const addFieldToCurrent = (field: string) => {
    // 刷新当前的字段列表
    if (superRefreshPlaceholders) {
      superRefreshPlaceholders(data.value)
    }
    // 添加字段
    if (superAddFieldToCurrent) {
      superAddFieldToCurrent(field)
    }
  }

  const refreshPlaceholders = () => {
    if (!markdownEditorView.value) {
      awaitQueue.value = [refreshPlaceholders]
      return
    }
    // 刷新当前的字段列表
    if (superRefreshPlaceholders) {
      superRefreshPlaceholders(data.value, true)
    }
    // 同步删除 已经修改过code 或者已经删除 的字段
    removeDeletedFields()
  }

  return {
    markdownEditorView,
    addFieldToCurrent,
    setContent,
    refreshPlaceholders,
  }
}
