<script setup lang="ts">
import { fetchAsStream } from '@acme/core'
import { reactive } from 'vue'
import { Guid } from './utils'
import { TaskQueue } from './utils/TaskQueue'

const data = reactive<
  {
    id: string
    result?: string
    score?: string
    scoreText?: string
  }[]
>([])
for (let i = 0; i < 10; i++) {
  data.push({ id: Guid() })
}

const taskManger = new TaskQueue({
  limit: 5,
})

const start = () => {
  for (let i = 0; i < data.length; i++) {
    const _d = data[i]
    taskManger.addTask(async (id) => {
      const stream = await fetchAsStream(`/api/Agent/StreamingChatCompletion/${id}`, {
        method: 'POST',
      })
      for await (const data of stream) {
        if (!_d.result) {
          _d.result = ''
        }
        if (data.text) {
          _d.result += data.text
        } else if (data.data) {
          data.data.forEach((item) => {
            // biome-ignore lint/suspicious/noExplicitAny: off
            const _item = item as any
            if (_item.type === 'score') {
              _d.score = _item.data.value
              _d.scoreText = _item.data.text
            }
          })
        }
      }
    }, _d.id)
  }
  taskManger.start()
}
</script>

<template>
  <a-button type="primary" @click="() => start()">开始</a-button>
  <div class="card-list">
    <a-card
      class="card-item"
      :title="`场景${index + 1}`"
      :bordered="false"
      v-for="(item, index) in data"
    >
      <template #extra v-if="item.score"> 评分：{{ item.score }} </template>
      <a-card title="输出">
        {{ item.result }}
      </a-card>

      <a-card title="评估">{{ item.scoreText }}</a-card>
    </a-card>
  </div>

  <!-- <a-button @click="() => addTask()">添加任务</a-button>
  <br />
  <a-button @click="() => runTask()">开始执行</a-button>

  <br />

  <p>
    运行的任务：<code>{{ taskStatus.running }}</code>
  </p>
  <p>
    等待中任务：<code>{{ taskStatus.queue }}</code>
  </p>
  <p>
    队列满员?：<code>{{ taskStatus.isFull }}</code>
  </p> -->
</template>
<style scoped lang="less">
.card-list {
  display: flex;
  flex-wrap: wrap;
  box-sizing: 'border-box';
  padding: '40px';
  background-color: #dddddd;
  .card-item {
    margin: 20px;
    width: 360px;
  }
}
</style>
