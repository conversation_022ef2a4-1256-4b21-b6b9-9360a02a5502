import { defineStore } from 'pinia'
import { configInfo } from './configData'

export const useConfigStore = defineStore('config', {
  state: () => ({
    config: configInfo,
    itemConfig: [],
    data: {},
    oldData: {},
    activeType: '',
    knowledge: [],
  }),
  getters: {
    configData: (state) => state.itemConfig,
    formData: (state) => state.data,
    oldFormData: (state) => state.oldData,
    knowledgeOptions: (state) => state.knowledge,
  },
  actions: {
    setData({ data }: any) {
      const type = data.type
      this.activeType = type
      this.itemConfig = type && this.config[type] ? this.config[type] : []
      this.data = data
      this.setOldData(data)
    },
    setOldData(data: any) {
      this.oldData = JSON.parse(JSON.stringify(data))
    },
    async updateKnowledgeOptions(options: any) {
      this.knowledge = options
    },
  },
})
