import { defineStore } from 'pinia'
import { getPluginList, getPluginServiceList } from '../api/plugin-api'

export const usePluginStore = defineStore('pluginApi', {
  state: () => ({
    pluginList: [],
    serviceList: [],
  }),
  getters: {
    pluginData: (state) => state.pluginList,
    serviceData: (state) => state.serviceList,
  },
  actions: {
    async getPluginList() {
      const { data }: any = await getPluginList()
      this.pluginList = data.data
    },
    getPluginServiceList(id: string) {
      if (id) {
        getPluginServiceList(id)
          .then(({ data }) => {
            this.serviceList = data?.data || []
          })
          .catch(() => {
            this.serviceList = []
          })
      } else {
        this.serviceList = []
      }
    },
  },
})
