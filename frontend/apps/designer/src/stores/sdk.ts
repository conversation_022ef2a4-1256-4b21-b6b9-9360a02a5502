import { defineStore } from 'pinia'
import {
  createMountedEvent,
  createOpenKnowledgeEvent,
  createOpenMcpServiceEvent,
  createOpenPluginEvent,
  emitEvent,
} from '../events'

export const useDesignerSdk = defineStore('designerSdk', {
  actions: {
    openPlugin(data: { text: string; value: string; origin: Record<string, string> }[]) {
      emitEvent(createOpenPluginEvent(), data)
    },
    openKnowledge(data: {
      data: { text: string; value: string; origin: Record<string, string | number> }[]
      title: string
      type: number | string
    }) {
      emitEvent(createOpenKnowledgeEvent(), data)
    },
    openMcp(data?: { text: string; value: string; origin: Record<string, string> }[]) {
      emitEvent(createOpenMcpServiceEvent(), data)
    },
    onMounted() {
      emitEvent(createMountedEvent(), {})
    },
  },
})
