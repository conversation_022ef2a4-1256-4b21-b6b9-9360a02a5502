import { Message } from '@arco-design/web-vue'
import { defineStore } from 'pinia'
import { getPromptTestScenes, getToken, runTest, saveData } from '../api/prompt'
import { Guid, getParams } from '../utils'
import { TaskQueue } from '../utils/TaskQueue'

export const usePromptStore = defineStore('prompt', {
  state: () => ({
    enableParam: false,
    promptData: [] as any[],
    promptTemplate: '',
    id: '',
    taskQueue: new TaskQueue({
      limit: 1,
    }),
    params: [] as PromptParams[],
    paramsMap: {} as PromptParamsMap,
    defaultData: {} as any,
    messageContent: [] as MessageContent[],
    title: '',
    requestToken: '',
  }),
  getters: {},
  actions: {
    async getData() {
      const id = getParams().oid
      const { data } = await getPromptTestScenes(id)
      const {
        promptParamDtos = [],
        promptTestSceneDtos = [],
        promptTemplate,
        messageContent,
        promptName,
        promptCode,
      } = data.data
      this.params =
        (promptParamDtos &&
          promptParamDtos.filter(
            (item: PromptParams) => item && !item.isSystemParam,
          )) ||
        []
      const defaultData: any = {}
      this.params.forEach((item: any) => {
        this.paramsMap[item.paramName] = item
        defaultData[item.paramName] = item.defaultValue
      })
      this.defaultData = defaultData
      this.promptData = promptTestSceneDtos
      this.promptData.forEach((item: any) => {
        item.promptParams = {
          ...defaultData,
          ...JSON.parse(item.promptParams || {}),
        }
      })
      this.promptTemplate = promptTemplate
      this.messageContent = messageContent || []
      this.title = `${promptName}（${promptCode}）`
      this.id = id
      return data
    },
    saveData() {
      const promptData = JSON.parse(JSON.stringify(this.promptData))
      promptData.forEach((item: any) => {
        item.promptParams = item.promptParams
          ? JSON.stringify(item.promptParams)
          : JSON.stringify({})
      })
      const messageContent: MessageContent[] = []
      this.messageContent.forEach((item: MessageContent, key: number) => {
        messageContent.push({
          ...item,
          index: key + 1,
        })
      })
      return saveData({
        PromptGUID: this.id,
        promptTemplate: this.promptTemplate,
        promptTestSceneDtos: promptData,
        messageContent: messageContent,
      })
    },
    updateTemplate(v: string) {
      this.promptTemplate = v
    },
    updateMessageContent(k: number, v: string) {
      this.messageContent[k].content = v
    },
    addMessage(role: string) {
      const len = this.messageContent.length
      if (len >= 10) {
        return Message.warning('样本数据最多支持10条！')
      }
      this.messageContent.push({
        content: '',
        index: len + 1,
        role: role,
      })
    },
    removeMessage(k: number) {
      this.messageContent.splice(k, 1)
    },
    addTest() {
      if (this.promptData.length >= 5) {
        Message.warning('测试场景最多支持5条数据')
        return
      }
      this.promptData.push({
        promptTestSceneGUID: Guid(),
        promptGUID: this.id,
        promptContent: '',
        promptParams: JSON.parse(JSON.stringify(this.defaultData)),
        outputContent: '',
        estimateContent: '',
        estimateRecord: 0,
      })
    },
    onDel(i: number) {
      this.promptData.splice(i, 1)
    },
    async run(item: any, gptEngineUrl: string, headers: any[]) {
      if (item.status === 'running') {
        return
      }
      item.outputContent = ''
      item.estimateContent = ''
      item.estimateRecord = 0
      const _headers: Record<string, string> = {}
      headers.forEach((d: any) => {
        _headers[d.key] = d.value
      })
      item.status = 'waiting'
      this.taskQueue.addTask(async (id) => {
        item.status = 'running'
        try {
          if (!this.requestToken) {
            this.requestToken = await getToken({})
          }
          if (this.requestToken) {
            _headers['access-token'] = this.requestToken
          }
          const stream = await runTest(id, gptEngineUrl, _headers)
          for await (const data of stream) {
            if (data.text) {
              item.outputContent += data.text
            } else if (data.data) {
              data.data.forEach((cardData: any) => {
                if (cardData.type === 'score') {
                  item.estimateRecord = cardData.data.value
                  item.estimateRecordMax = cardData.data.max || 5
                  item.estimateContent = cardData.data.text
                }
              })
            }
          }
        } catch (error) {
          console.log('执行测试失败')
        }
        item.status = 'idle'
      }, item.promptTestSceneGUID)
    },

    async runItem(id: string) {
      const item = this.promptData.find(
        (item: any) => item.promptTestSceneGUID === id,
      )
      if (item.status === 'running') {
        return
      }
      const { data } = await this.saveData()
      const gptEngineUrl = data.data.gptEngineUrl
      const headers = data.data.headers

      this.run(item, gptEngineUrl, headers)
    },
    //执行测试
    async runAll() {
      const { data } = await this.saveData()
      const gptEngineUrl = data.data.gptEngineUrl
      const headers = data.data.headers

      for (let i = 0; i < this.promptData.length; i++) {
        const item = this.promptData[i] as any
        this.run(item, gptEngineUrl, headers)
      }
    },
  },
})
