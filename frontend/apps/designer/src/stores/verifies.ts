import { defineStore } from 'pinia'
import { validateURL } from '../utils'
import { CARD_TEMPLATE_TYPE } from './configData'
import { CARD_TYPE, OUTPUT_TYPE } from './constants'
export const useVerifiesStore = defineStore('verifies', {
  state: () => ({
    verifies: {} as any,
  }),
  getters: {
    verifiesData: (state) => state.verifies,
  },
  actions: {
    verifyAll(nodes: any) {
      nodes.forEach((node: any) => {
        this.verify({
          data: node.data,
          cardId: node.id,
          cardType: node.data.type,
        })
      })
    },
    verify({ data, cardId, cardType }: any) {
      switch (cardType) {
        case CARD_TYPE.START:
          this.verifies[cardId] = this.verifyStart(data)?.verified
          return
        case CARD_TYPE.PROMPT:
          this.verifies[cardId] = this.verifyPrompt(data)?.verified
          return
        case CARD_TYPE.KNOWLEDGE:
          this.verifies[cardId] = this.verifyKnowledge(data)?.verified
          return
        case CARD_TYPE.CHAT_BI:
          this.verifies[cardId] = this.verifyChatBI(data)?.verified
          return
        case CARD_TYPE.MESSAGE:
        case CARD_TYPE.INTERACTIVE:
          this.verifies[cardId] = this.verifyCard(data)?.verified
          return
        case CARD_TYPE.PLUGIN:
          this.verifies[cardId] = this.verifyPlugin(data)?.verified
          return
        case CARD_TYPE.FINISH:
          this.verifies[cardId] = this.verifyEnd(data)?.verified
          return
        case CARD_TYPE.SELECTOR:
          this.verifies[cardId] = this.verifySelector(data)?.verified
          return
        case CARD_TYPE.PAGE:
          this.verifies[cardId] = this.verifyPage(data)?.verified
          return
        case CARD_TYPE.DOCUMENT_ANALYSIS:
          this.verifies[cardId] = this.verifyDocumentAnalysis(data)?.verified
          return
        case CARD_TYPE.IMAGE_ANALYSIS:
          this.verifies[cardId] = this.verifyImageAnalysis(data)?.verified
          return
        case CARD_TYPE.AUXILIARY_INPUT:
          this.verifies[cardId] = this.verifyAuxiliaryInput(data)?.verified
          return
        case CARD_TYPE.TEXT_TEMPLATE:
          this.verifies[cardId] = this.verifyTextTemplate(data)?.verified
          return
        case CARD_TYPE.INTENTION_RECOGNITION:
          this.verifies[cardId] =
            this.verifyIntentionRecognition(data)?.verified
          return
        case CARD_TYPE.SMART_CHECK:
          this.verifies[cardId] = this.verifyPlan(data)?.verified
      }
    },
    onLoopOutputs(data: CardImageData | CardDocumentData) {
      const loop = (
        outputs: {
          code: string
          name: string
          schema: { name: string; code: string }[]
        }[],
      ): boolean => {
        if (!outputs) return false
        return outputs?.some((item: any) => {
          if (!item || !item.code || !item.name) return true
          if (item.schema && item.schema?.length) {
            return loop(item.schema)
          }
          return false
        })
      }
      return loop(data?.outputs)
    },
    commonVerify(data: any) {
      if (!data.name) {
        return { verified: false, message: '请完善步骤名称' }
      }
      // 参数列表
      if (data.outputType === OUTPUT_TYPE.VARIABLES) {
        const verificationFailed = data.outputs.some(
          (item: any) => !item.code || !item.name,
        )
        if (!data.outputs.length || verificationFailed) {
          return { verified: false, message: '请完善参数列表' }
        }
      }
      return { verified: true }
    },
    verifyStart(data: any) {
      return this.commonVerify(data)
    },
    verifyPrompt(data: any) {
      // 提示模板必填
      if (!data.templateId) {
        return { verified: false, message: '提示模板必填' }
      }
      const verificationFailed = data.inputs.some(
        (item: any) => !!item.required && !item?.value?.content,
      )
      if (verificationFailed) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    // todo 数据知识库
    verifyChatBI(data: any) {
      if (!data.knowledges.length) {
        return { verified: false, message: '请选择问数场景' }
      }
      if (!data.inputs?.[0]?.value?.content) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    verifyKnowledge(data: any) {
      if (!data.knowledges.length) {
        return { verified: false, message: '请选择知识库' }
      }
      if (!data.inputs?.[0]?.value?.content) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    verifyCard(data: any) {
      if (data.templateId === CARD_TEMPLATE_TYPE.TEXT) {
        if (!data.content) return { verified: false, message: '请完善卡片模板' }
      }
      if (data.templateId === CARD_TEMPLATE_TYPE.FORM) {
        if (!data.title) return { verified: false, message: '请完善卡片标题' }
      }
      if (data.outputs?.length) {
        const verificationFailed = data.outputs.some((item: any) => {
          // 字段分组，只校验 name 必填
          if (item.type === 'group' && item.name) return false
          if (!item.code || !item.name || !item?.value?.content) {
            return true
          }
          if (item.schema?.length) {
            return item.schema.some(
              (subItem: any) =>
                !subItem.code || !subItem.name || !subItem?.value?.content,
            )
          }
          return false
        })
        if (verificationFailed) {
          return { verified: false, message: '请完善字段信息' }
        }
      }
      return this.commonVerify(data)
    },
    verifyPlugin(data: any) {
      if (!data.pluginId) {
        return { verified: false, message: '请选择插件' }
      }
      if (!data.toolId) {
        return { verified: false, message: '请选择插件服务' }
      }
      const verificationFailed = data.inputs.some(
        (item: any) => !!item.required && !item?.value?.content,
      )
      if (verificationFailed) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    verifyEnd(data: any) {
      const verificationFailed = data?.outputs?.some(
        (item: any) => !item.value?.content || !item.code || !item.name,
      )
      if (verificationFailed) {
        return { verified: false, message: '请完善输出参数' }
      }
      return { verified: true }
    },
    verifySelector(data: CardSelectorData) {
      const verificationFailed = data.conditions.some(
        (c: SelectorCondition) => {
          // else条件不需要验证表达式
          if (c.type === 'else') return false
          if (c.expressions.length === 0) return true
          return c.expressions[0].rules.some((rule: ExpressionRule) => {
            if (!rule.left?.value?.content) {
              return true
            }
            if (!rule.right?.value?.content) {
              if (['isempty', 'notisempty'].includes(rule.operator))
                return false
              return true
            }
            return false
          })
        },
      )
      if (verificationFailed) {
        return { verified: false, message: '请完善条件表达式' }
      }
      return this.commonVerify(data)
    },
    verifyPage(data: CardPageData) {
      if (!data.url?.content) {
        return { verified: false, message: '请完善页面地址' }
      }
      if (data.url?.type === 'literal') {
        const isRight = validateURL(data.url.content)
        if (!isRight) {
          return { verified: false, message: '请输入正确的URL' }
        }
      }
      return this.commonVerify(data)
    },
    verifyDocumentAnalysis(data: CardDocumentData) {
      if (!data.files?.content) {
        return { verified: false, message: '请完善输入参数' }
      }
      if (data.imageRecognizeType === 'ocr' && !data.ocrService) {
        return { verified: false, message: '请完善视觉识别服务' }
      }
      if (data.pattern === 1) {
        const verificationFailed = this.onLoopOutputs(data)
        if (verificationFailed || !data.outputs?.length) {
          return { verified: false, message: '请完善输出参数' }
        }
      }
      return this.commonVerify(data)
    },
    verifyImageAnalysis(data: CardImageData) {
      if (!data.files?.content) {
        return { verified: false, message: '请完善输入参数' }
      }
      if (data.recognizeType === 'ocr') {
        if (!data.ocrService) {
          return { verified: false, message: '请完善视觉识别服务' }
        }

        if (data.pattern === 1) {
          const verificationFailed = this.onLoopOutputs(data)
          if (verificationFailed || !data.outputs?.length) {
            return { verified: false, message: '请完善输出参数' }
          }
        }
        return { verified: true }
      } else {
        return this.verifyPrompt(data)
      }
    },
    verifyAuxiliaryInput(data: CardAuxiliaryInput) {
      if (!data.appCode) {
        return { verified: false, message: '请选择应用' }
      }
      if (!data.formId) {
        return { verified: false, message: '请选择表单' }
      }
      const verificationFailed = data.inputs?.some(
        (item: any) => !item.value?.content,
      )
      if (!data.inputs?.length || verificationFailed) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    verifyTextTemplate(data: CardTextTemplate) {
      if (!data?.content) {
        return { verified: false, message: '请完善模板内容' }
      }
      const verificationFailed = data.inputs?.some(
        (item: any) => !item.value?.content,
      )
      if (!data.inputs?.length || verificationFailed) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
    verifyIntentionRecognition(data: any) {
      if (!data._input?.content) {
        return { verified: false, message: '请完善输入参数' }
      }
      const verificationFailed = data.classifications?.some(
        (item: any) => !item?.intention,
      )
      if (!data.classifications?.length || verificationFailed) {
        return { verified: false, message: '请完善意图匹配' }
      }
      return this.commonVerify(data)
    },
    verifyPlan(data: CardPlan) {
      if (!data.id) return { verified: false, message: '请选择方案' }
      const verificationFailed = data.inputs.some(
        (item: any) => !!item.required && !item?.value?.content,
      )
      if (verificationFailed) {
        return { verified: false, message: '请完善输入参数' }
      }
      return this.commonVerify(data)
    },
  },
})
