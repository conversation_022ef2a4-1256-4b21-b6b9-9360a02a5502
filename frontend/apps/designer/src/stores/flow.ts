import { Message } from '@arco-design/web-vue'
import { MarkerType, useVueFlow } from '@vue-flow/core'
import { diff } from 'deep-object-diff'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getData, getSystemParam, saveData } from '../api'
import { getKnowledgeList } from '../api/knowledge'
import { FormFieldParams, OutputParamsType } from '../ts/editor'
import { clearParamValue, verifyAgentParams, verifyParams } from '../utils/verifies'
import { generateCode, getSaveTime } from './../utils/index'
import {
  ACTION_NAME,
  CARD_DESCRIPTION,
  CARD_TYPE,
  FLOW_DEFAULT_DATA,
  MODE_TYPE,
  getAddCondition,
} from './constants'

const { updateNodeData } = useVueFlow()

interface NodeMap {
  [id: string]: any
}

interface SystemParams {
  code: string
  value: string
  name: string
  label: string
  children?: SystemParams[]
}

let timer: any = null
export const skipConfig = ref(false)
export const useFlowStore = defineStore('flow', {
  state: () => ({
    cardWidth: 160,
    mode: '',
    activeCard: { id: '' } as CardNode,
    flowData: {} as any,
    nodes: [] as CardNode[],
    edges: [] as Edge[],
    systemParam: [] as SystemParams[],
    showError: false,
    saveStatus: {
      loading: false,
      status: false,
      message: '',
      errorMessage: '',
      time: '',
    },
    warningNodes: [] as { id: string; message: string }[],
    nodeSortMap: {} as { [id: string]: string[] },
    agentData: {
      prompt: {
        template: '',
        inputs: [],
      },
      tools: [],
      mcps: [],
      knowledgs: [],
      inputs: [],
      uploadables: '',
      useFileUpload: false,
      useQRCodeUpload: false,
      maxResultNumber: 10,
      maxContextTurnsNumber: 30,
      maxUploadNumber: 1,
      modelInstanceCode: '',
      generationDiversity: 'balance',
      executionSetting: '',
    } as AgentData,
  }),
  getters: {
    activeCardId: (state) => state.activeCard.id,
    flowNodes: (state) => state.nodes,
    flowEdges: (state) => state.edges,
    isView: (state) => state.mode === MODE_TYPE.VIEW,
    isDesigner: (state) => state.mode === MODE_TYPE.DESIGNER,
    nodeOrder: (state) => {
      const edgeList: string[] = []
      const loop = (target: string) => {
        let edge: any = state.edges.find((item: any) => item.source === target)
        if (edge) {
          edgeList.push(edge.data.source)
          if (edge.target.indexOf('_condition_') > 0) {
            edge = state.edges.find((item: any) => item.source === `${edge.source}_merge0`)
          }
          loop(edge.target)
        }
      }
      loop('start0')
      return edgeList
    },
    nodeMap: (state) => {
      const map: NodeMap = {}
      state.nodes.forEach((item: any) => {
        map[item.id] = item
      })
      return map
    },
    data: (state) => state.flowData,
    edgeMap: (state) => {
      const sourceMap: EdgeMap = {},
        targetMap: EdgeMap = {}
      state.edges.forEach((e: Edge) => {
        sourceMap[e.source] = e
        targetMap[e.target] = e
      })
      return { sourceMap, targetMap }
    },
  },
  actions: {
    async getSystemParam() {
      const { data }: any = await getSystemParam()
      const loopData = (data: SystemParams[]) => {
        return data.map((item: SystemParams) => {
          item.value = item.code
          if (item.code?.startsWith('System_')) {
            let code = item.code.replace('System_Input', 'input')
            code = code.replace('System_Keyword_', '')
            item.code = code.charAt(0).toLowerCase() + code.slice(1)
          }
          if (item.children && item.children?.length > 0) {
            item.children = loopData(item.children)
          }
          item.label = item.name
          return item
        })
      }
      this.systemParam = loopData(data.data)
    },
    setFlowMode(type: string) {
      this.mode = type
      this.getSystemParam()
    },
    setActiveCard(data: any) {
      this.activeCard = data
    },
    async getData() {
      let { data }: any = await getData()
      return this.setFlowData(data.data)
    },
    setFlowData(data: any) {
      this.showError = !data
      if (!data) {
        return
      }
      this.flowData = data
      if (data.mode === 'agent') {
        if (data.agent) {
          this.agentData = data.agent
        }
      }
      // 排版模式需要处理 nodes 数据
      if (data.mode !== 'flow') {
        return
      }
      // @ts-ignore
      let { flow, name, code, description } = this.flowData
      if (!flow) {
        flow = JSON.parse(JSON.stringify(FLOW_DEFAULT_DATA))
      }
      const nodes: any = flow.nodes
      const edges: any = flow.edges
      nodes.forEach((item: any) => {
        let type = item.type
        item.id = item.code
        if (item.config && item.config.type) {
          type = item.type + '_' + item.config.type
        }
        item.data = {
          ...(item.config || {}),
          type: type,
          _id: item.id,
          description: item.description || '',
        }
        if (item.data.props) {
          const data: { [key: string]: any } = {}
          const outputList: Map<string, OutputParamsType> = new Map()
          const columnsField: string[] = []
          item.data.outputs?.forEach((item: OutputParamsType) => {
            outputList.set(item.code, item)
            item.schema?.forEach((subItem: OutputParamsType) => {
              outputList.set(item.code + '_' + subItem.code, subItem)
            })
          })
          item.data.props.forEach((p: any) => {
            data[p.name] = p.value
            if (p.name === 'columns') {
              data[p.name] = p.value.map((item: any) => {
                if (item.code) columnsField.push(item.code)
                const columns = item?.columns?.map((col: any) => {
                  return {
                    ...col,
                    ...(outputList.get(item.code + '_' + col.code) || {}),
                    control: col.type,
                  }
                })
                return {
                  ...item,
                  ...(outputList.get(item.code) || {}),
                  control: item.type,
                  schema: columns,
                }
              })
            }
          })
          if (!data.actions) {
            data.actions = [{ code: 'card-confirm', name: '' }]
          }
          data.hiddenFields = item.data.outputs?.filter(
            (item: OutputParamsType) => item.code && !columnsField.includes(item.code),
          )
          item.data.props = data
        }
        delete item.config
        if (type === CARD_TYPE.START) {
          item.data.name = item.name
          item.data.startTitle = code ? `${name}（${code}）` : name || item.name
          item.data.describe = description
        } else {
          item.data.name = item.name
        }
        if (
          type === CARD_TYPE.DOCUMENT_ANALYSIS ||
          type === CARD_TYPE.IMAGE_ANALYSIS ||
          type === CARD_TYPE.PROMPT
        ) {
          item.data.files = item.data.files?.[0]?.value || {
            type: 'literal',
            content: '',
          }
        }
        if (type === CARD_TYPE.INTENTION_RECOGNITION) {
          item.data._input = item.data.inputs?.[0]?.value || {
            type: 'literal',
            content: '',
          }
        }
        item.data._actionName = ACTION_NAME[type]
        item.type = 'card'
      })
      edges.forEach((item: any) => {
        item.id = item.code
        item.sourceHandle = `${item.source}__handle-bottom`
        item.type = 'custom'
        item.markerEnd = MarkerType.ArrowClosed
        item.data = JSON.parse(JSON.stringify(item))
      })
      this.nodes = nodes
      this.edges = edges
      this.onComplete()
      return nodes
    },
    onComplete() {
      const { sourceMap } = this.edgeMap
      let addNodes: any = [],
        addEdges: any = [],
        edgeId = this.getNewEdgeId(true) as number
      const getEndEdge = (source: string) => {
        const loop = (source: string): string => {
          const sourceEdge = sourceMap[source]
          if (sourceEdge) {
            return loop(sourceEdge.target)
          }
          return source
        }
        return loop(source)
      }
      // 补齐选择器节点
      this.nodes.forEach((node: any) => {
        if (node.data.type === CARD_TYPE.SELECTOR) {
          node.data.conditions.forEach((c: SelectorCondition) => {
            c.id = generateCode(6)
            const endTarget = getEndEdge(c.target) || c.target
            const cFlow = getAddCondition(c.id, node.id, edgeId, c.target, endTarget)
            edgeId += cFlow.edges.length
            addNodes = addNodes.concat(cFlow.nodes)
            addEdges = addEdges.concat(cFlow.edges)
          })
          // 合并分支
          const mergeId = `${node.id}_merge0`
          addNodes.push({
            id: mergeId,
            code: mergeId,
            type: 'merge',
            position: { x: 0, y: 0 },
            parentNode: node.id,
            data: {
              _id: mergeId,
              type: CARD_TYPE.CONDITION_MERGE,
            },
          })
          let index = this.edges.findIndex((e: Edge) => e.source === node.id)
          let selectorEdge: any = index ? this.edges[index] : {}
          if (index > -1) this.edges.splice(index, 1)
          if (!selectorEdge && node.parentNode) {
            index = addEdges.findIndex((e: Edge) => e.source === node.id)
            selectorEdge = index ? addEdges[index] : {}
            if (index > -1) addEdges.splice(index, 1)
          }
          addEdges.push({
            code: `edge${edgeId}`,
            source: mergeId,
            target: selectorEdge?.target,
            type: 'custom',
          })
          edgeId += 1
        }
      })
      if (addNodes.length) {
        this.addSelector({ nodes: addNodes, edges: addEdges })
      }
    },
    saveData(showMsg = true) {
      if (this.flowData.mode === 'agent') {
        const message = verifyAgentParams(this.agentData)
        if (message) {
          Message.warning(message)
          return new Promise((_, reject) => {
            reject()
          })
        }
        return saveData({
          ...this.flowData,
          agent: this.agentData,
        })
          .then(({ data }) => {
            const message = data?.exception?.message || ''
            this.onSaveMessage(!!data.success, showMsg, message)
            skipConfig.value = true
          })
          .catch(() => {
            this.onSaveMessage(false, showMsg)
          })
      }
      const messages = this.onVerifyParams()
      if (messages) {
        Message.warning(messages)
        return new Promise((_, reject) => {
          reject()
        })
      }
      if (timer) clearTimeout(timer)
      const condition = '_condition_'
      this.saveStatus.loading = true

      // 保存更新node节点顺序
      let order: string[] = []
      this.nodeOrder.forEach((id: string) => {
        order.push(id)
        if (this.nodeSortMap[id]) {
          order = order.concat(this.nodeSortMap[id])
        }
      })
      order.push('end0')
      // 节点排序
      this.nodes.sort((a: any, b: any) => (order.indexOf(a.code) > order.indexOf(b.code) ? 1 : -1))

      const nodes: any = []
      clearParamValue(this.nodes)
      const copyNodes = JSON.parse(JSON.stringify(this.nodes))
      const copyEdges = JSON.parse(JSON.stringify(this.edges))
      copyNodes.forEach((item: any) => {
        let { _id, type, name, startTitle, _actionName, describe, description, ...props } =
          item.data
        const types = type.split('_')
        const config = props
        if (types[1]) config.type = types[1]
        if (item.id.indexOf(condition) > 0 || item.id.endsWith('_merge0')) return
        if (config.files) {
          config.files = [
            {
              code: 'fileInput',
              type: 'file',
              value: config.files,
              required: type !== CARD_TYPE.PROMPT,
            },
          ]
        }
        nodes.push({
          name: name,
          description,
          code: item.id,
          type: types[0],
          position: item.position,
          parentNode: item.parentNode,
          config,
        })
        if (config._input) {
          config.inputs = [{ code: 'input', value: config._input, required: true }]
          delete config._input
        }
        if (types[0] === CARD_TYPE.SELECTOR) {
          config.conditions.forEach((c: SelectorCondition) => {
            const targetEdge = copyEdges.find(
              (e: Edge) => e.source === `${item.id}${condition}${c.id}`,
            )
            c.target = targetEdge?.target || ''
            if (c.target.endsWith('_end')) {
              c.target = ''
            }
          })
          const edge: any = copyEdges.find((e: any) => e.source.startsWith(`${item.id}_merge0`))
          if (edge) {
            edge.source = item.id
          }
        }
        if (config.props) {
          const data: { name: string; value: string }[] = []
          Object.keys(config.props).forEach((key: string) => {
            let item = config.props[key]
            if (key === 'columns') {
              item = item.map(
                ({
                  name,
                  code,
                  control,
                  required,
                  readonly,
                  thousands,
                  options,
                  schema,
                }: FormFieldParams) => {
                  const col =
                    schema?.map(
                      ({
                        name,
                        code,
                        control,
                        required,
                        readonly,
                        thousands,
                        options,
                      }: FormFieldParams) => ({
                        name,
                        code,
                        type: control,
                        required,
                        readonly,
                        thousands,
                        options,
                      }),
                    ) || []
                  return {
                    name,
                    code,
                    type: control,
                    required,
                    readonly,
                    thousands,
                    options,
                    columns: col,
                  }
                },
              )
            }
            if (key !== 'hiddenFields') data.push({ name: key, value: item })
          })
          config.props = data
        }
      })
      const edges: any = []
      copyEdges.forEach((item: any) => {
        // 过滤 condition 连线
        if (item.source.indexOf(condition) > 0 || item.target.indexOf(condition) > 0) return
        edges.push({
          code: item.data.id,
          source: item.source,
          target: item.target,
        })
      })

      const submitData = {
        id: this.flowData?.id || '',
        mode: 'flow',
        saveMode: 3,
        flow: {
          nodes,
          edges,
        },
      }
      return saveData(submitData)
        .then(({ data }) => {
          const message = data?.exception?.message || ''
          this.onSaveMessage(!!data.success, showMsg, message)
          this.setFlowData({
            ...this.flowData,
            ...submitData,
          })
        })
        .catch(() => {
          this.onSaveMessage(false, showMsg)
        })
    },
    onVerifyParams() {
      const verifyRes = verifyParams(this.nodes)
      if (verifyRes?.length > 0) {
        this.warningNodes = verifyRes
        const messages = verifyRes.map((item) => item.message).join('，')
        return messages
      } else {
        this.warningNodes = []
        return ''
      }
    },
    onSaveMessage(isSuccess: boolean, showMsg: boolean, message: string = '') {
      if (isSuccess) {
        this.saveStatus = {
          ...this.saveStatus,
          message: '已保存',
          time: getSaveTime(),
          status: true,
          loading: false,
        }
        if (showMsg) Message.success('保存成功')
      } else {
        this.saveStatus = {
          ...this.saveStatus,
          errorMessage: message || '保存失败',
          status: false,
          message: this.saveStatus.time ? '上次保存' : '',
          loading: false,
        }
        if (showMsg) Message.warning(message || '保存失败')
      }
    },
    getSystemParamData(isSelectParams: boolean = false) {
      const dataList: any[] = []
      const data = this.systemParam.map((val: any) => {
        if (val.children?.length) {
          val.children.map((val: any) => {
            val.mapValue = `{{$${val.value}}}`
            dataList.push(val)
          })
          val.selectable = false
          val.hideIcon = true
          return val
        } else if (val.code) {
          const item = {
            ...val,
            mapValue: `{{$${val.value}}}`,
          }
          dataList.push(item)
          return item
        }
      })
      if (isSelectParams) {
        return data
      }
      return dataList
    },
    getParamsData(isSelectParams: boolean = false) {
      const { sourceMap, targetMap } = this.edgeMap
      let nodes: CardNode[] = []
      const loop = (id: string) => {
        let edge = targetMap[id]
        if (!edge) return
        const node = this.nodeMap[edge.source]
        if (
          [CARD_TYPE.CONDITION, CARD_TYPE.CONDITION_MERGE, CARD_TYPE.SELECTOR].includes(
            node.data.type,
          )
        ) {
        } else {
          nodes.unshift(node)
        }
        if (node.data.type === CARD_TYPE.CONDITION_MERGE) {
          const selectorId = node.id.replace('_merge0', '')
          const getChildren = (id: string): CardNode[] => {
            let children: CardNode[] = []
            this.nodes.forEach((n: CardNode) => {
              if (n.parentNode !== id) return
              if (n.data.type === CARD_TYPE.SELECTOR) {
                children = children.concat(getChildren(n.id))
                return
              }
              if (![CARD_TYPE.CONDITION, CARD_TYPE.CONDITION_MERGE].includes(n.data.type)) {
                children.push(n)
              }
            })
            return children
          }
          nodes = getChildren(selectorId).concat(nodes)
          // 跳过选择器
          edge = sourceMap[selectorId]
        }
        loop(edge.source)
      }
      loop(this.activeCard.id)

      const params = [
        {
          name: '系统变量',
          label: '系统变量',
          code: 'system',
          selectable: false,
          iconName: 'system',
          children: this.getSystemParamData(isSelectParams),
        },
      ]
      nodes.forEach((node: CardNode) => {
        if (node.data.async) {
          return
        }
        if (node?.data?.outputs) {
          // 暂不显示步骤
          // const index = this.nodeOrder.findIndex((id: string) => id === node.id)
          // getStepName(index)
          const item: any = {
            name: node.data.name,
            label: node.data.name,
            code: node.id,
            value: node.id,
            selectable: false,
            iconName: CARD_DESCRIPTION[node.data.type]?.icon,
            children: node?.data?.outputs
              .filter((val: any) => !!val.code && val.type !== 'group')
              .map((val: any) => {
                return {
                  ...val,
                  value: `NodeOutput_${node.id}_${val.code}`,
                  mapValue: `{{$NodeOutput_${node.id}_${val.code}}}`,
                  label: `${val.name}(${val.code})`,
                  name: `${node.data.name}-${val.name}`,
                }
              }),
          }
          params.push(item)
        }
      })
      return params
    },
    async getKnowledgeList() {
      const { data }: any = await getKnowledgeList()
      const knowledge = data.data.map((item: { name: string; code: string; typeEnum: number }) => {
        return { label: item.name, value: item.code, typeEnum: item.typeEnum }
      })
      return knowledge
    },
    updatePosition() {
      const { sourceMap } = this.edgeMap
      const positionY = 104
      Object.values(this.nodeMap).forEach((n: CardNode) => (n.children = []))
      this.nodes.forEach((node: any) => {
        if (node.data.width) node.data.width = 0
        if (node.data.height) node.data.height = 0
        if (node.parentNode) {
          this.nodeMap[node.parentNode].children.push(node)
        }
      })
      const parentNodes = this.nodes.filter((item: any) => {
        if (item.children?.length > 0) {
          let level = 0
          const loop = (id: string) => {
            level += 1
            if (!id) return
            const parent = this.nodeMap[id]
            loop(parent.parentNode)
          }
          loop(item.parentNode)
          item.level = level
          return true
        }
        return false
      })
      parentNodes.sort((a: any, b: any) => (b.level > a.level ? 1 : -1))
      // 20 为左右留白 10
      const boxWidth = this.cardWidth + 20
      const boxHeight = 64
      // 选择器内容节点上下间距
      const conditionPY = 48
      const nodeSortMap: { [key: string]: string[] } = {}
      parentNodes.forEach((node: CardSelectorNode | any) => {
        nodeSortMap[node.code] = []
        const group: any = {},
          conditionWidths: number[] = []
        node.data.conditions.forEach((c: SelectorCondition, key: number) => {
          group[c.id] = []
          conditionWidths[key] = boxWidth
          const loopGroup = (nodeId: string) => {
            let nextId: string = ''
            const node = this.nodeMap[nodeId]
            if (node.data.width > conditionWidths[key]) conditionWidths[key] = node.data.width
            group[c.id].push(nodeId)
            // 选择器
            if (this.nodeMap[nodeId]?.data?.type === CARD_TYPE.SELECTOR) {
              nextId = sourceMap[`${nodeId}_merge0`]?.target || ''
            } else {
              nextId = sourceMap[nodeId]?.target || ''
            }
            if (nextId && !nextId.endsWith('_merge0')) {
              loopGroup(nextId)
            }
          }
          loopGroup(`${node.id}_condition_${c.id}`)
        })
        // 内容宽度
        const contentWidth = eval(conditionWidths.join('+'))
        // 记录最大高
        let maxHeight = 0
        // 开始位置位置
        let left = -(contentWidth / 2)
        node.data.conditions.forEach((condition: SelectorCondition, key: number) => {
          let i = 0
          left += conditionWidths[key] / 2
          if (key > 0) {
            left += conditionWidths[key - 1] / 2
          }
          const loop = (nodeId: string, pX: number, pY: number) => {
            // 记录节点顺序
            if (nodeId.indexOf('_condition_') === -1) {
              nodeSortMap[node.code].push(nodeId)
              const sortNode = nodeSortMap[nodeId]
              if (sortNode) {
                nodeSortMap[node.code] = nodeSortMap[node.code].concat(sortNode)
                delete nodeSortMap[nodeId]
              }
            }
            i += 1
            if (pY > maxHeight) maxHeight = pY
            this.nodeMap[nodeId].position = { x: pX, y: pY }
            let nextId: string = ''
            // 选择器
            if (this.nodeMap[nodeId]?.data?.type === CARD_TYPE.SELECTOR) {
              nextId = sourceMap[`${nodeId}_merge0`]?.target || ''
              pY += this.nodeMap[nodeId]?.data.height
            } else {
              nextId = sourceMap[nodeId]?.target || ''
            }
            if (nextId && !nextId.endsWith('_merge0')) {
              loop(nextId, pX, pY + positionY - (i === 1 ? 32 : 0))
            }
          }
          loop(`${node.id}_condition_${condition.id}`, left, boxHeight + conditionPY)
        })

        node.children.forEach((n: CardNode) => {
          if (n.data.type === CARD_TYPE.CONDITION_MERGE) {
            n.position = { x: 0, y: maxHeight + conditionPY }
          }
          if (n.id.endsWith('_end')) {
            n.position.y = maxHeight
          }
        })

        node.data.width = contentWidth
        node.data.height = maxHeight
      })

      const loopNode = (nodeId: string, startX: number, pY: number) => {
        const targetEdge: any = sourceMap[nodeId]
        if (targetEdge) {
          const node = this.nodeMap[targetEdge.target]
          pY = pY + positionY
          node.position = { x: startX, y: pY }
          let nodeId = targetEdge.target
          if (node.data.type === CARD_TYPE.SELECTOR) {
            nodeId = nodeId + '_merge0'
            // 36 = 卡片高度 - 合流卡片高度
            pY = pY + node.data.height + 20
          }
          loopNode(nodeId, startX, pY)
        }
      }
      loopNode('start0', 100, 100)

      this.nodeSortMap = nodeSortMap

      // @ts-ignore
      updateNodeData(this.nodes, this.edges)
    },
    addSelector(flow: any) {
      this.nodes = this.nodes.concat(flow.nodes)
      flow.edges.map((edge: any) => {
        edge.id = edge.code
        edge.sourceHandle = `${edge.source}__handle-bottom`
        edge.data = JSON.parse(JSON.stringify(edge))
      })
      this.edges = this.edges.concat(flow.edges)
      // @ts-ignore
      updateNodeData(this.nodes, this.edges)
      this.updatePosition()
    },
    removeSelectorNodes(id: string, isUpdate: boolean) {
      const selectorNodes = this.nodes.filter((item: any) => item.data.type === CARD_TYPE.SELECTOR)
      let index = 0,
        nodes: { id: string; level: number }[] = []
      const loop = (id: string, i: number) => {
        i += 1
        selectorNodes.forEach((n: CardNode) => {
          if (n.parentNode === id) {
            nodes.push({ id: n.id, level: i })
            loop(n.id, i)
          }
        })
      }
      loop(id, index)
      nodes.sort((a, b) => (a.level > b.level ? -1 : 1))
      // 从里到外，删除所有选择器
      nodes.forEach(({ id }) => {
        this.removeSelector(id, false)
      })
      this.removeSelector(id, isUpdate)
    },
    removeSelector(id: string, isUpdate: boolean = false) {
      const { targetMap, sourceMap } = this.edgeMap
      const endNodeId = `${id}_merge0`
      const removeNodes: string[] = []
      this.nodes = this.nodes.filter((item: any) => {
        if (item.id === id) return false
        if (item.id.startsWith(`${id}_`)) return false
        if (item.parentNode === id) {
          removeNodes.push(item.id)
          return false
        }
        return true
      })
      const startEdge: any = targetMap[id]
      const endEdge: any = sourceMap[endNodeId]
      startEdge.target = endEdge.target
      if (startEdge?.data?.target) {
        startEdge.data.target = endEdge.target
      }
      this.edges = this.edges.filter((item: any) => {
        if (removeNodes.includes(item.target) || removeNodes.includes(item.source)) return false
        return !item.target.startsWith(`${id}_`) && !item.source.startsWith(`${id}_`)
      })
      if (isUpdate) {
        // @ts-ignore
        updateNodeData(this.nodes, this.edges)
        this.updatePosition()
      }
    },
    removeCondition(id: string) {
      const { sourceMap, targetMap } = this.edgeMap
      const removeNodes: string[] = [id]
      const selectorNodes: string[] = []
      const loop = (id: string) => {
        let edge: any = sourceMap[id]
        const targetNode = this.nodeMap[edge.target]
        const isSelector = targetNode.data.type === CARD_TYPE.SELECTOR
        if (isSelector) {
          selectorNodes.push(targetNode.id)
          // 跳过选择器，从当前选择器结束节点继续
          edge = targetMap[`${targetNode.id}_merge0`]
        }
        if (edge && (!edge.target.endsWith('_merge0') || isSelector)) {
          if (!isSelector) removeNodes.push(edge.target)
          loop(edge.target)
        }
      }
      loop(id)
      if (selectorNodes.length) {
        // 删除 condition 里面的选择器
        selectorNodes.forEach((id: string) => {
          this.removeSelectorNodes(id, false)
        })
      }
      removeNodes.push(`${id}_end`)
      removeNodes.forEach((id: string) => {
        if (id.indexOf('_condition_') === -1) {
          this.onDelNode(id)
        }
      })
      this.nodes = this.nodes.filter((item: any) => !removeNodes.includes(item.id))
      this.edges = this.edges.filter(
        (item: any) => !removeNodes.includes(item.source) && !removeNodes.includes(item.target),
      )
      // @ts-ignore
      updateNodeData(this.nodes, this.edges)
      this.updatePosition()
    },
    addNode(node: any) {
      // @ts-ignore
      this.nodes.push(node)
    },
    removeNode(id: string) {
      const index = this.nodes.findIndex((item: any) => item.id === id)
      if (index > -1) {
        this.nodes.splice(index, 1)
      }
    },
    addEdge(edge: any) {
      // @ts-ignore
      this.edges.push(edge)
    },
    removeEdge(id: string, isUpdate: boolean = true) {
      const { sourceMap, targetMap } = this.edgeMap
      const targetItem: any = targetMap[id]
      const sourceItem: any = sourceMap[id]
      targetItem.target = sourceItem.target
      const index = this.edges.findIndex((e: any) => e.source === id)
      if (index > -1) {
        this.edges.splice(index, 1)
      }
      if (!isUpdate) {
        const tIndex = this.edges.findIndex((e: any) => e.data.id === targetItem.data.id)
        this.edges.splice(tIndex, 1)
      }
      return { sourceItem, targetItem }
    },
    updateEdge(edge: any) {
      const item: any = this.edges.find((e: any) => e.data.id === edge.id)
      if (item) item.target = edge.target
    },
    onDelNode(id: string, isUpdate: boolean = false) {
      this.removeNode(id)
      this.removeEdge(id)
      if (isUpdate) {
        this.updatePosition()
      }
    },
    onAutoSave(v: any, old: any, callback: () => void) {
      if (timer) clearTimeout(timer)
      timer = setTimeout(() => {
        if (v._id !== old._id) return
        const diffData = diff(v, old)
        if (Object.keys(diffData).length) {
          this.saveData(false).then(callback)
        }
      }, 5000)
    },
    getNewEdgeId(isRetId: boolean = false) {
      let newEdgeId = 0
      this.edges.forEach((item: any) => {
        const index = parseInt(item.id.replace('edge', ''))
        if (index >= newEdgeId) {
          newEdgeId = index + 1
        }
      })
      if (isRetId) return newEdgeId
      return `edge${newEdgeId}`
    },
    getNewId(type: string, isRetId: boolean = false) {
      let index = 0
      this.nodes.forEach((node: any) => {
        if (node.id.toLocaleLowerCase().startsWith(type.toLocaleLowerCase())) {
          const nodeIndex = parseInt(node.id.replace(type, ''))
          if (nodeIndex >= index) {
            index = nodeIndex + 1
          }
        }
      })
      if (isRetId)
        return {
          id: type + index,
          index,
        }
      return type + index
    },
  },
})
