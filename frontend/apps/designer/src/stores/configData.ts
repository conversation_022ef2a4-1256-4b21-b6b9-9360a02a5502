import { CARD_TYPE } from './constants'

export const FIELD_OPTIONS = [
  { label: '字符串', value: 'string' },
  { label: '数值', value: 'number' },
  { label: '日期', value: 'date' },
]
export const FIELD_OBJECT_OPTIONS = [
  ...FIELD_OPTIONS,
  { label: '对象数组', value: 'array<object>' },
]
const defaultConfig = [
  {
    label: '执行动作',
    prop: '_actionName',
    placeholder: '',
    type: 'input',
    readonly: true,
  },
  {
    label: '步骤名称',
    prop: 'name',
    placeholder: '请输入步骤名称',
    type: 'input',
    rules: [{ required: true, message: '请输入步骤名称' }],
  },
  {
    label: '步骤描述',
    prop: 'description',
    placeholder: '请输入步骤描述',
    type: 'input',
    rules: [{ required: false, message: '请输入步骤描述' }],
  },
]
const startOutputConfig = {
  label: '输出参数',
  type: 'output-type',
  defaultData: { outputType: 'variables' },
  config: [
    {
      label: '参数格式',
      prop: 'outputType',
      type: 'radio',
      tooltip: `纯文本：用户提问的原始文本。<br />参数列表：通过AI分析用户输入中的参数信息，可以为这些参数定义名称、类型、是否必填，描述，从而让AI提取得更加准确。`,
      options: [
        { label: '纯文本', value: 'content' },
        { label: '参数列表', value: 'variables' },
      ],
    },
    {
      label: '格式定义',
      prop: 'outputs',
      type: 'dynamicForms',
      props: [
        {
          prop: 'name',
          placeholder: '请输入名称',
          width: '20%',
          type: 'input',
          default: '',
        },
        {
          prop: 'code',
          placeholder: '请输入编码',
          width: '20%',
          type: 'input',
          default: '',
        },
        {
          prop: 'type',
          placeholder: '请选择',
          width: '16%',
          type: 'select',
          options: FIELD_OPTIONS,
          default: 'string',
        },
        {
          prop: 'required',
          placeholder: '请选择',
          width: '14%',
          type: 'select',
          options: [
            { label: '必填', value: true },
            { label: '非必填', value: false },
          ],
          default: true,
        },
        {
          prop: 'description',
          placeholder: '请输入描述',
          width: '30%',
          type: 'input',
          default: '',
        },
      ],
      defaultData: [],
    },
  ],
}
const outputConfig: any = JSON.parse(JSON.stringify(startOutputConfig))
const configWidth: any = {
  name: '26%',
  code: '24%',
  type: '20%',
  description: '30%',
}
outputConfig.config[1].props.forEach((item: any, key: number) => {
  if (item.prop === 'required') {
    outputConfig.config[1].props.splice(key, 1)
  }
  item.width = configWidth[item.prop]
})

export const CARD_TEMPLATE_TYPE = {
  FORM: '401f4036-9449-4726-a318-4178dbc3c7ef',
  TEXT: 'ca1a0b77-6634-4828-9a12-0ac79cd0fcfc',
  IMPLANT: '08dc64ca-b4b8-4c56-8225-302339e29baf',
}

export const configInfo: any = {
  [CARD_TYPE.START]: [
    {
      label: '技能名称',
      prop: 'startTitle',
      placeholder: '请输入技能名称',
      type: 'input',
      readonly: true,
    },
    {
      label: '技能描述',
      prop: 'describe',
      placeholder: '请输入步骤描述',
      type: 'textarea',
      readonly: true,
      maxLength: 300,
    },
    startOutputConfig,
    {
      label: '文件上传',
      type: 'upload-params',
    },
  ],
  // 交互卡片
  [CARD_TYPE.INTERACTIVE]: [
    ...defaultConfig,
    {
      label: '卡片内容',
      type: 'title',
    },
    {
      type: 'interactive-form',
    },
    {
      type: 'variables',
      variables: [],
    },
    // {
    //     label: '卡片内容', prop: 'layout', placeholder: '', type: 'card-form', config: [
    //         {
    //             label: '字段', prop: 'code', placeholder: '请选择', type: 'treeSelect', treeData: [], width: '50%', default: ''
    //         },
    //         {
    //             label: '字段类型', prop: 'type', placeholder: '请选择', type: 'select', width: '50%', options: [
    //                 ...FIELD_OPTIONS,
    //                 {label: '用户', value: 'user'}
    //             ], default: ''
    //         },
    //     ]
    // }
  ],
  // 插件
  [CARD_TYPE.PLUGIN]: [
    ...defaultConfig,
    {
      label: '服务API配置',
      type: 'title',
    },
    {
      type: 'plugin-api',
      options: [
        {
          label: '选择插件',
          prop: 'pluginId',
          placeholder: '请选择',
          type: 'select',
          options: [],
          default: '',
        },
        {
          label: '插件服务',
          prop: 'toolId',
          placeholder: '请选择',
          type: 'select',
          options: [],
          default: '',
        },
        {
          label: '请求参数',
          prop: 'body',
          type: 'dynamicForms',
          props: [
            {
              prop: 'code',
              placeholder: '请选择',
              width: '50%',
              type: 'select',
              options: [],
              default: '',
            },
            {
              prop: 'value',
              placeholder: '请选择',
              width: '50%',
              type: 'select',
              options: [],
              default: '',
            },
          ],
          defaultData: [],
        },
      ],
    },
    {
      label: '异步调用',
      prop: 'async',
      type: 'checkbox',
      default: false,
      width: '100%',
      advancedconfig: true,
      tips: `<p>1、开启异步调用后，技能编排不再等待插件执行完成；</p><p>2、插件节点之后的节点不能使用该节点输出参数；</p>`,
    },
    {
      label: '输出到聊天对话框',
      prop: 'asMessage',
      type: 'checkbox',
      default: false,
      width: '100%',
      advancedconfig: true,
    },
  ],
  // 消息卡片
  [CARD_TYPE.MESSAGE]: [
    ...defaultConfig,
    {
      label: '消息内容',
      type: 'title',
    },
    {
      label: '',
      prop: 'content',
      placeholder: '请输入消息内容',
      type: 'template',
      required: true,
    },
  ],
  // 提示卡片
  [CARD_TYPE.PROMPT]: [
    ...defaultConfig,
    {
      label: '分析文档',
      prop: 'files',
      type: 'field-filling',
      minHeight: '30px',
      placeholder: '请选择参数',
      hideLabel: false,
      required: false,
      inputDisabled: true,
      disabledInputs: true,
    },
    {
      label: '提示模板',
      type: 'title',
    },
    {
      type: 'ai-prompt',
    },
    {
      label: '输出到聊天对话框',
      prop: 'asMessage',
      type: 'checkbox',
      default: true,
      width: '100%',
      advancedconfig: true,
    },
    // {
    //   label: '流式输出',
    //   prop: 'stream',
    //   type: 'checkbox',
    //   default: true,
    //   width: '100%',
    //   advancedconfig: true,
    // },
    {
      label: '会话记忆',
      type: 'memory-component',
      advancedconfig: true,
    },
  ],
  // 知识库
  [CARD_TYPE.KNOWLEDGE]: [
    ...defaultConfig,
    {
      label: '知识库',
      type: 'knowledge-form',
    },
    {
      label: '显示文档来源',
      prop: 'asSource',
      type: 'checkbox',
      default: true,
      width: '100%',
      advancedconfig: true,
      tips: '对话框底部显示来源文档连接，可以查看原始文档。',
    },
    {
      label: '问题优化',
      prop: 'isCOR',
      type: 'checkbox',
      default: false,
      width: '100%',
      advancedconfig: true,
      tips: '<p>1、根据历史聊天优化完善当前问题，需要提示词节点开启“会话记忆”</p><p>2、如：提问1 今天天气，提问2 昨天呢？ ，能够将转换问题为 昨天天气呢？</p>',
    },
  ],
  [CARD_TYPE.SELECTOR]: [...defaultConfig, { type: 'selector' }],
  [CARD_TYPE.FINISH]: [
    {
      label: '输出参数',
      type: 'title',
    },
    {
      label: '',
      prop: 'outputs',
      type: 'dynamicForms',
      props: [
        {
          prop: 'name',
          placeholder: '请输入名称',
          width: '25%',
          type: 'input',
          default: '',
        },
        {
          prop: 'code',
          placeholder: '请输入编码',
          width: '25%',
          type: 'input',
          default: '',
        },
        {
          prop: 'type',
          placeholder: '请选择',
          width: '20%',
          type: 'select',
          options: FIELD_OBJECT_OPTIONS,
          default: 'string',
        },
        {
          prop: 'value',
          placeholder: '',
          width: '30%',
          type: 'field-filling',
        },
      ],
      enableFillField: true,
    },
  ],
  [CARD_TYPE.PAGE]: [
    ...defaultConfig,
    {
      label: '页面配置',
      type: 'title',
    },
    {
      label: '打开方式',
      prop: 'mount',
      type: 'select',
      options: [
        { label: '左滑面板', value: 'page' },
        { label: '聊天区域', value: 'chat' },
      ],
    },
    {
      label: '页面地址',
      prop: 'url',
      type: 'field-filling',
      mode: 'input',
      minHeight: '30px',
      placeholder: '请输入页面地址',
    },
    {
      label: '默认显示',
      prop: 'visible',
      type: 'checkbox',
      default: true,
    },
  ],
  [CARD_TYPE.DOCUMENT_ANALYSIS]: [
    ...defaultConfig,
    {
      label: '分析文档',
      prop: 'files',
      type: 'field-filling',
      mode: 'tag',
      minHeight: '30px',
      placeholder: '请选择参数',
      hideLabel: false,
      required: true,
      inputDisabled: true,
    },
    {
      type: 'image-recognize',
      config: [
        {
          label: '图片分析方式',
          prop: 'imageRecognizeType',
          type: 'select',
          options: [
            { label: '不识别', value: 'none' },
            { label: '视觉识别', value: 'ocr' },
          ],
          default: 'none',
        },
      ],
    },
    {
      label: '知识库',
      type: 'document-analysis',
    },
    {
      label: '解析内容配置',
      type: 'document-scope',
      advancedconfig: true,
    },
  ],
  [CARD_TYPE.AUXILIARY_INPUT]: [
    ...defaultConfig,
    {
      type: 'auxiliary-input',
    },
  ],
  [CARD_TYPE.TEXT_TEMPLATE]: [
    ...defaultConfig,
    {
      type: 'text-template',
    },
  ],
  [CARD_TYPE.IMAGE_ANALYSIS]: [
    ...defaultConfig,
    {
      label: '分析图片',
      prop: 'files',
      type: 'field-filling',
      mode: 'tag',
      minHeight: '30px',
      placeholder: '请选择参数',
      hideLabel: false,
      required: true,
      inputDisabled: true,
    },
    {
      type: 'image-recognize',
      config: [
        {
          label: '分析方式',
          prop: 'recognizeType',
          type: 'select',
          options: [
            { label: '多模态识别', value: 'multimodal' },
            { label: '视觉识别', value: 'ocr' },
          ],
          default: 'multimodal',
        },
      ],
    },
    {
      type: 'image-analysis',
    },
    {
      label: '输出到聊天对话框',
      prop: 'asMessage',
      type: 'checkbox',
      default: true,
      width: '100%',
      hide: false,
      advancedconfig: true,
    },
  ],
  [CARD_TYPE.INTENTION_RECOGNITION]: [
    ...defaultConfig,
    {
      label: '输入参数',
      type: 'title',
    },
    {
      label: '输入参数',
      prop: '_input',
      type: 'field-filling',
      mode: 'input',
      minHeight: '30px',
      placeholder: '请选择参数',
      hideLabel: true,
      inputDisabled: false,
    },
    {
      label: '意图匹配',
      type: 'title',
    },
    {
      label: '',
      prop: 'classifications',
      type: 'intention',
      props: [
        {
          prop: 'intention',
          placeholder: '请输入意图描述',
          width: '100%',
          type: 'input',
          default: '',
        },
      ],
      defaultData: [],
    },
    {
      label: '输出参数',
      type: 'title',
    },
    {
      prop: 'outputs',
      type: 'table',
      columns: [
        { title: '名称', dataIndex: 'code' },
        { title: '类型', dataIndex: 'type' },
        { title: '描述', dataIndex: 'name' },
      ],
      tips: '<p style="margin-top: 8px;">1. 当输入参数与意图匹配时，输出意图匹配ID和原因。</p><p>2. 意图匹配ID可作为选择器判断条件使用。</p>',
    },
    {
      label: '会话记忆',
      type: 'memory-component',
      advancedconfig: true,
    },
  ],
  [CARD_TYPE.SMART_CHECK]: [
    ...defaultConfig,
    {
      label: '智能检查',
      type: 'smart-check',
    },
  ],
}

export const outputParamsConfig = [
  {
    prop: 'name',
    placeholder: '请输入名称',
    width: '25%',
    type: 'input',
    default: '',
  },
  {
    prop: 'code',
    placeholder: '请输入编码',
    width: '25%',
    type: 'input',
    default: '',
  },
  {
    prop: 'type',
    placeholder: '请选择',
    width: '25%',
    type: 'select',
    options: FIELD_OBJECT_OPTIONS,
    default: 'string',
  },
  {
    prop: 'description',
    placeholder: '请输入描述',
    width: '25%',
    type: 'input',
    default: '',
  },
]

export const INTERACTIVE_CONFIG = {
  SELECT: {
    label: '卡片模板',
    prop: 'templateId',
    placeholder: '请选择',
    type: 'select-template',
    options: [
      {
        label: '表单卡片',
        value: CARD_TEMPLATE_TYPE.FORM,
        tips: '说明：卡片表单用于用户确认字段信息，也可以通过点击编辑按钮或者对话方式修改字段。',
      },
      {
        label: '文本生成卡片',
        value: CARD_TEMPLATE_TYPE.TEXT,
        tips: '说明：文本生成卡片生成的内容，可通过点击卡片按钮填写到建模平台多行文本。',
      },
      {
        label: '嵌入卡片',
        value: CARD_TEMPLATE_TYPE.IMPLANT,
        tips: '说明：嵌入卡片允许在对话框中嵌入一个外部页面或者组件，支持在滑出面板、消息内容、聊天区域显示，从而实现复杂场景的交互逻辑。',
      },
    ],
    default: CARD_TEMPLATE_TYPE.FORM,
  },
  TEMPLATE: {
    label: '模板内容',
    prop: 'content',
    placeholder: '请输入模板内容',
    type: 'template',
    minHeight: '240px',
    rules: [{ required: true, message: '请输入模板内容' }],
  },
  TITLE: {
    label: '卡片标题',
    prop: 'title',
    placeholder: '请输入卡片标题',
    type: 'template',
    minHeight: '120px',
    rules: [{ required: true, message: '请输入卡片标题' }],
  },
}
