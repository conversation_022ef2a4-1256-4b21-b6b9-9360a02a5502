import { MarkerType } from '@vue-flow/core'
import { CardItem } from '../ts/card'
import { generateCode } from '../utils'

export const CARD_TYPE = {
  // 开始节点
  START: 'Start',
  // 卡片-交互卡片
  INTERACTIVE: 'Card_interactive',
  // 卡片-消息卡片
  MESSAGE: 'Card_message',
  // 提示卡片
  PROMPT: 'PromptTemplate',
  // 知识库
  KNOWLEDGE: 'Knowledge',
  // 插件
  PLUGIN: 'Plugin',
  // 选择器
  SELECTOR: 'Selector',
  // 结束
  FINISH: 'End',
  // 空占位
  CONDITION: 'Condition',
  // 合流
  CONDITION_MERGE: 'Merge',
  // 页面节点
  PAGE: 'Page',
  // 文档分析
  DOCUMENT_ANALYSIS: 'DocumentAnalysis',
  // 辅助录入
  AUXILIARY_INPUT: 'FormBinding',
  // 文本模板
  TEXT_TEMPLATE: 'TextTemplate',
  // 图片分析
  IMAGE_ANALYSIS: 'ImageAnalysis',
  // 意图识别
  INTENTION_RECOGNITION: 'Classification',
  // 智能检查
  SMART_CHECK: 'Plan',
  // 数据查询与总结
  CHAT_BI: 'ChatBI',
}

// 映射卡片类型对应的id名称
export const CARD_ID_NAME = {
  [CARD_TYPE.START]: 'start',
  [CARD_TYPE.INTERACTIVE]: 'card',
  [CARD_TYPE.MESSAGE]: 'card',
  [CARD_TYPE.PROMPT]: 'promptTemplate',
  [CARD_TYPE.KNOWLEDGE]: 'knowledge',
  [CARD_TYPE.PLUGIN]: 'plugin',
  [CARD_TYPE.SELECTOR]: 'selector',
  [CARD_TYPE.CONDITION]: 'condition',
  [CARD_TYPE.CONDITION_MERGE]: 'merge',
  [CARD_TYPE.PAGE]: 'page',
  [CARD_TYPE.DOCUMENT_ANALYSIS]: 'documentAnalysis',
  [CARD_TYPE.AUXILIARY_INPUT]: 'formBinding',
  [CARD_TYPE.TEXT_TEMPLATE]: 'textTemplate',
  [CARD_TYPE.IMAGE_ANALYSIS]: 'imageAnalysis',
  [CARD_TYPE.INTENTION_RECOGNITION]: 'classification',
  [CARD_TYPE.SMART_CHECK]: 'plan',
  [CARD_TYPE.CHAT_BI]: 'chatBI',
}

export const ACTION_NAME = {
  [CARD_TYPE.START]: '开始',
  [CARD_TYPE.INTERACTIVE]: '交互卡片',
  [CARD_TYPE.MESSAGE]: '消息卡片',
  [CARD_TYPE.PROMPT]: '提示词',
  [CARD_TYPE.KNOWLEDGE]: '知识库',
  [CARD_TYPE.PLUGIN]: '调用插件',
  [CARD_TYPE.SELECTOR]: '选择器',
  [CARD_TYPE.FINISH]: '结束',
  [CARD_TYPE.CONDITION]: '卡片规则',
  [CARD_TYPE.CONDITION_MERGE]: '合流',
  [CARD_TYPE.PAGE]: '页面',
  [CARD_TYPE.DOCUMENT_ANALYSIS]: '文档分析',
  [CARD_TYPE.AUXILIARY_INPUT]: '辅助录入',
  [CARD_TYPE.TEXT_TEMPLATE]: '文本模板',
  [CARD_TYPE.IMAGE_ANALYSIS]: '图片分析',
  [CARD_TYPE.INTENTION_RECOGNITION]: '意图识别',
  [CARD_TYPE.SMART_CHECK]: '智能检查',
  [CARD_TYPE.CHAT_BI]: '数据查询与总结',
}

export const CARD_DESCRIPTION = {
  [CARD_TYPE.INTERACTIVE]: {
    label: '交互卡片',
    type: CARD_TYPE.INTERACTIVE,
    icon: 'interactive',
    describe: '交互卡片',
    splitLine: true,
  },
  [CARD_TYPE.MESSAGE]: {
    label: '消息卡片',
    type: CARD_TYPE.MESSAGE,
    icon: 'message',
    describe: '消息卡片',
    splitLine: false,
  },
  [CARD_TYPE.PROMPT]: {
    label: '提示词',
    type: CARD_TYPE.PROMPT,
    icon: 'prompt',
    describe: '提示词',
    splitLine: false,
  },
  [CARD_TYPE.KNOWLEDGE]: {
    label: '知识库',
    type: CARD_TYPE.KNOWLEDGE,
    icon: 'knowledge',
    describe: '知识库',
    splitLine: false,
  },
  [CARD_TYPE.CHAT_BI]: {
    label: '数据查询与总结',
    type: CARD_TYPE.CHAT_BI,
    icon: 'knowledge',
    describe: '数据查询与总结',
    splitLine: false,
  },
  [CARD_TYPE.PLUGIN]: {
    label: '插件',
    type: CARD_TYPE.PLUGIN,
    icon: 'plugin',
    describe: '插件',
    splitLine: false,
  },
  [CARD_TYPE.SELECTOR]: {
    label: '选择器',
    type: CARD_TYPE.SELECTOR,
    icon: 'selector',
    describe: '选择器',
    splitLine: false,
  },
  [CARD_TYPE.PAGE]: {
    label: '页面',
    type: CARD_TYPE.PAGE,
    icon: 'page',
    describe: '页面',
    splitLine: false,
  },
  [CARD_TYPE.DOCUMENT_ANALYSIS]: {
    label: '文档分析',
    type: CARD_TYPE.DOCUMENT_ANALYSIS,
    icon: 'document',
    describe: '文档分析',
    splitLine: false,
  },
  [CARD_TYPE.AUXILIARY_INPUT]: {
    label: '辅助录入',
    type: CARD_TYPE.AUXILIARY_INPUT,
    icon: 'auxiliary',
    describe: '辅助录入',
    splitLine: false,
  },
  [CARD_TYPE.TEXT_TEMPLATE]: {
    label: '文本模板',
    type: CARD_TYPE.TEXT_TEMPLATE,
    icon: 'template',
    describe: '文本模板',
    splitLine: true,
  },
  [CARD_TYPE.IMAGE_ANALYSIS]: {
    label: '图片分析',
    type: CARD_TYPE.IMAGE_ANALYSIS,
    icon: 'image',
    describe: '图片分析',
    splitLine: false,
  },
  [CARD_TYPE.INTENTION_RECOGNITION]: {
    label: '意图识别',
    type: CARD_TYPE.INTENTION_RECOGNITION,
    icon: 'intention',
    describe: '意图识别',
    splitLine: false,
  },
  [CARD_TYPE.SMART_CHECK]: {
    label: '智能检查',
    type: CARD_TYPE.SMART_CHECK,
    icon: 'smart-check',
    describe: '智能检查',
    splitLine: true,
  },
}
export const CARD_LIST: CardItem[] = [
  CARD_DESCRIPTION[CARD_TYPE.PROMPT],
  CARD_DESCRIPTION[CARD_TYPE.KNOWLEDGE],
  CARD_DESCRIPTION[CARD_TYPE.CHAT_BI],
  CARD_DESCRIPTION[CARD_TYPE.AUXILIARY_INPUT],
  CARD_DESCRIPTION[CARD_TYPE.DOCUMENT_ANALYSIS],
  CARD_DESCRIPTION[CARD_TYPE.IMAGE_ANALYSIS],
  CARD_DESCRIPTION[CARD_TYPE.SMART_CHECK],
  CARD_DESCRIPTION[CARD_TYPE.MESSAGE],
  CARD_DESCRIPTION[CARD_TYPE.INTERACTIVE],
  CARD_DESCRIPTION[CARD_TYPE.SELECTOR],
  CARD_DESCRIPTION[CARD_TYPE.INTENTION_RECOGNITION],
  CARD_DESCRIPTION[CARD_TYPE.TEXT_TEMPLATE],
  CARD_DESCRIPTION[CARD_TYPE.PLUGIN],
  // CARD_DESCRIPTION[CARD_TYPE.PAGE],
]

// 查看的模式
export const MODE_TYPE = {
  VIEW: 'view',
  DESIGNER: 'designer',
  PROMPT: 'prompt-designer',
  AGENT: 'agent-designer',
}

// 出参参数类型
export const OUTPUT_TYPE = {
  CONTENT: 'content',
  VARIABLES: 'variables',
}

export const OUTPUT_DEFAULT_DATA = {
  [OUTPUT_TYPE.CONTENT]: [{ code: 'output', type: 'string', name: '默认输出' }],
  [OUTPUT_TYPE.VARIABLES]: [
    { code: '', type: 'string', name: '', description: '', required: true },
  ],
}

// 开始节点默认输入
export const SKILL_DEFAULT_INPUT = [
  {
    code: 'input',
    value: { type: 'ref', content: 'System_Input' },
    type: 'string',
    name: '用户输入',
  },
]

export const conditionData: SelectorCondition = {
  id: generateCode(6),
  title: '条件1',
  target: 'end0',
  type: 'if',
  priority: 1,
  collapse: false,
  expressions: [
    {
      type: 'and',
      rules: [
        {
          left: {
            code: generateCode(8),
            value: {
              type: 'literal',
              content: '',
            },
          },
          operator: 'eq',
          right: {
            code: generateCode(8),
            value: {
              type: 'literal',
              content: '',
            },
          },
        },
      ],
    },
  ],
}

function getDefaultConditionData() {
  const data = JSON.parse(JSON.stringify(conditionData))
  data.id = generateCode(6)
  return [
    data,
    {
      id: generateCode(6),
      title: '否则',
      target: 'end0',
      type: 'else',
      priority: 50,
      expressions: [],
    },
  ]
}

export const CARD_NODES = {
  [CARD_TYPE.START]: {
    name: ACTION_NAME[CARD_TYPE.START],
    code: 'start0',
    type: 'Start',
    position: { x: 100, y: 100 },
    config: {
      inputs: SKILL_DEFAULT_INPUT,
      outputType: OUTPUT_TYPE.CONTENT,
      outputs: OUTPUT_DEFAULT_DATA[OUTPUT_TYPE.CONTENT],
      uploadables: '',
      useFileUpload: false,
      useQRCodeUpload: false,
      maxUploadNumber: 1,
    },
  },
  [CARD_TYPE.INTERACTIVE]: {
    name: ACTION_NAME[CARD_TYPE.INTERACTIVE],
    code: 'card0',
    type: 'Card',
    position: { x: 100, y: 240 },
    config: {
      type: 'interactive',
      templateId: '401f4036-9449-4726-a318-4178dbc3c7ef',
      // 卡片标题模板
      content: '',
      // layout: [], // [{code: '', value: 'string'}],
      inputs: [], // [{code: '', type: 'string', value: {type: 'ref', content: 'System_Input'}}],
      /**表单卡片 */
      title: '',
      props: {},
      /**表单卡片 */
    },
  },
  [CARD_TYPE.MESSAGE]: {
    name: ACTION_NAME[CARD_TYPE.MESSAGE],
    code: 'card1',
    type: 'Card',
    position: { x: 100, y: 240 },
    config: {
      type: 'message',
      // 卡片标题模板
      content: '',
      inputs: [], // [{code: '', type: 'string', value: {type: 'ref', content: 'System_Input'}}],
    },
  },
  [CARD_TYPE.PROMPT]: {
    name: ACTION_NAME[CARD_TYPE.PROMPT],
    code: 'prompttemplate0',
    type: 'PromptTemplate',
    position: { x: 100, y: 240 },
    config: {
      // 卡片标题模板
      templateId: '',
      inputs: [], // [{code: '', type: 'string', value: {type: 'ref', content: 'System_Input'}}],
      outputs: [],
      stream: true,
      asMessage: true,
      memories: 0,
      files: { type: 'literal', content: '' },
    },
  },
  [CARD_TYPE.KNOWLEDGE]: {
    name: ACTION_NAME[CARD_TYPE.KNOWLEDGE],
    code: 'knowledge0',
    type: 'Knowledge',
    position: { x: 100, y: 240 },
    config: {
      knowledges: [], // ['code']
      minScore: 0.5,
      topK: 3,
      asSource: true,
      isCOR: false,
      pattern: 0,
      inputs: [
        {
          code: 'input',
          type: 'string',
          value: { type: 'ref', content: 'System_Input' },
          required: true,
        },
      ],
      outputs: [{ code: 'result', type: 'string', name: '查询结果' }],
    },
  },
  [CARD_TYPE.CHAT_BI]: {
    name: ACTION_NAME[CARD_TYPE.CHAT_BI],
    code: 'chatBI0',
    type: 'ChatBI',
    position: { x: 100, y: 240 },
    config: {
      knowledges: [], // ['code']
      inputs: [
        {
          code: 'input',
          type: 'string',
          value: { type: 'ref', content: 'System_Input' },
          required: true,
        },
      ],
    },
  },
  [CARD_TYPE.PLUGIN]: {
    name: ACTION_NAME[CARD_TYPE.PLUGIN],
    code: 'plugin0',
    type: 'Plugin',
    position: { x: 100, y: 240 },
    config: {
      pluginId: '',
      source: 0,
      toolId: '',
      inputs: [], // [{code: '', type: 'string', value: {type: 'literal', content: '固定值'}}],
      outputs: [], // [{code: "output", type: "object", name: "output", describe: "返回内容"}]
      async: false,
      asMessage: false,
    },
  },
  [CARD_TYPE.SELECTOR]: {
    name: ACTION_NAME[CARD_TYPE.SELECTOR],
    code: 'selector0',
    type: 'selector',
    position: { x: 100, y: 240 },
    config: {
      conditions: getDefaultConditionData(),
      selectorId: '',
      inputs: [], // [{code: '', type: 'string', value: {type: 'ref', content: 'System_Input'}}],
      outputs: [],
    },
  },
  [CARD_TYPE.FINISH]: {
    name: '结束步骤：完成执行动作',
    code: 'end0',
    type: 'End',
    position: { x: 100, y: 204 },
    config: {
      outputs: [],
    },
  },
  [CARD_TYPE.CONDITION]: {
    code: 'condition0',
    type: 'Condition',
    position: { x: 100, y: 240 },
    config: {},
  },
  [CARD_TYPE.CONDITION_MERGE]: {
    code: 'merge0',
    type: 'Merge',
    position: { x: 100, y: 240 },
    config: {},
  },
  [CARD_TYPE.PAGE]: {
    name: ACTION_NAME[CARD_TYPE.PAGE],
    code: 'page0',
    type: 'Page',
    position: { x: 100, y: 240 },
    config: {
      mount: 'page',
      url: {
        content: '',
        type: 'literal',
      },
      visible: true,
    },
  },
  [CARD_TYPE.DOCUMENT_ANALYSIS]: {
    name: ACTION_NAME[CARD_TYPE.DOCUMENT_ANALYSIS],
    code: CARD_ID_NAME[CARD_TYPE.DOCUMENT_ANALYSIS] + '0',
    type: CARD_TYPE.DOCUMENT_ANALYSIS,
    position: { x: 100, y: 240 },
    config: {
      files: { type: 'ref', content: 'System_Keyword_CurrentDocument' },
      imageRecognizeType: 'none',
      ocrService: '',
      // 分析模式
      pattern: 0,
      outputs: [], // [{code: "output", type: "object", name: "output", describe: "返回内容"}]
      useTOC: false,
      imagConverter: false,
      usePageRange: 'all',
      useFirstFewPages: 0,
      useLastFewPages: 0,
    },
  },
  [CARD_TYPE.AUXILIARY_INPUT]: {
    name: ACTION_NAME[CARD_TYPE.AUXILIARY_INPUT],
    code: CARD_ID_NAME[CARD_TYPE.AUXILIARY_INPUT] + '0',
    type: CARD_TYPE.AUXILIARY_INPUT,
    position: { x: 100, y: 240 },
    config: {
      // 子系统
      appCode: '',
      // 表单控件
      formId: '',
    },
  },
  [CARD_TYPE.TEXT_TEMPLATE]: {
    name: ACTION_NAME[CARD_TYPE.TEXT_TEMPLATE],
    code: CARD_ID_NAME[CARD_TYPE.TEXT_TEMPLATE] + '0',
    type: CARD_TYPE.TEXT_TEMPLATE,
    position: { x: 100, y: 240 },
    config: {
      inputs: [],
      content: '',
      outputs: [{ code: 'result', type: 'string', name: '文本内容' }],
    },
  },
  [CARD_TYPE.IMAGE_ANALYSIS]: {
    name: ACTION_NAME[CARD_TYPE.IMAGE_ANALYSIS],
    code: CARD_ID_NAME[CARD_TYPE.IMAGE_ANALYSIS] + '0',
    type: CARD_TYPE.IMAGE_ANALYSIS,
    position: { x: 100, y: 240 },
    config: {
      // 卡片标题模板
      templateId: '',
      inputs: [],
      files: { type: 'ref', content: 'System_Keyword_CurrentImage' },
      recognizeType: 'multimodal',
      ocrService: '',
      outputType: 'content',
      outputs: [{ code: 'result', type: 'string', name: '文本内容' }],
      asMessage: true,
      pattern: 0,
    },
  },
  [CARD_TYPE.INTENTION_RECOGNITION]: {
    name: ACTION_NAME[CARD_TYPE.INTENTION_RECOGNITION],
    code: CARD_ID_NAME[CARD_TYPE.INTENTION_RECOGNITION] + '0',
    type: CARD_TYPE.INTENTION_RECOGNITION,
    position: { x: 100, y: 240 },
    config: {
      inputs: [
        {
          code: 'input',
          type: 'string',
          value: { type: 'literal', content: '' },
          required: true,
        },
      ],
      classifications: [
        { id: 0, intention: '其它意图' },
        { id: 1, intention: '' },
      ],
      outputs: [
        { code: 'classificationId', type: 'number', name: '意图匹配ID' },
        { code: 'classification', type: 'string', name: '意图选项' },
        { code: 'reason', type: 'string', name: '意图匹配原因' },
      ],
    },
  },
  [CARD_TYPE.SMART_CHECK]: {
    name: ACTION_NAME[CARD_TYPE.SMART_CHECK],
    code: CARD_ID_NAME[CARD_TYPE.SMART_CHECK] + '0',
    type: CARD_TYPE.SMART_CHECK,
    position: { x: 100, y: 240 },
    config: {
      // 检查方案ID
      id: '',
      inputs: [],
      outputs: [
        {
          name: '执行结果',
          code: 'result',
          type: 'number',
          description: '执行结果',
        },
        {
          name: '规则分组',
          code: 'ruleGroups',
          type: 'array<object>',
          description: '规则分组',
        },
      ],
    },
  },
}
// flow为空的默认数据
export const FLOW_DEFAULT_DATA = {
  nodes: [CARD_NODES[CARD_TYPE.START], CARD_NODES[CARD_TYPE.FINISH]],
  edges: [{ code: 'edge0', source: 'start0', target: 'end0' }],
}

export function getSelectorData(newSelectorId: string, edgeId: number) {
  const mergeId = newSelectorId + '_merge0'
  const conditions = getDefaultConditionData()
  const condition0 = `${newSelectorId}_condition_${conditions[0].id}`
  const condition1 = `${newSelectorId}_condition_${conditions[1].id}`
  conditions[0].target = condition0
  conditions[1].target = condition1
  const selectorData = {
    nodes: [
      {
        id: newSelectorId,
        code: newSelectorId,
        type: 'card',
        position: { x: 100, y: 0 },
        data: {
          _id: newSelectorId,
          name: '选择器',
          _actionName: '选择器',
          type: CARD_TYPE.SELECTOR,
          conditions: conditions,
        },
      },
      {
        id: condition0,
        code: condition0,
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode: newSelectorId,
        data: {
          _id: condition0,
          type: CARD_TYPE.CONDITION,
          conditionId: conditions[0].id,
        },
      },
      {
        id: condition1,
        code: condition1,
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode: newSelectorId,
        data: {
          _id: condition1,
          type: CARD_TYPE.CONDITION,
          conditionId: conditions[1].id,
        },
      },
      {
        id: condition0 + '_end',
        code: condition0 + '_end',
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode: newSelectorId,
        data: {
          _id: condition0,
          type: CARD_TYPE.CONDITION,
        },
        with: 10,
        height: 1,
      },
      {
        id: condition1 + '_end',
        code: condition1 + '_end',
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode: newSelectorId,
        data: {
          _id: condition1,
          type: CARD_TYPE.CONDITION,
        },
        with: 10,
        height: 1,
      },
      {
        id: mergeId,
        code: mergeId,
        type: 'merge',
        position: { x: 0, y: 0 },
        parentNode: newSelectorId,
        data: {
          _id: mergeId,
          type: CARD_TYPE.CONDITION_MERGE,
        },
      },
    ],
    edges: [
      {
        code: `edge${edgeId}`,
        source: newSelectorId,
        target: condition0,
        type: 'custom',
      },
      {
        code: `edge${edgeId + 1}`,
        source: newSelectorId,
        target: condition1,
        type: 'custom',
      },
      {
        code: `edge${edgeId + 2}`,
        source: condition0,
        target: condition0 + '_end',
        type: 'custom',
      },
      {
        code: `edge${edgeId + 3}`,
        source: condition1,
        target: condition1 + '_end',
        type: 'custom',
      },
      {
        code: `edge${edgeId + 4}`,
        source: condition0 + '_end',
        target: mergeId,
        type: 'custom',
      },
      {
        code: `edge${edgeId + 5}`,
        source: condition1 + '_end',
        target: mergeId,
        type: 'custom',
      },
    ],
  }
  return selectorData
}

export function getAddCondition(
  cId: string,
  parentNode: string,
  edgeId: number,
  target: string | undefined,
  endTarget: string | undefined,
) {
  const conditionId = `${parentNode}_condition_${cId}`
  const conditionFlow = {
    nodes: [
      {
        id: conditionId,
        code: conditionId,
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode,
        data: {
          _id: conditionId,
          type: CARD_TYPE.CONDITION,
          conditionId: cId,
        },
      },
      {
        id: conditionId + '_end',
        code: conditionId + '_end',
        type: 'condition',
        position: { x: 0, y: 0 },
        parentNode,
        data: {
          _id: conditionId,
          type: CARD_TYPE.CONDITION,
        },
        with: 10,
        height: 1,
      },
    ],
    edges: [
      {
        code: `edge${edgeId}`,
        source: parentNode,
        target: conditionId,
        type: 'custom',
      },
      ...(target
        ? [
            {
              code: `edge${edgeId + 1}`,
              markerEnd: MarkerType.ArrowClosed,
              source: conditionId,
              target: target,
              type: 'custom',
            },
            {
              code: `edge${edgeId + 2}`,
              source: endTarget,
              target: conditionId + '_end',
              type: 'custom',
            },
          ]
        : [
            {
              code: `edge${edgeId + 1}`,
              source: conditionId,
              target: conditionId + '_end',
              type: 'custom',
            },
          ]),
      {
        code: `edge${edgeId + (target ? 3 : 2)}`,
        source: conditionId + '_end',
        target: `${parentNode}_merge0`,
        type: 'custom',
      },
    ],
  }
  return conditionFlow
}

// mock数据
export const mockData = {
  id: '8e3a48ab-6bae-49db-b73a-d9a0f075a232',
  saveMode: 3,
  flow: {
    nodes: [
      {
        name: '开始',
        code: 'start0',
        type: 'Start',
        position: {
          x: 100,
          y: 100,
        },
        parentNode: null,
        config: {
          inputs: [
            {
              code: 'input',
              value: {
                type: 'ref',
                content: 'System_Input',
              },
              type: 'string',
              name: '用户输入',
            },
          ],
          outputType: 'content',
          outputs: [
            {
              code: 'output',
              type: 'string',
              name: '默认输出',
            },
          ],
          uploadables: 'doc,docx,pdf,txt,jpg,jpeg,bmp,gif,png,tif,webp',
        },
      },
      {
        name: '智能检查_0',
        description: '技能描述',
        code: 'plan0',
        type: 'Plan',
        position: {
          x: 100,
          y: 204,
        },
        parentNode: '',
        config: {
          id: '6c0fa6e0-51c1-446e-a4e2-a79ebecece4d',
          inputs: [
            {
              code: 'content',
              name: '消息内容',
              required: false,
              type: 'string',
              description: '',
              value: {
                type: 'literal',
                content: '1',
              },
            },
            {
              code: 'businessId',
              name: '业务对象ID',
              required: false,
              type: 'string',
              description: '',
              value: {
                type: 'literal',
                content: '2',
              },
            },
            {
              code: 'data',
              name: '流程表单数据',
              required: false,
              type: 'string',
              description: '',
              value: {
                type: 'literal',
                content: '3',
              },
            },
            {
              code: 'attachments',
              name: '流程附件数据',
              required: false,
              type: 'string',
              description: '',
              value: {
                type: 'literal',
                content: '4',
              },
            },
          ],
          outputs: [
            {
              name: '执行结果',
              code: 'result',
              type: 'number',
              description: '执行结果',
            },
            {
              name: '规则分组',
              code: 'ruleGroups',
              type: 'array<object>',
              description: '规则分组',
            },
          ],
        },
      },
      {
        name: '文档分析_0',
        code: 'documentAnalysis0',
        type: 'DocumentAnalysis',
        position: {
          x: 100,
          y: 308,
        },
        parentNode: '',
        config: {
          files: [
            {
              code: 'fileInput',
              type: 'file',
              value: {
                type: 'ref',
                content: 'System_Keyword_CurrentDocument',
              },
              required: true,
            },
          ],
          pattern: 1,
          outputs: [
            {
              name: '工程名称',
              code: 'name',
              type: 'string',
              description: '工程名称',
            },
            {
              name: '开标时间',
              code: 'time',
              type: 'string',
              description: '开标时间',
            },
            {
              name: '开标地点',
              code: 'place',
              type: 'string',
              description: '开标地点',
            },
            {
              name: '招标人',
              code: 'preson',
              type: 'string',
              description: '招标人',
            },
            {
              name: '深圳市信任行工程造价咨询有限公司的开标情况',
              code: 'recode2',
              type: 'string',
              description: '投标人为深圳市信任行工程造价咨询有限公司的开标情况记录',
            },
            {
              name: '投标信息',
              code: 'bidInformation',
              type: 'array<object>',
              description: '',
              collapse: false,
              schema: [
                {
                  name: '投标人',
                  code: 'bidder',
                  type: 'string',
                  description: '',
                },
                {
                  name: '投标总价',
                  code: 'totalBidPrice',
                  type: 'string',
                  description: '',
                },
                {
                  name: '技术标',
                  code: 'tTechnicalProposal',
                  type: 'string',
                  description: '',
                },
              ],
            },
          ],
          inputs: [],
          imageRecognizeType: 'none',
          ocrService: '',
          useTOC: false,
          imagConverter: false,
          usePageRange: 'all',
          useFirstFewPages: 0,
          useLastFewPages: 0,
        },
      },
      {
        name: '交互卡片_0',
        code: 'card0',
        type: 'Card',
        position: {
          x: 100,
          y: 412,
        },
        parentNode: '',
        config: {
          templateId: '401f4036-9449-4726-a318-4178dbc3c7ef',
          content: '',
          inputs: [],
          title: '请确认如下信息：',
          props: [
            {
              name: 'editable',
              value: true,
            },
            {
              name: 'mode',
              value: 'view',
            },
            {
              name: 'columns',
              value: [
                {
                  name: '工程名称',
                  code: 'name',
                  type: 'Input',
                  required: true,
                  columns: [],
                },
                {
                  name: '开标时间',
                  code: 'time',
                  type: 'Input',
                  required: true,
                  columns: [],
                },
                {
                  name: '开标地点',
                  code: 'place',
                  type: 'Input',
                  required: true,
                  readonly: true,
                  columns: [],
                },
                {
                  name: '招标人',
                  code: 'preson',
                  type: 'Input',
                  required: true,
                  columns: [],
                },
                {
                  name: '投标信息',
                  code: 'bidInformation',
                  type: 'DataTable',
                  required: true,
                  readonly: false,
                  columns: [
                    {
                      name: '投标人',
                      code: 'bidder',
                      type: 'Input',
                      required: true,
                      readonly: false,
                    },
                    {
                      name: '投标总价',
                      code: 'totalBidPrice',
                      type: 'Input',
                      required: true,
                      readonly: true,
                    },
                    {
                      name: '技术标',
                      code: 'tTechnicalProposal',
                      type: 'Input',
                      required: true,
                      readonly: false,
                    },
                  ],
                },
              ],
            },
            {
              name: 'fields',
              value: [],
            },
          ],
          outputs: [
            {
              name: '工程名称',
              code: 'name',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_name',
              },
              required: true,
              type: 'string',
              schema: [],
            },
            {
              name: '开标时间',
              code: 'time',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_time',
              },
              required: true,
              type: 'string',
              schema: [],
            },
            {
              name: '开标地点',
              code: 'place',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_place',
              },
              required: true,
              type: 'string',
              schema: [],
            },
            {
              name: '招标人',
              code: 'preson',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_preson',
              },
              required: true,
              type: 'string',
              schema: [],
            },
            {
              name: '投标信息',
              code: 'bidInformation',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_bidInformation',
              },
              required: true,
              type: 'array<object>',
              schema: [
                {
                  name: '投标人',
                  code: 'bidder',
                  value: {
                    type: 'ref',
                    content: 'bidder',
                  },
                  required: true,
                  type: 'string',
                },
                {
                  name: '投标总价',
                  code: 'totalBidPrice',
                  value: {
                    type: 'ref',
                    content: 'totalBidPrice',
                  },
                  required: true,
                  type: 'string',
                },
                {
                  name: '技术标',
                  code: 'tTechnicalProposal',
                  value: {
                    type: 'ref',
                    content: 'tTechnicalProposal',
                  },
                  required: true,
                  type: 'string',
                },
              ],
            },
            {
              code: 'recode2',
              value: {
                type: 'ref',
                content: 'NodeOutput_documentAnalysis0_recode2',
              },
              type: 'string',
              required: true,
              name: '深圳市信任行工程造价咨询有限公司的开标情况',
              description: '投标人为深圳市信任行工程造价咨询有限公司的开标情况记录',
            },
          ],
          type: 'interactive',
        },
      },
      {
        name: '交互卡片_2',
        code: 'card2',
        type: 'Card',
        position: {
          x: 100,
          y: 516,
        },
        parentNode: '',
        config: {
          templateId: '08dc64ca-b4b8-4c56-8225-302339e29baf',
          content: '',
          inputs: [],
          title: '',
          props: [
            {
              name: 'mount',
              value: 'message',
            },
            {
              name: 'visible',
              value: true,
            },
            {
              name: 'url',
              value: {
                type: 'literal',
                content: 'https://www.baidu.com/',
              },
            },
          ],
          outputs: [],
          type: 'interactive',
        },
      },
      {
        name: '结束步骤：完成执行动作',
        code: 'end0',
        type: 'End',
        position: {
          x: 100,
          y: 620,
        },
        parentNode: null,
        config: {
          outputs: [],
        },
      },
    ],
    edges: [
      {
        code: 'edge0',
        source: 'start0',
        target: 'plan0',
      },
      {
        code: 'edge1',
        source: 'documentAnalysis0',
        target: 'card0',
      },
      {
        code: 'edge5',
        source: 'card0',
        target: 'card2',
      },
      {
        code: 'edge7',
        source: 'card2',
        target: 'end0',
      },
      {
        code: 'edge8',
        source: 'plan0',
        target: 'documentAnalysis0',
      },
    ],
  },
}
