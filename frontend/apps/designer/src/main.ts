import ArcoVue from '@arco-design/web-vue'
import '@arco-design/web-vue/dist/arco.css'
import { createPinia } from 'pinia'
import 'virtual:svg-icons-register'
import { createApp } from 'vue'
import App from './App.vue'
import './assets/style/index.less'
import svgIcon from './components/svg-icon.vue'
import { useFlowStore } from './stores/flow'
import './style.css'

const onInit = (elementId: string) => {
  const mountDom = document.getElementById(elementId)
  if (!mountDom) return
  mountDom.innerHTML = ''
  const mode: string = mountDom.getAttribute('data-mode') || ''
  const pinia = createPinia()
  const app = createApp(App)
  app.component('svg-icon', svgIcon)
  app.use(ArcoVue)
  app.use(pinia)
  const flowStore = useFlowStore()
  flowStore.setFlowMode(mode)
  app.mount(`#${elementId}`)
}
window._designerInit = onInit
onInit('skillDesigner')
