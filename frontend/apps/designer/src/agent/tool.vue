<script setup lang="ts">
import ToolGroup from './tool-group.vue'
import InputParams from './input-params.vue'
import UploadParams from './upload-params.vue'
import { computed, onMounted, ref } from 'vue'
import { useDesignerSdk } from '../stores/sdk'
import {
  createKnowledgeCallbackEvent,
  createPluginCallbackEvent,
  createMcpServiceCallbackEvent,
} from '../events'
import { useFlowStore } from '../stores/flow'
import GroupDesigner from './group-designer.vue'
import { Setting } from './setting'

const data = ref({})
const sdk = useDesignerSdk()
const flowStore = useFlowStore()

const tools = computed(() => {
  return (
    flowStore.agentData.tools?.map((item) => ({
      text: item.name,
      value: item.path,
      origin: item,
    })) || []
  )
})
const handleAddPlugin = () => {
  sdk.openPlugin(tools.value)
}
const knowledgs = computed(() => {
  return (
    flowStore.agentData.knowledgs?.map((item) => ({
      text: item.name,
      value: item.id,
      origin: item,
    })) || []
  )
})

enum KnowledgeType {
  Document = 1,
  Data = 3,
}

const knowledgeType = ref<KnowledgeType>()

const onKnowledgeAdd = (v: any) => {
  const type = +v as KnowledgeType
  // v为文本转换为枚举
  knowledgeType.value = type
  let title = '选择知识库'
  if (type === KnowledgeType.Document) {
    title = '选择文档知识库'
  } else if (type === KnowledgeType.Data) {
    title = '选择数据知识库'
  }
  const data = knowledgs.value.filter((x) => x.origin.type === type)

  sdk.openKnowledge({
    data,
    title,
    type,
  })
}
const onPluginCallback = (event: Event) => {
  flowStore.agentData.tools = (event as CustomEvent).detail
}
const onKnowledgeCallback = (event: Event) => {
  const data = (event as CustomEvent).detail as KnowledgeData[]
  if (knowledgeType.value) {
    flowStore.agentData.knowledgs = flowStore.agentData.knowledgs.filter((v) => {
      return v.type !== knowledgeType.value
    })
  }
  flowStore.agentData.knowledgs = [...flowStore.agentData.knowledgs, ...data]
}
const onMcpServiceCallback = (event: Event) => {
  const newMcps = (event as CustomEvent).detail
  flowStore.agentData.mcps = [...(flowStore.agentData.mcps || []), ...newMcps]
}
onMounted(() => {
  document.addEventListener(createPluginCallbackEvent(), onPluginCallback)
  document.addEventListener(createKnowledgeCallbackEvent(), onKnowledgeCallback)
  document.addEventListener(createMcpServiceCallbackEvent(), onMcpServiceCallback)
})
const delPlugin = (index: number) => {
  flowStore.agentData.tools.splice(index, 1)
}
const delKnowledge = (index: number) => {
  flowStore.agentData.knowledgs.splice(index, 1)
}

const handleAddMcpService = () => {
  sdk.openMcp()
}

const delMcpService = (index: number) => {
  flowStore.agentData.mcps.splice(index, 1)
}

const renderMCPTitle = (item: MCPData) => {
  return item.toolName ? `${item.serviceName} / ${item.toolName}` : item.serviceName
}
const renderMCPDescription = (item: MCPData) => {
  return item.toolGUID ? item.toolDescription : item.serviceDescription
}
</script>

<template>
  <a-form
    :model="data"
    label-align="left"
    :label-col-props="{ span: 6, offset: 0 }"
    :wrapper-col-props="{ span: 18, offset: 0 }"
  >
    <div class="agent-tool p-[20px]">
      <div class="agent-tool-title">工具</div>
      <tool-group
        title="插件"
        icon-name="plugin"
        :data="flowStore.agentData.tools"
        @add="handleAddPlugin"
        @del="delPlugin"
        @click=""
      />
      <tool-group
        title="MCP"
        icon-name="document"
        iconField="serviceIcon"
        :data="flowStore.agentData.mcps"
        :renderTitle="renderMCPTitle"
        :renderDescription="renderMCPDescription"
        @add="handleAddMcpService"
        @del="delMcpService"
        @click=""
      >
        <template #tag="{ item }">
          <span
            v-if="item.serviceName && !item.toolGUID"
            class="rounded-[8px] bg-[rgba(0,_0,_0,_.08)] px-[4px] py-[0] text-xs font-normal text-[#999]"
            >auto</span
          >
        </template>
      </tool-group>

      <div class="agent-tool-title mt-[20px]">知识库</div>
      <tool-group
        title="知识库"
        icon-name="knowledge"
        :data="flowStore.agentData.knowledgs"
        @del="delKnowledge"
        @click=""
      >
        <template #btn>
          <a-dropdown @select="onKnowledgeAdd" position="br">
            <span @click.stop>
              <svg-icon name="add-bg" size="20px" />
            </span>
            <template #content>
              <a-doption value="1">选择文档知识库</a-doption>
              <a-doption value="3">选择数据知识库</a-doption>
            </template>
          </a-dropdown>
        </template>
      </tool-group>
      <div class="agent-tool-title mt-[24px]">变量</div>
      <input-params title="输入参数" />
      <upload-params
        title="文件上传"
        :data="flowStore.agentData"
        :show-tips="false"
        :support-files="['doc,docx', 'pdf', 'xls,xlsx', 'ppt,pptx', 'txt']"
      />
      <group-designer title="设置" @add="">
        <template #btn></template>
        <template #content>
          <Setting
            v-model:model-code="flowStore.agentData.modelInstanceCode"
            v-model:max-loops="flowStore.agentData.maxContextTurnsNumber"
            v-model:execution-setting="flowStore.agentData.executionSetting"
            v-model:generation-diversity="flowStore.agentData.generationDiversity"
          />
        </template>
      </group-designer>
    </div>
  </a-form>
</template>

<style lang="less">
.agent-tool-title {
  height: 22px;
  color: #999999;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
}

.agent-tool {
  .arco-form-item {
    margin-bottom: 12px;
  }
}
</style>
