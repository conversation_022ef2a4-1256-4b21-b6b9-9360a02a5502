<script setup lang="ts">
import { watch, ref } from 'vue'
import GroupDesigner from './group-designer.vue'
import { getDocumentFileInfo } from '../api/doc-service'

const { iconField, data } = defineProps({
  iconName: {
    type: String,
  },
  title: {
    type: String,
  },
  data: {
    type: Object,
  },
  titleField: {
    type: String,
    default: 'name',
  },
  descriptionField: {
    type: String,
    default: 'description',
  },
  iconField: String,
  renderTitle: Function,
  renderDescription: Function,
})
const emit = defineEmits(['click', 'del', 'add'])
const handleClick = () => {
  emit('click')
}
const handleDel = (index: string) => {
  emit('del', Number(index))
}
const handleAdd = () => {
  emit('add')
}

const documentUrlList = ref<Record<string, string>>({})

const getIconUrl = async (item: any, key: number) => {
  const documentIcons =
    iconField && item[iconField] && item[iconField] !== '' ? JSON.parse(item[iconField]) : null
  const documentIcon = documentIcons && documentIcons[0] ? documentIcons[0] : {}
  if (documentIcon.documentGuid) {
    const res = await getDocumentFileInfo(documentIcon.documentGuid)
    documentUrlList.value[key] = res.downloadUrl || ''
  }
}

watch(
  () => data,
  (val) => {
    if (iconField && val && Array.isArray(val)) {
      val.forEach((item: any, key: number) => {
        getIconUrl(item, key)
      })
    }
  },
  { immediate: true },
)
</script>

<template>
  <group-designer :title="title" @add="handleAdd">
    <template #btn v-if="$slots.btn">
      <slot name="btn" />
    </template>
    <template #content>
      <div
        v-if="data?.length > 0"
        class="agent-group_item relative inline-block w-full cursor-pointer rounded-[8px] py-[4px] pl-[4px]
          pr-[8px]"
        v-for="(item, key) in data"
        @click="handleClick"
        :key="key"
      >
        <div
          class="float-left flex h-[40px] w-[40px] items-center justify-center overflow-hidden rounded-[6px]
            border-[1px] border-solid border-[#EAEAEA] bg-[#FFF]"
        >
          <svg-icon v-if="!iconField || !documentUrlList[key]" :name="iconName" size="24px" />
          <img v-if="iconField && documentUrlList[key]" :src="documentUrlList[key]" />
        </div>
        <div class="float-left ml-[8px] w-[calc(100%_-_56px)] flex-1 not-italic">
          <div
            class="float-left text-ellipsis text-[13px] font-semibold leading-[22px] text-[#333333]"
          >
            {{ renderTitle ? renderTitle(item) : item[titleField] }}
            <slot name="tag" :item="item"></slot>
          </div>
          <span class="text-ellipsis text-xs text-[#999999]">{{
            renderDescription ? renderDescription(item) : item[descriptionField] || '暂无描述信息'
          }}</span>
        </div>
        <div
          class="delete-btn invisible absolute bottom-[0] right-[4px] top-[0] float-left flex cursor-pointer
            items-center"
          @click="handleDel(key)"
        >
          <svg-icon name="minus" color="#8992A2" size="16px" />
        </div>
      </div>
      <div
        class="mb-[12px] flex items-center justify-center rounded-[8px] bg-[#F3F4F9] px-[4px] py-[8px]"
        v-else
      >
        <span class="text-center text-xs font-normal not-italic leading-[20px] text-[#999999]"
          >暂无{{ title }}</span
        >
      </div>
    </template>
  </group-designer>
</template>
