<script setup lang="ts">
import { ref } from 'vue'

defineProps({
  title: {
    type: String,
  },
})
const emit = defineEmits(['add'])
const visible = ref(true)
const handleToggle = () => {
  visible.value = !visible.value
}
const handleAdd = () => {
  emit('add')
}

const toggle = (v: boolean) => {
  visible.value = v
}

defineExpose({
  toggle,
})
</script>

<template>
  <div
    class="agent-group border-bottom:#EAEAEA mt-[12px] border-b"
    :class="{ 'agent-group_collapse': !visible }"
  >
    <div
      class="mb-[12px] flex h-[32px] cursor-pointer items-center justify-between px-[4px] py-[5px]"
      @click="handleToggle"
    >
      <span class="flex items-center">
        <svg-icon name="down-line" size="16px" class="agent-group_toggle-bar mr-[8px]" />
        {{ title }}
      </span>
      <slot name="btn" v-if="$slots.btn"></slot>
      <span v-else @click.stop="handleAdd">
        <svg-icon name="add-bg" size="20px" />
      </span>
    </div>
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-screen"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 max-h-screen"
      leave-to-class="opacity-0 max-h-0"
    >
      <div class="agent-group_content" v-if="visible">
        <slot name="content"></slot>
      </div>
    </transition>
  </div>
</template>

<style lang="less">
.agent-group_item:hover {
  background: linear-gradient(90deg, #eff3ff 0%, #f1f0ff 100%);
  transition: 0.3s;

  .delete-btn {
    visibility: visible;
  }
}

.agent-group_item:last-child {
  margin-bottom: 12px;
}

.agent-group_collapse {
  .agent-group_toggle-bar {
    transform: rotate(-90deg);
  }
}
</style>
