<script setup lang="ts">
import { computed, useTemplateRef } from 'vue'
import GroupDesigner from './group-designer.vue'
import FieldForm from '../components/subControls/field-form.vue'
import { FIELD_OPTIONS } from '../stores/configData'
import { useFlowStore } from '../stores/flow'
defineProps({
  title: {
    type: String,
  },
})
const flowStore = useFlowStore()
const fieldFormRef = useTemplateRef('fieldForm')
const handleAdd = () => {
  fieldFormRef.value?.add()
}
const config = computed(() => {
  return [
    {
      label: '参数名称',
      prop: 'name',
      placeholder: '请输入名称',
      width: '32%',
      type: 'input',
      default: '',
    },
    {
      label: '参数编码',
      prop: 'code',
      placeholder: '请输入编码',
      width: '30%',
      type: 'input',
      default: '',
    },
    {
      label: '是否必填',
      prop: 'required',
      placeholder: '请选择',
      width: '12%',
      type: 'checkbox',
      default: true,
    },
    {
      label: '字段类型',
      prop: 'type',
      placeholder: '请选择',
      width: '28%',
      type: 'select',
      options: FIELD_OPTIONS,
      default: 'string',
    },
  ]
})
</script>

<template>
  <group-designer :title="title" @add="handleAdd">
    <template #content>
      <field-form
        ref="fieldForm"
        :value="flowStore.agentData.inputs"
        @input="flowStore.agentData.inputs = $event"
        :config="config"
        :enable-sort="true"
        :allow-add="false"
        :show-label="true"
      />
    </template>
  </group-designer>
</template>
