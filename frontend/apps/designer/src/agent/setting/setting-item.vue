<script lang="ts" setup>
import { IconQuestionCircle } from '@arco-design/web-vue/es/icon'

defineProps<{
  label: string
  tooltip?: string
}>()
</script>

<template>
  <a-form-item>
    <template #label>
      <span>{{ label }}</span>
      <a-popover v-if="tooltip" trigger="hover" placement="bottom" :style="{ width: '300px' }">
        <template #content>
          <div v-html="tooltip"></div>
        </template>
        <IconQuestionCircle class="ml-2 text-[#a3a3a3]" />
      </a-popover>
    </template>
    <slot />
  </a-form-item>
</template>
