type ExecutionSettingConfigItem = {
  name: string
  tips: string
  minValue: number
  maxValue: number
  rounding: number
  defaultValue?: number
}

type GenerationDiversityConfig = {
  temperature: number
  top_p: number
}

export type ExecutionSettingConfig = {
  temperature: ExecutionSettingConfigItem
  top_p: ExecutionSettingConfigItem
  max_tokens: ExecutionSettingConfigItem
}

export type ExecutionSetting = {
  config: ExecutionSettingConfig
  generationDiversity: {
    precise: GenerationDiversityConfig
    balance: GenerationDiversityConfig
    creative: GenerationDiversityConfig
  } & Record<string, GenerationDiversityConfig>
}

export const diversityOptions = [
  { label: '精确模式', value: 'precise' },
  { label: '平衡模式', value: 'balance' },
  { label: '创意模式', value: 'creative' },
  { label: '自定义', value: 'custom' },
]

export const thinkingOptions = [
  {
    label: '否',
    value: false,
  },
  {
    label: '是',
    value: true,
  },
]
