<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'
import { ModelOption } from './model'
import { diversityOptions, thinkingOptions } from './setting'
import SettingItem from './setting-item.vue'
import { getModels } from '../../api/model'

const modelCode = defineModel<string>('modelCode')
const maxLoops = defineModel<number>('maxLoops')
const generationDiversity = defineModel<string>('generationDiversity')
const executionSetting = defineModel<ExecutionSetting | null>('executionSetting')

const modelOptions = ref<ModelOption[]>([])

watch(modelCode, () => {
  generationDiversity.value = 'precise'
  executionSetting.value = null
})

onMounted(async () => {
  const models = await getModels()

  modelOptions.value = models.map<ModelOption>((model) => {
    let executionSetting = null
    if (model.executionSetting) {
      try {
        executionSetting = JSON.parse(model.executionSetting)
      } catch {}
    }

    return {
      label: model.modelInstanceName,
      value: model.modelInstanceCode,
      reasoning: model.supportDeepThink === 1,
      executionSetting,
    }
  })
})

const modelOption = computed(() => {
  return modelOptions.value.find((option) => option.value === modelCode.value)
})

const supportThinking = computed(() => modelOption.value?.reasoning)

const executionSettingData = computed(() => modelOption.value?.executionSetting)

const executionSettingConfig = computed(() => executionSettingData.value?.config ?? null)

const generationDiversityConfig = computed(
  () => executionSettingData.value?.generationDiversity ?? null,
)

watch(generationDiversityConfig, (v) => {
  if (!generationDiversity.value || !v) {
    return
  }

  console.log(modelOption)

  const x = v[generationDiversity.value]
  if (!x) {
    return
  }

  executionSetting.value = {
    ...executionSetting.value,
    temperature: x.temperature ?? 0,
    top_p: x.top_p ?? 0,
    max_tokens: maxTokensConfig.value?.defaultValue ?? 0,
  }
})

const temperatureConfig = computed(() => executionSettingConfig.value?.temperature ?? null)

const topPConfig = computed(() => executionSettingConfig.value?.top_p ?? null)

const maxTokensConfig = computed(() => executionSettingConfig.value?.max_tokens ?? null)

const enableThinking = computed({
  get: () => executionSetting.value?.enable_thinking ?? false,
  set: (v) => {
    if (v) {
      executionSetting.value = {
        ...executionSetting.value,
        enable_thinking: v,
      }
    } else {
      // 移除思考配置
      const x = executionSetting.value ?? {}
      delete x.enable_thinking
      delete x.thinking_budget
      executionSetting.value = x
    }
  },
})

const thinkingBudget = computed({
  get: () => executionSetting.value?.thinking_budget ?? 0,
  set: (v) => {
    executionSetting.value = {
      ...(executionSetting.value ?? {}),
      thinking_budget: v,
    }
  },
})

const temperature = computed({
  get: () => executionSetting.value?.temperature ?? 0,
  set: (v) => {
    executionSetting.value = {
      ...(executionSetting.value ?? {}),
      temperature: v,
    }
  },
})

const topP = computed({
  get: () => executionSetting.value?.top_p ?? 0,
  set: (v) => {
    executionSetting.value = {
      ...(executionSetting.value ?? {}),
      top_p: v,
    }
  },
})

const maxTokens = computed({
  get: () => executionSetting.value?.max_tokens ?? 0,
  set: (v) => {
    executionSetting.value = {
      ...(executionSetting.value ?? {}),
      max_tokens: v,
    }
  },
})

watch(generationDiversity, (v) => {
  if (!generationDiversityConfig.value || !v) {
    return
  }
  const x = generationDiversityConfig.value[v]
  if (!x) {
    return
  }

  executionSetting.value = {
    ...(executionSetting.value ?? {}),
    temperature: x.temperature ?? 0,
    top_p: x.top_p ?? 0,
  }
})

const getStep = (rounding: number) => {
  return Math.pow(10, -rounding)
}
</script>

<template>
  <div class="agent-tool-setting">
    <SettingItem label="模型设置">
      <a-select v-model="modelCode" placeholder="请选择" allow-clear>
        <a-option v-for="option in modelOptions" :key="option.value" :value="option.value">{{
          option.label
        }}</a-option>
      </a-select>
    </SettingItem>
    <template v-if="modelOption">
      <template v-if="supportThinking">
        <SettingItem label="深度思考">
          <a-radio-group v-model="enableThinking">
            <a-radio :value="option.value" v-for="(option, index) in thinkingOptions" :key="index">
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </SettingItem>
        <SettingItem label="思考长度" v-if="enableThinking">
          <a-slider
            v-model="thinkingBudget"
            show-input
            :default-value="0"
            :min="0"
            :max="38912"
            :step="1"
            :marks="{ '0': '0', '38912': '38912' }"
          />
        </SettingItem>
      </template>
      <template v-if="executionSettingConfig">
        <SettingItem label="生成多样性">
          <a-radio-group v-model="generationDiversity">
            <a-radio :value="option.value" v-for="option in diversityOptions" :key="option.value">
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </SettingItem>

        <SettingItem
          v-if="temperatureConfig"
          :label="temperatureConfig.name"
          :tooltip="temperatureConfig.tips"
        >
          <a-slider
            v-model="temperature"
            show-input
            :default-value="temperatureConfig.defaultValue"
            :min="temperatureConfig.minValue"
            :max="temperatureConfig.maxValue"
            :step="getStep(temperatureConfig.rounding)"
            :marks="{
              '0': '0',
              '1': '1',
              [temperatureConfig.maxValue]: temperatureConfig.maxValue + '',
            }"
          />
        </SettingItem>

        <SettingItem :label="topPConfig.name" v-if="topPConfig" :tooltip="topPConfig.tips">
          <a-slider
            v-model="topP"
            show-input
            :default-value="topPConfig.defaultValue"
            :min="topPConfig.minValue"
            :max="topPConfig.maxValue"
            :step="getStep(topPConfig.rounding)"
            :marks="{
              '0': '0',
              [topPConfig.maxValue]: topPConfig.maxValue + '',
            }"
          />
        </SettingItem>

        <SettingItem
          :label="maxTokensConfig.name"
          v-if="maxTokensConfig"
          :tooltip="maxTokensConfig.tips"
        >
          <a-slider
            v-model="maxTokens"
            show-input
            :default-value="maxTokensConfig.defaultValue"
            :min="maxTokensConfig.minValue"
            :max="maxTokensConfig.maxValue"
            :step="getStep(maxTokensConfig.rounding)"
            :marks="{
              '0': '0',
              [maxTokensConfig.maxValue]: maxTokensConfig.maxValue + '',
            }"
          />
        </SettingItem>
      </template>
    </template>

    <SettingItem label="携带上下文轮数">
      <a-slider
        v-model="maxLoops"
        :default-value="30"
        show-input
        :min="1"
        :max="60"
        :step="1"
        :marks="{ '1': '1', '30': '默认', '60': '60' }"
      />
    </SettingItem>
  </div>
</template>

<style lang="less">
.agent-tool-setting {
  .arco-form-item-label {
    display: flex;
    align-items: center;
  }
  .arco-slider-with-marks {
    margin: 0;
    padding: 5px 12px;
  }
  .arco-slider {
    display: inline-flex;
  }
}
</style>
