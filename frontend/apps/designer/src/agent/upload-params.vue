<script setup lang="ts">
import { onMounted, useTemplateRef } from 'vue'
import FileType from '../components/subControls/file-type.vue'
import GroupDesigner from './group-designer.vue'

const { data } = defineProps<{
  data: AgentData
  title: string
  supportFiles: string[]
  showTips: boolean
}>()
const groupDesignerRef = useTemplateRef('groupDesigner')
const handleClick = () => {
  if (data.useFileUpload) {
    groupDesignerRef.value?.toggle(true)
  }
}

onMounted(() => {
  if (data.maxUploadNumber === undefined) {
    data.maxUploadNumber = 1
  }
})

const onMaxUploadNumberChange = () => {
  if (!data.maxUploadNumber) {
    data.maxUploadNumber = 1
  }
}
</script>

<template>
  <group-designer :title="title" @add="" ref="groupDesigner">
    <template #btn>
      <a-switch
        v-model="data.useFileUpload"
        :default-value="false"
        size="small"
        @click.stop="handleClick"
      ></a-switch>
    </template>
    <template #content>
      <a-form-item label="移动端上传" :disabled="!data.useFileUpload">
        <a-checkbox v-model="data.useQRCodeUpload"></a-checkbox>
      </a-form-item>
      <file-type
        :form-data="data"
        :support="supportFiles"
        :show-tips="showTips"
        :disabled="!data.useFileUpload"
      />
      <a-form-item
        label="允许上传文件数"
        :disabled="!data.useFileUpload"
        :required="data.useFileUpload"
      >
        <a-input-number
          v-model="data.maxUploadNumber"
          placeholder="请输入文件数量"
          :precision="0"
          :min="1"
          :max="10"
          model-event="input"
          @blur="onMaxUploadNumberChange"
        />
      </a-form-item>
    </template>
  </group-designer>
</template>
