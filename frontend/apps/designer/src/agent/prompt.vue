<script setup lang="ts">
import { computed, onMounted, ref, watch, nextTick } from 'vue'
// @ts-ignore
import SelectField from '../components/subControls/select-field.vue'
import { useFlowStore } from '../stores/flow'
import { debounce } from 'radash'
import { useMarkdownEditor, PlaceholderType } from '../composables/useMarkdownEditor'


const { placeholder } = defineProps<{
  placeholder: string
}>()
const flowStore = useFlowStore()
const promptInputs = ref<PromptInputs[]>([])
const agentInputs = computed(() => JSON.parse(JSON.stringify(flowStore.agentData.inputs ?? [])))
const update = (value: string) => {
  flowStore.agentData.prompt.template = value
}
const regex = /{{\$([^{}]+)}}/g // 正则表达式匹配 {entity.field} 形式的文本
const updateInputs = (text: string) => {
  const fieldsMap: Record<string, PromptField> = sourceMap.value
  const content = text.replace(/[\n\r\t\f\v\u2028\u2029]/g, '')
  const tokens = []

  let match

  while ((match = regex.exec(content)) !== null) {
    const [, fieldText] = match
    const item = fieldsMap[fieldText] || {}
    if (item.field) {
      tokens.push({
        code: fieldText,
        value: {
          type: 'ref',
          content: item.field,
        },
      })
    } else {
      // 通过输入的变量名 注入对应的变量
      nextTick(() => {
        handleInputField(fieldText)
      })
    }
  }
  promptInputs.value = [...tokens]
}

const treeData = computed(() => {
  const inputs: { code: string; name: string; type: string }[] = agentInputs.value
  const arr: PromptField[] = []
  inputs.forEach((item) => {
    arr.push({
      ...item,
      label: item.name,
      value: item.code,
      field: item.code,
      title: item.name,
    })
  })
  return [
    {
      name: '系统变量',
      value: 'system',
      code: 'system',
      children: systemData.value,
    },
    ...arr,
  ] as unknown as SelectFieldParamsType[]
})

const sourceMap = computed(() => {
  const arr = promptInputs.value ?? []
  const map: Record<string, PromptField> = {}
  arr.forEach((item) => {
    if (item.value && item.value.type === 'ref') {
      const v = mapList.value[item.value.content]
      if (v) {
        map[item.code] = v
      }
    }
  })
  return { ...mapList.value, ...map }
})

const mapList = computed<Record<string, PromptField>>(() => {
  const arr: PromptField[] = agentInputs.value
  systemData.value?.forEach((item) => {
    arr.push(...(item.children || []))
  })
  const map: Record<string, PromptField> = {}
  arr.forEach((item) => {
    if (!item.value) {
      item.value = item.code
    }
    item.field = item.value
    item.title = item.name
    item.label = item.name
    map[item.value] = item
  })
  return map
})

const originMapKey = computed(() => {
  return Object.keys(mapList.value).map((key) => mapList.value[key].code)
})

onMounted(() => {
  const { inputs } = flowStore.agentData.prompt
  promptInputs.value = inputs || []
  updateInputs(content.value)
})

const { template } = flowStore.agentData.prompt
const content = ref(template)
const fieldMaps = computed(() => {
  const map: Record<string, any> = {}
  Object.keys(sourceMap.value).forEach((key) => {

    const item = sourceMap.value[key]
    map[`$${key}`] = {
      ...item,
      type: PlaceholderType.VARIABLE
    }
  })
  return map
})
const { setContent, addFieldToCurrent, refreshPlaceholders } = useMarkdownEditor({
  content: content, 
  data: fieldMaps, 
  elementId: 'refEditor',
  minHeight: '100%',
  maxHeight: '100%',
  update: (v) => {
    content.value = v
  }
})

const systemData = computed(() => {
  return flowStore.getSystemParamData(true)
})
const getCodeValue = (value: string) => {
  let i = 0
  promptInputs.value.forEach((item: any) => {
    if (item.code.startsWith(value)) {
      const index = Number(item.code.replace(value, ''))
      const isNumber = typeof index === 'number'
      if (isNumber) {
        i = index + 1
      }
    }
  })
  return value + i
}
const handleSelect = (item: { name: string; value: string; code: string }) => {
  const code = getCodeValue(item.code)
  promptInputs.value.push({ code: code, value: { type: 'ref', content: item.value } })
  addFieldToCurrent(code)
}

const handleInputField = (text: string) => {
  if (originMapKey.value.includes(text)) {
    const code = getCodeValue(text)
    const itemKey = Object.keys(mapList.value).find((key) => {
      return mapList.value[key].code === text
    })
    const item = mapList.value[itemKey || ''] || {}
    if (item) {
      // 更新字段列表
      promptInputs.value.push({ code: code, value: { type: 'ref', content: item.value } })
      // 替换content的内容
      update(content.value.replace(`{{$${text}}}`, `{{$${code}}}`))
      setContent(content.value.replace(`{{$${text}}}`, `{{$${code}}}`))
    }
  }
}
const updateContent = () => {
  // 参数列表更新时 要同步更新输入框中的参数名称 code如果修改就删除字段 
  updateInputs(content.value)
  refreshPlaceholders()
  nextTick(() => {
    setContent(content.value)
  })
}

const debouncedUpdate = debounce({ delay: 500 }, updateContent)

watch(promptInputs, (v) => {
  flowStore.agentData.prompt.inputs = v
})
watch(agentInputs, () => {
  debouncedUpdate()
})

// 更新content数据
watch(content, (v) => {
  update(v)
  updateInputs(v)
})
</script>
<template>
  <div class="designer-prompt h-full">
    <div
      class="prompt-header flex items-center justify-between pb-[6px] pl-[20px] pr-[20px] pt-[18px]"
    >
      <span class="text-sm font-semibold not-italic leading-[22px] text-[#333333]">指令提示词</span>
      <select-field
        :paramsData="treeData"
        :selected="[]"
        @select="handleSelect"
        :filterSystemCode="false"
        :noLevel="true"
        stepName="输入参数"
        :key="treeData.length"
      >
        <div
          class="inline-flex cursor-pointer items-center gap-[4px] rounded-[20px] bg-[#EBF2FF] px-[8px] py-[4px]"
        >
          <svg-icon name="add-bg" size="18px" />
          <span class="text-xs font-normal not-italic leading-[20px] text-[#266eff]">插入变量</span>
        </div>
      </select-field>
    </div>
    <div class="prompt-content h-[calc(100%_-_52px)] overflow-y-auto" ref="refEditor"></div>
  </div>
</template>

<style lang="less">
.designer-prompt {
  .prompt-content {
    resize: none;
    padding: 6px 14px 14px;
  }
}
</style>
