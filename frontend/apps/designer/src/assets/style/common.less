@color-primary: #266eff;
html, body {
  overflow: hidden;
}
div, span, svg {
  outline: none;
}
.skill_layout {
  min-width: 1280px;
  
  .sortable-ghost {
    opacity: 0.5;
  }

  .sortable-chosen {
    background-color: #f0f9ff;
  }

  .sortable-drag {
    opacity: 0.8;
  }

}
.custom-grid {
  .arco-table-container {
    border-radius: 3px;
    overflow: hidden;
  }
  .arco-table-cell {
    min-height: 36px;
    padding: 6px 12px;
    box-sizing: border-box;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    color: #333333;
  }
  .arco-table-tr-empty .arco-table-cell {
    height: auto;
  }
  .arco-table-th-title {
    font-weight: 600;
  }
  thead .arco-table-tr {
    background-color: #fbfbfb;
  }
  .arco-table-th {
    background-color: transparent;
    & + .arco-table-th {
      border-left: 1px solid #eaeaea;
    }
  }
  .arco-table-border .arco-table-tr .arco-table-th {
    border-bottom: 1px solid #eaeaea;
  }
  .arco-table-td {
    background-color: transparent !important;
    min-width: 80px;
  }
}

div.arco-modal-simple {
  padding: 32px 24px 24px 24px;
  .arco-modal-header {
    margin-bottom: 8px;
  }
  .arco-modal-footer {
    margin-top: 24px;
    text-align: right;
  }
}
.arco-btn {
  min-width: 70px;
  padding: 5px 12px;
  border-radius: 4px;
  &.arco-btn-secondary,
  .arco-btn-secondary[type='button'] {
    border: 1px solid #ddd;
    background-color: #fff;
  }
}

.field-form {
  .arco-select-view-single.arco-select-view-disabled {
    color: #999999;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
  }
  .field-form_row .arco-row {
    float: left;
    display: inline-block;
  }
  .arco-col {
    width: calc(100% - 6px);
  }
  .field-form_btn {
    margin: 9px 0;
    cursor: pointer;
    display: inline-block;
    width: 24px;
    line-height: 0;
  }
  .field-form_row-inner + svg,
  .field-form_row .arco-icon {
    margin: 8px 0px 8px 8px;
    cursor: pointer;
  }
  .field-form_row-inner.field-form_sort {
    padding-left: 18px;
    box-sizing: border-box;
    position: relative;
  }
  .field-item_date .arco-form-item-content-wrapper,
  .arco-picker,
  .arco-input-wrapper,
  .arco-select-view-single,
  .arco-textarea-wrapper,
  .arco-select-view-multiple,
  .arco-input-tag {
    background-color: transparent;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333333;
    font-size: 13px;
  }

  .arco-select-view-single .arco-select-view-value {
    min-height: 28px;
    padding-top: 3px;
    padding-bottom: 3px;
  }
  .arco-input-wrapper.arco-input-disabled:hover {
    border-color: #ddd;
  }
  .arco-select-view-single .arco-select-view-input[disabled],
  .arco-input-wrapper .arco-input[disabled] {
    -webkit-text-fill-color: #999;
  }
  .arco-input-prepend,
  .arco-input-append {
    border: 1px solid #ddd !important;
    background: #f5f5f5 !important;
  }
  .arco-input-prepend {
    border-right: 0 !important;
  }
  .arco-input-append {
    border-left: 0 !important;
  }
  .arco-input.arco-input-size-medium {
    font-size: 13px !important;
  }
  .arco-form-item {
    margin-bottom: 12px;
  }
  .arco-form-item_column .arco-form-item-content {
    flex-direction: column;
    align-items: start;
    > span {
      padding: 4px 0;
    }
    > p {
      color: #999999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
}

.form-custom {
  .field-form_row-inner {
    width: calc(100% - 24px);
    display: flex;
    flex-direction: row;
    &.field-form_full {
      width: 100% !important;
      + .field-form_btn {
        display: none;
      }
    }
    .arco-row {
      .arco-col {
        width: 100%;
      }
      & + .arco-row {
        box-sizing: border-box;
      }
    }
  }
  .arco-form-item-layout-inline {
    margin-right: 8px;
    position: relative;
  }
  .arco-form-item_group-line {
    .arco-form-item-label-col {
      display: block;
      position: relative;
      left: auto;
    }
  }
  .arco-form-item-label-col {
    display: none;
    position: absolute;
    left: -10px;
    svg {
      margin: 8px 0;
    }
  }
}

// 层级结构输出参数
.form-custom.field-form_params {
  .field-form_row {
    position: relative;
  }
  .field-form_row-collapse {
    position: absolute;
    left: -24px;
    top: 8px;
    cursor: pointer;
    background: #fff;
    z-index: 1;
  }
  .field-form_sub {
    position: relative;
    display: inline-block;
    .field-form_row{
      position: relative;
      &:before {
        content: '';
        display: inline-block;
        left: -40.5px;
        width: 32px;
        border-left: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        position: absolute;
        top: -32px;
        border-bottom-left-radius: 4px;
        height: 48px;
        box-sizing: border-box;
      }
    }
    .field-form_box {
      padding-bottom: 0 !important;
    }
  }
  .field-form_row-line {
    position: absolute;
    left: -40.5px;
    top: 0;
    border-left: 1px solid #ddd;
    display: inline-block;
    bottom: 32px;
  }
  .arco-form-item-message {
    display: none;
  }
  .field-form_btn-box {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #ddd;
    display: inline-block;
    text-align: center;
    margin-right: 8px;
    cursor: pointer;
    float: left;
    padding: 4px 0;
    box-sizing: border-box;
    &:hover {
      border-color: #266EFF;
    }
  }
}

.arco-btn.text-button {
  margin: 0;
}
.arco-btn.text-button {
  min-width: 16px;
  min-height: 22px;
  width: 16px;
  height: 22px;
  padding: 0;
  + .text-button {
    margin-left: 10px;
  }
  &.arco-btn-size-small:not(.arco-btn-only-icon) .arco-btn-icon {
    margin-right: 0;
  }
  &.arco-btn-loading::before {
    background: transparent;
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  white-space: nowrap;
  width: 100%;
}
div.arco-form-item-message,
.text-tips {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.text-tips {
  color: #999999;
}
.skill-popover-content {
  padding: 4px 12px 8px 12px;
  color: #333333;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  max-width: 200px;
  .arco-form-item {
    margin: 0;
  }
  .skill-text-btn {
    min-width: 60px;
    color: #333333 !important;
    height: auto;
    padding: 0 12px 4px 12px;
    font-size: 14px;
    &:hover {
      background-color: #f2f3f5 !important;
    }
  }
}

.skill-svg-hover_primary:hover {
  svg {
    color: #266EFF !important;
  }
}
.field-editor {
  &--p {
    line-height: 24px;
    color: #222;

    field:first-child {
      margin-left: 0;
    }

    img {
      max-width: unset;
      display: inline-block;
    }
  }

  &--field {
    border-radius: 2px;
    line-height: 20px;
    color: #333;
    background: #eaeaea;
    background-clip: padding-box;
    font-weight: 400;
    font-size: 12px;
    padding: 0 2px 0 8px;
    display: inline-block;
    user-select: none;
    height: 20px;

    &.ProseMirror-selectednode {
      border-color: rgba(76, 153, 254, 0.4);
    }
  }

  &_close {
    display: inline-block;
    width: 16px;
    line-height: 20px;
    font-size: 16px;
    margin-left: 2px;
    color: #999999;
    cursor: pointer;
    float: right;

    &:hover {
      color: @color-primary;
    }
  }
}