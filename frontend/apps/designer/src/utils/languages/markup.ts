// markup 类标记语言（markup、svg、mathml、ssml、rss、atom）语法高亮插件
const markup = () => {
  return {
    name: 'markup',
    contains: [
      {
        className: 'tag',
        begin: /<\s*\/?([a-zA-Z0-9-]+)\s*[^>]*>/, // 匹配 RSS 标签
        end: /\/?>/,
        subLanguage: 'xml',
      },
      {
        className: 'attribute',
        begin: /\s+[a-zA-Z-]+="[^"]*"/, // 匹配属性
        relevance: 0,
      },
      {
        className: 'value',
        begin: /="[^"]*"/, // 匹配属性值
        relevance: 0,
      },
    ],
  }
}

export default markup
