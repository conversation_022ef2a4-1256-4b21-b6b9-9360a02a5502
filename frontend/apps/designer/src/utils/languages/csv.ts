// csv 语法高亮插件
const csv = () => {
  return {
    name: 'csv',
    contains: [
      // 处理字符串（用双引号包裹）
      {
        className: 'string',
        begin: '"',
        end: '"',
        relevance: 0,
      },
      // 处理逗号分隔符
      {
        className: 'separator',
        begin: ',',
        end: ',',
        relevance: 0,
      },
      // 处理数字
      {
        className: 'number',
        begin: '\\b\\d+\\b',
        relevance: 0,
      },
      // 处理基本的字母（作为普通文本）
      {
        className: 'literal',
        begin: '[a-zA-Z]+',
        relevance: 0,
      },
    ],
  }
}

export default csv
