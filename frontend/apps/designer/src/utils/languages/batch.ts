// batch 语法高亮插件
const batch = () => {
  return {
    name: 'batch',
    // 默认规则 - 高亮命令和变量
    contains: [
      // 匹配关键字（命令）
      {
        className: 'keyword',
        begin:
          /\b(echo|set|if|for|goto|pause|rem|exit|call|cls|start|title|attrib|xcopy|del|copy|mkdir|rmdir|dir|cd|pushd|popd|type)\b/,
        relevance: 10,
      },
      // 匹配控制语句 (if, for, etc.)
      {
        className: 'control-flow',
        begin: /\b(if|for|goto|exit)\b/,
        relevance: 8,
      },
      // 匹配注释
      {
        className: 'comment',
        variants: [
          {
            begin: /::/,
            end: /$/,
          }, // :: 注释
          {
            begin: /rem/,
            end: /$/,
          }, // rem 注释
        ],
        relevance: 10,
      },
      // 匹配字符串
      {
        className: 'string',
        begin: /"[^"]*"/, // 双引号字符串
        end: /"/,
        relevance: 5,
      },
      // 匹配变量（%VAR%）
      {
        className: 'variable',
        begin: /%\w+%/,
        relevance: 5,
      },
      // 匹配文件路径
      {
        className: 'path',
        begin: /[A-Za-z]:[\/\\][^\n]*/,
        relevance: 2,
      },
      // 匹配数字
      {
        className: 'number',
        begin: /\b\d+\b/,
        relevance: 2,
      },
    ],
  }
}

export default batch
