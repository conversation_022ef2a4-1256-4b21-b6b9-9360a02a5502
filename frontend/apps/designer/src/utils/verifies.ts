import { CARD_TYPE } from '../stores/constants'
import { OutputParamsType } from '../ts/editor'

export const verifyParams = (nodes: any) => {
  const message: { id: string; message: string }[] = []
  nodes.forEach((node: any) => {
    let params = []
    if (
      node.data?.outputType === 'variables' ||
      node.data?.pattern === 1 ||
      node.data?.type === CARD_TYPE.INTERACTIVE
    ) {
      params = node.data?.outputs || []
    }
    if (node.data?.type === CARD_TYPE.TEXT_TEMPLATE) {
      params = node.data?.inputs || []
    }
    if (node.data?.type === CARD_TYPE.FINISH) {
      params = node.data?.outputs || []
    }
    if (params?.length) {
      const fields: string[] = []
      params.some((item: { code: string, name: string }) => {
        // 开始节点中 code必填校验
        if (node.data?.type === CARD_TYPE.START && (!item.code || !item.name)) {
          message.push({
            id: node.id,
            message: `请完善【${node.data?.name}】节点参数！`
          })
          return true
        }
        // code不重复校验
        if (fields.includes(item.code)) {
          message.push({
            id: node.id,
            message: `【${node.data?.name}】节点的参数字段存在重复！`,
          })
          return true
        } else {
          if (item.code) fields.push(item.code)
        }
      })
    }
  })
  return message
}

export const clearParamValue = (nodes: any) => {
  let params: Record<string, string> = {}
  nodes.forEach((node: CardNode) => {
    if (node.data?.outputs?.length) {
      node.data.outputs.forEach((val: OutputParamsType) => {
        const key = `NodeOutput_${node.id}_${val.code}`
        const value = val.value
        if (value && value.type === 'ref' && value.content?.startsWith('NodeOutput_')) {
          // 在上面的节点不存在了则清空
          if (!params[value.content]) val.value = { type: 'literal', content: '' }
        }
        if (val.code) params[key] = val.code
        if (val?.schema?.length) {
          val.schema.forEach((subVal: OutputParamsType) => {
            const subKey = key + '_' + subVal.code
            const subValue = subVal.value
            if (
              subValue &&
              subValue.type === 'ref' &&
              subValue.content &&
              !subValue.content?.startsWith('System_')
            ) {
              if (!value?.content || !params[value.content + '_' + subValue.content])
                subVal.value = { type: 'literal', content: '' }
            }
            if (subVal.code) params[subKey] = subVal.code
          })
        }
      })
    }
    if (node.data?.inputs?.length) {
      node.data.inputs.forEach((val: { value: { type: string; content: string } }) => {
        const value = val.value
        if (value && value.type === 'ref' && value.content?.startsWith('NodeOutput_')) {
          if (!params[value.content]) val.value = { type: 'literal', content: '' }
        }
      })
    }
    const files = (node.data as CardDocumentData)?.files
    if (files && files.type === 'ref' && files.content?.startsWith('NodeOutput_')) {
      if (!params[files.content]) {
        files.type = 'literal'
        files.content = ''
      }
    }
    const conditions = (node.data as CardSelectorData)?.conditions
    if (conditions?.length) {
      conditions.forEach((val) => {
        if (val.expressions?.length) {
          val.expressions.forEach((expression: ConditionExpression) => {
            if (expression?.rules?.length) {
              expression.rules.forEach((rule) => {
                const { left, right } = rule
                const leftValue = left?.value || {}
                if (
                  leftValue?.type === 'ref' &&
                  typeof leftValue?.content === 'string' &&
                  leftValue?.content?.startsWith('NodeOutput_')
                ) {
                  if (!params[leftValue.content]) {
                    left.value = { type: 'literal', content: '' }
                  }
                }
                const rightValue = right?.value || {}
                if (
                  rightValue.type === 'ref' &&
                  typeof rightValue?.content === 'string' &&
                  rightValue?.content?.startsWith('NodeOutput_')
                ) {
                  if (!params[rightValue.content]) {
                    left.value = { type: 'literal', content: '' }
                  }
                }
              })
            }
          })
        }
      })
    }
    // 消息卡片、交互卡片引用节点被删除后移除对应的输入参数
    if (node.data.type === CARD_TYPE.INTERACTIVE || node.data.type === CARD_TYPE.MESSAGE) {
      node.data.inputs =
        node.data.inputs?.filter(({ value }: InputParamValue) => value?.content) || []
      if ('content' in node.data && node.data.content) {
        node.data.content = clearMessageContent(node.data.content, node.data.inputs)
      }
      if ('title' in node.data && node.data.title) {
        node.data.title = clearMessageContent(node.data.title, node.data.inputs)
      }
    }
  })
  params = {}
}

const clearMessageContent = (c: string, inputs: InputParamValue[]) => {
  let content = c
  const fieldsMap: Record<string, boolean> = {}
  inputs.forEach(({ code }) => {
    fieldsMap[code] = true
  })

  const regex = /{{\$([^{}]+)}}/g // 正则表达式匹配 {entity.field} 形式的文本

  let match

  while ((match = regex.exec(content)) !== null) {
    const [, fieldText] = match
    const item = fieldsMap[fieldText]
    if (!item) {
      content = content.replaceAll(`{{$${fieldText}}}`, '')
    }
  }

  return content
}

export const verifyAgentParams = (data: AgentData) => {
  if (data.inputs.length) {
    if (data.inputs.some((item) => !item.code || !item.name)) {
      return '请完善输入参数'
    }
  }
  return ''
}
