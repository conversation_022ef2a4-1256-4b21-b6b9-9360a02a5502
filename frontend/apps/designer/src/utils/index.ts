interface Params {
  [key: string]: string
}
export function getParams() {
  var params: Params = {}
  var pairs = window.location.href.split('?')
  if (pairs.length > 1) {
    pairs = pairs[1].split('&')

    pairs.forEach((pair) => {
      var [key, value] = pair.split('=')
      params[decodeURIComponent(key)] = decodeURIComponent(value || '')
    })
  }
  return params
}

// 中文数字字符
const CHINESE_NUMBERS = '零一二三四五六七八九'
// 中文单位字符（万、亿等，这里只实现到万）
const CHINESE_UNITS = ['', '十', '百', '千', '万']
export const numberToChineseCapital = (num: number) => {
  if (num === 0) {
    return CHINESE_NUMBERS[0] // 如果数字是0，直接返回“零”
  }

  let result = ''
  let zeroFlag = false // 标记是否连续出现零

  // 逐个处理数字的每一位（从低位到高位）
  for (let i = 0; num > 0; i++) {
    const digit = num % 10 // 获取当前位的数字
    num = Math.floor(num / 10) // 去掉已经处理的位

    if (digit === 0) {
      // 如果当前位是0
      if (!zeroFlag) {
        // 如果前面没有出现过0，则标记为出现过0，并在结果中添加“零”
        zeroFlag = true
        result = CHINESE_NUMBERS[0] + result
      }
      // 否则忽略连续的0
    } else {
      // 如果当前位不是0
      zeroFlag = false // 重置零标记
      // 添加当前位的中文数字和对应的单位（如果有的话）
      result = CHINESE_NUMBERS[digit] + (CHINESE_UNITS[i] || '') + result

      // 处理特殊情况：“十”后面跟“零”要省略“零”
      if (
        i !== 0 &&
        CHINESE_UNITS[i] === '十' &&
        result.startsWith(CHINESE_NUMBERS[0])
      ) {
        result = result.slice(1) // 去掉开头的“零”
      }
    }
  }

  // 处理特殊情况：“一十”替换为“十”
  if (result.startsWith(CHINESE_NUMBERS[1] + CHINESE_UNITS[1])) {
    result = result.slice(1)
  }

  if (num % 10 === 0 && result.endsWith(CHINESE_NUMBERS[0])) {
    const index = result.lastIndexOf(CHINESE_NUMBERS[0])
    result = result.slice(0, index)
  }

  return result
}

/**
 * 获取步骤
 * @param num
 * @returns
 */
export const getStepName = (num: number) => {
  if (num < 0) return ''
  return `步骤${numberToChineseCapital(num + 1)}：`
}

export const getSaveTime = () => {
  const [, time] = new Date().toLocaleString().split(' ')
  return time
}

export const Guid = () => {
  var CHARS =
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  var chars = CHARS,
    uuid = [],
    i
  // rfc4122, version 4 form
  var r
  // rfc4122 requires these characters
  uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
  uuid[14] = '4'
  for (i = 0; i < 36; i++) {
    if (!uuid[i]) {
      r = 0 | (Math.random() * 16)
      uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
    }
  }

  var ret = uuid.join('')
  return ret
}

export function generateCode(length = 10) {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

export function validateURL(url: string): boolean {
  var pattern = new RegExp(
    '^(https?://)?' + // 协议，可选
      '(([0-9]{1,3}\\.){3}[0-9]{1,3}|' + // IPv4地址
      '([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})' + // 域名
      '(:[0-9]{1,5})?' + // 端口号，可选
      '(/[-a-zA-Z0-9()@:%_+.~#?&//=]*)?' + // 路径，可选
      '(\\?[;&a-zA-Z0-9()@:%_+.~#?&//=]*)?' + // 查询字符串，可选
      '(#[-a-zA-Z0-9_]*)?$',
    'i',
  ) // 锚点，可选

  return pattern.test(url)
}
