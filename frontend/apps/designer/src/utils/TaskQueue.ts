import { Guid } from './index'

export class Task {
  public id: string = ''
  private params: any = []

  private func: (...args: any[]) => Promise<any> = () => {
    return new Promise((resolve) => {
      resolve(null)
    })
  }

  constructor(func: (...args: any[]) => Promise<any>, ...params: any[]) {
    this.id = Guid()
    this.func = func
    this.params = params
  }

  async run() {
    try {
      return await this.func.apply(this, Object.values(this.params))
    } catch (error) {
      console.error(`Task ${this.id} failed with error:`, error)
      throw error // 根据实际情况决定是否需要重新抛出异常
    }
  }
}

interface TaskQueueOptions {
  limit: number
}

type TaskQueueEventMap = {
  taskRun: (task: Task, running: number, queue: number) => any
  taskDone: (task: Task, running: number, queue: number) => any
}

export class TaskQueue {
  private queue: Task[] = []
  private runningTasks: Task[] = [] // 优化3: 使用Task对象代替ID
  private limit: number = 5
  private status: 'pause' | 'running' | 'ready' = 'ready'

  private events: {
    [K in keyof TaskQueueEventMap]: TaskQueueEventMap[K][]
  } = {
    taskRun: [],
    taskDone: [],
  }

  private next() {
    while (
      this.status === 'running' &&
      this.queue.length > 0 &&
      this.runningTasks.length < this.limit
    ) {
      const task = this.queue.shift()
      if (task) {
        this.runningTasks.push(task)
        this.emit('taskRun', task, this.runningTasks.length, this.queue.length)
        task.run().then(() => {
          this.runningTasks = this.runningTasks.filter((t) => t.id !== task.id)
          this.emit(
            'taskDone',
            task,
            this.runningTasks.length,
            this.queue.length,
          )
          this.next()
        })
      }
    }
  }

  private emit<E extends keyof TaskQueueEventMap>(
    event: E,
    task: Task,
    running: number,
    queue: number,
  ) {
    this.events[event].forEach((callback) => {
      callback(task, running, queue)
    })
  }

  public constructor(options: TaskQueueOptions) {
    this.limit = options.limit
    this.status = 'running'
  }

  public addTask(func: (...args: any[]) => Promise<any>, ...params: any[]) {
    this.queue.push(new Task(func, ...params))
    if (this.status === 'running') {
      this.next()
    }
  }

  public start() {
    if (this.status === 'running') {
      console.warn('当前TaskQueue已经运行，请勿重复运行')
      return
    }
    this.status = 'running'
    this.next()
  }

  public pause() {
    if (this.status === 'pause') {
      console.warn('当前TaskQueue已经暂停，请勿重复暂停')
      return
    }
    this.status = 'pause'
  }

  public isQueueFull() {
    return this.runningTasks.length >= this.limit
  }

  public on<E extends keyof TaskQueueEventMap>(
    event: E,
    callback: TaskQueueEventMap[E],
  ) {
    this.events[event].push(callback)
  }

  public off<E extends keyof TaskQueueEventMap>(
    event: E,
    callback: TaskQueueEventMap[E],
  ) {
    this.events[event] = this.events[event].filter((c) => c !== callback)
  }
}
