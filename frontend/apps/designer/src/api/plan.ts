import axios from 'axios'
import { spaceGUID } from './index'

export const onQueryPlan = (query: string) => {
  return axios.request({
    url: '/api/42001401/plan/searchPlan',
    method: 'POST',
    data: {
      planName: query,
      planCode: query,
      spaceGUID,
    },
  })
}

export const onQueryPlanGroup = (id: string) => {
  return axios.request({
    url: `/api/42001401/plan/findPlanRuleGroup?planGUID=${id}`,
    method: 'POST',
  })
}

export const findPlanParams = (id: string) => {
  return axios.request({
    url: `/api/42001401/plan/findPlanParams?planGUID=${id}`,
    method: 'POST',
  })
}
