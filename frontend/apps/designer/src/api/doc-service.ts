import { DocServiceV2 } from '@ipaas/doc-service'
import axios from 'axios'

// 缓存对象
const docFileInfoCache: Record<string, { data: any; timestamp: number }> = {}
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟，单位毫秒

export const getDocumentFileInfo = async (documentGuid: string): Promise<any> => {
  let response: any = {}
  const now = Date.now()
  const cache = docFileInfoCache[documentGuid]

  if (cache && now - cache.timestamp < CACHE_DURATION) {
    response = cache.data
  } else {
    response = await axios.request({
      url: '/api/42000101/assistant/getDocumentInfo',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  const { data } = response.data || {}
  const { url, token } = data
  const MyDocServiceV2 = new DocServiceV2({
    url: url, //文档服务地址
    token: token,
  })
  return {
    previewUrl: MyDocServiceV2.getViewUrl({
      documentGuid,
      ratio: 1.5,
    }),
    downloadUrl: MyDocServiceV2.getDownloadUrl({
      documentGuid,
    }),
    id: documentGuid,
  }
}
