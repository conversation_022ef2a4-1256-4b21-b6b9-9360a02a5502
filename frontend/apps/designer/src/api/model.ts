import axios from 'axios'

type Model = {
  modelInstanceGUID: string
  modelInstanceCode: string
  modelInstanceName: string
  enableCustomModel: 0
  customModelCode: ''
  supportDeepThink: 1 | 0
  executionSetting: string
}

export const getModels = async () => {
  const res = await axios.request({
    url: '/api/42000501/ModelInstance/reasoningList',
    method: 'POST',
  })
  return res.data.data as Model[]
}
