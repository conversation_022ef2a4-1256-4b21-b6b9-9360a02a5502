import axios from 'axios'
export const onGetAllMyApplications = () => {
  return axios.request({
    url: '/api/42000301/skill/GetAllMyApplications',
    method: 'POST',
  })
}

export const GetFunctionControlsByAppCode = ({
  appCode,
  keyword,
}: {
  appCode: string
  keyword: string
}) => {
  return axios.request({
    url: '/api/42000301/skill/GetFunctionControlsByAppCode',
    method: 'POST',
    data: {
      appCode,
      keyword,
    },
  })
}

export const GetFunctionControlFieldsById = ({ id }: { id: string }) => {
  return axios.request({
    url: `/api/42000301/skill/GetFunctionControlFieldsById?id=${id}`,
    method: 'POST',
  })
}
