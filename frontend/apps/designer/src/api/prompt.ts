import { fetchAsStream } from '@acme/core'
import { Message } from '@arco-design/web-vue'
import axios from 'axios'

export const getPromptTestScenes = (id: string) => {
  return axios.request({
    url: `/api/42001201/prompt/getPromptTestScenes?promptGUID=${id}`,
    method: 'POST',
  })
}

// biome-ignore lint/suspicious/noExplicitAny: off
export const saveData = (data: any) => {
  return axios
    .request({
      url: '/api/42001201/prompt/savePromptTestScenes',
      method: 'POST',
      data,
    })
    .then((data) => {
      Message.success('保存成功')
      return data
    })
}

export const runTest = (id: string, gptEngineUrl: string, headers: Record<string, string>) => {
  return fetchAsStream(`${gptEngineUrl}/Prompt/TestPromptScene?promptTestSceneGUID=${id}`, {
    method: 'POST',
    headers,
  })
}

export const getToken = async (fetchData: { [key: string]: string }) => {
  try {
    // biome-ignore lint/suspicious/noExplicitAny: off
    const response: any = await axios.request({
      url: '/api/42001301/token/getToken',
      method: 'POST',
      data: fetchData,
      headers: {
        'Content-Type': 'application/json',
      },
    })
    if (response.status !== 200) {
      throw new Error(`数据请求异常: ${response.status}`)
    }
    const data = response.data?.data || {}
    return data?.accessToken || ''
  } catch (error) {
    console.error('数据请求异常：', error)
  }
}
