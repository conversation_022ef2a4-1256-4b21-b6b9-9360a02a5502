import axios from 'axios'
import { spaceGUID } from './index'

export const getPluginList = () => {
  return axios.request({
    url: '/api/42000801/pluginManager/pluginList',
    method: 'POST',
    data: {
      spaceGUID,
    },
  })
}

export const getPluginServiceList = (id: string) => {
  return axios.request({
    url: `/api/42000801/pluginManager/pluginServiceList?pluginGUID=${id}`,
    method: 'POST',
  })
}
