import axios from 'axios'
import { mockData } from '../stores/constants'
import { getParams } from '../utils'

export const spaceGUID = getParams()?.space || ''
export const getData = () => {
  const { oid, _debug } = getParams()
  if (!oid && _debug === 'true') {
    return { data: { data: mockData } }
  }
  if (!oid) {
    return { data: { data: {} } }
  }
  return axios.request({
    url: `/api/42000301/skill/detail?skillGUID=${oid}`,
    method: 'POST',
  })
}

// biome-ignore lint/suspicious/noExplicitAny: off
export const saveData = (data: any) => {
  return axios.request({
    url: '/api/42000301/skill/saveAndUpdateNode',
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
    },
    data: data,
  })
}

export const getSystemParam = () => {
  return axios.request({
    url: '/api/42000301/skill/systemParam',
    method: 'POST',
  })
}

// biome-ignore lint/suspicious/noExplicitAny: off
export const onMapping = (data: any) => {
  return axios.request({
    url: '/api/42000301/skill/paramMapping',
    method: 'POST',
    data,
  })
}
