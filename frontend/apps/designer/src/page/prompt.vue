<script setup lang="ts">
import { useSortable } from '@vueuse/integrations/useSortable'
import { computed, ref } from 'vue'
import ConfirmDialog from '../components/confirm-dialog.vue'
import PromptForm from '../components/prompt/prompt-form.vue'
import TemplateEditor from '../components/subControls/template-editor.vue'
import { usePromptStore } from '../stores/prompt'
import { MD_CLASS, renderMarkdown } from '../utils/markdown'

const promptStore = usePromptStore()
promptStore.getData()

const visible = ref(false)
const messageKey = ref(0)
const listData: any = computed(() => promptStore.promptData)
const promptTemplate: Record<string, any> = computed(
  () => promptStore.promptTemplate,
)
const params = computed(() => promptStore.params)
const inputs = computed(() => {
  const data: PromptTestScenes[] = []
  params.value.forEach((item: PromptParams) => {
    data.push({
      code: item.paramCode,
      mapValue: `{{$${item.paramCode}}}`,
      name: item.paramName,
      value: item.paramCode,
      title: item.paramName,
      field: item.paramCode,
      label: `${item.paramName}(${item.paramCode})`,
    })
  })
  return data
})
const editorParams = computed(() => {
  return inputs.value
})

const hasMessage = computed(() => {
  return promptStore.messageContent?.length > 0
})

const onChange = (v: string) => {
  promptStore.updateTemplate(v)
}

const handleAdd = () => {
  promptStore.addTest()
}
let delIndex = ref(-1)
const msgType = ref('')
const handleOpen = (index: number) => {
  msgType.value = 'example'
  delIndex.value = index
  visible.value = true
}
const handleDel = () => {
  switch (msgType.value) {
    case 'example':
      promptStore.onDel(delIndex.value)
      break
    case 'message':
      promptStore.removeMessage(delIndex.value)
      messageKey.value = new Date().getTime()
      break
  }
}
const getTitle = (role: string, index: number) => {
  let text = ''
  let _i = 0
  let currentI = 0
  promptStore.messageContent.some(function (item, k) {
    if (item.role === 'user') {
      _i += 1
    }
    if (k + 1 === index) {
      currentI = _i
      return true
    }
    return false
  })
  if (role === 'user') {
    text = '用户提问_' + currentI
  }
  if (role === 'assistant') {
    text = currentI < 1 ? 'AI回复' : 'AI回复_' + currentI
  }
  return text + ' #' + role
}

const handleDelMsg = (key: number) => {
  msgType.value = 'message'
  delIndex.value = key
  visible.value = true
}

const msgTitle = computed(() => {
  switch (msgType.value) {
    case 'example':
      return `请确认是否删除#场景${delIndex.value + 1}`
    case 'message':
      return `请确认是否删除当前样本数据`
  }
  return ''
})

const allowDel = computed(() => promptStore.messageContent.length > 1)

const refSelector = ref(null)
const dragging = ref(false)
const { option } = useSortable(refSelector, promptStore.messageContent, {
  handle: '.handle',
  onUpdate: (e: { oldIndex: number; newIndex: number }) => {
    const oldIndex = e.oldIndex
    const newIndex = e.newIndex
    const data = JSON.parse(JSON.stringify(promptStore.messageContent))
    const item = data[oldIndex]
    data.splice(oldIndex, 1)
    data.splice(newIndex, 0, item)
    promptStore.messageContent = data
    messageKey.value += 1
  },
  onMove: (e: any) => {
    if (e.related.className.endsWith('disabled')) {
      return false
    }
    return true
  },
  onStart: () => {
    dragging.value = true
  },
  onEnd: () => {
    dragging.value = false
  },
})
option('animation', 200)
</script>

<template>
  <div class="prompt-designer h-[100%] w-[100%]">
    <div
      class="float-left box-border h-[100%] w-[400px] overflow-y-auto
        [border-right:1px_solid_#EAEAEA]"
    >
      <div
        class="px-[20px] py-[13px] text-sm font-semibold not-italic leading-[22px]
          text-[#333333]"
      >
        {{ promptStore.title }}
      </div>
      <div
        class="prompt-editor box-border h-[calc(100%_-_48px)] max-h-[calc(100%_-_48px)]
          p-[20px]"
        :class="{ 'message-content': hasMessage }"
      >
        <TemplateEditor
          :key="promptStore.id"
          :value="promptTemplate"
          :inputs="inputs"
          :isCustom="true"
          :customParams="editorParams"
          :max-height="hasMessage ? '400px' : '100%'"
          :title="hasMessage ? '系统提示词 #system' : ''"
          :noLevel="true"
          stepName="参数"
          @input="onChange"
        />
      </div>
      <div ref="refSelector" class="display: inline-block;">
        <div
          class="prompt-editor_message pl-[20px] pr-[20px]"
          :class="{ dragging }"
          v-if="hasMessage"
          v-for="(item, key) in promptStore.messageContent"
          :key="messageKey + 'message_' + key"
        >
          <TemplateEditor
            :value="item.content"
            :inputs="inputs"
            :isCustom="true"
            :customParams="editorParams"
            :title="getTitle(item.role, key + 1)"
            :allowDel="allowDel"
            minHeight="80px"
            max-height="200px"
            :noLevel="true"
            stepName="参数"
            @input="(v: string) => promptStore.updateMessageContent(key, v)"
            @del="handleDelMsg(key)"
          />
          <div class="handle cursor-all-scroll">
            <svg-icon name="drop" size="16px" />
          </div>
        </div>
      </div>
      <a-popover
        position="bottom"
        :arrow-style="{ display: 'none' }"
        :content-style="{ padding: '2px 12px', marginTop: '-20px' }"
        popup-container=".designer-app"
      >
        <span class="ml-[20px] cursor-pointer pb-[20px]" v-if="hasMessage">
          <svg-icon name="add" size="16px" style="margin-right: 4px"></svg-icon
          >添加对话
        </span>
        <template #content>
          <div class="sample-item" @click="promptStore.addMessage('user')">
            用户提问
          </div>
          <div class="sample-item" @click="promptStore.addMessage('assistant')">
            AI回复
          </div>
        </template>
      </a-popover>
    </div>
    <div class="float-left h-[100%] w-[calc(100%_-_400px)] bg-[#f3f4f9]">
      <div class="h-[48px] bg-[#FFF] px-[20px] py-[8px]">
        <div
          class="float-left text-sm font-semibold not-italic leading-[32px] text-[#333333]"
        >
          测试场景
        </div>
        <div class="float-right flex flex-row items-center">
          <div class="flex flex-row items-center">
            <a-switch size="small" v-model="promptStore.enableParam" />
            <span class="ml-[8px] text-[13px] text-[#333333]">输入参数</span>
          </div>
          <a-button class="ml-[24px]" @click="handleAdd">
            添加测试场景
          </a-button>
          <a-button
            type="primary"
            class="ml-[12px]"
            @click="() => promptStore.runAll()"
            :loading="promptStore.promptData.some((c) => c.loading)"
          >
            执行测试
          </a-button>
        </div>
      </div>
      <div
        class="box-border flex h-[calc(100%_-_48px)] w-full flex-col overflow-x-auto
          overflow-y-auto p-[20px]"
      >
        <div class="prompt-grid">
          <div
            class="prompt-item_tool"
            v-for="(item, index) in listData"
            :key="'test_' + index"
          >
            <div
              class="float-left text-sm font-semibold not-italic leading-[22px] text-[#333333]"
            >
              #场景{{ index + 1 }}
            </div>
            <div class="float-right text-[#266EFF]">
              <a-button-group>
                <a-button
                  type="text"
                  size="small"
                  class="text-button"
                  :loading="
                    item.status === 'running' || item.status === 'waiting'
                  "
                  @click="() => promptStore.runItem(item.promptTestSceneGUID)"
                >
                  <svg-icon
                    v-if="
                      !(item.status === 'running' || item.status === 'waiting')
                    "
                    name="implement"
                    size="16px"
                  />
                </a-button>
                <a-button
                  type="text"
                  size="small"
                  class="text-button"
                  v-if="!item.loading"
                  :loading="item.loading"
                  @click="handleOpen(index)"
                >
                  <svg-icon name="trash" color="#266EFF" size="16px" />
                </a-button>
              </a-button-group>
            </div>
          </div>
        </div>
        <div class="prompt-grid" v-if="promptStore.enableParam">
          <div
            class="prompt-item_result"
            v-for="(item, index) in listData"
            style="min-height: auto"
            :key="'form_' + index"
          >
            <div class="prompt-item_title">输入参数</div>
            <div class="prompt-item_form">
              <PromptForm
                :key="item.promptTestSceneGUID"
                :data="item.promptParams"
                :config="params"
              />
            </div>
          </div>
        </div>
        <div class="prompt-grid">
          <div
            class="prompt-item_result"
            v-for="(item, index) in listData"
            style="min-height: auto"
            :key="'res_' + index"
          >
            <div class="prompt-item_title field-form">
              <strong class="arco-form-item-label-required-symbol">
                <svg
                  fill="currentColor"
                  viewBox="0 0 1024 1024"
                  width="1em"
                  height="1em"
                >
                  <path
                    d="M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"
                  ></path>
                </svg>
              </strong>
              预期结果
            </div>
            <div class="prompt-item_content no-padding">
              <a-textarea
                placeholder="请输入预期结果"
                v-model="item.expectContent"
                auto-size
              />
            </div>
          </div>
        </div>
        <div class="prompt-grid">
          <div
            class="prompt-item_result"
            v-for="(item, index) in listData"
            style="min-height: 180px"
            :key="'output_' + index"
          >
            <div class="prompt-item_title">输出结果</div>
            <div
              class="prompt-item_content"
              :class="MD_CLASS"
              v-html="renderMarkdown(item.outputContent)"
            ></div>
          </div>
        </div>
        <div class="prompt-grid">
          <div
            class="prompt-item_result relative"
            style="min-height: 180px"
            v-for="(item, index) in listData"
            :key="'result_' + index"
          >
            <div class="prompt-item_title">评估结果</div>
            <div
              class="absolute right-[20px] top-[0] float-right mt-[24px] inline-flex w-1/2 flex-row
                items-center justify-items-center text-[13px] font-normal not-italic
                leading-[22px] text-[#666666]"
            >
              <span class="absolute -top-[6px] right-[128px]">分值</span>
              <a-progress
                class="absolute right-[0] top-[0]"
                :percent="item.estimateRecord / (item.estimateRecordMax || 5)"
              >
                <template v-slot:text>
                  {{ item.estimateRecord }}
                </template>
              </a-progress>
            </div>
            <div
              class="prompt-item_content"
              :class="MD_CLASS"
              v-html="renderMarkdown(item.estimateContent)"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <confirm-dialog
    :show="visible"
    :content="msgTitle"
    @onOk="handleDel"
    @input="visible = $event"
  ></confirm-dialog>
</template>

<style lang="less">
.prompt-designer {
  .pd-input-textarea,
  .prompt-editor > div {
    height: 100%;
  }
  .prompt-editor {
    .ProseMirror {
      max-height: 100%;
    }
    &.message-content {
      height: auto;
      padding-bottom: 0;
      .ProseMirror {
        max-height: 400px;
      }
    }
  }
  .prompt-editor_message {
    position: relative;
    .handle {
      position: absolute;
      top: 5px;
      left: 28px;
    }
    .pd-input-textarea__header {
      padding-left: 32px;
    }
    .ProseMirror {
      max-height: 200px;
    }
    &.dragging .pd-input-textarea {
      pointer-events: none;
    }
  }
  .pd-input-textarea__body {
    height: calc(100% - 34px);
  }
  .arco-progress-line-text {
    margin-left: 0;
  }
  div.arco-progress {
    width: 120px;
    right: 0;
    position: absolute;
  }
  .gpt-code-generation {
    display: none;
  }
}
.prompt-grid {
  width: 100%;
  display: flex;
  > div {
    max-width: 380px;
    min-width: 380px;
    word-wrap: break-word;
    float: left;
  }
}
.prompt-item_result,
.prompt-item_tool {
  margin-right: 20px;
}
.prompt-item_result + .prompt-item_result,
.prompt-item_tool + .prompt-item_tool {
  margin-left: 20px;
  position: relative;
  &:before {
    content: '';
    display: inline-block;
    position: absolute;
    left: -20px;
    border-left: 1px solid #eaeaea;
    height: calc(100% + 12px);
    top: -12px;
  }
}

.prompt-item_tool {
  display: inline-block;
  width: 100%;
  padding: 7px 12px;
  border-radius: 4px;
  background: linear-gradient(90deg, #dee7ff 0%, #e4e2ff 100%);
  &:before {
    top: 0 !important;
  }
}
.prompt-item_result {
  padding: 12px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 0 3px 0 #0000000a;
  min-height: 300px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 12px;
}
.prompt-item_title {
  height: 32px;
  padding: 5px 0;
  color: #333333;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
}
.prompt-item_form,
.prompt-item_content {
  padding: 5px 12px;
  box-sizing: border-box;
  border-radius: 4px;
}
.prompt-item_content {
  background: #f5f5f5;
  color: #333333;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  width: 100%;
  height: 236px;
  min-height: 20px;
  border: 1px solid #ddd;
  height: calc(100% - 32px);
  &.no-padding {
    padding: 0;
  }
  .arco-textarea-wrapper {
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
  > pre {
    white-space: break-spaces;
  }
}
.sample-item {
  line-height: 30px;
  color: #333;
  font-size: 13px;
  cursor: pointer;
  &:hover {
    color: #266eff;
  }
}
</style>
