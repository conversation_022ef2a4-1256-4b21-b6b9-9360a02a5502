<script setup lang="ts">
import tool from '../agent/tool.vue'
import tipsIcon from '../components/tips-icon.vue'
import Prompt from '../agent/prompt.vue'
import { useFlowStore, skipConfig } from '../stores/flow'
import { onMounted, ref, watch } from 'vue'
const flowStore = useFlowStore()
const isChange = ref(false)
onMounted(() => {
  // 监听 store 中数据的变化
  flowStore.$subscribe(() => {
    isChange.value = true
  })
})
watch(
  () => skipConfig.value,
  (v) => {
    if (v) {
      isChange.value = false
      skipConfig.value = false
    }
  },
)
</script>

<template>
  <div class="agent-content flex h-full flex-row overflow-hidden bg-[#FCFCFF]">
    <div class="flex h-full w-2/3 flex-col">
      <div class="height: 52px; px-[20px] py-[14px] [border-bottom:1px_solid_#EAEAEA]">
        <span class="text-base font-semibold not-italic leading-[24px] text-[#333333]">编排</span>
      </div>
      <div class="flex h-[calc(100%_-_53px)]">
        <div class="w-1/2 [border-right:1px_solid_#EAEAEA]">
          <prompt placeholder="请输入提示词" :inputs="[]"></prompt>
        </div>
        <div class="h-full w-1/2 overflow-y-auto">
          <tool></tool>
        </div>
      </div>
    </div>
    <div class="w-1/3 [border-left:1px_solid_#EAEAEA]">
      <div
        class="height: 52px; flex justify-between bg-[#fff] px-[20px] py-[14px] [border-bottom:1px_solid_#EAEAEA]"
      >
        <span class="text-base font-semibold not-italic leading-[24px] text-[#333333]"
          >预览与调试</span
        >
        <tips-icon v-if="isChange" message="技能已修改，请先保存再预览" type="primary" />
      </div>
      <div class="relative z-0 h-[calc(100%_-_53px)] overflow-hidden" id="agentPreview"></div>
    </div>
  </div>
</template>
