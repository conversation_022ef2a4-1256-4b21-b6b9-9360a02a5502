<script setup lang="ts">
defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  data: {
    type: Object,
    default: () => {},
  },
})
</script>

<template>
  <a-form label-align="left" class="field-form prompt-form" :model="data">
    <template v-for="item in config">
      <a-form-item
        :label="item.paramName"
        :field="item.paramCode"
        :rules="
          !!item.isRequired ? [{ required: true, message: '该参数为必填' }] : []
        "
        label-col-flex="12 12"
        :style="{ width: '100%' }"
      >
        <a-input-number
          v-if="item.fieldType === 'number'"
          v-model="data[item.paramCode]"
        />
        <a-date-picker
          v-else-if="item.fieldType === 'date'"
          v-model="data[item.paramCode]"
          format="YYYY-MM-DD"
        />
        <a-input v-else v-model="data[item.paramCode]" />
      </a-form-item>
    </template>
  </a-form>
</template>

<style lang="less">
.disabled-input-style {
  padding: 4px 8px 4px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: #f5f5f5;
  color: #666666;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  box-sizing: border-box;
  width: 100%;
  display: inline-block;
}
.prompt-form {
  .arco-picker {
    width: 100%;
  }
}
</style>
