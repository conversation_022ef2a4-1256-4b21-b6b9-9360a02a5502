<template>
  <svg aria-hidden="true" class="svg-icon inline-block" :style="style">
    <use :xlink:href="symbolId" fill="currentColor" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '#A0A5AC',
  },
  size: {
    type: String,
    default: '1em',
  },
  width: {
    type: String,
    default: '',
  },
  height: {
    type: String,
    default: '',
  },
})

const symbolId = computed(() => `#svg-${props.name}`)
const style = computed(() => {
  if (!props.color) return ''
  return {
    color: props.color,
    width: props.width || props.size,
    height: props.height || props.size,
  }
})
</script>
