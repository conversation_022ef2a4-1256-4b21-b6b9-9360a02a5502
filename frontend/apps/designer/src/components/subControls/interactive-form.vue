<script setup lang="ts">
import { FormCard, WidgetCard } from '@acme/card'
import { computed, ref, watch } from 'vue'
import { CARD_TEMPLATE_TYPE, INTERACTIVE_CONFIG } from '../../stores/configData'
import SelectTemplate from './select-template.vue'
import TemplateEditor from './template-editor.vue'
import CardOutputs from './card-outputs.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['update-config'])
const selectConfig = computed(() => {
  return INTERACTIVE_CONFIG.SELECT
})
const templateContent = computed(() => {
  if (isForm.value) return INTERACTIVE_CONFIG.TITLE
  return INTERACTIVE_CONFIG.TEMPLATE
})
const isTextForm = computed(() => {
  return props.formData.templateId === CARD_TEMPLATE_TYPE.TEXT
})
const isForm = computed(() => {
  return props.formData.templateId === CARD_TEMPLATE_TYPE.FORM
})
const variables = computed(() => {
  if (isForm.value) return FormCard.variables
  if (isTextForm.value) return []
  return WidgetCard.variables
})

const refreshKey = ref(0)

watch(
  () => variables.value,
  (v) => {
    emit('update-config', { type: 'variables', variables: v })
  },
  { immediate: true },
)
const onChange = () => {
  refreshKey.value += 1
  props.formData.inputs = []
  props.formData.outputs = []
  props.formData.content = ''
  props.formData.props = {}
  props.formData.title = ''
  props.formData.actionName = ''
}
</script>

<template>
  <select-template :form-data="formData" :item="selectConfig" @change="onChange" />

  <template v-if="isForm">
    <a-form-item class="form-item_title" :label="templateContent.label"></a-form-item>
    <TemplateEditor
      :key="'inputs_' + refreshKey"
      :value="formData[templateContent.prop]"
      :inputs="formData.inputs"
      @inputs="formData.inputs = $event"
      :minHeight="templateContent.minHeight"
      :placeholder="templateContent.placeholder"
      @input="formData[templateContent.prop] = $event"
    />
  </template>

  <template v-if="isTextForm">
    <a-form-item :label="templateContent.label" class="form-item_title"></a-form-item>
    <TemplateEditor
      :key="'inputs_' + refreshKey"
      :value="formData[templateContent.prop]"
      :inputs="formData.inputs"
      @inputs="formData.inputs = $event"
      placeholder="请输入模板内容"
      title=""
      @input="formData[templateContent.prop] = $event"
    />
    <CardOutputs :formData="formData" />
  </template>
</template>
