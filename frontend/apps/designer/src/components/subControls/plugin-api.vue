<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { onMapping } from '../../api'
import { useFlowStore } from '../../stores/flow'
import { usePluginStore } from '../../stores/plugin'
import FieldForm from '../subControls/field-form.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => {},
  },
  config: {
    type: Object,
    default: () => {},
  },
})
const pluginStore = usePluginStore()
pluginStore.getPluginList()
const flowStore = useFlowStore()
const emit = defineEmits(['input', 'toolId'])
let bodyParams = ref([])
let loading = ref(false)

// biome-ignore lint/suspicious/noExplicitAny: off
const pluginData: any = ref({})
// biome-ignore lint/suspicious/noExplicitAny: off
let inputKey: any = ref(new Date().getTime())
// biome-ignore lint/suspicious/noExplicitAny: off
const pluginOptions: any = computed(() => pluginStore.pluginData || [])
// biome-ignore lint/suspicious/noExplicitAny: off
const serviceOptions: any = computed(() => pluginStore.serviceData || [])
// biome-ignore lint/suspicious/noExplicitAny: off
const bodyParamsMap: any = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const map: any = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  bodyParams.value.forEach((item: any) => {
    map[item.value] = item
  })
  return map
})
const columns = computed(() => [
  { title: '名称', dataIndex: 'code' },
  { title: '类型', dataIndex: 'type' },
  { title: '描述', dataIndex: 'description' },
])

// biome-ignore lint/suspicious/noExplicitAny: off
const onPluginChange = (value: any) => {
  pluginData.value.pluginId = value
  pluginData.value.source =
    // biome-ignore lint/suspicious/noExplicitAny: off
    pluginOptions.value.find((item: any) => item.pluginGUID === value)?.source || 0
  pluginData.value.toolId = ''
  pluginData.value.contentType = ''
  pluginData.value.operationId = ''
  pluginData.value.inputs = []
  pluginData.value.outputs = []
}
// biome-ignore lint/suspicious/noExplicitAny: off
const onApiChange = (value: any) => {
  pluginData.value.inputs = []
  pluginData.value.outputs = []
  pluginData.value.toolId = value
  const service = serviceOptions.value.find(
    (item: { path: string; code: string }) => item.path === value || item.code === value,
  )
  pluginData.value.contentType = service?.contentType || ''
  pluginData.value.operationId = service?.operationId || ''
  linkageParams()
  inputKey.value = new Date().getTime()
}
// biome-ignore lint/suspicious/noExplicitAny: off
const onBodyChange = (data: any) => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  data.forEach((item: any) => {
    const value = bodyParamsMap.value[item.code]
    if (value) {
      item.required = value.required
      item.type = value.type
      item.name = value.label
    }
  })
  pluginData.value.inputs = data
}
// biome-ignore lint/suspicious/noExplicitAny: off
const onOutputChange = (data: any) => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const results = (data || []).map((item: any) => {
    return {
      name: item.describe || item.name,
      code: item.name,
      type: 'string',
      required: item.required || false,
      description: item.describe,
    }
  })
  pluginData.value.outputs = results
}
const configInfo = [
  {
    prop: 'code',
    placeholder: '请选择',
    width: '40%',
    type: 'select',
    options: [],
    disabled: true,
  },
  {
    prop: 'required',
    placeholder: '请选择',
    width: '20%',
    type: 'select',
    options: [
      { label: '必填', value: true },
      { label: '非必填', value: false },
    ],
    disabled: true,
  },
  {
    prop: 'value',
    placeholder: '',
    width: '40%',
    type: 'field-filling',
  },
]
// biome-ignore lint/suspicious/noExplicitAny: off
let configData: any = ref(configInfo)

const linkageParams = () => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const item: any =
    pluginStore.serviceData.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => (item.path || item.code) === pluginData.value.toolId,
    ) || {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  const bodyData: any = []
  // biome-ignore lint/suspicious/noExplicitAny: off
  let inputParams: any = bodyParams.value
  if (item.params) {
    // 过滤掉隐藏字段
    // biome-ignore lint/suspicious/noExplicitAny: off
    inputParams = item.params.filter((item: any) => item.show)
    // biome-ignore lint/suspicious/noExplicitAny: off
    bodyParams.value = inputParams.map(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: { required: any; type: any; describe: any; name: any }) => {
        bodyData.push({
          code: item.name,
          value: { type: 'literal', content: '' },
        })
        const label = item.name
        return {
          label,
          value: item.name,
          type: item.type,
          required: item.required,
        }
      },
    )
  }
  onOutputChange(item.results)
  configData.value[0].options = bodyParams.value
  onBodyChange(bodyData)
}

watch(
  () => props.formData,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (v: any) => {
    pluginData.value = v
  },
  { immediate: true },
)

watch(
  () => props.formData.pluginId,
  (v: string) => {
    pluginStore.getPluginServiceList(v)
  },
  { immediate: true },
)

watch(
  () => pluginData.value,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (v: any) => {
    emit('input', v)
  },
)

const paramsData = computed(() => flowStore.getParamsData())
const sourceMap = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const map: any = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  paramsData.value.forEach((item: any) => {
    if (item.children) {
      if (item.code === 'system') return
      // biome-ignore lint/suspicious/noExplicitAny: off
      item.children.forEach(({ value, description, name, type }: any) => {
        map[value] = { description, name, type, code: value }
      })
    }
  })
  return map
})

const systemMap = computed(() => {
  const data: InputParamsMap = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.getSystemParamData().forEach((item: any) => {
    data[item.value] = item
  })
  return data
})

const handleMapping = () => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const item: any =
    pluginStore.serviceData.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => (item.path || item.code) === pluginData.value.toolId,
    ) || {}
  if (!item.params) return
  loading.value = true
  onMapping({
    source: Object.values(sourceMap.value),
    target: item.params
      // biome-ignore lint/suspicious/noExplicitAny: off
      .filter((item: any) => item.show)
      // biome-ignore lint/suspicious/noExplicitAny: off
      .map((item: any) => {
        return { ...item, code: item.name }
      }),
  })
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then((res: any) => {
      if (res.status !== 200 || !res?.data?.data) return
      if (typeof res.data.data === 'object') {
        // biome-ignore lint/suspicious/noExplicitAny: off
        const map: any = {}
        res.data.data.forEach((item: any) => {
          map[item.code] = item?.value
        })
        const inputs = JSON.parse(JSON.stringify(pluginData.value.inputs))
        // biome-ignore lint/suspicious/noExplicitAny: off
        inputs.forEach((item: any) => {
          const currentItem = map[item.code]
          const refField = currentItem?.content
          if (refField && (sourceMap.value[refField] || systemMap.value[refField])) {
            item.value = { type: 'ref', content: refField }
          }
        })
        inputKey.value = new Date().getTime()
        pluginData.value.inputs = inputs
      }
      loading.value = false
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <a-form-item field="pluginId" label="选择插件" :required="true" :style="{ width: '100%' }">
    <a-select v-model="pluginData.pluginId" placeholder="请选择" @change="onPluginChange">
      <a-option
        v-for="(option, _index) of pluginOptions"
        :value="option.pluginGUID"
        :key="'option_' + _index"
        >{{ option.pluginName }}</a-option
      >
    </a-select>
  </a-form-item>
  <a-form-item field="toolId" label="插件服务" :required="true" :style="{ width: '100%' }">
    <a-select v-model="pluginData.toolId" placeholder="请选择" @change="onApiChange">
      <a-option
        v-for="(option, _index) of serviceOptions"
        :value="option.path || option.code"
        :key="'option_' + _index"
        >{{ option.name }}</a-option
      >
    </a-select>
  </a-form-item>
  <a-form-item label="输入参数" class="form-item_title">
    <div class="w-full">
      <a-popover
        position="top"
        :content-style="{ padding: '4px 12px 8px 12px' }"
        popup-container=".designer-app"
      >
        <a-button
          type="primary"
          class="float-right rounded-[4px]"
          :loading="loading"
          @click="handleMapping"
        >
          参数自动映射
          <span class="ml-[4px]">
            <svg-icon name="help" size="16px" color="white" />
          </span>
        </a-button>
        <template #content>
          <div class="w-[120px] text-[13px] leading-[22px] text-[#333333]">
            通过插件服务参数，自动生成输入参数。
          </div>
        </template>
      </a-popover>
    </div>
  </a-form-item>
  <FieldForm
    :key="inputKey"
    :value="pluginData.inputs"
    :config="configData"
    :allowAdd="false"
    :allowDelete="false"
    @input="onBodyChange"
  />
  <a-form-item label="输出参数" class="form-item_title"></a-form-item>
  <a-table :pagination="false" class="custom-grid" :columns="columns" :data="pluginData.outputs" />
</template>
