<script setup lang="ts">
// @ts-ignore
import { computed, nextTick, onMounted, ref, shallowRef, watch } from 'vue'
import { useFlowStore } from '../../stores/flow'
import SvgIcon from '../svg-icon.vue'
import <PERSON>Field from './select-field.vue'
import { useMarkdownEditor } from '../../composables/useMarkdownEditor'

const props: any = defineProps({
  value: {
    type: Object,
    default: () => {},
  },
  minHeight: {
    type: String,
    default: '240px',
  },
  maxHeight: {
    type: String,
    default: '400px',
  },
  prompt: {
    type: String,
    default: '',
  },
  inputs: {
    type: Array,
    default: () => [],
  },
  mode: {
    type: String,
    default: 'textarea',
  },
  placeholder: {
    type: String,
    default: '请输入提示词',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isCustom: {
    type: Boolean,
    default: false,
  },
  customParams: {
    type: Object,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
  allowDel: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Array,
    default: () => [],
  },
  filterSystemCode: {
    type: Boolean,
    default: false,
  },
  // 参数无层级
  noLevel: {
    type: Boolean,
    default: false,
  },
  // 步骤标题
  stepName: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['input', 'inputs', 'del', 'fieldInfo'])

const flowStore = useFlowStore()
const paramsData = computed(() => (props.isCustom ? props.customParams : flowStore.getParamsData()))
const treeData = computed(() => {
  return props.isCustom ? props.customParams : flowStore.getParamsData(true)
})

let content = ref('')
const update = (value: string) => {
  content.value = value
}

const sourceMap = computed(() => {
  const map: any = {}
  paramsData.value.forEach((item: any) => {
    if (item.children) {
      item.children.forEach((val: any) => {
        val.title = val.name
        map[val.value] = val
        if (val.children) {
          val.children.forEach((child: any) => {
            child.title = child.name
            map[child.value] = child
          })
        }
      })
    }
    if (props.noLevel) {
      item.title = item.name
      map[item.value] = item
    }
  })
  return map
})

const inputsMap = computed(() => {
  return props.inputs.reduce((map: any, item: any) => {
    if (item.value?.content) {
      map[item.code] = sourceMap.value[item.value.content]
    }
    return map
  }, {})
})

const mapList = computed(() => {
  if (props.isCustom) return sourceMap.value
  return inputsMap.value 
})

const regex = /{{\$([^{}]+)}}/g // 正则表达式匹配 {entity.field} 形式的文本
const updateInputs = (text: string) => {
  const fieldsMap = mapList.value
  const content = text.replace(/[\n\r\t\f\v\u2028\u2029]/g, '')
  const tokens = []

  let match

  while ((match = regex.exec(content)) !== null) {
    const [, fieldText] = match
    const item = fieldsMap[fieldText] || {}
    if (item.value) {
      tokens.push({
        code: fieldText,
        value: {
          type: 'ref',
          content: item.value,
        },
      })
    } else {
      // 通过输入的变量名 注入对应的变量
      nextTick(() => {
        handleInputField(fieldText)
      })
    }
  }
  emit('inputs', tokens)
}

const originMapKey = computed(() => {
  return Object.keys(sourceMap.value).map((key) => sourceMap.value[key].code)
})

// 传入编辑器组件的字段列表
const fieldMaps = computed(() => {
  const map: Record<string, any> = {}
  Object.keys(mapList.value).forEach((key) => {
    const item = mapList.value[key]
    map[`$${key}`] = {
      ...item,
      type: 'variable',
      label: item.name || item.title || item.label || item.value
    }
  })

  return map
})

onMounted(() => {
  content.value = props.value
  // 初始化的时候更新一次inputs列表 确保传入编辑器的列表正确
  updateInputs(content.value)
})

// 初始化编辑器
const { setContent, addFieldToCurrent, refreshPlaceholders } = useMarkdownEditor({
  content: content, 
  data: fieldMaps, 
  elementId: 'refEditor',
  disabled: props.disabled,
  minHeight: props.minHeight,
  maxHeight: props.maxHeight,
  update: (v) => {
    emit('input', v)
  }
})


// 自动编码选择的参数后缀
const getCodeValue = (value: string) => {
  let i = 0
  props.inputs.forEach((item: any) => {
    if (item.code.startsWith(value)) {
      const index = Number(item.code.replace(value, ''))
      const isNumber = typeof index === 'number'
      if (isNumber) {
        i = index + 1
      }
    }
  })
  return value + i
}

// 选择参数时的回调
const handleSelect = (item: { name: string; value: string; code: string }) => {
  if (props.isCustom) {
    addFieldToCurrent(item.value)
    return
  }
  
  const code = getCodeValue(item.code)
  emit('inputs', [...props.inputs, { code: code, value: { type: 'ref', content: item.value } }])
  // 下一次更新中更新content，使传入编辑器的字段列表更新后，正常展示中文名称
  nextTick(() => {
    addFieldToCurrent(code)
  })
}

// 当textarea模式下手动输入字段时 自动映射
const handleInputField = (text: string) => {
  if (originMapKey.value.includes(text)) {
    const code = getCodeValue(text)
    const itemKey = Object.keys(sourceMap.value).find((key) => {
      return sourceMap.value[key].code === text
    })
    const item = sourceMap.value[itemKey || ''] || {}
    if (item) {
      // 更新字段列表
      emit('inputs', [...props.inputs, { code: code, value: { type: 'ref', content: item.value } }])
      // 替换content的内容
      nextTick(() => {
        update(content.value.replace(`{{$${text}}}`, `{{$${code}}}`))
        setContent(content.value.replace(`{{$${text}}}`, `{{$${code}}}`))
      })
    }
  }
}

watch(
  () => content.value,
  (v: string) => {
    updateInputs(content.value)
    setContent(v)
    nextTick(() => {
      refreshPlaceholders()
      emit('input', v)
    })
    return
  },
)
const handleDel = () => {
  emit('del')
}

</script>

<template>
  <div class="designer-template-editor w-[100%]">
    <div
      class="pd-input-textarea"
      :class="{
        'pd-input-textarea_input': true,
        'pd-input-textarea_disabled': disabled,
      }"
    >
      <div class="pd-input-textarea__header">
        <div class="pd-input-textarea__header-title" v-if="title">
          {{ title }}
        </div>
        <div
          v-if="allowDel"
          class="pd-input-textarea__header-btn del"
          title="删除"
          @click="handleDel"
        >
          <svg-icon name="trash" size="16px" color="#999" />
        </div>
        <div class="pd-input-textarea__header-btn">
          <slot name="btn"></slot>
        </div>
        <SelectField
          v-if="!$slots.btn"
          :paramsData="treeData"
          :selected="selected"
          @select="handleSelect"
          :filterSystemCode="filterSystemCode"
          :noLevel="noLevel"
          :stepName="stepName"
        >
          <div class="pd-input-textarea__header-btn">
            <svg-icon name="add" size="16px"></svg-icon>插入变量
          </div>
        </SelectField>
      </div>
      <div
        ref="refEditor"
        class="pd-input-textarea__body"
        :style="{ 
          'min-height': minHeight, 
          'max-height': maxHeight, 
          '--editor-min-height': `${minHeight}`,
          '--editor-max-height': `${maxHeight}`,
        }"
      ></div>  
    </div>
    <span class="text-xs font-normal not-italic leading-[20px] text-[#999999]" v-if="prompt">{{
      prompt
    }}</span>
  </div>
</template>

<style lang="less">
@color-primary: #266eff;

.designer-app .pd-input-textarea {
  position: relative;
  font-size: 12px;
  border: 1px solid #ddd;
  transition: border-color 0.2s;
  border-radius: 4px;
  margin-bottom: 12px;

  &.is-focus {
    border-color: @color-primary;
  }

  &__popover {
    padding: 8px;
    margin-right: 15px;
  }

  &__empty,
  &__items {
    width: 342px;
  }

  &__empty {
    color: #d3d8e9;
    padding: 4px;
  }

  &__bar {
    position: absolute;
    right: 8px;
    bottom: 5px;
    line-height: 1.5;
    color: @color-primary;
    cursor: pointer;
    user-select: none;
    font-weight: 400;
    font-size: 12px;
    padding-left: 14px;

    span {
      position: relative;
      top: 1px;
    }

    &--empty {
      color: #b9caff;
    }

    .hc-icon-plus {
      font-size: 12px;
      font-weight: bold;
    }
  }

  &__header {
    height: 34px;
    width: 100%;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    font-size: 14px;
    line-height: 22px;
    padding: 6px 16px;
    box-sizing: border-box;
    border-radius: 4px 4px 0 0;
    background-color: #fbfbfb;

    svg {
      color: @color-primary;
      display: inline-block;
      width: 20px;
      margin-right: 4px;
      margin-top: 2px;
      float: left;
    }
  }

  &__header-btn {
    cursor: pointer;
    float: right;
    margin-left: 12px;

    &:not(.del):hover {
      color: @color-primary;
    }

    &.del {
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        display: inline-block;
        height: 20px;
        border-left: 1px solid #ddd;
      }
    }
    .svg-icon {
      margin-top: 3px !important;
      margin-right: 4px !important;
    }
  }

  &__header-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
    display: inline-block;
  }

  &.pd-input-textarea_input {
    margin-bottom: 0;

    svg {
      margin: 0;
    }

    .ProseMirror {
      min-height: 20px;
      padding: 3px 12px;
      max-height: 30px;
    }
  }

  .pd-input-textarea .field-editor--field {
    border-radius: 4px;
    border: 1px solid rgba(76, 153, 254, 0.05);
    line-height: 22px;
    color: #266eff;
    background: rgba(76, 153, 254, 0.05);
    background-clip: padding-box;
    font-weight: 400;
    font-size: 12px;
    margin: 0 2px;
    cursor: pointer;
    display: inline-block;
    padding: 0 4px;
    -webkit-user-select: none;
    user-select: none;
  }
}

.pd-input-textarea_disabled {
  .pd-input-textarea__body,
  .ProseMirror {
    background: #f5f5f5;
  }

  .field-editor--p {
    color: #999;
  }

  .field-editor_close {
    display: none;
  }

  .field-editor--field {
    padding-right: 8px;
    pointer-events: none;
  }
}

</style>
