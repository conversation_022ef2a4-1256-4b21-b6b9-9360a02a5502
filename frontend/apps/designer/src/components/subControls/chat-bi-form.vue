<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useConfigStore } from '../../stores/config'
import FieldFilling from './field-filling.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(['input'])
// biome-ignore lint/suspicious/noExplicitAny: off
const data: any = ref({})
// biome-ignore lint/suspicious/noExplicitAny: off
const queryTemplate: Record<string, any> = ref('')
watch(
  () => props.formData,
  () => {
    data.value = props.formData || {}

    queryTemplate.value = data.value?.inputs?.[0]?.value || {
      type: 'literal',
      content: '',
    }
  },
  { immediate: true },
)
watch(
  () => data.value,
  () => {
    emit('input', data.value)
  },
  { deep: true },
)
watch(
  () => queryTemplate.value,
  (v: string) => {
    data.value.inputs = [
      { code: 'input', type: 'string', value: v, required: true },
    ]
  },
  { immediate: true },
)

const configStore = useConfigStore()

// biome-ignore lint/suspicious/noExplicitAny: off
const options: any = computed(() => configStore.knowledgeOptions.filter((item: any) => item.typeEnum && item.typeEnum === 3))
onMounted(() => {
 
})

</script>

<template>
  <a-form-item
    field="knowledges"
    label="问数场景"
    :rules="[{ required: true, message: '请至少选择一个问数场景' }]"
    :required="true"
  >
    <a-select
      multiple
      v-model="data.knowledges"
      :style="{ width: '100%' }"
      placeholder="请选择问数场景"
    >
      <a-option
        v-for="(option, _index) of options"
        :value="option.value"
        :key="'option_' + _index"
        >{{ option.label }}</a-option
      >
    </a-select>
  </a-form-item>

  <a-form-item label="输入参数" :style="{ width: '100%' }" :required="true">
    <FieldFilling
      :value="queryTemplate"
      minHeight="30px"
      mode="input"
      placeholder="请输入"
      @input="queryTemplate = $event"
    />
  </a-form-item>
</template>

<style lang="less">
.knowledge_slider {
  .arco-slider {
    display: flex;
  }
  .arco-slider-with-marks {
    margin: 0;
    padding: 5px 0;
  }
}
</style>
