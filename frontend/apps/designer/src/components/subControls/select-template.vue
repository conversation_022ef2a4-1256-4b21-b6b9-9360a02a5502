<script setup lang="ts">
import { computed, ref, watch } from 'vue'

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['change'])
const prop = computed(() => props.item.prop)
const options = computed(() => props.item.options)
const data = ref(props.item.default)
const tips = ref('')
watch(
  () => data.value,
  (v) => {
    props.formData[prop.value] = v
    // biome-ignore lint/suspicious/noExplicitAny: off
    tips.value = options.value.find((item: any) => item.value === v)?.tips
  },
)
watch(
  () => props.formData[prop.value],
  (v: string) => {
    if (v && data.value === v) return
    data.value = v || props.item.default
  },
  { immediate: true },
)
// biome-ignore lint/suspicious/noExplicitAny: off
const handleChange = (v: string | any) => {
  emit('change', v)
}
</script>

<template>
  <a-form-item
    :field="item.prop"
    :label="item.label"
    :required="!!item.required"
    :style="{ width: item.width || '100%' }"
  >
    <a-select
      v-model="data"
      :placeholder="item.placeholder"
      @change="handleChange"
    >
      <a-option
        v-for="(option, _index) of item.options"
        :value="option.value"
        :key="'option_' + _index"
        >{{ option.label }}</a-option
      >
    </a-select>
    <template #help>
      <div>{{ tips }}</div>
    </template>
  </a-form-item>
</template>
