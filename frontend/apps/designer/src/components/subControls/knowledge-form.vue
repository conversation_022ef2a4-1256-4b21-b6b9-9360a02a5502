<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useConfigStore } from '../../stores/config'
import { outputParamsConfig } from '../../stores/configData'
import OutputParams from './output-params.vue'
import FieldFilling from './field-filling.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(['input'])
// biome-ignore lint/suspicious/noExplicitAny: off
const data: any = ref({})
// biome-ignore lint/suspicious/noExplicitAny: off
const queryTemplate: Record<string, any> = ref('')
const minScore = ref(5)
watch(
  () => props.formData,
  () => {
    data.value = props.formData || {}
    if (typeof data.value.minScore === 'undefined') {
      data.value.minScore = 0.5
    }
    minScore.value = data.value.minScore * 10
    if (typeof data.value.topK === 'undefined') {
      data.value.topK = 3
    }
    queryTemplate.value = data.value?.inputs?.[0]?.value || {
      type: 'literal',
      content: '',
    }
  },
  { immediate: true },
)
watch(
  () => data.value,
  () => {
    emit('input', data.value)
  },
  { deep: true },
)
watch(
  () => queryTemplate.value,
  (v: string) => {
    data.value.inputs = [
      { code: 'input', type: 'string', value: v, required: true },
    ]
  },
  { immediate: true },
)

const configStore = useConfigStore()

// biome-ignore lint/suspicious/noExplicitAny: off
const options: any = computed(() =>  configStore.knowledgeOptions.filter((item: any) => item.typeEnum !== 3))

const outputParams = computed(() => props.formData.outputs)

// biome-ignore lint/suspicious/noExplicitAny: off
const marks: any = ref({ '1': 1, '3': '默认', '10': 10 })
// biome-ignore lint/suspicious/noExplicitAny: off
const scoreMarks: any = ref({ '0': 0, '5': '默认', '10': 10 })
const fieldProps = computed(() => {
  const data = JSON.parse(JSON.stringify(outputParamsConfig))
  data.splice(3, 1)
  // biome-ignore lint/suspicious/noExplicitAny: off
  data.forEach((item: any) => {
    item.width = '33%'
  })
  data[2].options.push({ label: '对象', value: 'object' })
  return data
})

watch(
  () => minScore.value,
  () => {
    data.value.minScore = minScore.value / 10
  },
  { immediate: true },
)

watch(
  () => data.value.topK,
  (v) => {
    if (v < 1) data.value.topK = 1
  },
)
const columns = computed(() => [
  { title: '名称', dataIndex: 'name' },
  { title: '编码', dataIndex: 'code' },
  { title: '类型', dataIndex: 'type' },
])

onMounted(() => {
  if (typeof props.formData.pattern === 'undefined') {
    props.formData.pattern = 0
  }
})

// biome-ignore lint/suspicious/noExplicitAny: off
const handleChange = (v: number | any) => {
  if (v === 0) {
    props.formData.outputs = [
      { code: 'result', type: 'string', name: '查询结果' },
    ]
  }
  if (v === 1) {
    props.formData.outputs = [
      {
        name: '查询结果',
        code: 'result',
        type: 'array<object>',
        description: '',
        schema: [
          {
            name: '分段内容',
            code: 'content',
            type: 'string',
            description: '',
          },
          { name: '分段标题', code: 'title', type: 'string', description: '' },
          { name: '分段连接', code: 'url', type: 'string', description: '' },
          {
            name: '分段元数据',
            code: 'metadata',
            type: 'object',
            description: '',
          },
        ],
      },
    ]
  }
}
</script>

<template>
  <a-form-item
    field="knowledges"
    label="知识库"
    :rules="[{ required: true, message: '请至少选择一个知识库' }]"
    :required="true"
  >
    <a-select
      multiple
      v-model="data.knowledges"
      :style="{ width: '100%' }"
      placeholder="请选择知识库"
    >
      <a-option
        v-for="(option, _index) of options"
        :value="option.value"
        :key="'option_' + _index"
        >{{ option.label }}</a-option
      >
    </a-select>
  </a-form-item>
  <a-form-item field="topK" class="knowledge_slider" label="最大结果数量">
    <a-slider
      v-model="data.topK"
      @input="data.topK = $event"
      :min="1"
      :max="10"
      :step="1"
      :marks="marks"
    />
  </a-form-item>
  <a-form-item field="minScore" class="knowledge_slider" label="最小匹配度">
    <a-slider
      v-model="minScore"
      :min="0"
      :max="10"
      :step="0.1"
      :marks="scoreMarks"
    />
  </a-form-item>
  <a-form-item label="输入参数" :style="{ width: '100%' }" :required="true">
    <FieldFilling
      :value="queryTemplate"
      minHeight="30px"
      mode="input"
      placeholder="请输入"
      @input="queryTemplate = $event"
    />
  </a-form-item>
  <a-form-item label="输出参数" class="form-item_title"></a-form-item>
  <a-form-item field="pattern" label="分析模式">
    <a-radio-group v-model="formData.pattern" @change="handleChange">
      <a-radio :value="0">纯文本</a-radio>
      <a-radio :value="1">参数列表</a-radio>
    </a-radio-group>
  </a-form-item>
  <a-table
    v-if="formData.pattern === 0"
    :pagination="false"
    class="custom-grid"
    :columns="columns"
    :data="outputParams"
  />
  <output-params
    v-else
    :allow-add="false"
    :allow-delete="false"
    :value="formData.outputs"
    :config="fieldProps"
    @input="formData.outputs = $event"
  />
</template>

<style lang="less">
.knowledge_slider {
  .arco-slider {
    display: flex;
  }
  .arco-slider-with-marks {
    margin: 0;
    padding: 5px 0;
  }
}
</style>
