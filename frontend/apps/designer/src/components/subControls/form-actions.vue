<script lang="ts" setup>
import { onMounted } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
onMounted(() => {
  if (!props.formData.props?.actions?.length) {
    if (props.formData.props)
      props.formData.props.actions = [{ code: 'card-confirm', name: '', description: '' }]
  }
})
</script>

<template>
  <template v-if="formData.props?.actions">
    <a-form-item field="props.actions[0].name" label="按钮标题" :style="{ width: '100%' }">
      <a-input v-model="formData.props.actions[0].name" placeholder="请输入按钮标题" />
    </a-form-item>
    <a-form-item field="props.actions[0].description" label="按钮说明" :style="{ width: '100%' }">
      <a-textarea
        v-model="formData.props.actions[0].description"
        placeholder="请输入按钮说明"
        allow-clear
      />
    </a-form-item>
  </template>
</template>
