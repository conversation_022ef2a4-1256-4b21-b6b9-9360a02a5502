<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useSortable } from '@vueuse/integrations/useSortable'
import { type Ref, computed, nextTick, ref } from 'vue'
import { useConditionStore } from '../../stores/condition'
import { conditionData, getAddCondition } from '../../stores/constants'
import { useFlowStore } from '../../stores/flow'
import { generateCode } from '../../utils'
import confirmDialog from '../confirm-dialog.vue'
import FieldFilling from './field-filling.vue'

interface SelectorProps {
  conditions: SelectorCondition[]
  id: string
}
enum ValueType {
  Ref = 'ref',
  Literal = 'literal',
}
const props = defineProps<SelectorProps>()
const visible = ref(false)
const flowStore = useFlowStore()
const conditionStore = useConditionStore()
const refSelector = ref(null)
// biome-ignore lint/suspicious/noExplicitAny: off
const refInputs: Ref<{ [key: string]: any }> = ref({})

const conditionMap = {
  eq: { label: '等于', value: 'eq' },
  neq: { label: '不等于', value: 'neq' },
  lgthgt: { label: '长度大于', value: 'lgthgt' },
  lgthgte: { label: '长度大于等于', value: 'lgthgte' },
  lgthlt: { label: '长度小于', value: 'lgthlt' },
  lgthlte: { label: '长度小于等于', value: 'lgthlte' },
  contains: { label: '包含', value: 'contains' },
  notcontains: { label: '不包含', value: 'notcontains' },
  isempty: { label: '为空', value: 'isempty' },
  notisempty: { label: '不为空', value: 'notisempty' },
  gt: { label: '大于', value: 'gt' },
  gte: { label: '大于等于', value: 'gte' },
  lt: { label: '小于', value: 'lt' },
  lte: { label: '小于等于', value: 'lte' },
}
const options = ref([
  conditionMap.eq,
  conditionMap.neq,
  conditionMap.lgthgt,
  conditionMap.lgthgte,
  conditionMap.lgthlt,
  conditionMap.lgthlte,
  conditionMap.contains,
  conditionMap.notcontains,
  conditionMap.isempty,
  conditionMap.notisempty,
])
const numberOptions = ref([
  conditionMap.eq,
  conditionMap.neq,
  conditionMap.isempty,
  conditionMap.notisempty,
  conditionMap.gt,
  conditionMap.gte,
  conditionMap.lt,
  conditionMap.lte,
])
const hideInput = ref(['isempty', 'notisempty'])
const allowDel = computed(() => props.conditions.length > 2)

const paramsData = computed(() => flowStore.getParamsData())
const sourceMap = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const map: any = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  paramsData.value.forEach((item: any) => {
    if (item.children) {
      // biome-ignore lint/suspicious/noExplicitAny: off
      item.children.forEach((val: any) => {
        val.title = val.name
        map[val.value] = val
      })
    }
  })
  return map
})

const maxNum = 30
const handleAdd = () => {
  const len = props.conditions.length
  if (len >= maxNum) {
    return Message.warning(`最多支持${maxNum}个条件分支！`)
  }
  const targetIndex = len - 1
  const condition = JSON.parse(JSON.stringify(conditionData))
  condition.id = generateCode(6)
  condition.priority = len
  condition.title = `条件${len}`
  condition.type = len > 1 ? 'else if' : 'if'
  condition.expressions[0].rules[0].left.code = generateCode(8)
  condition.expressions[0].rules[0].right.code = generateCode(8)
  props.conditions.splice(targetIndex, 0, condition)

  const edgeId = flowStore.getNewEdgeId(true)
  const data = getAddCondition(
    condition.id,
    props.id as string,
    edgeId as number,
    '',
    '',
  )
  flowStore.addSelector(data)
  onSortPriority(true)
}
const handleAddRule = (rules: ExpressionRule[]) => {
  rules.push({
    left: {
      code: generateCode(8),
      value: {
        type: ValueType.Ref,
        content: '',
      },
    },
    operator: 'eq',
    right: {
      code: generateCode(8),
      value: {
        type: ValueType.Literal,
        content: '',
      },
    },
  })
}

let delIndex = -1
const handleDialog = (index: number) => {
  delIndex = index
  visible.value = true
}
const handleDel = () => {
  if (delIndex < 0) return
  const item = props.conditions[delIndex]
  props.conditions.splice(delIndex, 1)
  flowStore.removeCondition(`${props.id}_condition_${item.id}`)
  onSortPriority()
}

const onChangeRelation = (expression: ConditionExpression) => {
  expression.type = expression.type === 'and' ? 'or' : 'and'
}

const handleToggle = (condition: SelectorCondition) => {
  condition.collapse = !condition.collapse
}

const { option } = useSortable(refSelector, props.conditions, {
  handle: '.handle',
  onUpdate: (e: { oldIndex: number; newIndex: number }) => {
    const targetItem = props.conditions[e.newIndex]
    if (targetItem.type === 'else') return
    const item: SelectorCondition = props.conditions[e.oldIndex]
    props.conditions.splice(e.oldIndex, 1)
    props.conditions.splice(e.newIndex, 0, item)
    nextTick(() => {
      onSortPriority(true)
    })
  },
  // biome-ignore lint/suspicious/noExplicitAny: off
  onMove: (e: any) => {
    if (e.related.className.endsWith('disabled')) {
      return false
    }
    return true
  },
})
option('animation', 200)

const onSortPriority = (updateFlow = false) => {
  props.conditions.forEach((item: SelectorCondition, index: number) => {
    item.priority = index + 1
    if (item.type === 'else') return
    item.type = index > 0 ? 'else if' : 'if'
  })
  if (updateFlow) {
    flowStore.updatePosition()
  }
}

const handleEdit = (item: SelectorCondition) => {
  item.enableEdit = true
  nextTick(() => {
    refInputs.value[item.id].focus()
  })
}

const getOptions = (item: ExpressionRule) => {
  const { value } = item.left || {}
  let opts = options.value
  if (value?.type === 'ref' && value?.content) {
    const param = sourceMap.value[value.content]
    if (param && param.type === 'number') {
      opts = numberOptions.value
    }
  }
  const isInclude = opts.some((i) => i.value === item.operator)
  if (!isInclude) {
    item.operator = opts[0].value
  }
  return opts
}
</script>

<template>
  <a-form-item class="form-item_title relative" label="条件分支">
    <div
      class="absolute right-[0] flex cursor-pointer items-center hover:text-[#266EFF]"
      @click="handleAdd"
    >
      <svg-icon name="add" size="16px" />
      <span class="ml-[4px] text-[13px] font-normal">新增分支</span>
    </div>
  </a-form-item>
  <div ref="refSelector">
    <div
      v-for="(item, index) in conditions"
      :key="'fz_' + item.id"
      class="selector-branch"
      :class="{
        disabled: item.type === 'else',
        'is-active': conditionStore.conditionId === item.id,
      }"
    >
      <template v-if="item.type === 'else'">
        <div class="flex items-center pb-[12px]">
          <span class="mr-[8px] text-[13px] font-semibold">否则</span>
        </div>
      </template>
      <template v-else>
        <div class="relative flex items-center pb-[12px]">
          <svg-icon
            name="drop"
            class="handle cursor-all-scroll"
            @mousedown="item.collapse = true"
          />
          <svg-icon
            name="down"
            class="mx-[4px] my-[0] cursor-pointer"
            :class="{ '-rotate-90 transform': item.collapse }"
            @click="handleToggle(item)"
          />
          <div class="branch-title mr-[8px] inline-block">
            <a-input
              v-if="!!item.enableEdit"
              v-model="item.title"
              :max-length="10"
              placeholder="请输入条件标题"
              :error="!item.title"
              @blur="item.enableEdit = false"
              :ref="(el) => (refInputs[item.id] = el)"
            />
            <span class="inline-block text-[13px] font-semibold" v-else>
              {{ item.title }}
              <svg-icon
                name="edit"
                size="16px"
                style="cursor: pointer; margin-left: 4px"
                @click="handleEdit(item)"
              />
            </span>
          </div>
          <a-tag color="gray">优先级{{ item.priority }}</a-tag>
          <div
            v-if="allowDel"
            class="absolute right-0 cursor-pointer"
            @click="handleDialog(index)"
          >
            <svg-icon name="minus" size="16px" />
          </div>
        </div>
        <div
          v-if="!item.collapse"
          v-for="(expression, key) in item.expressions"
          :class="{
            'selector-expression': true,
            'is-multiple': expression?.rules?.length > 1,
          }"
          :key="'expression_' + key"
        >
          <div class="expression_relation float-left">
            <span
              @click="onChangeRelation(expression)"
              class="z-[1] flex w-[24px] cursor-pointer items-center justify-center rounded-[4px]
                border-[1px] border-solid border-[#A1CAFF] bg-[#EBF2FF] px-[4px] py-px
                text-[13px] text-[#266EFF] hover:border-[#266EFF]"
            >
              {{ expression.type === 'and' ? '且' : '或' }}
            </span>
          </div>
          <div
            class="expression_form float-left"
            v-for="(fieldItem, i) in expression.rules"
          >
            <a-form-item
              class="rule-item float-left"
              :hide-label="true"
              style="width: calc(50% - 80px)"
            >
              <FieldFilling
                :key="'left_' + item.id"
                :value="fieldItem.left.value"
                minHeight="30px"
                mode="input"
                placeholder="请选择/输入参数值"
                @input="fieldItem.left.value = $event"
              />
            </a-form-item>
            <a-form-item
              class="float-left mx-[8px] my-[0]"
              :hide-label="true"
              style="width: 120px"
            >
              <a-select v-model="fieldItem.operator">
                <a-option
                  v-for="(option, _index) of getOptions(fieldItem)"
                  :value="option.value"
                  :key="'option_' + _index"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>
            <a-form-item
              class="float-left"
              :hide-label="true"
              style="width: calc(50% - 80px)"
              v-if="!hideInput.includes(fieldItem.operator)"
            >
              <FieldFilling
                :key="'right_' + item.id"
                :value="fieldItem.right.value"
                minHeight="30px"
                mode="input"
                placeholder="请选择/输入参数值"
                @input="fieldItem.right.value = $event"
              />
            </a-form-item>
            <div
              v-if="expression.rules.length > 1"
              class="float-left ml-[8px] flex h-[32px] w-[16px] cursor-pointer items-center"
            >
              <svg-icon
                name="minus"
                size="16px"
                @click="() => expression.rules.splice(i, 1)"
              />
            </div>
          </div>

          <div
            class="flex w-[48px] cursor-pointer flex-row items-center pb-[12px]"
            @click="handleAddRule(expression.rules)"
          >
            <svg-icon name="add" size="16px" />
            <span
              class="ml-[4px] text-center text-[13px] font-normal not-italic leading-[22px]
                text-[#333333]"
              >添加</span
            >
          </div>
        </div>
      </template>
    </div>
  </div>
  <confirm-dialog
    :show="visible"
    content="请确认是否删除当前条件分支？"
    @onOk="handleDel"
    @input="visible = $event"
  />
</template>

<style lang="less">
.selector-branch {
  padding: 12px 12px 0 12px;
  border-radius: 4px;
  background: #f7f8fa;
  margin-top: 12px;
  .arco-input-tag {
    background: #fff;
  }
  .arco-tag {
    border-radius: 12px !important;
    border: 1px solid #c5c5c5 !important;
    color: #576275 !important;
    font-size: 12px !important;
    padding: 0 8px !important;
    height: 20px !important;
  }
  .branch-title {
    .arco-input-wrapper {
      border: 1px solid #266eff !important;
      border-radius: 4px;
      min-width: 50px;
      height: 22px;
      background: #fff;
    }
  }
  &.is-active {
    background: #ebf2ff;
  }
  .pd-input-textarea {
    background: #fff;
  }
  .arco-select-view-single {
    background-color: #fff !important;
  }
}
.selector-expression {
  .expression_relation {
    display: none;
  }
  .expression_form {
    width: 100%;
    .arco-input-tag{
      background-color: #fff !important;
    }
  }
  &.is-multiple {
    position: relative;
    display: inline-block;
    .expression_relation {
      width: 32px;
      align-items: center;
      justify-items: center;
      position: absolute;
      top: 0;
      bottom: 46px;
      left: 0;
      margin: auto;
      display: flex;
      z-index: 1;
      &:after {
        content: '';
        width: 18px;
        position: absolute;
        border: 1px solid #ddd;
        border-right: 0px;
        top: 8px;
        bottom: 8px;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        left: 12px;
      }
    }
    .expression_form {
      margin-left: 32px;
      width: calc(100% - 32px);
    }
  }
}
</style>
