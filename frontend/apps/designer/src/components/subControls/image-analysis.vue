<script setup lang="ts">
import { computed } from 'vue'
import aiPrompt from './ai-prompt.vue'
import DocumentAnalysisForm from './document-analysis-form.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const isOcr = computed(() => props.formData.recognizeType === 'ocr')
const contentData = computed(() => [
  { name: '文本内容', code: 'content', type: 'string' },
])
</script>

<template>
  <template v-if="!isOcr">
    <ai-prompt :form-data="formData"></ai-prompt>
  </template>
  <template v-if="isOcr">
    <DocumentAnalysisForm
      :form-data="formData"
      :content-data="contentData"
      type="image"
    />
  </template>
</template>
