<script setup lang="ts">
import { computed, onMounted, type ComputedRef } from 'vue'
import { outputParamsConfig } from '../../stores/configData'
import OutputParams from './output-params.vue'

interface TableData {
  name: string
  code: string
  type: string
}

const props = defineProps({
  formData: {
    type: Object,
    default: () => {},
  },
  contentData: {
    type: Object,
    default: () => [{ name: '文档内容', code: 'content', type: 'markdown' }],
  },
  type: {
    type: String,
    default: 'document',
  },
})
const columns = computed(() => [
  { title: '名称', dataIndex: 'name' },
  { title: 'ID', dataIndex: 'code' },
  { title: '类型', dataIndex: 'type' },
])
const outputParams: ComputedRef<TableData[]> = computed(
  () => (props.contentData as TableData[]) || [],
)
const fieldProps = computed(() => outputParamsConfig)
const textLabel = computed(() =>
  props.type === 'image' ? '文本读取' : '文档读取',
)

onMounted(() => {
  if (props.formData.pattern === 0) {
    props.formData.outputs = outputParams.value
  }
})

// biome-ignore lint/suspicious/noExplicitAny: off
const onPatternChange = (v: number | any) => {
  if (v === 0) {
    props.formData.outputs = outputParams.value
  }
  if (v === 1) {
    props.formData.outputs = [
      { name: '', code: '', type: 'string', description: '' },
    ]
  }
}
</script>

<template>
  <a-form-item field="pattern" label="分析模式">
    <a-radio-group v-model="formData.pattern" @change="onPatternChange">
      <a-radio :value="0">{{ textLabel }}</a-radio>
      <a-radio :value="1">信息提取</a-radio>
    </a-radio-group>
  </a-form-item>
  <a-form-item label="输出参数" class="form-item_title"></a-form-item>
  <template v-if="formData.pattern === 0">
    <a-table
      :pagination="false"
      class="custom-grid"
      :columns="columns"
      :data="outputParams"
    />
  </template>
  <div
    class="ml-[20px] inline-block w-[calc(100%_-_20px)]"
    v-if="formData.pattern === 1"
  >
    <output-params
      :value="formData.outputs"
      :config="fieldProps"
      :enable-sort="true"
      @input="formData.outputs = $event"
    />
  </div>
</template>
