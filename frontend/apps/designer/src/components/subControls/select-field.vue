<script setup lang="ts">
import {
  computed,
  type ComputedRef,
  onMounted,
  onUnmounted,
  type Ref,
  ref,
  watch,
} from 'vue'
type Props = {
  paramsData: SelectFieldParamsType[]
  filterSystemCode: boolean
  noLevel: boolean
  stepName: string
}
const props = defineProps<Props>()

const emit = defineEmits(['input', 'select'])
const selectType = ref('step')
const selectParams: Ref<SelectFieldParamsType[]> = ref([])
const visible = ref(false)
const queryText = ref('')
const selectValue = ref('')

const showSystemParams = computed(() => {
  return props.paramsData.some((item) => {
    return item.code === 'system'
  })
})

const treeData: ComputedRef<SelectFieldParamsType[]> = computed(() => {
  if (props.noLevel) return props.paramsData
  return props.paramsData.filter((item) => {
    return item.children?.length > 0
  })
})

const options: ComputedRef<SelectFieldParamsType[]> = computed(() => {
  if (selectType.value === 'step') {
    return treeData.value.filter(
      (item: { code: string }) => item.code !== 'system',
    )
  }
  const params = treeData.value.filter(
    (item: { code: string }) => item.code === 'system',
  )
  return params?.[0]?.children || []
})

watch(
  () => options.value,
  (v) => {
    selectParams.value = v
    if (queryText.value) {
      handleSearch()
    }
  },
  {
    immediate: true,
  },
)

const handleChange = (v: string | number) => {
  selectType.value = v as string
}

const handleClick = (v: string | unknown) => {
  let select = null
  options.value.find((item) => {
    if (item.children) {
      item.children.find((child) => {
        // step
        if (child.value === v) {
          select = child
          return true
        }
        // system
        if (child.children) {
          return child.children.find((c) => {
            if (c.value === v) {
              select = c
              return true
            }
          })
        }
      })
    }
    if (item.value === v) {
      select = item
      return true
    }
  })

  emit('select', select)
  emit('input', false)
  visible.value = false
}

const recTree = (value: string, arr: SelectFieldParamsType[]) => {
  if (!arr) {
    return []
  }
  if (!value) return arr
  value = value.toLocaleLowerCase()
  const label = 'label'
  const children = 'children'
  const newArr: SelectFieldParamsType[] = []
  if (Object.prototype.toString.call(arr) === '[object Array]') {
    arr.forEach((element) => {
      const labelValue = element[label]?.toLocaleLowerCase() || ''
      const codeValue = element.code?.toLocaleLowerCase() || ''
      if (labelValue.indexOf(value) > -1 || codeValue.indexOf(value) > -1) {
        // const ab = recTree(value, element[children])
        const obj = {
          ...element,
          children: element[children],
        }
        newArr.push(obj)
      } else {
        if (element[children] && element[children].length > 0) {
          const ab = recTree(value, element[children])
          const obj = {
            ...element,
            children: ab,
          }
          if (ab && ab.length > 0) {
            newArr.push(obj)
          }
        }
      }
    })
  }
  return newArr
}

const handleSearch = () => {
  selectParams.value = recTree(queryText.value, options.value)
}

const listenerClick = (e: MouseEvent) => {
  if (!visible.value) return
  if (e.target instanceof HTMLElement) {
    if (e.target.classList.contains('arco-trigger-content')) {
      visible.value = false
    }
  }
}

onMounted(() => {
  document.addEventListener('click', listenerClick)
})

onUnmounted(() => {
  document.removeEventListener('click', listenerClick)
})
</script>

<template>
  <a-popover
    v-if="$slots.default"
    trigger="click"
    placement="right"
    position="br"
    v-model:popup-visible="visible"
    :content-style="{
      padding: 0,
      boxShadow: 'none',
      borderWidth: 0,
      background: 'transparent',
      width: '424px',
      display: 'flex',
      justifyContent: 'flex-end',
    }"
    :arrow-style="{ display: 'none' }"
    popup-container=".designer-app"
  >
    <div class="float-right inline-block" @click="visible = true">
      <slot></slot>
    </div>
    <template #content>
      <div class="select-field_cascader">
        <a-input-search
          :style="{ width: '100%' }"
          placeholder="请输入"
          v-model="queryText"
          @input="handleSearch"
        />
        <a-tabs
          :default-active-key="selectType"
          hide-content
          @change="handleChange"
        >
          <a-tab-pane key="step" :title="stepName || '步骤变量'"></a-tab-pane>
          <a-tab-pane
            key="system"
            title="系统变量"
            v-if="showSystemParams"
          ></a-tab-pane>
        </a-tabs>
        <a-cascader-panel
          :key="selectType"
          :options="selectParams"
          v-model="selectValue"
          @change="handleClick"
        />
      </div>
    </template>
  </a-popover>
</template>

<style lang="less">
.select-field_cascader {
  display: inline-block;
  background: #fff;
  box-shadow: 0 3px 8px 0 #0000001a;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  min-width: 210px;
  // width: 423px;

  .arco-input-search {
    border-width: 0px 0px 1px !important;
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-bottom: 1px solid #eaeaea;
    height: 32px;
  }
  .arco-input-wrapper {
    background: transparent !important;
  }

  .arco-cascader-panel {
    border-width: 0;
    box-shadow: none;

    .arco-cascader-option {
      width: 210px;
      .arco-cascader-option-label {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-right: 12px;
      }
    }

    .arco-empty {
      margin: 20px 76px;
    }
  }

  .arco-cascader-option-active {
    background: #f2f4f6;
  }

  .arco-tabs-tab {
    padding: 6px 0;
    line-height: 22px;

    + .arco-tabs-tab {
      margin-left: 4px;
    }

    .arco-tabs-tab-title {
      font-size: 13px;
      color: #666666;
      font-weight: 400;

      &:before {
        display: none;
      }
    }

    &:hover,
    &.arco-tabs-tab-active {
      .arco-tabs-tab-title {
        font-weight: 600;
      }
    }
  }
}
</style>
