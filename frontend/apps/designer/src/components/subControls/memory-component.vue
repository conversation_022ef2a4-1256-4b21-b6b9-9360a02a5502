<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  formData: { type: Object, default: () => {} },
})

// biome-ignore lint/suspicious/noExplicitAny: off
const handleChange = (e: boolean | any) => {
  props.formData.memories = e ? 5 : 0
}
// biome-ignore lint/suspicious/noExplicitAny: off
const marks: any = ref({ '1': 1, '5': '默认', '10': 10 })
</script>

<template>
  <a-form-item field="memories" label="会话记忆">
    <a-checkbox
      :model-value="formData.memories > 0"
      @change="handleChange"
    ></a-checkbox>
  </a-form-item>
  <a-form-item
    v-if="formData.memories > 0"
    field="memories"
    class="knowledge_slider"
    label="会话记忆数量（对数）"
  >
    <a-slider
      v-model="formData.memories"
      @input="formData.memories = $event"
      :min="1"
      :max="10"
      :step="1"
      :marks="marks"
    />
  </a-form-item>
</template>
