<template>
  <div class="field-form_box intention-config w-[100%] pb-[12px]">
    <a-form
      class="field-form form-custom"
      :model="form"
      :style="{ width: '100%' }"
      layout="inline"
    >
      <div ref="refSelector" style="width: 100%">
        <div
          class="disabled mb-[12px] flex flex-row items-center pl-[20px] text-[13px] text-[#999]"
        >
          <span>意图ID</span>
          <span class="ml-[12px]">意图原因</span>
        </div>
        <template v-for="(node, key) of form.nodeParam" :key="refreshKey">
          <div
            class="field-form_row inline-block w-[100%]"
            :class="{
              disabled: node.id === 0,
            }"
          >
            <div
              class="field-form_row-inner float-left"
              :class="{ 'field-form_full': !allowDelete }"
            >
              <a-form-item style="margin-right: 0">
                <span class="flex h-[28px] w-[20px] items-center">
                  <svg-icon
                    v-if="node.id > 0"
                    name="drop"
                    class="handle cursor-all-scroll"
                  />
                </span>
                <span class="intention-id">{{ node.id }}</span>
              </a-form-item>
              <a-form-item field="intention" style="width: calc(100% - 40px)">
                <a-input
                  :disabled="node.id === 0"
                  v-model="node.intention"
                  placeholder="请输入意图"
                />
              </a-form-item>
            </div>
            <svg-icon
              v-if="node.id > 0"
              name="minus"
              size="16px"
              @click="handleDelete(key)"
            />
          </div>
        </template>
      </div>
    </a-form>
    <div
      v-if="allowAdd"
      class="flex w-[48px] cursor-pointer flex-row items-center"
      @click="handleAdd"
    >
      <svg-icon name="add" size="16px" />
      <span
        class="ml-[4px] text-center text-[13px] font-normal not-italic leading-[22px]
          text-[#333333]"
        >添加</span
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSortable } from '@vueuse/integrations/useSortable'
import { reactive, ref, watch } from 'vue'

const props = defineProps({
  value: {
    type: Object,
    default: () => [],
  },
  allowAdd: {
    type: Boolean,
    default: true,
  },
  allowDelete: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['input'])
const refreshKey = ref(0)

// biome-ignore lint/suspicious/noExplicitAny: off
const row: any = { id: '', intention: '' }

// biome-ignore lint/suspicious/noExplicitAny: off
const form: any = reactive({
  nodeParam: [],
})
watch(
  () => props.value,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (data: any) => {
    form.nodeParam = data
  },
  { immediate: true },
)
const handleAdd = () => {
  row.id = form.nodeParam.length
  form.nodeParam.push({ ...row })
  emit('input', form.nodeParam)
}
const handleDelete = (index: number) => {
  form.nodeParam.splice(index, 1)

  // biome-ignore lint/suspicious/noExplicitAny: off
  form.nodeParam.forEach((item: any, index: number) => {
    item.id = index
  })
  refreshKey.value = new Date().getTime()
  emit('input', form.nodeParam)
}
const refSelector = ref(null)
const { option } = useSortable(refSelector, form.nodeParam, {
  handle: '.handle',
  // biome-ignore lint/suspicious/noExplicitAny: off
  onUpdate: (e: { oldIndex: any; newIndex: any }) => {
    const oldIndex = e.oldIndex - 1
    const newIndex = e.newIndex - 1
    const data = form.nodeParam
    const item: SelectorCondition = data[oldIndex]
    data.splice(oldIndex, 1)
    data.splice(newIndex, 0, item)

    // biome-ignore lint/suspicious/noExplicitAny: off
    data.forEach((item: any, index: number) => {
      item.id = index
    })
    form.nodeParam = data
    refreshKey.value = new Date().getTime()
    emit('input', form.nodeParam)
  },
  // biome-ignore lint/suspicious/noExplicitAny: off
  onMove: (e: any) => {
    if (e.related.className.endsWith('disabled')) {
      return false
    }
    return true
  },
})
option('animation', 200)
</script>

<style lang="less">
.intention-id {
  padding: 5px 0;
  border-radius: 4px 0 0 4px;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  border-left: 1px solid #ddd;
  background: #f5f5f5;
  width: 40px;
  box-sizing: border-box;
  text-align: center;
  height: 32px;
}

.intention-config .arco-input-wrapper {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  height: 32px;
}
</style>
