<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useFlowStore } from '../../stores/flow'
import FieldForm from './field-form.vue'
const props = withDefaults(
  // biome-ignore lint/suspicious/noExplicitAny: off
  defineProps<{ label?: string; config: any[]; formData: any }>(),
  {
    label: '',
    config: () => [],
    formData: () => ({}),
  },
)
const emit = defineEmits(['input'])
const flowStore = useFlowStore()

// biome-ignore lint/suspicious/noExplicitAny: off
const data: any = ref({})

const paramsData = computed(() => flowStore.getParamsData())
const configData = computed(() => {
  props.config[0].treeData = paramsData.value
  return props.config
})
// biome-ignore lint/suspicious/noExplicitAny: off
const sourceMap: any = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const sourceMap: any = {}

  // biome-ignore lint/suspicious/noExplicitAny: off
  paramsData.value.forEach((item: any) => {
    if (item.children) {
      // biome-ignore lint/suspicious/noExplicitAny: off
      item.children.forEach((val: any) => {
        val.title = val.name
        sourceMap[val.mapValue] = val
      })
    }
  })
  return sourceMap
})

watch(
  () => props.formData,
  () => {
    data.value = props.formData || {}
    if (!data.value?.layout) {
      data.value.layout = []
    }
  },
  { immediate: true },
)
watch(
  () => data.value,
  () => {
    emit('input', data.value)
  },
)
watch(
  () => data.value.layout,
  (data) => {
    // biome-ignore lint/suspicious/noExplicitAny: off
    data.forEach((item: any) => {
      const param = sourceMap.value[item.code]
      if (param) {
        item.name = param.name
      }
    })
  },
)
</script>

<template>
  <a-form-item :label="label" style="width: 100%"></a-form-item>
  <FieldForm
    :value="data.layout"
    :config="configData"
    @input="data.layout = $event"
  />
</template>
