<template>
  <div class="field-form_box w-[100%] pb-[12px]">
    <div class="mb-[8px] box-border flex w-[calc(100%_-_24px)] flex-row pl-[18px]" v-if="showLabel">
      <template v-for="item of fieldConfig">
        <div
          :style="{ width: item.width }"
          class="text-[13px] font-normal not-italic leading-[22px] text-[#999999]"
        >
          {{ item.label }}
        </div>
      </template>
    </div>
    <a-form
      class="field-form form-custom"
      :model="form"
      :style="{ width: '100%' }"
      layout="inline"
      ref="refSelector"
    >
      <div
        class="field-form_row w-[100%]"
        v-for="(node, key) of form.nodeParam"
        :key="'row_' + key + '_' + refreshKey"
      >
        <div
          class="field-form_row-inner float-left"
          :class="{
            'field-form_full': !allowDelete,
            'field-form_sort': enableSort,
          }"
        >
          <span class="absolute left-[0] flex h-[32px] w-[18px] items-center" v-if="enableSort">
            <svg-icon name="drop" class="handle cursor-all-scroll" />
          </span>
          <template v-for="item of fieldConfig">
            <a-form-item
              v-if="!item.hide && item.type === 'input'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :rules="rules[item.prop] || []"
              :style="{ width: item.width }"
            >
              <a-input
                v-model="node[item.prop]"
                :placeholder="item.placeholder"
                @change="(v: string) => handleChange(node, item.prop, v)"
              />
            </a-form-item>
            <a-form-item
              v-if="!item.hide && item.type === 'checkbox'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :rules="rules[item.prop] || []"
              :style="{ width: item.width }"
            >
              <a-checkbox
                v-model="node[item.prop]"
                :placeholder="item.placeholder"
                class="left-[0] right-[0] m-auto"
              ></a-checkbox>
            </a-form-item>
            <a-form-item
              v-if="!item.hide && item.type === 'select'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :disabled="!!item.disabled"
              :style="{ width: item.width }"
            >
              <a-select v-model="node[item.prop]" :placeholder="item.placeholder">
                <a-option
                  v-for="(option, _index) of item.options"
                  :value="option.value"
                  :disabled="!!option.disabled"
                  :key="'option_' + _index"
                  >{{ option.label }}</a-option
                >
              </a-select>
            </a-form-item>
            <a-form-item
              v-if="!item.hide && item.type === 'treeSelect'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :style="{ width: item.width }"
            >
              <a-tree-select
                :fieldNames="{
                  key: 'mapValue',
                  title: 'name',
                  children: 'children',
                }"
                :data="item.treeData"
                v-model="node[item.prop]"
                :placeholder="item.placeholder"
              ></a-tree-select>
            </a-form-item>

            <a-form-item
              v-if="item.type === 'template-input'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :style="{ width: item.width }"
            >
              <TemplateEditor
                :value="node[item.prop]"
                minHeight="30px"
                :mode="item.mode || 'input'"
                :placeholder="item.placeholder || '请选择/输入参数值'"
                :input-disabled="item.inputDisabled"
                @input="node[item.prop] = $event"
                @fieldInfo="(v) => fieldChange(node, v)"
                :selected="selected"
                :filterSystemCode="filterSystemCode"
              />
            </a-form-item>

            <a-form-item
              v-if="item.type === 'field-filling'"
              :field="`nodeParam[${key}][${item.prop}]`"
              :style="{ width: item.width }"
            >
              <FieldFilling
                :value="node[item.prop]"
                :placeholder="item.placeholder || '请选择/输入参数值'"
                :input-disabled="item.inputDisabled"
                @input="node[item.prop] = $event"
                @fieldInfo="(v) => fieldChange(node, v)"
                :selected="selected"
                :filterSystemCode="filterSystemCode"
              />
            </a-form-item>
          </template>
        </div>
        <svg-icon v-if="allowDelete" name="minus" size="16px" @click="handleDelete(key)" />
      </div>
    </a-form>
    <div
      v-if="allowAdd"
      class="flex w-[48px] cursor-pointer flex-row items-center"
      @click="handleAdd"
    >
      <svg-icon name="add" size="16px" />
      <span
        class="ml-[4px] text-center text-[13px] font-normal not-italic leading-[22px] text-[#333333]"
        >添加</span
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSortable } from '@vueuse/integrations/useSortable'
import { computed, reactive, ref, watch } from 'vue'
import TemplateEditor from './template-editor.vue'
import FieldFilling from './field-filling.vue'

const props = defineProps({
  config: {
    type: Object,
    default: () => [],
  },
  value: {
    type: Object,
    default: () => [],
  },
  allowAdd: {
    type: Boolean,
    default: true,
  },
  allowDelete: {
    type: Boolean,
    default: true,
  },
  selected: {
    type: Array,
    default: () => [],
  },
  filterSystemCode: {
    type: Boolean,
    default: false,
  },
  isFillField: {
    type: Boolean,
    default: false,
  },
  defaultRow: {
    type: Object,
  },
  enableSort: {
    type: Boolean,
    default: false,
  },
  showLabel: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['input', 'change'])

// biome-ignore lint/suspicious/noExplicitAny: off
let row: any = {}
const fieldConfig = ref(props.config)
const refreshKey = ref(0)

if (props.defaultRow) {
  row = props.defaultRow
} else {
  // biome-ignore lint/suspicious/noExplicitAny: off
  props.config.forEach((item: any) => {
    row[item.prop] = item.default
  })
}

// biome-ignore lint/suspicious/noExplicitAny: off
const form: any = reactive({
  nodeParam: [],
})
watch(
  () => props.value,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (data: any) => {
    form.nodeParam = data
  },
  { immediate: true },
)
watch(
  () => form.nodeParam,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (data: any) => {
    emit('change', data)
  },
  {
    deep: true,
  },
)
const handleAdd = () => {
  // @ts-ignore
  form.nodeParam.push({ ...row })
  refreshKey.value += 1
  emit('input', form.nodeParam)
}
const handleDelete = (index: number) => {
  form.nodeParam.splice(index, 1)
  refreshKey.value += 1
  emit('input', form.nodeParam)
}

// biome-ignore lint/suspicious/noExplicitAny: off
const rules: any = computed(() => {
  return {
    name: [
     {
        required: true,
        message: '请完善名称',
      },
      {
        // biome-ignore lint/suspicious/noExplicitAny: off
        validator: (value: string, cb: any) => {
          if (!value) {
            cb('请完善名称')
          }
        },
      },
    ],
    code: [
      {
        required: true,
        message: '请完善编码',
      },
      {
        // biome-ignore lint/suspicious/noExplicitAny: off
        validator: (value: string, cb: any) => {
          if (!value) {
            cb('请完善编码')
          }
          if (/^[A-Za-z_][A-Za-z0-9_]*$/.test(value)) {
            const repeatRow = form.nodeParam.filter(
              // biome-ignore lint/suspicious/noExplicitAny: off
              (item: any) => item.code === value,
            )
            if (repeatRow.length > 1) {
              cb('编码不允许重复')
            } else {
              cb()
            }
          } else {
            cb('编码支持使用字母、数字和下划线')
          }
        },
      },
    ],
  }
})
const fieldChange = (node: EditorParamsType, value: SelectParamsType) => {
  if (!props.isFillField) return
  if (value.value && node.value?.content === value.value) return
  // 遍历 node 的属性并进行赋值
  for (const key in node) {
    if (Object.prototype.hasOwnProperty.call(node, key) && value[key] !== undefined) {
      switch (key) {
        case 'type':
          node[key] = value[key] ?? 'string'
          break
        case 'required':
          node[key] = !!value[key]
          break
        case 'schema':
          node[key] = JSON.parse(JSON.stringify(value[key] || []))
          break
        default:
          node[key] = value[key]
          break
      }
    }
  }
}

// biome-ignore lint/suspicious/noExplicitAny: off
const handleChange = (node: any, prop: string, value: string) => {
  if (prop === 'code') {
    node[prop] = value.trim()
  }
}

const refSelector = ref(null)
const { option } = useSortable(refSelector, form.nodeParam, {
  handle: '.handle',
  onUpdate: (e: { oldIndex: number; newIndex: number }) => {
    const oldIndex = e.oldIndex
    const newIndex = e.newIndex
    const data = form.nodeParam
    const item = data[oldIndex]
    data.splice(oldIndex, 1)
    data.splice(newIndex, 0, item)
    form.nodeParam = data
    refreshKey.value += 1
    emit('input', form.nodeParam)
  },

  // biome-ignore lint/suspicious/noExplicitAny: off
  onMove: (e: any) => {
    if (e.related.className.endsWith('disabled')) {
      return false
    }
    return true
  },
})
option('animation', 200)
defineExpose({
  add: handleAdd,
})
</script>
