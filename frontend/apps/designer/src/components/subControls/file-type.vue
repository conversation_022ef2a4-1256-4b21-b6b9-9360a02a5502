<script setup lang="ts">
import { computed, onMounted, type Ref, ref, watch } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  showTips: {
    type: Boolean,
    default: false,
  },
  support: {
    type: Array,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const files: Ref<string[]> = ref([])
const customFile = ref('')
const options = computed(() => {
  return [
    { label: 'Word文档 (doc, docx)', value: 'doc,docx' },
    { label: 'PDF文件 (pdf)', value: 'pdf' },
    { label: 'Excel文档 (xls, xlsx)', value: 'xls,xlsx' },
    { label: 'PowerPoint文档 (ppt, pptx)', value: 'ppt,pptx' },
    { label: '文本文件 (txt)', value: 'txt' },
    {
      label: '图片文件 (jpg, jpeg, bmp, gif, png, tif, webp)',
      value: 'jpg,jpeg,bmp,gif,png,tif,webp',
    },
    {
      label: '视频文件 (avi, mpg, mpeg, mov, wav, ram, mp3, mp4 )',
      value: 'avi,mpg,mpeg,mov,wav,ram,mp3,mp4',
    },
    { label: '自定义', value: 'custom' },
  ]
})

onMounted(() => {
  let filesTxt = props.formData.uploadables || ''
  options.value.forEach((item) => {
    if (filesTxt.indexOf(item.value) > -1) {
      files.value.push(item.value)
      filesTxt = filesTxt.replace(item.value, '')
    }
  })
  customFile.value = filesTxt
    .split(',')
    .filter((v: string) => !!v)
    .join(',')
  if (customFile.value) {
    files.value.push('custom')
  }
})

const hasCustom = computed(() => {
  return files.value.includes('custom')
})

watch(
  () => hasCustom.value,
  (v) => {
    if (!v) {
      customFile.value = ''
      props.formData.customFile = ''
    }
  },
)

watch(
  () => [customFile.value, files.value],
  () => {
    const data = JSON.parse(JSON.stringify(files.value))
    if (hasCustom.value) {
      data.splice(data.indexOf('custom'), 1)
    }
    props.formData.uploadables = [...data, customFile.value].filter((v: string) => !!v).join(',')
  },
)

const rules = computed(() => {
  return [
    {
      required: true,
      message: '请完善自定义文件类型',
    },
    {
      // biome-ignore lint/suspicious/noExplicitAny: off
      validator: (value: string, cb: any) => {
        const content = value.replace(/\s+/g, '').replace(/;/g, ',').split(',')
        const showError = content.some((t) => {
          // 排查空场景
          if (!t) return false
          return !/^\w+$/.test(t)
        })
        if (showError) {
          cb('自定义文件类型格式不正确')
        } else {
          cb()
        }
      },
    },
  ]
})
const fileOptions = computed(() => {
  if (!props.support?.length) return options.value
  return options.value.filter((item: any) => {
    return props.support?.some((v) => v === item.value)
  })
})
</script>

<template>
  <a-alert type="normal" class="alert-normal" v-if="showTips">
    <svg-icon name="tips" size="14px" />
    word、pdf、txt 文件已支持文档分析，其他类型文档可通过插件自定义分析文档。
  </a-alert>
  <a-form-item label="可传文件类型" :disabled="disabled">
    <a-checkbox-group direction="vertical" v-model="files">
      <a-checkbox v-for="(item, key) in fileOptions" :key="'file_' + key" :value="item.value">{{
        item.label
      }}</a-checkbox>
    </a-checkbox-group>
  </a-form-item>
  <a-form-item v-if="hasCustom" :rules="rules" class="custom-file" label="" :disabled="disabled">
    <a-textarea
      v-model="customFile"
      style="min-height: 84px"
      placeholder="请输入文件扩展名，多个请用逗号分隔，如：xml, mp3"
      auto-size
    />
  </a-form-item>
</template>

<style lang="less">
.arco-alert-normal.alert-normal {
  padding: 9px 12px;
  border-radius: 4px;
  background: #f8f8f8;

  svg {
    margin-right: 4px;
  }

  .arco-alert-content {
    color: #666666;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
}
.custom-file .arco-form-item-label {
  visibility: hidden;
}
</style>
