<template>
  <div class="field-form_box w-[100%] pb-[12px]" :style="formBoxStyle">
    <a-form
      class="field-form form-custom field-form_params"
      :model="form"
      :style="{ width: '100%' }"
      layout="inline"
      :disabled="disabled"
      ref="refSelector"
    >
      <div
        class="inline-block w-full"
        :style="itemLeft"
        v-for="(node, key) of form.nodeParam"
        :key="'row_' + key + '_' + refreshKey"
      >
        <div class="field-form_row w-[100%]">
          <span
            class="handle absolute z-10 flex h-[32px] w-[18px] cursor-all-scroll items-center"
            :style="sortLeft"
            v-if="enableSort"
          >
            <svg-icon name="drop" />
          </span>
          <span
            v-if="isSub"
            class="field-form_row-line"
            :style="getStyle(key)"
          ></span>
          <svg-icon
            class="field-form_row-collapse"
            v-if="node.type === OBJECT_ARRAY"
            @click="node.collapse = !node.collapse"
            :class="{ '-rotate-90 transform': node.collapse }"
            name="down"
            size="16px"
          />
          <div
            class="field-form_row-inner float-left"
            :class="{ 'field-form_full': !allowDelete }"
            :style="innerRowStyle"
          >
            <template v-if="enableGroup && node.control === 'GroupLine'">
              <div class="arco-form-item_group-line w-[100%]">
                <a-form-item
                  label="字段分组"
                  :field="`nodeParam[${key}].control`"
                  :required="true"
                >
                  <a-input
                    v-model="node.name"
                    placeholder="请输入分组名称"
                    :disabled="disabled"
                  />
                </a-form-item>
              </div>
            </template>
            <template v-else v-for="item of fieldConfig">
              <a-form-item
                v-if="!item.hide && item.type === 'input'"
                :field="`nodeParam[${key}][${item.prop}]`"
                :rules="rules[item.prop] || []"
                :style="{ width: item.width }"
              >
                <a-input
                  v-model="node[item.prop]"
                  :placeholder="item.placeholder"
                  @change="(v: string) => handleChangeValue(node, item.prop, v)"
                />
              </a-form-item>
              <a-form-item
                v-if="!item.hide && item.type === 'select'"
                :field="`nodeParam[${key}][${item.prop}]`"
                :disabled="!!item.disabled || disabled"
                :style="{ width: item.width }"
              >
                <a-select
                  v-model="node[item.prop]"
                  :placeholder="item.placeholder"
                  @change="(v: string | any) => handleChange(v, node)"
                >
                  <a-option
                    v-for="(option, _index) of getOptions(
                      item.options,
                      item,
                      node?.type,
                    )"
                    :value="option.value"
                    :disabled="!!option.disabled"
                    :key="'option_' + _index"
                    >{{ option.label }}</a-option
                  >
                </a-select>
              </a-form-item>
              <a-form-item
                v-if="!item.hide && item.type === 'treeSelect'"
                :field="`nodeParam[${key}][${item.prop}]`"
                :style="{ width: item.width }"
              >
                <a-tree-select
                  :fieldNames="{
                    key: 'mapValue',
                    title: 'name',
                    children: 'children',
                  }"
                  :data="item.treeData"
                  v-model="node[item.prop]"
                  :placeholder="item.placeholder"
                ></a-tree-select>
              </a-form-item>

              <a-form-item
                v-if="item.type === 'field-filling'"
                :field="`nodeParam[${key}][${item.prop}]`"
                :style="{ width: item.width }"
              >
                <FieldFilling
                  :value="node[item.prop]"
                  :placeholder="item.placeholder || '请选择/输入参数值'"
                  :input-disabled="item.inputDisabled"
                  :isCustom="level !== 0"
                  :customParams="getFieldParams()"
                  @input="node[item.prop] = $event"
                  @fieldInfo="(v) => fieldChange(node, v)"
                />
              </a-form-item>

              <a-form-item
                v-if="item.type === 'form-field'"
                :field="`nodeParam[${key}][${item.prop}]`"
                :style="{ width: item.width }"
              >
                <TemplateEditor
                  :value="node[item.prop]"
                  minHeight="30px"
                  :mode="item.mode || 'input'"
                  :placeholder="item.placeholder || '请选择/输入参数值'"
                  :input-disabled="item.inputDisabled"
                  @input="node[item.prop] = $event"
                  @fieldInfo="(v) => fieldChange(node, v)"
                  :isCustom="level !== 0"
                  :customParams="getFieldParams()"
                />
              </a-form-item>
            </template>
          </div>
          <a-popover
            position="br"
            trigger="click"
            content-class="skill-popover-content skill-advanced-config"
            popup-container=".designer-app"
            v-if="hasAdvancedconfig"
          >
            <span
              class="field-form_btn-box skill-svg-hover_primary"
              :class="{
                invisible: enableGroup && node.control === 'GroupLine',
              }"
            >
              <svg-icon name="setting" size="16px" />
            </span>
            <template #content>
              <template v-for="item of fieldAdvancedConfig">
                <a-form-item
                  v-if="item.type !== 'option-group'"
                  :label="item.label"
                  :field="`nodeParam[${key}][${item.prop}]`"
                  v-show="
                    item.prop !== 'thousands' || node.control === 'InputNumber'
                  "
                >
                  <a-checkbox
                    v-model="node[item.prop]"
                    v-if="item.type === 'checkbox'"
                  ></a-checkbox>
                  <a-select
                    v-if="item.type === 'select'"
                    v-model="node[item.prop]"
                    :placeholder="item.placeholder"
                    :disabled="!!item.disabled"
                  >
                    <a-option
                      v-for="(option, _index) of item.options"
                      :value="option.value"
                      :disabled="!!option.disabled"
                      :key="'option_' + _index"
                      >{{ option.label }}</a-option
                    >
                  </a-select>
                </a-form-item>
                <a-form-item
                  :label="item.label"
                  :field="`nodeParam[${key}][${item.prop}]`"
                  v-if="
                    item.type === 'option-group' &&
                    node.control === 'RadioGroup'
                  "
                >
                  <span
                    class="cursor-pointer pl-[5px]"
                    @click="onClickOptionGroup(key)"
                  >
                    <svg-icon name="edit" size="16px"></svg-icon>
                  </span>
                </a-form-item>
              </template>
            </template>
          </a-popover>
          <span class="field-form_btn" v-if="hasPaddingLeft">
            <svg-icon
              v-if="node.type === OBJECT_ARRAY && allowAdd"
              name="add-sublevel"
              size="16px"
              @click="handleAddSubLevel(node)"
            ></svg-icon>
          </span>
          <svg-icon
            class="field-form_btn"
            v-if="allowDelete"
            name="minus"
            size="16px"
            @click="handleDelete(key)"
          />
        </div>
        <div
          class="field-form_sub w-[100%]"
          v-if="
            node.type === OBJECT_ARRAY && !node.collapse && node?.schema?.length
          "
        >
          <output-params
            :is-sub="true"
            :key="'dynamicForms' + key"
            :value="node.schema"
            :config="config"
            :allowAdd="allowAdd"
            :allow-delete="allowDelete"
            :level="level + 1"
            :isFillField="isFillField"
            :defaultRow="defaultRow"
            :parentNode="node"
            :enable-sort="enableSort"
            @input="node.schema = $event"
          />
        </div>
      </div>
    </a-form>
    <output-params-toolbar
      v-if="allowAdd && level === 0"
      :enable-group="enableGroup"
      @addRow="handleAdd"
      @addGroup="handleAddGroup"
    />
  </div>
  <option-group
    v-if="visible"
    :visible="visible"
    :form-data="form.nodeParam[selectRowIndex]"
    @close="onClose"
  ></option-group>
</template>

<script setup lang="ts">
import { useSortable } from '@vueuse/integrations/useSortable'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useFlowStore } from '../../stores/flow'
import { OptionType, OutputParamsType } from '../../ts/editor'
import { generateCode } from '../../utils'
import outputParamsToolbar from './output-params-toolbar.vue'
import TemplateEditor from './template-editor.vue'
import FieldFilling from './field-filling.vue'

const props = defineProps({
  config: {
    type: Object,
    default: () => [],
  },
  value: {
    type: Object,
    default: () => [],
  },
  allowAdd: {
    type: Boolean,
    default: true,
  },
  allowDelete: {
    type: Boolean,
    default: true,
  },
  isSub: {
    type: Boolean,
    default: false,
  },
  level: {
    type: Number,
    default: 0,
  },
  isFillField: {
    type: Boolean,
    default: false,
  },
  defaultRow: {
    type: Object,
  },
  parentNode: {
    type: Object,
  },
  enableSort: {
    type: Boolean,
    default: false,
  },
  enableGroup: {
    type: Boolean,
    default: false,
  },
})
const OBJECT_ARRAY = ref('array<object>')
const emit = defineEmits(['input'])
const flowStore = useFlowStore()
let row: any = {}
const refreshKey = ref(0)
const visible = ref(false)
const selectRowIndex = ref(-1)

if (props.defaultRow) {
  row = props.defaultRow
} else {
  // biome-ignore lint/suspicious/noExplicitAny: off
  props.config.forEach((item: any) => {
    row[item.prop] = item.default
  })
}

const fieldConfig = computed(() => {
  const config = JSON.parse(JSON.stringify(props.config))
  if (props.level > 3) {
    const typeConfig = config.find(
      (item: { prop: string }) => item.prop === 'type',
    )
    if (typeConfig && typeConfig.options?.length) {
      typeConfig.options = typeConfig.options.filter(
        (item: { value: string }) => item.value !== OBJECT_ARRAY.value,
      )
    }
  }
  // biome-ignore lint/suspicious/noExplicitAny: off
  return config.filter((item: any) => !item.advancedconfig)
})
const fieldAdvancedConfig = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  return props.config.filter((item: any) => item.advancedconfig)
})
const disabled = computed(() => {
  return !props.allowAdd && !props.allowDelete
})
// biome-ignore lint/suspicious/noExplicitAny: off
const form: any = reactive({
  nodeParam: [],
})
const hasPaddingLeft = computed(() => {
  if (props.level > 0) return true
  return form.nodeParam.some(
    (item: { type: string }) => item.type === OBJECT_ARRAY.value,
  )
})
const formBoxStyle = computed(() => {
  let leftWidth = 0
  if (hasPaddingLeft.value) {
    leftWidth += 24
  }
  return `padding-left: ${leftWidth}px`
})
watch(
  () => props.value,
  // biome-ignore lint/suspicious/noExplicitAny: off
  (data: any) => {
    form.nodeParam = data
  },
  { immediate: true },
)
const handleAdd = () => {
  // @ts-ignore
  form.nodeParam.push({ ...row })
  refreshKey.value += 1
  emit('input', form.nodeParam)
}
const handleAddGroup = () => {
  // @ts-ignore
  form.nodeParam.push({
    ...row,
    type: 'group',
    control: 'GroupLine',
    code: `group_${generateCode(6)}`,
  })
  refreshKey.value += 1
  emit('input', form.nodeParam)
}
const handleDelete = (index: number) => {
  form.nodeParam.splice(index, 1)
  refreshKey.value += 1
  emit('input', form.nodeParam)
}
// biome-ignore lint/suspicious/noExplicitAny: off
const rules: any = computed(() => {
  return {
    name: [
      {
        required: true,
        message: '请完善名称',
      },
    ],
    code: [
      {
        required: true,
        message: '请完善编码',
      },
      {
        // biome-ignore lint/suspicious/noExplicitAny: off
        validator: (value: string, cb: any) => {
          if (/^[A-Za-z_][A-Za-z0-9_]*$/.test(value)) {
            const repeatRow = form.nodeParam.filter(
              // biome-ignore lint/suspicious/noExplicitAny: off
              (item: any) => item.code === value,
            )
            if (repeatRow.length > 1) {
              cb('编码不允许重复')
            } else {
              cb()
            }
          } else {
            cb('编码支持使用字母、数字和下划线')
          }
        },
      },
    ],
  }
})
const hasAdvancedconfig = computed(() => {
  return props.config.some(
    ({ advancedconfig }: { advancedconfig: boolean | undefined }) =>
      !!advancedconfig,
  )
})
const leftWidth = computed(() => {
  let width = 24
  if (hasPaddingLeft.value) width += 24
  return width
})

const sortLeft = computed(() => {
  const num = props.level - 1
  const value = num * 24 + leftWidth.value + 18
  return `left: -${value}px;`
})

const itemLeft = computed(() => {
  if (!props.enableSort) return ''
  const num = props.level - 1
  const value = num * 24 + leftWidth.value + 18
  return `margin-left: -${value}px; padding-left: ${value}px; width: calc(100% + ${value}px);`
})

const innerRowStyle = computed(() => {
  let width = leftWidth.value
  if (hasAdvancedconfig.value) width += 40
  return `width: calc(100% - ${width}px);`
})
const handleChange = (_: string | any, node: any) => {
  if (node.type === OBJECT_ARRAY.value) {
    if (!node.schema) node.schema = []
    node.collapse = false
  } else {
    node.schema = []
  }
  node.options = []
  node.thousands = node.control === 'InputNumber'
}
const handleAddSubLevel = (node: any) => {
  node.schema.push({ ...row })
  node.collapse = false
}
const getStyle = (index: number) => {
  if (index > 0) {
    const data = form.nodeParam?.[index - 1]
    if (data?.collapse) return 'display: none'
    let num = data?.schema?.length || 0
    if (num) {
      const loop = (data: { schema: any[] }[]) => {
        data.forEach((item: any) => {
          const len = item?.schema?.length || 0
          if (len && !item.collapse) {
            num += len
            loop(item.schema)
          }
        })
      }
      loop(data.schema)
    }
    if (num) {
      return `top: -${(num + 1) * 43}px`
    }
  }
  return 'display: none'
}
const fieldChange = (node: EditorParamsType, value: SelectParamsType) => {
  if (!props.isFillField) return
  if (value.value && node.value?.content === value.value) return
  const oldType = node.type
  // 遍历 node 的属性并进行赋值
  for (const key in node) {
    if (
      Object.prototype.hasOwnProperty.call(node, key) &&
      value[key] !== undefined
    ) {
      switch (key) {
        case 'type':
          node[key] = value[key] ?? 'string'
          break
        case 'required':
          node[key] = !!value[key]
          break
        case 'schema':
          node.schema = []
          break
        default:
          node[key] = value[key]
          break
      }
    }
  }
  // 类型未改变，不初始化控件类型
  if (node.type === oldType) return
  // 避免重置类型后，备选项被清空
  if (node.control === 'RadioGroup' && ['number', 'string'].includes(node.type))
    return
  switch (node.type) {
    case 'number':
      node.control = 'InputNumber'
      node.thousands = true
      break
    case 'date':
      node.control = 'DatePicker'
      break
    case 'array<object>':
      node.control = 'DataTable'
      break
    default:
      node.control = 'Input'
  }
}
const handleChangeValue = (node: any, prop: string, value: string) => {
  if (prop === 'code') {
    node[prop] = value.trim()
  }
}
const getFieldParams = () => {
  if (props.level === 0) return
  const content = props.parentNode?.value?.content || ''
  const [, id, code] = content?.split('_') || []
  if (id && code) {
    const node = flowStore.nodeMap[id]
    const outputs = node?.data?.outputs ?? []
    const item = outputs.find((item: any) => item.code === code)
    return [
      {
        name: '系统变量',
        code: 'system',
        selectable: false,
        iconName: 'system',
        children: flowStore.getSystemParamData(true),
      },
      {
        name: props.parentNode?.name || '参数',
        label: props.parentNode?.name,
        code: 'paramChild',
        value: 'paramChild',
        selectable: false,
        children:
          item?.schema
            ?.filter((v: OutputParamsType) => v.type !== OBJECT_ARRAY.value)
            ?.map((v: OutputParamsType) => {
              return {
                ...v,
                mapValue: `{{$${v.code}}}`,
                value: v.code,
                title: v.name,
                field: v.code,
                label: v.name,
              }
            }) || [],
      },
    ]
  }
  return []
}
const getOptions = (
  options: OptionType,
  item: any,
  type: string = 'string',
) => {
  const option = props.config.find(
    (v: { prop: string }) => v.prop === item.prop,
  )
  if (typeof option.filterOptions === 'function') {
    return option.filterOptions(type, props.level)
  }
  return options
}

const onClickOptionGroup = (index: number) => {
  visible.value = true
  selectRowIndex.value = index
}

const onClose = () => {
  visible.value = false
  selectRowIndex.value = -1
}

const refSelector = ref(null)
const { option } = useSortable(refSelector, form.nodeParam, {
  handle: '.handle',
  onUpdate: (e: { oldIndex: number; newIndex: number }) => {
    const oldIndex = e.oldIndex
    const newIndex = e.newIndex
    const data = JSON.parse(JSON.stringify(form.nodeParam))
    const item = data[oldIndex]
    data.splice(oldIndex, 1)
    data.splice(newIndex, 0, item)
    form.nodeParam = JSON.parse(JSON.stringify(data))
    emit('input', form.nodeParam)
    nextTick(() => {
      refreshKey.value += 1
    })
  },
  onMove: (e: any) => {
    if (e.related.className.endsWith('disabled')) {
      return false
    }
    return true
  },
})
option('animation', 200)
</script>

<style lang="less">
.skill-advanced-config {
  .arco-select-view-single.arco-select-view-disabled {
    background-color: transparent;
    padding: 0;
    color: #333;
    .arco-select-view-suffix {
      display: none;
    }
  }
  .arco-form-item-label-col {
    width: 90px;
    justify-content: flex-start;
  }
}
</style>
