<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { Ref, computed, onMounted, ref } from 'vue'
import { onMapping, spaceGUID } from '../../api'
import { findPlanParams, onQueryPlan, onQueryPlanGroup } from '../../api/plan'
import { useFlowStore } from '../../stores/flow'
import FieldForm from './field-form.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
// biome-ignore lint/suspicious/noExplicitAny: off
let data: any = ref([])
let inputKey: Ref<number> = ref(0)
const configInfo = [
  {
    prop: 'name',
    placeholder: '请选择',
    width: '40%',
    type: 'select',
    options: [],
    disabled: true,
  },
  {
    prop: 'required',
    placeholder: '请选择',
    width: '20%',
    type: 'select',
    options: [
      { label: '必填', value: true },
      { label: '非必填', value: false },
    ],
    disabled: true,
  },
  {
    prop: 'value',
    placeholder: '',
    width: '40%',
    type: 'field-filling',
  },
]
const flowStore = useFlowStore()
// biome-ignore lint/suspicious/noExplicitAny: off
let configData: any = ref(configInfo)
let loading: Ref<boolean> = ref(false)
let loadingSearch = ref(false)
const treeData = ref<Array<{ name: string; value: string; children: any[] }>>([])

onMounted(() => {
  handleSearch('')
  if (props.formData.id) updatePlanParams()
})

const handleSearch = (value: string) => {
  loadingSearch.value = true
  let currentItem: { planGUID: string } = {
    planGUID: '',
  }
  if (props.formData.id) {
    currentItem = data.value.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => item.planGUID === props.formData.id,
    )
  }
  onQueryPlan(value)
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then((res: any) => {
      let hasSelectValue = false
      data.value =
        // biome-ignore lint/suspicious/noExplicitAny: off
        res.data.data?.map((item: any) => {
          if (item.planGUID === currentItem?.planGUID) {
            hasSelectValue = true
          }
          return {
            ...item,
            label: `${item.planName}（${item.planCode}）`,
            value: item.planGUID,
          }
        }) || []
      if (!hasSelectValue && currentItem?.planGUID) {
        data.value.push(currentItem)
      }
      loadingSearch.value = false
    })
    .catch(() => {
      data.value = []
      loadingSearch.value = false
    })
}

const buildTree = (
  group: any[],
  parentId?: string | null
): Array<{ name: string; value: string; children: any[] }> => {
  return group
    .filter((f) => f.parentId === parentId)
    .map((item: { id: string; name: string; rules: any[] }) => {
      const children =
        item.rules && item.rules.length > 0
          ? item.rules?.map((value) => ({ value: value.id, name: value.name })) || []
          : buildTree(group, item.id)
      return {
        name: item.name,
        value: item.id,
        children,
      }
    })
}

const updatePlanParams = async () => {
  const group = (await onQueryPlanGroup(props.formData.id))?.data?.data || []
  treeData.value = buildTree(group, null)

  const params = (await findPlanParams(props.formData.id))?.data?.data || []
  const inputMap: Record<string, InputParamValue> = {}
  props.formData.inputs?.forEach((item: InputParams) => {
    inputMap[item.code] = item?.value
  })
  // biome-ignore lint/suspicious/noExplicitAny: off
  props.formData.inputs = params.map((item: any) => {
    return {
      code: item.ParamsId,
      name: item.ParamsName,
      required: !!item.Required,
      type: 'string',
      description: '',
      value: inputMap[item.ParamsId] || { type: 'literal', content: '' },
    }
  })
  inputKey.value += 1
}

const sourceMap = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const map: any = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.getParamsData().forEach((item: any) => {
    if (item.children) {
      if (item.code === 'system') return
      // biome-ignore lint/suspicious/noExplicitAny: off
      item.children.forEach(({ value, description, name, type }: any) => {
        map[value] = { description, name, type, code: value }
      })
    }
  })
  return map
})

const systemMap = computed(() => {
  const data: InputParamsMap = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.getSystemParamData().forEach((item: any) => {
    data[item.value] = item
  })
  return data
})

const handleMapping = () => {
  if (!props.formData?.inputs?.length) return
  loading.value = true
  onMapping({
    source: Object.values(sourceMap.value),
    target: props.formData.inputs,
  })
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      loading.value = false
      if (typeof data?.data === 'object') {
        // biome-ignore lint/suspicious/noExplicitAny: off
        const map: any = {}
        // biome-ignore lint/suspicious/noExplicitAny: off
        data.data.forEach((item: any) => {
          map[item.code] = item?.value
        })
        const inputs = JSON.parse(JSON.stringify(props.formData.inputs))
        // biome-ignore lint/suspicious/noExplicitAny: off
        inputs.forEach((item: any) => {
          const currentItem = map[item.code]
          const refField = currentItem?.content
          if (
            refField &&
            (sourceMap.value[refField] || systemMap.value[refField])
          ) {
            item.value = { type: 'ref', content: refField }
          }
        })
        props.formData.inputs = inputs
        inputKey.value += 1
      }
    })
    .catch(() => {
      loading.value = false
    })
}
const handleChange = () => {
  props.formData.inputs = []
  treeData.value = []
  updatePlanParams()
}

const columns = computed(() => [
  { title: '参数编码', dataIndex: 'code' },
  { title: '类型', dataIndex: 'type' },
  { title: '说明', dataIndex: 'description' },
])

const handleEdit = () => {
  if (!props.formData.id) {
    Message.warning('请先选择方案')
  }
  window.open(
    `/std/42001401/08dce8d6-936f-4ba1-858f-d8421f16a3a6?mode=2&autoTitle=true&_mp=crumbs&title=方案&SpaceGUID=${spaceGUID}&oid=${props.formData.id}`,
  )
}
</script>

<template>
  <a-form-item
    label="选择方案"
    field="id"
    :rules="[{ required: true, message: '请选择方案' }]"
  >
    <a-select
      v-model="formData.id"
      :allow-search="true"
      :loading="loadingSearch"
      placeholder="请输入方案名称、编码"
      @search="handleSearch"
      @change="handleChange"
      :filter-option="false"
    >
      <a-option v-for="item of data" :value="item.value">{{
        item.label
      }}</a-option>
    </a-select>
  </a-form-item>
  <a-form-item label="方案预览" :hide-label="true" v-if="!!formData.id">
    <div class="pd-input-textarea skill-card-plan_tree w-[100%]">
      <div class="pd-input-textarea__header">
        <div class="pd-input-textarea__header-title">方案预览</div>
        <div class="pd-input-textarea__header-btn" @click="handleEdit">
          <svg-icon name="edit" size="16px" color="#266EFF"></svg-icon
          >编辑检查方案
        </div>
      </div>
      <div
        class="pd-input-textarea__body max-h-[224px] min-h-[224px] overflow-y-auto px-[16px]
          py-[5px]"
      >
        <a-tree
          ref="tree"
          :data="treeData"
          :default-expand-all="true"
          block-node
        >
          <template #title="nodeData">
            <div
              class="flex items-center text-[13px] font-normal text-[#333333]"
            >
              <template v-if="nodeData.children">
                <svg-icon name="folder-fill" size="16px" />
                <span class="ml-[6px] inline-block">{{ nodeData.name }}</span>
              </template>
              <template v-else>
                {{ nodeData.name }}
              </template>
            </div>
          </template>
        </a-tree>
      </div>
    </div>
  </a-form-item>

  <template v-if="!!formData.inputs?.length">
    <a-form-item label="输入参数" class="form-item_title">
      <div class="w-full">
        <a-popover
          position="top"
          :content-style="{ padding: '4px 12px 8px 12px' }"
          popup-container=".designer-app"
        >
          <a-button
            type="primary"
            class="float-right rounded-[4px]"
            :loading="loading"
            @click="handleMapping"
          >
            参数自动映射
            <span class="ml-[4px]">
              <svg-icon name="help" size="16px" color="white" />
            </span>
          </a-button>
          <template #content>
            <div class="w-[120px] text-[13px] leading-[22px] text-[#333333]">
              通过提示模板参数，自动生成输入参数。
            </div>
          </template>
        </a-popover>
      </div>
    </a-form-item>
    <FieldForm
      :key="'field-form_' + inputKey"
      :value="formData.inputs"
      :config="configData"
      :allowAdd="false"
      :allowDelete="false"
    />
  </template>

  <a-form-item
    label="输出参数"
    class="form-item_title"
    v-if="!!formData.id"
  ></a-form-item>
  <a-table
    v-if="!!formData.id"
    :pagination="false"
    class="custom-grid"
    :columns="columns"
    :data="formData.outputs"
  />
</template>

<style lang="less">
.skill-card-plan_tree {
  .arco-tree-node-switcher-icon {
    color: #999999;
  }
  .arco-tree-node-title:hover {
    background-color: transparent;
  }
  .arco-tree-node {
    cursor: unset;
  }
}
</style>
