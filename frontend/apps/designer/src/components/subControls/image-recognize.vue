<script setup lang="ts">
import { computed, onMounted, type Ref, ref } from 'vue'
import { getOcrServiceList } from '../../api/image-recognize'
import { useConfigStore } from '../../stores/config'
import { CARD_TYPE } from '../../stores/constants'

const props = defineProps({
  config: {
    type: Object,
    default: () => [],
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
})

const configStore = useConfigStore()
const loadingSearch = ref(false)
const options: Ref<{ value: string; label: string }[]> = ref([])
const prop = computed(() => props.config[0].prop)

const handleSearch = (v: string) => {
  loadingSearch.value = true
  // biome-ignore lint/suspicious/noExplicitAny: off
  let currentItem: any = null
  if (props.formData.ocrService) {
    currentItem = options.value.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => item.value === props.formData.ocrService,
    )
  }
  getOcrServiceList(v)
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      let hasSelectValue = false
      options.value =
        // biome-ignore lint/suspicious/noExplicitAny: off
        data?.data?.map((item: any) => {
          if (item?.modelInstanceCode === currentItem?.value) {
            hasSelectValue = true
          }
          return {
            value: item.modelInstanceCode,
            label: item.modelInstanceName,
          }
        }) || []
      if (!hasSelectValue && currentItem?.value) {
        options.value.push(currentItem)
      }
      loadingSearch.value = false
    })
    .catch(() => {
      loadingSearch.value = false
      options.value = []
    })
}

const isImageAnalysis = computed(() => {
  return props.formData.type === CARD_TYPE.IMAGE_ANALYSIS
})
// biome-ignore lint/suspicious/noExplicitAny: off
const handleChangeType = (v: string | any) => {
  if (v !== 'ocr') {
    props.formData.ocrService = ''
  }
  if (isImageAnalysis.value) {
    props.formData.templateId = ''
    props.formData.inputs = []
    props.formData.outputs = []
  }
  updateConfig()
}

onMounted(() => {
  handleSearch('')
  updateConfig()
})

const updateConfig = () => {
  const asMessage: { hide?: boolean } =
    configStore.configData.find(
      (item: { prop: string }) => item.prop === 'asMessage',
    ) || {}
  if (asMessage) asMessage.hide = props.formData.recognizeType === 'ocr'
  if (asMessage.hide) props.formData.asMessage = false
}
</script>

<template>
  <a-form-item v-for="item in config" :field="item.prop" :label="item.label">
    <a-radio-group v-model="formData[item.prop]" @change="handleChangeType">
      <a-radio
        v-for="option in item.options"
        :value="option.value"
        :key="option.value"
        >{{ option.label }}</a-radio
      >
    </a-radio-group>
  </a-form-item>
  <a-form-item
    v-if="formData[prop] === 'ocr'"
    label="视觉识别服务"
    field="ocrService"
    :rules="[{ required: true, message: '请选择视觉识别服务' }]"
  >
    <a-select
      v-model="formData.ocrService"
      :allow-search="true"
      :loading="loadingSearch"
      placeholder="请输入"
      @search="handleSearch"
      :filter-option="false"
    >
      <a-option v-for="item of options" :value="item.value">{{
        item.label
      }}</a-option>
    </a-select>
  </a-form-item>
</template>
