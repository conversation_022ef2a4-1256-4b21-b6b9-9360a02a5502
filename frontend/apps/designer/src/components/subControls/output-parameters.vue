<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { OUTPUT_DEFAULT_DATA, OUTPUT_TYPE } from '../../stores/constants'
import FieldForm from './field-form.vue'
const props = defineProps({
  config: {
    type: Array,
    default: () => [],
  },
  formData: {
    type: Object,
    default: () => {},
  },
  defaultData: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(['input'])
// biome-ignore lint/suspicious/noExplicitAny: off
const data: any = ref({})
watch(
  () => props.formData,
  () => {
    data.value = props.formData
    if (!props.formData?.outputType) {
      data.value.outputType = props.defaultData.outputType
    }
  },
  { immediate: true },
)
watch(
  () => data.value,
  () => {
    emit('input', data.value)
  },
)
const isVar = computed(() => data.value.outputType === OUTPUT_TYPE.VARIABLES)

// biome-ignore lint/suspicious/noExplicitAny: off
const onChange = (v: any | string) => {
  if (v === OUTPUT_TYPE.CONTENT) {
    data.value.outputs = OUTPUT_DEFAULT_DATA[OUTPUT_TYPE.CONTENT]
  }
  if (v === OUTPUT_TYPE.VARIABLES) {
    data.value.outputs = OUTPUT_DEFAULT_DATA[OUTPUT_TYPE.VARIABLES]
  }
}

// biome-ignore lint/suspicious/noExplicitAny: off
const configData: any = computed(() => props.config)
</script>

<template>
  <template v-for="item in configData">
    <a-form-item
      v-if="item.type === 'radio'"
      :field="item.prop"
      :label="item.label"
      :class="{ 'arco-form-item_column': !!item.tooltip }"
      style="width: 100%"
    >
      <a-radio-group
        v-model="data[item.prop]"
        @input="data[item.prop] = $event"
        @change="onChange"
      >
        <a-radio
          v-for="(option, key) in item.options"
          :key="'output' + key"
          :value="option.value"
          >{{ option.label }}</a-radio
        >
      </a-radio-group>
      <template slot="extra" v-if="item.tooltip">
        <p v-html="item.tooltip"></p>
      </template>
    </a-form-item>
    <template v-if="isVar && item.type === 'dynamicForms'">
      <FieldForm
        :key="'dynamicForms' + item.prop"
        :value="data[item.prop]"
        :config="item.props"
        :enable-sort="true"
        @input="data[item.prop] = $event"
      />
    </template>
  </template>
</template>
