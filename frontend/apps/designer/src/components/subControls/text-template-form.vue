<script setup lang="ts">
import { computed } from 'vue'
import FieldForm from './field-form.vue'

defineProps({
  formData: {
    type: Object,
    default: () => {},
  },
})
const config = computed(() => {
  return [
    {
      prop: 'code',
      placeholder: '请输入变量名',
      width: '50%',
      type: 'input',
      default: '',
    },

    {
      prop: 'value',
      placeholder: '',
      width: '50%',
      type: 'field-filling',
    },
  ]
})
const handleClick = () => {
  window.open('https://handlebarsjs.com/')
}
const columns = computed(() => [
  { title: '名称', dataIndex: 'name' },
  { title: '编码', dataIndex: 'code' },
  { title: '类型', dataIndex: 'type' },
])
const defaultRow = computed(() => ({
  code: '',
  value: { type: 'literal', content: '' },
  required: true,
}))
</script>

<template>
  <a-form-item label="输入变量" class="form-item_title"></a-form-item>
  <FieldForm
    :key="formData._id"
    :value="formData.inputs"
    :config="config"
    :defaultRow="defaultRow"
    :enable-sort="true"
    @input="formData.inputs = $event"
  />
  <div class="form-textarea">
    <div class="form-textarea-header">
      <span class="text-xs" @click="handleClick">只支持 Handlebars 模板语法</span>
    </div>
    <a-textarea
      placeholder="请输入"
      allow-clear
      :auto-size="{
        minRows:10,
        maxRows:10
      }"
      v-model:model-value="formData.content"
    />
  </div>

  <a-form-item label="输出变量" class="form-item_title"></a-form-item>
  <a-table
    :pagination="false"
    class="custom-grid"
    :columns="columns"
    :data="formData.outputs"
  />
</template>
