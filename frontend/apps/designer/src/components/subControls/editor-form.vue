<script setup lang="ts">
import { computed, onMounted, type Ref, ref, watch } from 'vue'
import { FIELD_OBJECT_OPTIONS } from '../../stores/configData'
import type { FormFieldParams, OutputParamsType } from '../../ts/editor'
import FieldForm from './field-form.vue'
import OutputParams from './output-params.vue'
import TemplateEditor from './template-editor.vue'
import FieldFilling from './field-filling.vue'

interface FormData {
  // biome-ignore lint/suspicious/noExplicitAny: off
  [key: string]: any
}
const emit = defineEmits(['input', 'outputs'])
const props = defineProps({
  cardConfig: {
    type: Array as () => VariablesConfigType[],
    default: () => [],
  },
  data: {
    type: Array as () => FormData,
    default: () => {},
  },
  outputs: {
    type: Array as () => OutputParamsType[],
    default: () => {},
  },
})
const formConfig: Ref<VariablesConfigType[]> = ref([])
const templateKey = ref(0)
const controlOptions = computed(() => {
  return [
    { label: '单行文本', value: 'Input', supportType: ['string', 'number'] },
    { label: '多行文本', value: 'Textarea', supportType: ['string'] },
    { label: '数字', value: 'InputNumber', supportType: ['number', 'string'] },
    { label: '日期', value: 'DatePicker', supportType: ['date', 'string'] },
    { label: '单选', value: 'RadioGroup', supportType: ['string', 'number'] },
    { label: '子列表', value: 'DataTable', supportType: ['array<object>'] },
    { label: '子表单', value: 'DataForm', supportType: ['array<object>'] },
  ]
})
const fieldConfig = computed(() => {
  return [
    {
      prop: 'value',
      placeholder: '请选择/输入参数值',
      width: '30%',
      type: 'field-filling',
      inputDisabled: false,
    },
    {
      prop: 'name',
      placeholder: '选择字段后自动填入名称',
      width: '25%',
      type: 'input',
      default: '',
    },
    {
      prop: 'code',
      placeholder: '选择字段后自动填入编码',
      width: '25%',
      type: 'input',
      default: '',
    },
    {
      prop: 'control',
      placeholder: '请选择',
      width: '20%',
      type: 'select',
      options: controlOptions.value,
      default: 'string',
      filterOptions: (type: string, level: number) => {
        return controlOptions.value.filter((item) => {
          // 子列表和子表单不支持单选组件
          if (level > 0 && item.value === 'RadioGroup') return false
          return item.supportType.includes(type)
        })
      },
    },
    {
      label: '字段类型',
      prop: 'type',
      placeholder: '请选择',
      type: 'select',
      options: FIELD_OBJECT_OPTIONS,
      disabled: true,
      default: false,
      advancedconfig: true,
    },
    {
      label: '是否必填',
      prop: 'required',
      placeholder: '请选择',
      type: 'checkbox',
      default: true,
      advancedconfig: true,
    },
    {
      label: '是否只读',
      prop: 'readonly',
      placeholder: '请选择',
      type: 'checkbox',
      default: false,
      advancedconfig: true,
    },
    {
      label: '使用千分位',
      prop: 'thousands',
      placeholder: '请选择',
      type: 'checkbox',
      default: true,
      advancedconfig: true,
    },
    {
      label: '定义备选项',
      prop: 'options',
      type: 'option-group',
      default: [],
      advancedconfig: true,
    },
  ]
})
const hideFieldConfig = computed(() => {
  return [
    {
      prop: 'code',
      placeholder: '选择字段后自动填入编码',
      width: '30%',
      type: 'input',
      default: '',
    },
    {
      prop: 'name',
      placeholder: '选择字段后自动填入名称',
      width: '30%',
      type: 'input',
      default: '',
    },
    {
      prop: 'value',
      placeholder: '请选择/输入参数值',
      width: '40%',
      type: 'field-filling',
    },
  ]
})
onMounted(() => {
  formConfig.value = JSON.parse(JSON.stringify(props.cardConfig))
  const data: FormData = {}
  formConfig.value?.forEach((item: VariablesConfigType) => {
    if (item.defaultValue) {
      data[item.name] = item.defaultValue
    } else {
      data[item.name] = ''
    }
    if (item.events) {
      const isUndefined = typeof props.data?.[item.name] === 'undefined'
      onEvents('change', isUndefined ? item.defaultValue : props.data[item.name], item)
    }
  })
  // biome-ignore lint/suspicious/noExplicitAny: off
  const formData: Record<string, any> = {
    ...data,
    ...props.data,
    hiddenFields: [],
  }
  for (const key in formData) {
    props.data[key] = formData[key]
  }
  const columns = props.data?.columns || []
  const codes = columns?.map((item: OutputParamsType) => item.code) || []
  props.data.hiddenFields = props.outputs?.filter(
    ({ code }: OutputParamsType) => !codes.includes(code),
  )
})

watch(
  () => [props.data?.columns, props.data?.hiddenFields],
  () => {
    if (!props.data?.columns) return
    const data = JSON.parse(JSON.stringify(props.data))

    // biome-ignore lint/performance/noDelete: off
    delete data.hiddenFields
    const outputs =
      data?.columns?.map(({ name, code, value, required, type, schema }: FormFieldParams) => {
        const columns =
          schema?.map((item: FormFieldParams) => {
            return {
              name: item.name,
              code: item.code,
              value: item.value,
              required: item.required,
              type: item.type,
            }
          }) || []
        return { name, code, value, required, type, schema: columns }
      }) || []
    emit('outputs', [...outputs, ...(props.data?.hiddenFields || [])])
    templateKey.value = new Date().getTime()
  },
  {
    deep: true,
  },
)

// biome-ignore lint/suspicious/noExplicitAny: off
const onEvents = (type: string, value: any, item: any) => {
  if (item.events) {
    // biome-ignore lint/suspicious/noExplicitAny: off
    const event = item.events.find((event: any) => event.name === type)
    if (event && event.type === 'script') {
      const config: { [key: string]: VariablesConfigType } = {}
      formConfig.value?.forEach((item: VariablesConfigType) => {
        config[item.name] = item
      })
      const callbackFunction = new Function('value', 'formData', 'config', event.callback)
      callbackFunction(value, props.data, config)
    }
  }
}
const defaultRow = computed(() => ({
  code: '',
  value: { type: 'literal', content: '' },
  type: 'string',
  required: false,
  schema: [],
  control: 'Input',
  name: '',
  readonly: false,
  options: [],
  thousands: false,
}))
const hideFieldDefaultRow = computed(() => {
  return {
    code: '',
    value: { type: 'literal', content: '' },
    type: 'string',
    required: true,
    name: '',
    description: '',
  }
})
</script>

<template>
  <a-form :model="data" label-align="left">
    <template v-for="(item, key) in formConfig">
      <template v-if="item?.editor?.type === 'Fields'">
        <template v-if="item?.name === 'hiddenFields'">
          <a-form-item label="隐藏字段" class="form-item_title"></a-form-item>
          <FieldForm
            :value="data.hiddenFields"
            :config="hideFieldConfig"
            :isFillField="true"
            :defaultRow="hideFieldDefaultRow"
            :enable-sort="true"
            @input="data.hiddenFields = $event"
          />
        </template>
        <template v-else>
          <a-form-item label="表单字段" class="form-item_title"></a-form-item>
          <div class="ml-[20px] inline-block w-[calc(100%_-_20px)]">
            <output-params
              :value="data.columns"
              :config="fieldConfig"
              :isFillField="true"
              :defaultRow="defaultRow"
              :enable-sort="true"
              :enable-group="true"
              @input="data.columns = $event"
            />
          </div>
        </template>
      </template>
      <a-form-item
        v-else
        :field="item.name"
        :label="item.label"
        :required="!!item.required"
        :rules="item.rules || []"
        :tooltip="item.tooltip || ''"
        :disabled="!!item.disabled"
        :validateTrigger="item.validateTrigger || 'change'"
        :hideLabel="!!item.hideLabel"
        :key="'item_' + key"
        v-show="!item.hide"
      >
        <template v-if="item.editor && item.editor.type === 'Input'">
          <a-input
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :default-value="item.defaultValue || ''"
            :placeholder="item.editor?.placeholder || ''"
          />
        </template>
        <template v-if="item.editor && item.editor.type === 'InputNumber'">
          <a-input-number
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :placeholder="item.editor?.placeholder || ''"
          />
        </template>
        <template v-if="item.editor && item.editor.type === 'Checkbox'">
          <a-checkbox
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :default-value="item.defaultValue || false"
            @change="(v) => onEvents('change', v, item)"
          >
            {{ item.editor?.placeholder || '' }}
          </a-checkbox>
        </template>
        <template v-if="item.editor && item.editor.type === 'Switch'">
          <a-switch
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :default-value="item.defaultValue || false"
          >
            {{ item.editor?.placeholder || '' }}
          </a-switch>
        </template>
        <template v-if="item.editor && item.editor.type === 'Slider'">
          <a-slider v-bind="item.editor.props" v-model="data[item.name]"></a-slider>
        </template>
        <template v-if="item.editor && item.editor.type === 'Select'">
          <a-select
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :placeholder="item.editor?.placeholder || ''"
            :default-value="item.defaultValue || ''"
            :options="[]"
          >
            <template v-for="option in item.editor?.props?.options || []">
              <a-option :value="option.value" :disabled="option.disabled" v-if="!option.hide">{{
                option.text
              }}</a-option>
            </template>
          </a-select>
        </template>
        <template v-if="item.editor && item.editor.type === 'Radio'">
          <a-radio-group
            v-bind="item.editor.props"
            v-model="data[item.name]"
            :placeholder="item.editor?.placeholder || ''"
            :default-value="item.defaultValue || ''"
            :options="[]"
          >
            <template v-for="option in item.editor?.props?.options || []">
              <a-radio :value="option.value" :disabled="option.disabled" v-if="!option.hide">{{
                option.text
              }}</a-radio>
            </template>
          </a-radio-group>
        </template>
        <template v-if="item.editor && item.editor.type === 'Template'">
          <TemplateEditor
            :key="templateKey"
            :value="data[item.name]"
            :minHeight="item.editor.props?.minHeight || '30px'"
            :placeholder="item.editor.placeholder || ''"
            @input="data[item.name] = $event"
          />
        </template>
        <template v-if="item.editor && item.editor.type === 'field-filling'">
          <FieldFilling
            :value="data[item.name]"
            :placeholder="item.placeholder || '请选择/输入参数值'"
            :input-disabled="item.inputDisabled"
            @input="data[item.name] = $event"
          />
        </template>
        <template v-if="item.extra" #extra>
          <span v-html="item.extra"></span>
        </template>
      </a-form-item>
    </template>
  </a-form>
</template>
