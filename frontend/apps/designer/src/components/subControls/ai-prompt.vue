<script setup lang="ts">
import { type Ref, computed, onMounted, ref, watch } from 'vue'
import { onMapping } from '../../api'
import { onQueryPrompt } from '../../api/ai-prompt'
import { outputParamsConfig } from '../../stores/configData'
import { useFlowStore } from '../../stores/flow'
import { getParams } from '../../utils'
import FieldForm from './field-form.vue'
import OutputParams from './output-params.vue'
import TemplateEditor from './template-editor.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
// biome-ignore lint/suspicious/noExplicitAny: off
const data: any = ref([])
const inputKey: Ref<number> = ref(0)
const configInfo = [
  {
    prop: 'name',
    placeholder: '请选择',
    width: '40%',
    type: 'select',
    options: [],
    disabled: true,
  },
  {
    prop: 'required',
    placeholder: '请选择',
    width: '20%',
    type: 'select',
    options: [
      { label: '必填', value: true },
      { label: '非必填', value: false },
    ],
    disabled: true,
  },
  {
    prop: 'value',
    placeholder: '',
    width: '40%',
    type: 'field-filling',
  },
]
let oldInputs: InputParams[] = []
onMounted(() => {
  oldInputs = props.formData.inputs || []
})
const flowStore = useFlowStore()
// biome-ignore lint/suspicious/noExplicitAny: off
const configData: any = ref(configInfo)
const loading: Ref<boolean> = ref(false)
// biome-ignore lint/suspicious/noExplicitAny: off
const allPrompt: any = ref([])
const loadingSearch = ref(false)

const fieldProps = computed(() => outputParamsConfig)

const selectData = computed(() => {
  return allPrompt.value.find(
    // biome-ignore lint/suspicious/noExplicitAny: off
    (item: any) => item.value === props.formData.templateId,
  )
})

const inputsData = computed(() => {
  const data: PromptTestScenes[] = []
  JSON.parse(JSON.stringify(props.formData.inputs)).forEach(
    (item: InputParams) => {
      data.push({
        ...item,
        title: item.name,
        field: item.code,
        value: item.code,
      })
    },
  )
  return [
    {
      name: '参数',
      code: 'param',
      selectable: false,
      children: data,
    },
  ]
})

// biome-ignore lint/suspicious/noExplicitAny: off
const promptTemplate: Record<string, any> = ref('')
const messageContent: Ref<MessageContent[]> = ref([])
watch(
  () => selectData.value,
  (value) => {
    if (!value) return
    promptTemplate.value = value?.promptTemplate || ''
    messageContent.value = value?.messageContent || []
    // biome-ignore lint/suspicious/noExplicitAny: off
    const inputs: any = []
    // biome-ignore lint/suspicious/noExplicitAny: off
    const outputs: any = []
    if (value?.promptParam) {
      // biome-ignore lint/suspicious/noExplicitAny: off
      value.promptParam.forEach((item: any) => {
        const currentItem = {
          name: item.paramName,
          code: item.paramCode,
          type: item.fieldType,
          description: item.describe,
          required: !!item.isRequired,
          schema: [],
        }
        if (item.paramType === 1) {
          inputs.push({
            ...currentItem,
            // biome-ignore lint/suspicious/noExplicitAny: off
            value: oldInputs.find((input: any) => input.code === item.paramCode)
              ?.value || {
              type: 'literal',
              content: '',
            },
          })
        }
        if (item.paramType === 2) {
          if (item.schema) {
            currentItem.schema = item.schema
          }
          outputs.push(currentItem)
        }
      })
      // biome-ignore lint/suspicious/noExplicitAny: off
      const loop = (data: any) => {
        // biome-ignore lint/suspicious/noExplicitAny: off
        return data.map((item: any) => {
          const currentItem = {
            name: item.paramName,
            code: item.paramCode,
            type: item.fieldType,
            description: item.describe,
            required: !!item.isRequired,
            schema: item.schema?.length ? loop(item.schema) : [],
          }
          return currentItem
        })
      }
      // biome-ignore lint/suspicious/noExplicitAny: off
      outputs.forEach((item: any) => {
        if (item.schema?.length) {
          item.schema = loop(item.schema)
        }
      })
    }
    inputKey.value = new Date().getTime()
    props.formData.pattern = value.outputType === 'content' ? 0 : 1
    props.formData.inputs = inputs
    props.formData.outputs = outputs
  },
)

onMounted(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  onQueryPrompt('').then((res: any) => {
    // biome-ignore lint/suspicious/noExplicitAny: off
    allPrompt.value = res.data.data.map((item: any) => {
      return {
        ...item,
        label: `${item.promptName}（${item.promptCode}）`,
        value: item.promptGUID,
      }
    })
    data.value = allPrompt.value
  })
})

const handleSearch = (value: string) => {
  loadingSearch.value = true
  let currentItem: { promptGUID: string } = {
    promptGUID: '',
  }
  if (props.formData.templateId) {
    currentItem = data.value.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => item.promptGUID === props.formData.templateId,
    )
  }
  onQueryPrompt(value)
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then((res: any) => {
      let hasSelectValue = false
      // biome-ignore lint/suspicious/noExplicitAny: off
      data.value = res.data.data.map((item: any) => {
        if (item.promptGUID === currentItem?.promptGUID) {
          hasSelectValue = true
        }
        return {
          ...item,
          label: `${item.promptName}（${item.promptCode}）`,
          value: item.promptGUID,
        }
      })
      if (!hasSelectValue && currentItem?.promptGUID) {
        data.value.push(currentItem)
      }
      loadingSearch.value = false
    })
    .catch(() => {
      data.value = []
      loadingSearch.value = false
    })
}
const handleEdit = () => {
  const id = props.formData.templateId
  if (!id) return
  const params = getParams()
  window.open(
    `/std/42001201/08dc8b7c-8383-4e0e-8739-c28f29f61e9f?mode=2&autoTitle=true&_mp=crumbs&oid=${id}&_title=提示词&SpaceGUID=${params.space}`,
  )
}

const sourceMap = computed(() => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const map: any = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.getParamsData().forEach((item: any) => {
    if (item.children) {
      if (item.code === 'system') return
      // biome-ignore lint/suspicious/noExplicitAny: off
      item.children.forEach(({ value, description, name, type }: any) => {
        map[value] = { description, name, type, code: value }
      })
    }
  })
  return map
})

const systemMap = computed(() => {
  const data: InputParamsMap = {}
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.getSystemParamData().forEach((item: any) => {
    data[item.value] = item
  })
  return data
})

const templateTitle = computed(() => {
  if (messageContent.value.length > 0) {
    return '系统提示词 #system'
  }
  return ''
})

const handleMapping = () => {
  if (!props.formData?.inputs?.length) return
  loading.value = true
  onMapping({
    source: Object.values(sourceMap.value),
    target: props.formData.inputs,
  })
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      loading.value = false
      if (typeof data?.data === 'object') {
        // biome-ignore lint/suspicious/noExplicitAny: off
        const map: any = {}
        // biome-ignore lint/suspicious/noExplicitAny: off
        data.data.forEach((item: any) => {
          map[item.code] = item?.value
        })
        const inputs = JSON.parse(JSON.stringify(props.formData.inputs))
        // biome-ignore lint/suspicious/noExplicitAny: off
        inputs.forEach((item: any) => {
          const currentItem = map[item.code]
          const refField = currentItem?.content
          if (
            refField &&
            (sourceMap.value[refField] || systemMap.value[refField])
          ) {
            item.value = { type: 'ref', content: refField }
          }
        })
        props.formData.inputs = inputs
        inputKey.value = new Date().getTime()
      }
    })
    .catch(() => {
      loading.value = false
    })
}

const getTitle = (role: string, index: number) => {
  let text = ''
  let _i = 0
  let currentI = 0
  messageContent.value.some((item, k) => {
    if (item.role === 'user') {
      _i += 1
    }
    if (k + 1 === index) {
      currentI = _i
      return true
    }
    return false
  })
  if (role === 'user') {
    text = `用户提问_${currentI}`
  }
  if (role === 'assistant') {
    text = currentI < 1 ? 'AI回复' : `AI回复_${currentI}`
  }
  return `${text} #${role}`
}
const handleChange = () => {
  oldInputs = []
}
</script>

<template>
  <a-form-item
    label="选择提示"
    field="templateId"
    :rules="[{ required: true, message: '请选择提示' }]"
  >
    <a-select
      v-model="formData.templateId"
      :allow-search="true"
      :loading="loadingSearch"
      placeholder="请输入提示词名称、编码"
      @search="handleSearch"
      @change="handleChange"
      :filter-option="false"
    >
      <a-option v-for="item of data" :value="item.promptGUID">{{
        item.label
      }}</a-option>
    </a-select>
  </a-form-item>
  <a-form-item label="" field="promptContent">
    <TemplateEditor
      :key="'editor_' + inputKey"
      placeholder=" "
      :value="promptTemplate"
      :inputs="formData.inputs"
      :disabled="true"
      :is-custom="true"
      :custom-params="inputsData"
      :title="templateTitle"
    >
      <template #btn>
        <span @click="handleEdit">
          <svg-icon name="edit" size="16px" color="#266EFF" />
          编辑提示词
        </span>
      </template>
    </TemplateEditor>
  </a-form-item>

  <a-form-item v-for="(item, key) in messageContent" :key="'message_' + key">
    <TemplateEditor
      :key="'message_' + key + inputKey"
      placeholder=" "
      :value="item.content"
      :inputs="formData.inputs"
      :disabled="true"
      :is-custom="true"
      :custom-params="inputsData"
      :title="getTitle(item.role, key + 1)"
      min-height="40px"
    >
      <template #btn></template>
    </TemplateEditor>
  </a-form-item>

  <template v-if="!!formData.inputs?.length">
    <a-form-item label="输入参数" class="form-item_title">
      <div class="w-full">
        <a-popover
          position="top"
          :content-style="{ padding: '4px 12px 8px 12px' }"
          popup-container=".designer-app"
        >
          <a-button
            type="primary"
            class="float-right rounded-[4px]"
            :loading="loading"
            @click="handleMapping"
          >
            参数自动映射
            <span class="ml-[4px]">
              <svg-icon name="help" size="16px" color="white" />
            </span>
          </a-button>
          <template #content>
            <div class="w-[120px] text-[13px] leading-[22px] text-[#333333]">
              通过提示模板参数，自动生成输入参数。
            </div>
          </template>
        </a-popover>
      </div>
    </a-form-item>
    <FieldForm
      :key="'field-form_' + inputKey"
      :value="formData.inputs"
      :config="configData"
      :allowAdd="false"
      :allowDelete="false"
    />
  </template>

  <a-form-item label="输出参数" class="form-item_title"></a-form-item>
  <a-form-item field="pattern" label="参数格式">
    <a-radio-group v-model="formData.pattern" :disabled="true">
      <a-radio :value="0">纯文本</a-radio>
      <a-radio :value="1">参数列表</a-radio>
    </a-radio-group>
  </a-form-item>
  <output-params
    :allow-add="false"
    :allow-delete="false"
    :value="formData.outputs"
    :config="fieldProps"
    @input="formData.outputs = $event"
  />
</template>
