<script setup lang="ts">
defineProps({
  enableGroup: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['add-row', 'add-group'])
const handleAdd = () => {
  emit('add-row')
}
const handleAddGroup = () => {
  emit('add-group')
}
</script>

<template>
  <a-popover
    v-if="enableGroup"
    content-class="skill-popover-content"
    position="bottom"
    :content-style="{ padding: 0 }"
    :arrow-style="{ display: 'none' }"
    popup-container=".designer-app"
  >
    <div class="flex w-[48px] cursor-pointer flex-row items-center">
      <svg-icon name="add" size="16px" />
      <span
        class="ml-[4px] text-center text-[13px] font-normal not-italic leading-[22px] text-[#333333]"
        >添加</span
      >
    </div>
    <template #content>
      <div class="flex flex-col">
        <a-button class="skill-text-btn" type="text" @click="handleAdd"
          >新增字段</a-button
        >
        <a-button class="skill-text-btn" type="text" @click="handleAddGroup"
          >新增分组</a-button
        >
      </div>
    </template>
  </a-popover>
  <div
    v-else
    class="flex w-[48px] cursor-pointer flex-row items-center"
    @click="handleAdd"
  >
    <svg-icon name="add" size="16px" />
    <span
      class="ml-[4px] text-center text-[13px] font-normal not-italic leading-[22px] text-[#333333]"
      >添加</span
    >
  </div>
</template>
