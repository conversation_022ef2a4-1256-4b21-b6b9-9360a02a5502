<script setup lang="ts">
import { computed, ref } from 'vue'

// biome-ignore lint/suspicious/noExplicitAny: off
const props = defineProps<{ formData: any }>()
const options = computed(() => {
  return [
    { label: '所有页', value: 'all' },
    { label: '指定页', value: 'range' },
  ]
})
// biome-ignore lint/suspicious/noExplicitAny: off
const marks: any = ref({ '1': 1, '2': '默认', '10': 10 })
const handleChangeType = () => {
  const isRange = props.formData.usePageRange === 'range'
  props.formData.useFirstFewPages = isRange ? 2 : 0
  props.formData.useLastFewPages = isRange ? 2 : 0
}
</script>

<template>
  <a-form-item field="useTOC" label="解析目录">
    <a-checkbox v-model="formData.useTOC"></a-checkbox>
  </a-form-item>
  <a-form-item field="imagConverter" label="全文图片转换">
    <a-checkbox v-model="formData.imagConverter"></a-checkbox>
    <template #help>
      <div>
        整个文档内容转换为图片后使用视觉识别服务，要求对应文档不大于<b> 10 </b
        >页。
      </div>
    </template>
  </a-form-item>
  <a-form-item field="usePageRange" label="解析范围">
    <a-radio-group
      v-model="formData.usePageRange"
      default-value="all"
      @change="handleChangeType"
    >
      <a-radio
        v-for="option in options"
        :value="option.value"
        :key="option.value"
        >{{ option.label }}</a-radio
      >
    </a-radio-group>
  </a-form-item>
  <template v-if="formData.usePageRange === 'range'">
    <a-form-item
      field="useFirstFewPages"
      class="knowledge_slider"
      label="解析前几页"
    >
      <a-slider
        :default-value="2"
        v-model="formData.useFirstFewPages"
        @input="formData.useFirstFewPages = $event"
        :min="1"
        :max="10"
        :step="1"
        :marks="marks"
      />
    </a-form-item>
    <a-form-item
      field="useLastFewPages"
      class="knowledge_slider"
      label="解析后几页"
    >
      <a-slider
        :default-value="2"
        v-model="formData.useLastFewPages"
        @input="formData.useLastFewPages = $event"
        :min="1"
        :max="10"
        :step="1"
        :marks="marks"
      />
    </a-form-item>
  </template>
</template>
