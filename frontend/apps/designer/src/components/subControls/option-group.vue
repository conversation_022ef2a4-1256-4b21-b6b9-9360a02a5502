<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useSortable } from '@vueuse/integrations/useSortable'
import { computed, nextTick, onMounted, Ref, ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['close'])
const optionData: Ref<{ text: string; value: string }[]> = ref([])
const refSelector = ref(null)
const refreshKey = ref(0)
const row = {
  text: '',
  value: '',
}
const columns = computed(() => [
  { text: '', value: 'sort' },
  { text: '选项', value: 'text' },
  { text: '值', value: 'value' },
  { text: '操作', value: 'action' },
])

onMounted(() => {
  optionData.value = JSON.parse(JSON.stringify(props.formData.options || [row]))
  if (!optionData.value.length) {
    optionData.value.push({ ...row })
  }

  nextTick(() => {
    const { option } = useSortable(refSelector, optionData.value, {
      handle: '.handle',
      onUpdate: (e: { oldIndex: number; newIndex: number }) => {
        const oldIndex = e.oldIndex
        const newIndex = e.newIndex
        const data = JSON.parse(JSON.stringify(optionData.value))
        const item = data[oldIndex]
        data.splice(oldIndex, 1)
        data.splice(newIndex, 0, item)
        optionData.value = JSON.parse(JSON.stringify(data))
        refreshKey.value += 1
      },
    })
    option('animation', 200)
  })
})

const handleOk = () => {
  let repeatName = ''
  optionData.value.some((item) => {
    if (optionData.value.filter((v) => v.value === item.value)?.length > 1) {
      repeatName = '值'
      return true
    }
    if (optionData.value.filter((v) => v.text === item.text)?.length > 1) {
      repeatName = '名称'
      return true
    }
  })
  if (repeatName) {
    return Message.warning(`选项${repeatName}存在重复！`)
  }
  props.formData.options = optionData.value
  emit('close')
}
const handleClose = () => {
  emit('close')
}
const handleAdd = () => {
  optionData.value.push({ ...row })
  refreshKey.value += 1
}

const handleDelete = (index: number) => {
  if (optionData.value?.length < 2) {
    return Message.warning('至少保留一个选项')
  }
  optionData.value.splice(index, 1)
  refreshKey.value += 1
}
</script>

<template>
  <a-modal
    :visible="visible"
    :simple="true"
    :maskClosable="false"
    title-align="start"
    :modal-style="{
      padding: '12px 20px 20px 20px',
    }"
    :width="600"
    popup-container=".designer-app"
    @ok="handleOk"
    @cancel="handleClose"
  >
    <template #title>
      <div class="flex flex-row justify-center">
        <span
          class="text-base font-semibold not-italic leading-[24px] text-[#333333]"
          >定义备选项</span
        >
      </div>
    </template>
    <div>
      <div
        class="float-right mb-[8px] flex cursor-pointer items-center"
        @click="handleAdd"
      >
        <svg-icon name="add" size="16px" />
        <span class="ml-[4px]">新增</span>
      </div>
      <table class="gpt-option-group_table">
        <thead>
          <tr>
            <th v-for="(column, key) in columns" :key="'col' + key">
              {{ column.text }}
            </th>
          </tr>
        </thead>
        <tbody ref="refSelector">
          <tr
            v-for="(row, rowIndex) in optionData || []"
            :key="'row' + refreshKey + rowIndex"
          >
            <td v-for="(column, key) in columns" :key="'col' + key">
              <div v-if="column.value === 'sort'" class="flex items-center">
                <span class="handle cursor-all-scroll">
                  <svg-icon name="drop" size="16px" />
                </span>
                <span class="ml-[32px]">{{ rowIndex + 1 }}</span>
              </div>
              <div
                v-else-if="column.value === 'action'"
                class="cursor-pointer"
                @click="handleDelete(rowIndex)"
              >
                <span class="ml-[8px] text-[13px] text-[#266eff]">删除</span>
              </div>
              <a-input
                v-else
                v-model="row[column.value]"
                :placeholder="`请输入${column.text}`"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </a-modal>
</template>

<style lang="less">
.gpt-option-group_table {
  // border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: #fff;
  width: 100%;

  tr + tr {
    border-top: 1px solid #eaeaea;
  }

  td + td,
  th + th {
    // border-left: 1px solid #e5e7eb;
  }

  thead tr {
    border-bottom: 1px solid #eaeaea;
    background-color: #fbfbfb;

    th {
      padding: 9px 12px;
      min-width: 52px;
      max-width: 140px;
      color: #333333;
      font-size: 13px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      text-align: left;
    }
  }

  td {
    min-width: 52px;
    max-width: 140px;
    padding: 8px 4px;
    position: relative;
  }

  .arco-input-wrapper {
    border: 1px solid #ddd;
    background-color: transparent;
    border-radius: 4px;
  }
}
</style>
