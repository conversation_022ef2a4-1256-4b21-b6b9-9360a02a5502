<script setup lang="ts">
import { computed } from 'vue'
import FieldForm from './field-form.vue'
const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const config = computed(() => {
  return [
    {
      prop: 'code',
      placeholder: '请输入变量名',
      width: '30%',
      type: 'input',
      default: '',
    },
    {
      prop: 'name',
      placeholder: '请输入名称',
      width: '30%',
      type: 'input',
      default: '',
    },
    {
      prop: 'value',
      placeholder: '',
      width: '40%',
      type: 'field-filling',
    },
  ]
})
const defaultRow = computed(() => ({
  name: '',
  code: '',
  value: { type: 'literal', content: '' },
  type: 'string',
  required: true,
}))
</script>
<template>
  <a-form-item label="输出参数" class="form-item_title"></a-form-item>
  <FieldForm
    :value="formData.outputs"
    :config="config"
    :defaultRow="defaultRow"
    :isFillField="true"
    :enable-sort="true"
  />
</template>
