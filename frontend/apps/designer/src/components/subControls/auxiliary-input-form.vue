<script setup lang="ts">
import { computed, nextTick, onMounted, type Ref, ref, watch } from 'vue'
import {
  GetFunctionControlFieldsById,
  GetFunctionControlsByAppCode,
  onGetAllMyApplications,
} from '../../api/auxiliary-input'
import FieldForm from './field-form.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
})
const loadingSearch = ref(false)
const formList: Ref<
  {
    id: string
    name: string
    myFunction: { name: string }
    functionPage: { name: string }
  }[]
> = ref([])
const fieldKey = ref(0)
const options = computed(() => {
  let hasSelectValue = false
  const data = formList.value.map((item) => {
    if (item.id === selectItem?.value?.value) {
      hasSelectValue = true
    }
    return {
      ...item,
      label: `${item.myFunction?.name}/${item.functionPage?.name}/${item.name}`,
      value: item.id,
    }
  })
  if (!hasSelectValue) {
    data.push(selectItem.value)
  }
  return data
})
const sysOptions: Ref<{ name: string; application: string }[]> = ref([])
const fieldOptions: Ref<{ label: string; value: string }[]> = ref([])

// biome-ignore lint/suspicious/noExplicitAny: off
const inputsConfig: any = computed(() => {
  return [
    {
      prop: 'code',
      placeholder: '请选择',
      width: '50%',
      type: 'select',
      options: [],
      default: '',
      disabled: false,
    },
    {
      prop: 'value',
      placeholder: '',
      width: '50%',
      type: 'field-filling',
    },
  ]
})
const defaultRow = computed(() => {
  return { code: '', required: true, value: { type: 'literal', content: '' } }
})

onMounted(() => {
  getApplications()
  if (props.formData.formId) {
    handleSearch()
    getFormFields()
  }
})

const getApplications = () => {
  onGetAllMyApplications()
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      sysOptions.value = data?.data || []
    })
    .catch(() => {
      sysOptions.value = []
    })
}

// biome-ignore lint/suspicious/noExplicitAny: off
const selectItem: any = ref({})
const handleSearch = (value = '') => {
  loadingSearch.value = true
  if (props.formData.formId) {
    selectItem.value =
      options.value.find(
        (item: { value: string }) => item.value === props.formData.formId,
      ) || {}
  } else {
    selectItem.value = {}
  }
  GetFunctionControlsByAppCode({
    appCode: props.formData.appCode,
    keyword: value || props.formData.formId || '',
  })
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      formList.value = data?.data || []
      loadingSearch.value = false
    })
    .catch(() => {
      formList.value = []
      loadingSearch.value = false
    })
}

const setDisabled = () => {
  const options = JSON.parse(JSON.stringify(fieldOptions.value))
  const disabledFields = props.formData.inputs?.map(
    (item: { code: string }) => item.code,
  )
  inputsConfig.value[0].options = options.map((item: { value: string }) => {
    return {
      ...item,
      disabled: disabledFields?.includes(item.value),
    }
  })
}

const getFormFields = (isSearch = false) => {
  GetFunctionControlFieldsById({ id: props.formData.formId })
    // biome-ignore lint/suspicious/noExplicitAny: off
    .then(({ data }: any) => {
      const fields = data?.data || []
      const options: { label: string; value: string }[] = []
      // biome-ignore lint/suspicious/noExplicitAny: off
      const inputs = fields.map((item: any) => {
        options.push({
          label: `${item.field}（${item.title}）`,
          value: item.field,
        })
        return {
          code: item.field,
          label: item.title,
          type: item.controlName,
          required: true,
          value: { type: 'literal', content: '' },
        }
      })
      fieldOptions.value = options
      if (isSearch) {
        props.formData.inputs = inputs
        fieldKey.value = Date.now()
      } else {
        setDisabled()
      }
    })
    .catch(() => {
      props.formData.inputs = []
    })
}

watch(
  () => props.formData.inputs,
  () => {
    nextTick(() => {
      setDisabled()
    })
  },
  { deep: true },
)

const handleChange = () => {
  getFormFields(true)
}

const onSysChange = () => {
  props.formData.formId = ''
  props.formData.inputs = []
  handleSearch('')
}
</script>

<template>
  <a-form-item field="appCode" label="子系统" :required="true">
    <a-select
      v-model="formData.appCode"
      placeholder="请选择子系统"
      @change="onSysChange"
    >
      <a-option
        v-for="(option, _index) of sysOptions"
        :value="option.application"
        :key="'option_' + _index"
        >{{ option.name }}</a-option
      >
    </a-select>
  </a-form-item>
  <a-form-item field="formId" label="表单控件" :required="true">
    <a-select
      v-model="formData.formId"
      placeholder="请选表单控件"
      :allow-search="true"
      :loading="loadingSearch"
      @search="handleSearch"
      @change="handleChange"
      :filter-option="false"
    >
      <a-option
        v-for="(option, _index) of options"
        :value="option.value"
        :key="'option_' + _index"
        >{{ option.label }}</a-option
      >
    </a-select>
    <template #help>
      <div>请输入“建模表单控件名称”查询，或直接输入元数据 ID</div>
    </template>
  </a-form-item>
  <a-form-item
    field="inputs"
    class="form-item_no-pb"
    label="录入字段"
    :required="true"
  >
    <FieldForm
      :key="fieldKey + '_inputs'"
      :value="formData.inputs"
      :config="inputsConfig"
      :default-row="defaultRow"
      :enable-sort="true"
      @input="formData.inputs = $event"
    />
    <template #help>
      <div>支持单行、多行文本、下拉选择、日期、金额类字段</div>
    </template>
  </a-form-item>
</template>

<style lang="less">
.form-item_no-pb {
  .field-form_box {
    padding-bottom: 0;
  }
}
</style>
