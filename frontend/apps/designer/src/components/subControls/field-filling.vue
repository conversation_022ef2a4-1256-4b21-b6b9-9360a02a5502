<script setup lang="ts">
// @ts-ignore
import { computed, nextTick, onMounted, ref, shallowRef, watch } from 'vue'
import { useFlowStore } from '../../stores/flow'
import SvgIcon from '../svg-icon.vue'
import <PERSON><PERSON>ield from './select-field.vue'

const props: any = defineProps({
  value: {
    type: Object,
    default: () => {},
  },
  prompt: {
    type: String,
    default: '',
  },
  inputs: {
    type: Array,
    default: () => [],
  },
  placeholder: {
    type: String,
    default: '请输入提示词',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  isCustom: {
    type: Boolean,
    default: false,
  },
  customParams: {
    type: Object,
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
  // 是否是禁用输入的单tag模式
  inputDisabled: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Array,
    default: () => [],
  },
  filterSystemCode: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  // 参数无层级
  noLevel: {
    type: Boolean,
    default: false,
  },
  // 步骤标题
  stepName: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['input', 'inputs', 'del', 'fieldInfo'])

const flowStore = useFlowStore()
const paramsData = computed(() => (props.isCustom ? props.customParams : flowStore.getParamsData()))

/**
 * * @description 字段选择树
 */
const treeData = computed(() => {
  return props.isCustom ? props.customParams : flowStore.getParamsData(true)
})

/**
 * * @description 字段选择映射
 */
const sourceMap = computed(() => {
  const map: any = {}
  paramsData.value.forEach((item: any) => {
    if (item.children) {
      item.children.forEach((val: any) => {
        val.title = val.name
        map[val.value] = val
        if (val.children) {
          val.children.forEach((child: any) => {
            child.title = child.name
            map[child.value] = child
          })
        }
      })
    }
    if (props.noLevel) {
      item.title = item.name
      map[item.value] = item
    }
  })
  return map
})

/**
 * * @description 内部字段值
 */
let content = ref('')
const update = (value: string) => {
  content.value = value
}

onMounted(() => {
  content.value = props.value?.content || ''
  const isNotRef = props.value?.type === 'literal'
  if (isNotRef && content.value) innerInputText.value = content.value
})

/**
 * * @description 允许输入时，输入框的展示值
 */
const innerInputText = ref('')

/**
 *  禁用输入后，tag输入框的展示值 为数组
 */
const onlyTagModeValue = computed((): {}[] => {
  const field = sourceMap.value?.[content.value]
  if (!field) return []
  return [{
    value: content.value,
    label: field?.name,
    closable: true,
    tagProps: {
      size: 'small',
    },
  }]
})

/**
 * * @description 选择tag
 */
const handleSelect = (item: { name: string; value: string; code: string }) => {
  if (props.inputDisabled) {
    // 单tag模式下 只需要更新content得值
    update(item.value)
    return
  }
  // 此时要清空当前的输入框 再插入字段
  if (!!innerInputText.value) {
    // 先清空展示值
    innerInputText.value = ''
    // 再更新content的值为tag的值
  }
  update(item.value)

}


watch(
  () => content.value,
  (v: string) => {
    if (props.inputDisabled) {
      emit('fieldInfo', sourceMap.value?.[v] || {})
      emit('input', { type: 'ref', content: v})
      return
    } 
    const contenteditable = !!innerInputText.value
    const field = contenteditable ? innerInputText.value : v

    emit('fieldInfo', sourceMap.value?.[field] || {})
    emit(
      'input',
      contenteditable ? { type: 'literal', content: innerInputText.value } : { type: 'ref', content: field || '' },
    )
  },
)
/**
 * * @description 输入框值变化
 */
const handleInputChange = (val:string) => {
  if (props.inputDisabled) {
    // 单tag模式下 不支持text输入
    return
  }
  // 当是input模式的时候先清除掉当前的输入框 再设置text的值
  if (content.value && sourceMap.value[content.value]) {
    // 先清空
    update('')
  }
  // 再更新值
  innerInputText.value = val
  update(val)

}

</script>

<template>
  <div class="designer-field-selector w-[100%]">
    <AInputTag
      :modelValue="onlyTagModeValue"
      :inputValue="innerInputText"
      :disabled="disabled"
      :placeholder="placeholder"
      size="medium" 
      :retain-input-value="!inputDisabled"
      :maxTagCount="1"
      @remove="() => update('')"
      @input-value-change="handleInputChange"
    />

    <SelectField
      v-if="!disabled "
      :paramsData="treeData"
      :selected="selected"
      @select="handleSelect"
      :filterSystemCode="filterSystemCode"
      :noLevel="noLevel"
      :stepName="stepName"
    >
      <div class="absolute right-[8px] top-[7px] flex cursor-pointer">
        <svg-icon name="add" size="16px"></svg-icon>
      </div>
    </SelectField>
  </div>
</template>

<style lang="less">
.designer-field-selector {
  position: relative;
  .arco-input-tag {
    &:not(arco-input-tag-disabled) {
      background-color: transparent;
    }
    .arco-input-tag-inner{
      padding-top: 1px;
      padding-bottom: 1px;
    }
    .arco-input-tag-tag {
      margin: 0px;
      line-height: 20px;
      min-height: 22px;
      max-width: calc(100% - 20px);
      .arco-icon{
        margin: 4px 0px 4px 8px !important;
      }
    }
  }
}
</style>
