<script setup lang="ts">
import { computed } from 'vue'
import useDragAndDrop from '../drag'

const { onDragStart } = useDragAndDrop()

const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
  draggable: {
    type: Boolean,
    default: true,
  },
})
const data = computed(() => props.item)

// biome-ignore lint/suspicious/noExplicitAny: off
const handleDrag = (event: DragEvent, item: any) => {
  onDragStart(event, item)
}
</script>
<template>
  <a-tooltip
    :content="data.describe"
    position="right"
    background-color="#FFFFFF"
    :content-style="{
      color: '#333333',
      padding: '8px 12px',
      'border-radius': '4px',
      'box-shadow': '0 3px 8px 0 #0000001a',
    }"
    :arrow-style="{
      border: '1px solid #EAEAEA',
      'box-shadow': '0 3px 8px 0 #0000001a',
    }"
    popup-container=".designer-app"
  >
    <div
      :draggable="draggable"
      @dragstart="handleDrag($event, item)"
      class="card-tool_item flex h-[64px] w-[100%] cursor-pointer flex-col items-center
        justify-center gap-[4px] hover:bg-[#F2F4F6]"
    >
      <div class="h-[20px] w-[20px] text-xl">
        <svg-icon :name="data.icon" />
      </div>
      <div
        class="mt-[4px] text-center text-xs font-normal not-italic leading-[20px]
          text-[#333333]"
      >
        {{ data.label }}
      </div>
    </div>
  </a-tooltip>
  <div v-if="data.splitLine" class="mx-[0] my-[8px] h-[1px] bg-[#EAEAEA]"></div>
</template>

<style lang="less">
.card-tool_item {
  &:active {
    background: #fff !important;
  }
}
</style>
