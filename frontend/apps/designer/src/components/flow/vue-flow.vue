<script setup lang="ts">
import { Background } from '@vue-flow/background'
// biome-ignore lint/style/useImportType: off
import { MarkerType, PanOnScrollMode, VueFlow, useVueFlow, Connection } from '@vue-flow/core'
import type { Node, Edge } from '@vue-flow/core'
import { computed, onMounted } from 'vue'
import { useConditionStore } from '../../stores/condition'
import { useConfigStore } from '../../stores/config'
import { useFlowStore } from '../../stores/flow'
import CardCondition from './card-condition.vue'
import CardEdge from './card-edge.vue'
import CardMerge from './card-merge.vue'
import CardNode from './card-node.vue'
import useDragAndDrop from './drag'

const flowStore = useFlowStore()
const configStore = useConfigStore()
const conditionStore = useConditionStore()

onMounted(() => {
  if (flowStore.nodes?.length > 0) {
    handleSelect(flowStore.nodes[0])
  }
})

const { addEdges, panOnDrag, panOnScrollMode, panOnScroll } = useVueFlow()

panOnDrag.value = true
panOnScroll.value = true
panOnScrollMode.value = PanOnScrollMode.Vertical

const { isDragOver } = useDragAndDrop()

const nodes = computed<Node[]>(() => flowStore.nodes)

// biome-ignore lint/suspicious/noExplicitAny: off
const edges = computed<any[]>(() => flowStore.edges)

const isView = computed(() => flowStore.isView)

function onConnect(params: Connection) {
  if (
    !params.targetHandle?.endsWith('__handle-top') ||
    !params.sourceHandle?.endsWith('__handle-bottom')
  )
    return
  const hasConnected = edges.value.some((item) => item.target === params.target)
  if (hasConnected) return
  const item = {
    ...params,
    type: 'custom',
    markerEnd: MarkerType.ArrowClosed,
  } as Edge
  item.data = JSON.parse(JSON.stringify(item))
  addEdges([item])
}

// biome-ignore lint/suspicious/noExplicitAny: off
const handleSelect = (data: any) => {
  conditionStore.setActive('')
  flowStore.setActiveCard(data)
  configStore.setData(data)
}
flowStore.getKnowledgeList().then((data) => {
  configStore.updateKnowledgeOptions(data)
})
</script>

<template>
  <VueFlow
    :nodes="nodes"
    v-model:edges="edges"
    @connect="onConnect"
    :max-zoom="1.2"
    :min-zoom="0.8"
    :style="flowStore.isDesigner ? { background: '#f3f4f9' } : {}"
    elevate-edges-on-select
    :delete-key-code="null"
  >
    <template #node-card="props">
      <CardNode :nodeProps="props" v-model:edges="edges" @select="handleSelect" />
    </template>
    <template #node-condition="props">
      <CardCondition :nodeProps="props" v-model:edges="edges" @select="handleSelect" />
    </template>
    <template #node-merge="props">
      <CardMerge :nodeProps="props" v-model:edges="edges" @select="handleSelect" />
    </template>
    <template #edge-custom="customEdgeProps">
      <CardEdge v-bind="customEdgeProps" />
    </template>
    <Background
      v-if="isView"
      variant="lines"
      :gap="19"
      patternColor="#F3F5F8"
      :style="{
        backgroundColor: isDragOver ? '#e7f3ff' : '#FAFAFA',
        transition: 'background-color 0.2s ease',
      }"
    >
      <slot />
    </Background>
  </VueFlow>
</template>

<style lang="less">
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

@primary-color: #266eff;
.vue-flow .vue-flow__handle {
  width: 8px;
  height: 8px;
  border: 1px solid #a7adb9;
  background: #fff;
}
.vue-flow .vue-flow__edge-path {
  stroke: #a7adb9;
}
.vue-flow__handle.handle-hidden {
  min-width: 0;
  height: 0;
  width: 0;
  border: 0;
}
.card-node {
  cursor: pointer;
  line-height: 1;
}
.vue-flow div.node-active,
.vue-flow .card-node:hover {
  border-color: @primary-color;
  background: #ebf2ff;
}
.vue-flow__edge-custom.selected .vue-flow__edge-path {
  stroke: @primary-color;
}
.vue-flow__transformationpane {
  width: 440px;
  left: auto;
}
.vue-flow__pane.vue-flow__container {
  display: flex;
  justify-content: center;
}
</style>
