<script setup lang="ts">
import { computed } from 'vue'
import { useConditionStore } from '../../stores/condition'
import { useFlowStore } from '../../stores/flow'

const props = defineProps({
  nodeProps: {
    type: Object,
    default: () => {
      return {}
    },
  },
})
const emit = defineEmits(['select'])
const flowStore = useFlowStore()
const conditionStore = useConditionStore()
const cardWidth = computed(() => flowStore.cardWidth)

// biome-ignore lint/suspicious/noExplicitAny: off
const parentNode: any = computed(() =>
  // biome-ignore lint/suspicious/noExplicitAny: off
  flowStore.nodes.find((item: any) => item.id === props.nodeProps.parent),
)
const condition = computed(() => {
  if (!parentNode.value) return null
  return (
    parentNode.value.data.conditions.find(
      // biome-ignore lint/suspicious/noExplicitAny: off
      (item: any) => item.id === props.nodeProps.data.conditionId,
    ) || null
  )
})
const handleClick = () => {
  const { parent } = props.nodeProps
  const node = flowStore.nodes.find((n: CardNode) => n.id === parent)
  emit('select', node)
  conditionStore.setActive(props.nodeProps.id)
}

const isActive = computed(() => props.nodeProps.id === conditionStore.activeId)
</script>

<template>
  <div class="card-condition nodrag" :style="{ width: cardWidth + 'px' }">
    <div
      class="card-condition_content"
      :class="{ 'is-active': isActive }"
      @click="handleClick"
      v-if="condition"
    >
      <svg-icon name="fx" size="16px"></svg-icon>
      <span class="ml-[4px] text-[13px] leading-[22px] text-[#333333]">{{
        condition.title
      }}</span>
    </div>
    <div class="card-condition_line" v-else></div>
  </div>
</template>

<style lang="less">
.card-condition {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-self: stretch;
}
.card-condition_content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 90px;
  max-width: 100%;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background: #fff;
  text-align: center;
  padding: 0 4px;
  color: #333333;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  &:hover,
  &.is-active {
    border-color: #266eff;
    background: #ebf2ff;
    box-shadow: 0 0 3px 0 #0000000a;
  }
}
.card-condition_line {
  height: 1px;
  width: 0;
  border: 1px solid rgba(167, 173, 185, 0.5);
}
</style>
