<script setup lang="ts">
import {
  EdgeLabel<PERSON><PERSON><PERSON>,
  getBezier<PERSON>ath,
  MarkerType,
  SmoothStepEdge,
  type EdgeProps,
} from '@vue-flow/core'
import { computed, ref } from 'vue'
import { useConfigStore } from '../../stores/config'
import { CARD_ID_NAME, CARD_LIST, CARD_TYPE } from '../../stores/constants'
import { useFlowStore } from '../../stores/flow'
import type { CardItem } from '../../ts/card'
import useDragAndDrop from './drag'

const flowStore = useFlowStore()
const configStore = useConfigStore()

const props = defineProps<
  EdgeProps & {
    sourceX: number
    sourceY: number
    targetX: number
    targetY: number
  }
>()

const path = computed(() => getBezierPath(props))
const boxStyle = computed(
  () =>
    `position: absolute; transform: translate(-50%, -50%) translate(${path.value[1]}px,${props.sourceY + 16}px`,
)
const customStyle = computed(() => {
  return `width: ${flowStore.cardWidth}px; padding: 5px 10px; text-align: center; pointer-events: all;); border-radius: 2px; ${boxStyle.value}`
})
const { onDragOver, onDragLeave, isDragging, draggedType, edge, onDrop } =
  useDragAndDrop()
const dragging = ref(false)

const getEdgeData = () => {
  // biome-ignore lint/suspicious/noExplicitAny: off
  const edge: any = flowStore.edges.find(
    // biome-ignore lint/suspicious/noExplicitAny: off
    ({ data }: any) => data.id === props.data.id,
  )
  edge.markerEnd = MarkerType.ArrowClosed
  const data = JSON.parse(JSON.stringify(edge))
  data.newId = flowStore.getNewEdgeId()
  return data
}

const handleDragOver = (event: DragEvent) => {
  dragging.value = true
  const data = getEdgeData()
  onDragOver(event, data)
}

const handleDragLeave = () => {
  dragging.value = false
  onDragLeave()
}

const allowDragging = computed(() => {
  if (flowStore.isView) return false
  const source = props.data.source
  const target = props.data.target

  if (
    source.startsWith(CARD_ID_NAME[CARD_TYPE.SELECTOR]) &&
    target.startsWith(CARD_ID_NAME[CARD_TYPE.SELECTOR])
  ) {
    // 分支里面使用选择器，选择器顶部不能添加节点
    if (
      target.endsWith('_end') ||
      flowStore.nodeMap[target]?.data?.type === CARD_TYPE.SELECTOR
    )
      return true
    return false
  }
  return true
})

const cardList = computed(() => CARD_LIST)

const lineStyle = computed(() => {
  const source = props.data.source
  if (source !== flowStore.activeCardId) return {}
  return source.startsWith(CARD_ID_NAME[CARD_TYPE.SELECTOR])
    ? { stroke: '#266EFF' }
    : {}
})

const handleClick = (item: CardItem) => {
  draggedType.value = item
  const data = getEdgeData()
  edge.value = data
  onDrop(flowStore, configStore)
}

const handleDrop = () => {
  dragging.value = false
  onDrop(flowStore, configStore)
}
</script>

<template>
  <SmoothStepEdge v-bind="props" :style="lineStyle" />
  <EdgeLabelRenderer>
    <template v-if="allowDragging">
      <div
        :class="{ 'is-dragging': dragging, 'drag-box': true }"
        :style="customStyle"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
      >
        <div
          v-show="isDragging"
          class="drag-box_inner inline-block h-[14px] w-[100%]"
        ></div>
        <span
          class="add-card flex h-[14px] w-[14px] cursor-pointer items-center justify-center"
        >
          <a-popover
            position="rt"
            trigger="hover"
            :content-style="{ padding: 0, border: 0 }"
            :arrow-style="{ display: 'none' }"
            popup-container=".designer-app"
          >
            <svg-icon name="add-card" size="14px" />
            <template #content>
              <div class="add-card_popover">
                <div
                  class="add-card_item"
                  v-for="(item, key) in cardList"
                  :key="'add_' + key"
                  @click="handleClick(item)"
                >
                  <svg-icon :name="item.icon" size="16px" />
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </template>
          </a-popover>
        </span>
      </div>
    </template>
  </EdgeLabelRenderer>
</template>

<style lang="less">
.add-card {
  background: #f3f4f9;
  cursor: pointer;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  use {
    color: #a7adb9;
  }
}
.drag-box {
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.drag-box_inner {
  background: transparent;
}
.is-dragging .add-card,
.add-card:hover {
  use {
    color: #266eff;
  }
}
.is-dragging {
  .add-card {
    background: transparent;
  }
  .drag-box_inner {
    background: #ccddff;
  }
}
.add-card_popover {
  width: 120px;
  padding: 4px 0;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background: #fff;
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.1);
}
.add-card_item {
  display: flex;
  padding: 5px 12px;
  align-items: center;
  flex-direction: row;
  width: 100%;
  height: 32px;
  box-sizing: border-box;
  cursor: pointer;
  > span {
    color: #333;
    text-overflow: ellipsis;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-left: 8px;
  }
  &:hover {
    background: #ebf2ff;
    > span {
      color: #266eff;
    }
  }
}
</style>
