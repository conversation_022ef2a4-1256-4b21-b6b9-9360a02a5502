<script setup lang="ts">
import { <PERSON>le, Position, useVueFlow, type Edge } from '@vue-flow/core'
import { computed, ref } from 'vue'
import { CARD_TYPE } from '../../stores/constants'
import { useFlowStore } from '../../stores/flow'
import { useVerifiesStore } from '../../stores/verifies'
import { getStepName } from '../../utils'

const { updateEdge, removeNodes, removeEdges } = useVueFlow()

const flowStore = useFlowStore()

const props = defineProps({
  nodeProps: {
    type: Object,
    default: () => {},
  },
  edges: {
    type: Object,
    default: () => [],
  },
})
const emit = defineEmits(['select'])
const visible = ref(false)
const data = computed(() => props.nodeProps.data)
const isConnectTop = computed(() =>
  props.edges.some((item: Edge) => item.target === props.nodeProps.id),
)
const isConnectBottom = computed(() =>
  props.edges.some((item: Edge) => item.source === props.nodeProps.id),
)

const nodeWidth = computed(() => {
  // if (props.nodeProps.data.type === CARD_TYPE.SELECTOR) return 240
  return flowStore.cardWidth
})

const handleDelete = () => {
  if (props.nodeProps.id === flowStore.activeCardId) {
    emit('select', flowStore.flowNodes[0])
  }
  if (props.nodeProps.data.type === CARD_TYPE.SELECTOR) {
    emit('select', flowStore.flowNodes[0])
    flowStore.removeSelectorNodes(props.nodeProps.id, true)
  } else {
    flowStore.removeNode(props.nodeProps.id)
    const { targetItem, sourceItem } = flowStore.removeEdge(props.nodeProps.id)
    if (!sourceItem.markerEnd) {
      // biome-ignore lint/performance/noDelete: off
      delete targetItem.markerEnd
    }
    updateEdge(targetItem, targetItem)
    removeNodes(props.nodeProps.id)
    removeEdges(sourceItem)
    flowStore.updatePosition()
  }
}

// const handleAddNode = () => {
//     const node = props.nodeProps
//     const copyNode = {
//         id: Math.random().toString(),
//         type: 'card',
//         position: { x: node.position.x + 20, y: node.position.y + 20 },
//         data: {
//             ...node.data,
//             name: '副本：' + node.data.name
//         }
//     }
//     addNodes(copyNode)
// }

const isActive = computed(() => !flowStore.isView && flowStore.activeCardId === props.nodeProps.id)

const handleClick = () => {
  emit('select', props.nodeProps)
}
const showTopHandle = computed(
  () => !flowStore.isView && props.nodeProps.data.type !== CARD_TYPE.START,
)
const showBottomHandle = computed(
  () => !flowStore.isView && props.nodeProps.data.type !== CARD_TYPE.FINISH,
)
const stepName = computed(() => {
  const index = flowStore.nodeOrder.findIndex((id: string) => id === props.nodeProps.id)
  return getStepName(index) || ''
})
const verifiesStore = useVerifiesStore()
const isVerified = computed(() => !!verifiesStore.verifiesData?.[props.nodeProps.id])

const onDel = () => {
  visible.value = true
}

const stepNameContent = computed(() => {
  return stepName.value + (data.value.name || data.value.describe || '')
})

const isWarning = computed(() => {
  return flowStore.warningNodes?.some(({ id }) => id === props.nodeProps.id)
})
</script>

<template>
  <Handle
    v-if="showTopHandle"
    type="source"
    :position="Position.Top"
    :class="{
      'handle-hidden': isConnectTop,
    }"
  />
  <div
    :class="{ 'node-active': isActive, nodrag: true }"
    @click="handleClick"
    :style="{ width: nodeWidth + 'px' }"
    class="card-node flex-col items-center justify-center rounded-[4px] border-[1px] border-solid
      border-[#EAEAEA] bg-[#FFF] px-[12px] py-[10px]"
  >
    <div class="flex items-center justify-center gap-[6px] self-stretch leading-[22px]">
      <svg-icon v-if="isWarning" name="warning" color="#ff9902" size="20px" />
      <svg-icon v-else name="success" size="20px" :color="isVerified ? '#1CC78D' : '#C5C5C5'" />
      <span class="ml-[6px] w-[194px] text-[13px] font-semibold not-italic text-[#333333]">{{
        data._actionName
      }}</span>
    </div>
    <div
      class="text-ellipsis text-xs font-normal not-italic leading-[20px] text-[#999999]"
      :title="stepNameContent"
    >
      {{ stepNameContent }}
    </div>

    <a-popover
      position="bottom"
      :arrow-style="{ display: 'none' }"
      :content-style="{
        padding: '4px 0',
        display: 'inline-block',
        width: '82px',
      }"
      popup-container=".designer-app"
    >
      <svg-icon
        v-if="showTopHandle && showBottomHandle"
        name="more"
        size="16px"
        class="card-btn absolute right-[12px] top-[12px] cursor-pointer"
      />
      <template #content>
        <!-- <a-button type="text" class="float-left w-[100%]" style="color: #333" @click="handleAddNode">复制</a-button> -->
        <a-button type="text" class="float-left w-[100%]" style="color: #333" @click="onDel"
          >删除</a-button
        >
      </template>
    </a-popover>
  </div>
  <Handle
    v-if="showBottomHandle"
    type="source"
    :position="Position.Bottom"
    :class="{
      'handle-hidden': isConnectBottom,
    }"
  />
  <a-modal
    v-model:visible="visible"
    :simple="true"
    title-align="start"
    popup-container=".designer-app"
    @ok="handleDelete"
  >
    <template #title>
      <div class="flex flex-row justify-center">
        <svg-icon name="warning" color="#FF9902" size="24px" />
        <span class="ml-[12px] text-base font-semibold not-italic leading-[24px] text-[#333333]"
          >确认删除</span
        >
      </div>
    </template>
    <div class="ml-[36px] text-[13px] font-normal not-italic leading-[22px] text-[#666666]">
      是否确认删除当前卡片？
    </div>
  </a-modal>
</template>

<style>
.card-btn {
  visibility: hidden;
}
.card-node:hover .card-btn {
  visibility: visible;
}
</style>
