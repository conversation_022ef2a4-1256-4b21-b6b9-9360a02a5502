import { MarkerType } from '@vue-flow/core'
import { ref, watch } from 'vue'
import {
  CARD_ID_NAME,
  CARD_NODES,
  CARD_TYPE,
  getSelectorData,
} from '../../stores/constants'

interface Item {
  id?: string
  type?: string
  label?: string
  // biome-ignore lint/suspicious/noExplicitAny: off
  data?: any
  position?: { x: number; y: number }
}
const state = {
  draggedType: ref({} as Item),
  isDragOver: ref(false),
  isDragging: ref(false),
  edge: ref({}),
}

export default function useDragAndDrop() {
  const { draggedType, isDragOver, isDragging, edge } = state

  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging ? 'none' : ''
  })

  // biome-ignore lint/suspicious/noExplicitAny: off
  function onDragStart(event: DragEvent, item: any) {
    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vueflow', item.type)
      event.dataTransfer.effectAllowed = 'move'
    }

    draggedType.value = item
    isDragging.value = true

    document.addEventListener('drop', onDragEnd)
  }

  function onDragOver(event: DragEvent, item: string) {
    event.preventDefault()

    if (draggedType.value) {
      isDragOver.value = true
      edge.value = item

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false
    edge.value = {}
  }

  function onDragEnd() {
    isDragging.value = false
    isDragOver.value = false
    draggedType.value = {}
    document.removeEventListener('drop', onDragEnd)
  }

  // biome-ignore lint/suspicious/noExplicitAny: off
  function getCardNode(flowStore: any, item: any) {
    if (!item || !item.type) return {}
    const idName = CARD_ID_NAME[item.type]
    const { id, index } = flowStore.getNewId(idName, true)
    const { name, config } = JSON.parse(JSON.stringify(CARD_NODES[item.type]))

    // biome-ignore lint/suspicious/noExplicitAny: off
    const node: any = {
      id,
      code: id,
      name: name,
      type: 'card',
      position: { x: 100, y: 0 },
      data: {
        _id: id,
        ...config,
        name: `${name}_${index}`,
        _actionName: name,
        type: item.type,
      },
    }
    return node
  }

  // biome-ignore lint/suspicious/noExplicitAny: off
  function onDrop(flowStore: any, configStore: any) {
    // biome-ignore lint/suspicious/noExplicitAny: off
    let newNode: any = getCardNode(flowStore, draggedType.value)
    if (!newNode?.id) return
    const isSelector = newNode.data.type === CARD_TYPE.SELECTOR

    // biome-ignore lint/suspicious/noExplicitAny: off
    let selectorData: any = {}
    if (isSelector) {
      selectorData = getSelectorData(
        newNode.id,
        flowStore.getNewEdgeId(true) + 1,
      )
      // biome-ignore lint/suspicious/noExplicitAny: off
      newNode = selectorData.nodes.find((item: any) => item.id === newNode.id)
    }
    // biome-ignore lint/suspicious/noExplicitAny: off
    const edgeValue: any = edge.value
    newNode.parentNode = flowStore.nodeMap[edgeValue.target]?.parentNode || ''
    const isConditionEnd =
      edgeValue.target.startsWith(CARD_ID_NAME[CARD_TYPE.SELECTOR]) &&
      edgeValue.target.endsWith('_end')

    // biome-ignore lint/suspicious/noExplicitAny: off
    const newEdge: any = {
      id: edgeValue.newId,
      code: edgeValue.newId,
      markerEnd: MarkerType.ArrowClosed,
      source: isSelector ? `${newNode.id}_merge0` : newNode.id,
      sourceHandle: `${newNode.id}__handle-bottom`,
      target: edgeValue.target,
      type: 'custom',
    }
    if (isConditionEnd) {
      // biome-ignore lint/performance/noDelete: off
      delete newEdge.markerEnd
    }
    newEdge.data = JSON.parse(JSON.stringify(newEdge))
    const updateData = {
      ...edgeValue,
      target: newNode.id,
    }
    if (isSelector) {
      flowStore.addSelector(selectorData)
    } else {
      flowStore.addNode(newNode)
    }
    flowStore.updateEdge(updateData)
    flowStore.addEdge(newEdge)
    flowStore.updatePosition()
    flowStore.setActiveCard(newNode)
    configStore.setData(newNode)
    // 生成步骤后清空
    draggedType.value = {}
  }

  return {
    draggedType,
    edge,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onDrop,
  }
}
