<script setup lang="ts">
import { computed } from 'vue'
import { useFlowStore } from '../../stores/flow'

const flowStore = useFlowStore()
const cardWidth = computed(() => flowStore.cardWidth)
</script>

<template>
  <div class="card-merge nodrag" :style="{ width: cardWidth + 'px' }">
    <div class="card-merge_content">
      <svg-icon name="merge" size="16px" />
    </div>
  </div>
</template>

<style lang="less">
.card-merge {
  display: flex;
  justify-content: center;
  cursor: pointer;
}
.card-merge_content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  background: #fff;
  box-shadow: 0 3px 8px 0 #0000001a;
}
</style>
