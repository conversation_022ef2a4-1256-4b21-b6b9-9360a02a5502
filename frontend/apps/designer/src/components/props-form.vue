<script setup lang="ts">
import { computed, type Ref, ref, watch } from 'vue'
import { useConfigStore } from '../stores/config'
import { CARD_TEMPLATE_TYPE } from '../stores/configData'
import { CARD_TYPE } from '../stores/constants'
import { useFlowStore } from '../stores/flow'
import { useVerifiesStore } from '../stores/verifies'
import AiPrompt from './subControls/ai-prompt.vue'
import AuxiliaryInputForm from './subControls/auxiliary-input-form.vue'
import CardForm from './subControls/card-form.vue'
import DocumentAnalysisConfig from './subControls/document-analysis-config.vue'
import DocumentAnalysisForm from './subControls/document-analysis-form.vue'
import EditorForm from './subControls/editor-form.vue'
import FieldForm from './subControls/field-form.vue'
import UploadParams from '../agent/upload-params.vue'
import ImageAnalysis from './subControls/image-analysis.vue'
import ImageRecognize from './subControls/image-recognize.vue'
import Intention from './subControls/intention.vue'
import interactiveForm from './subControls/interactive-form.vue'
import KnowledgeForm from './subControls/knowledge-form.vue'
import MemoryComponent from './subControls/memory-component.vue'
import OutputParameters from './subControls/output-parameters.vue'
import PluginApi from './subControls/plugin-api.vue'
import SelectTemplate from './subControls/select-template.vue'
import SelectorForm from './subControls/selector-form.vue'
import SmartCheck from './subControls/smart-check.vue'
import TemplateEditor from './subControls/template-editor.vue'
import TextTemplateForm from './subControls/text-template-form.vue'
import CardOutputs from './subControls/card-outputs.vue'
import FieldFilling from './subControls/field-filling.vue'
import ChatBiForm from './subControls/chat-bi-form.vue'

const configStore = useConfigStore()
const flowStore = useFlowStore()
const verifiesStore = useVerifiesStore()
const configData: Ref<ConfigDataType[]> = ref([])
const advancedconfig: Ref<ConfigDataType[]> = ref([])
const visibleAdvancedconfig = ref(false)

// biome-ignore lint/suspicious/noExplicitAny: off
const formData: any = computed(() => {
  return (
    flowStore.nodes.find((item: { id: string }) => item.id === flowStore.activeCardId)?.data || {}
  )
})

// biome-ignore lint/suspicious/noExplicitAny: off
const nodeId: any = computed(() => flowStore.activeCardId)
// biome-ignore lint/suspicious/noExplicitAny: off
const component: any = computed(() => {
  switch (configStore.activeType) {
    default:
      return null
  }
})
const showTip = computed(() => {
  return configStore.activeType === CARD_TYPE.FINISH
})

// biome-ignore lint/suspicious/noExplicitAny: off
let timer: any = null
const messages = ref('')
watch(
  () => formData.value,

  // biome-ignore lint/suspicious/noExplicitAny: off
  (v: any) => {
    verifiesStore.verify({
      data: v,
      cardId: nodeId.value,
      cardType: configStore.activeType,
    })
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      messages.value = flowStore.onVerifyParams()
    }, 1000)
    // flowStore.onAutoSave(
    //   JSON.parse(JSON.stringify(v)),
    //   JSON.parse(JSON.stringify(configStore.oldFormData)),
    //   () => {
    //     if (nodeId.value === v._id) {
    //       configStore.setOldData(v)
    //     }
    //   },
    // )
  },
  { deep: true },
)

watch(
  () => configStore.configData,
  (v: ConfigDataType[]) => {
    const data = JSON.parse(JSON.stringify(v))

    // biome-ignore lint/suspicious/noExplicitAny: off
    configData.value = data.filter((item: any) => !item.advancedconfig)
    advancedconfig.value =
      // biome-ignore lint/suspicious/noExplicitAny: off
      data.filter((item: any) => !!item.advancedconfig && !item.hide) || []
  },
)

const handleClick = () => {
  visibleAdvancedconfig.value = !visibleAdvancedconfig.value
}

const stepNameRules = computed(() => {
  return [
    { required: true, message: '请输入步骤名称' },
    {
      // biome-ignore lint/suspicious/noExplicitAny: off
      validator: (value: string, cb: any) => {
        // biome-ignore lint/suspicious/noExplicitAny: off
        const isRepeat = flowStore.nodes.some((item: any) => {
          return item.data.name === value && item.id !== formData.value._id
        })
        if (isRepeat) {
          cb('步骤名称不允许重复')
        } else {
          cb()
        }
      },
    },
  ]
})

const handleInputs = (val: InputParamValue[], disabled: boolean) => {
  if (!disabled) {
    formData.value.inputs = val
  }
}

const refreshKey = ref(0)
const appendConfig: Ref<ConfigDataType[]> = ref([])
const updateConfig = (data: { type: string; variables: VariablesConfigType[] }) => {
  appendConfig.value = []

  // biome-ignore lint/suspicious/noExplicitAny: off
  const item = configData.value.find((item: any) => item.type === data.type)
  if (item) {
    item.variables = data.variables.filter((item: VariablesConfigType) => !item.advancedconfig)
    refreshKey.value += 1
  }
  data?.variables?.filter((item: VariablesConfigType) => {
    if (item.advancedconfig) {
      appendConfig.value.push({
        type: item.name,
        label: item.label,
        prop: item.name,
      })
    }
  })

  if (
    formData.value?.type === CARD_TYPE.INTERACTIVE &&
    formData.value.templateId === CARD_TEMPLATE_TYPE.FORM
  ) {
    appendConfig.value.push({ type: 'card-form-tips', prop: 'tip' })
  }
}

const computedAdvancedconfig = computed(() => [...advancedconfig.value, ...appendConfig.value])

watch(
  () => nodeId.value,
  () => {
    appendConfig.value = []
  },
)
</script>

<template>
  <a-alert v-if="messages" type="warning" class="flow-alert warning"
    >{{ messages }}</a-alert
  >
  <a-alert v-if="showTip" class="flow-alert"
    >输出参数用于技能 SDK 调用场景，对话框交互无需配置输出参数，默认使用 AI 提示。</a-alert
  >
  <component v-if="component" :is="component" />
  <a-form
    v-else
    label-align="left"
    class="field-form"
    :key="nodeId"
    :model="formData"
    :style="{ width: '100%', padding: '20px', overflow: 'hidden' }"
  >
    <template v-for="item of configData">
      <a-form-item
        v-if="item.type === 'input'"
        :field="item.prop"
        :label="item.label"
        :rules="item.prop === 'name' ? stepNameRules : item.rules || []"
        :style="{ width: item.width || '100%' }"
      >
        <span v-if="!!item.readonly">{{ formData[item.prop] }}</span>
        <a-input v-else v-model="formData[item.prop]" :placeholder="item.placeholder" />
      </a-form-item>

      <a-form-item
        v-if="item.type === 'textarea'"
        :field="item.prop"
        :label="item.label"
        :style="{ width: item.width || '100%' }"
      >
        <span v-if="!!item.readonly">{{ formData[item.prop] }}</span>
        <a-textarea
          v-else
          v-model="formData[item.prop]"
          :placeholder="item.placeholder"
          :maxLength="item.maxLength"
        />
      </a-form-item>

      <a-form-item
        v-if="item.type === 'select'"
        :field="item.prop"
        :label="item.label"
        :required="!!item.required"
        :style="{ width: item.width || '100%' }"
      >
        <a-select v-model="formData[item.prop]" :placeholder="item.placeholder">
          <a-option
            v-for="(option, _index) of item.options"
            :value="option.value"
            :key="'option_' + _index"
            >{{ option.label }}</a-option
          >
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="item.type === 'checkbox'"
        :field="item.prop"
        :label="item.label"
        :style="{ width: item.width || '100%' }"
      >
        <a-checkbox v-model="formData[item.prop]"></a-checkbox>
      </a-form-item>

      <template v-if="item.type === 'dynamicForms'">
        <a-form-item v-if="item.label" :label="item.label"></a-form-item>
        <FieldForm
          :key="'dynamicForms' + item.prop"
          :value="formData[item.prop]"
          :config="item.props"
          :enable-sort="true"
          :is-fill-field="!!item.enableFillField"
          @input="formData[item.prop] = $event"
        />
      </template>

      <a-form-item
        v-if="item.type === 'title'"
        :label="item.label"
        class="form-item_title"
      ></a-form-item>

      <template v-if="item.type === 'template'">
        <a-form-item v-if="item.label" :label="item.label"></a-form-item>
        <TemplateEditor
          :value="formData[item.prop]"
          :inputs="formData.inputs"
          @inputs="formData.inputs = $event"
          :minHeight="item.minHeight"
          :placeholder="item.placeholder"
          @input="formData[item.prop] = $event"
        />
      </template>

      <template v-if="item.type === 'field-filling'">
        <a-form-item
          v-if="item.label"
          :label="item.label"
          :hide-label="!!item.hideLabel"
          :required="!!item.required"
        >
          <FieldFilling
            :value="formData[item.prop]"
            :inputs="formData.inputs"
            @inputs="(v) => handleInputs(v, !!item.disabledInputs)"
            :minHeight="item.minHeight"
            :placeholder="item.placeholder"
            :input-disabled="item.inputDisabled"
            @input="formData[item.prop] = $event"
          />
        </a-form-item>
      </template>

      <template v-if="item.type === 'plugin-api'">
        <PluginApi :form-data="formData" :config="item" @input="formData = $event" />
      </template>

      <template v-if="item.type === 'output-type'">
        <a-form-item :label="item.label" class="form-item_title"></a-form-item>
        <OutputParameters
          :key="'params' + formData._id"
          :form-data="formData"
          :config="item.config"
          :defaultData="item.defaultData"
          @input="formData = $event"
        />
      </template>

      <template v-if="item.type === 'card-form'">
        <CardForm
          :form-data="formData"
          :config="item.config"
          :label="item.label"
          @input="formData = $event"
        />
      </template>

      <template v-if="item.type === 'knowledge-form'">
        <KnowledgeForm :form-data="formData" @input="formData = $event" />
      </template>

      <template v-if="item.type === 'chat-bi-form'">
        <ChatBiForm :form-data="formData" @input="formData = $event" />
      </template>

      <template v-if="item.type === 'select-template'">
        <select-template :form-data="formData" :item="item" />
      </template>

      <template v-if="item.type === 'ai-prompt'">
        <ai-prompt :form-data="formData"></ai-prompt>
      </template>

      <template v-if="item.type === 'image-analysis'">
        <ImageAnalysis :form-data="formData" />
      </template>

      <template v-if="item.type === 'selector'">
        <selector-form :id="formData._id" :conditions="formData.conditions" />
      </template>

      <template v-if="item.type === 'interactive-form'">
        <interactive-form :form-data="formData" @update-config="updateConfig" />
      </template>

      <template v-if="item.type === 'document-analysis'">
        <DocumentAnalysisForm :form-data="formData" />
      </template>

      <template v-if="item.type === 'auxiliary-input'">
        <AuxiliaryInputForm :form-data="formData" />
      </template>

      <template v-if="item.type === 'text-template'">
        <TextTemplateForm :form-data="formData" />
      </template>

      <template v-if="item.type === 'table'">
        <a-table
          :pagination="false"
          class="custom-grid"
          :columns="item.columns"
          :data="formData[item.prop]"
        />
        <div v-if="item.tips" class="text-tips" v-html="item.tips"></div>
      </template>

      <template v-if="item.type === 'intention'">
        <Intention :value="formData[item.prop]" :key="nodeId" />
      </template>

      <template v-if="item.type === 'image-recognize'">
        <ImageRecognize :form-data="formData" :config="item.config" />
      </template>

      <template v-if="item.type === 'smart-check'">
        <SmartCheck :form-data="formData" :config="item.config" />
      </template>

      <template v-if="item.type === 'variables' && item.variables?.length">
        <editor-form
          :key="formData.templateId + refreshKey"
          :card-config="item.variables"
          :data="formData.props"
          :outputs="formData.outputs"
          @outputs="formData.outputs = $event"
        ></editor-form>
        <CardOutputs
          v-if="formData.templateId === CARD_TEMPLATE_TYPE.IMPLANT"
          :form-data="formData"
        ></CardOutputs>
      </template>
      <template v-if="item.type === 'upload-params'">
        <upload-params title="文件上传" :data="formData" :show-tips="true" :support-files="[]" />
      </template>
    </template>
    <div class="mt-[12px]" v-if="!!computedAdvancedconfig.length">
      <div class="inline-block flex cursor-pointer flex-row items-center" @click="handleClick">
        <span class="form-item_title mr-[8px]">高级配置</span>
        <svg
          :style="{
            transform: `rotate(${visibleAdvancedconfig ? 0 : 180}deg)`,
          }"
          class="rotate-180 transform"
          fill="none"
          stroke="currentColor"
          stroke-width="4"
          viewBox="0 0 48 48"
          width="1em"
          height="1em"
        >
          <path d="M39.6 17.443 24.043 33 8.487 17.443"></path>
        </svg>
      </div>
      <div class="mt-[12px]" v-if="visibleAdvancedconfig">
        <template v-for="item of computedAdvancedconfig">
          <a-form-item
            v-if="item.type === 'checkbox'"
            :field="item.prop"
            :label="item.label"
            :style="{ width: item.width || '100%' }"
          >
            <a-checkbox v-model="formData[item.prop]"></a-checkbox>
            <template #help v-if="item.tips">
              <div v-html="item.tips"></div>
            </template>
          </a-form-item>
          <MemoryComponent v-if="item.type === 'memory-component'" :form-data="formData" />
          <DocumentAnalysisConfig
            :form-data="formData"
            v-if="item.type === 'document-scope'"
          ></DocumentAnalysisConfig>

          <div class="skill-config-tips" v-if="item.type === 'card-form-tips'">
            <svg-icon size="16px" name="tips" />
            <div class="skill-config-tips_content">
              <div class="skill-config-tips_title">置信度：</div>
              <div class="skill-config-tips_describe">
                在文档和图片分析节点中使用视觉识别能力时，系统会自动生成置信度评分。置信度评分数值越高，表示系统对识别结果的确信程度越高。用户可以针对置信度较低的字段进行核对和修订，确认无误后再录入到系统。
              </div>
            </div>
          </div>

          <template v-if="item.type === 'actions'">
            <form-actions :form-data="formData" />
          </template>
        </template>
      </div>
    </div>
  </a-form>
</template>

<style lang="less">
.arco-alert.flow-alert {
  border-radius: 4px;
  border: 1px solid #a1caff;
  background: #ebf2ff;
  margin: 32px 20px 0 20px;
  width: calc(100% - 40px);
  &.arco-alert-info .arco-alert-content {
    color: #666;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
  }
  &.warning {
    border-color: rgb(255 125 0 / 60%);
    background: #fff7e8;
  }
}
.field-form {
  .arco-form-item-label-col > .arco-form-item-label {
    color: #666666;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .arco-form-item-label-col-left .arco-form-item-label {
    height: 32px;
    padding: 5px 0;
    box-sizing: border-box;
  }
  .form-item_title,
  .form-item_title .arco-form-item-label-col > .arco-form-item-label {
    color: #333333;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
  .arco-form-item-content > span {
    color: #333333;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .arco-form-item-label-required-symbol {
    float: left;
    padding: 2px 0;
  }
}
.skill-config-tips {
  padding: 9px 12px;
  border-radius: 4px;
  background: #f8f8f8;
  display: inline-block;
  > svg {
    margin-top: 3px;
    float: left;
  }
  .skill-config-tips_title {
    color: #333333;
    font-size: 13px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 4px;
  }
  .skill-config-tips_describe {
    color: #666666;
    font-size: 13px;
    line-height: 22px;
  }
  .skill-config-tips_content {
    padding-left: 8px;
    float: left;
    width: calc(100% - 16px);
  }

}
.form-textarea{
  .form-textarea-header {
    height: 34px;
    width: 100%;
    border-bottom: 1px solid #dddddd;
    color: #333;
    font-size: 14px;
    line-height: 22px;
    padding: 6px 16px;
    box-sizing: border-box;
    border-radius: 4px 4px 0 0;
    background-color: #fbfbfb;
    border: 1px solid #ddd;
    text-align: right;
    span {
      cursor: pointer;
      &:hover{
       color: #266EFF;
      }
    }
  }
  .arco-textarea-wrapper {
    border-top-right-radius: 0 !important;
    border-top-left-radius: 0 !important;
    border-top: 0px !important;
  }
}
</style>
