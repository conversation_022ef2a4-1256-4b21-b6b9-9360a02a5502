<script setup lang="ts">
defineProps<{
  message: string
  type: 'error' | 'success' | 'info' | 'warning' | 'primary'
  time?: string
}>()
</script>

<template>
  <div
    :class="`save-status_${type}`"
    class="ml-[16px] flex h-[24px] flex-row items-center gap-[4px] rounded-[12px] px-[8px] py-[2px] font-normal
      not-italic leading-[20px]"
  >
    <svg-icon v-if="type === 'success'" name="success" color="#1cc78d" size="14px" />
    <svg-icon v-if="type === 'error'" name="warning" color="#ff4c4c" size="14px" />
    <svg-icon v-if="type === 'warning'" name="warning" color="#FF9902" size="14px" />
    <svg-icon v-if="type === 'primary'" name="warning" color="#266EFF" size="14px" />
    <span class="ml-[4px] text-xs"
      >{{ message }}<span class="ml-[4px]" v-if="time">{{ time }}</span></span
    >
  </div>
</template>

<style lang="less">
.save-status_success {
  background: #1cc78d1a;
  color: #13a374;
}
.save-status_error {
  background: #ff4c4c1a;
  color: #ff4c4c;
}
.save-status_info {
  background: #0000000d;
  color: #666666;
}
.save-status_warning {
  background: #ff99021a;
  color: #ff9902;
}
.save-status_primary {
  background: #ebf2ff;
  color: #266eff;
}
</style>
