<template>
  <a-modal
    v-model:visible="visible"
    :simple="true"
    title-align="start"
    popup-container=".designer-app"
    @ok="handleDelete"
    @close="handleClose"
  >
    <template #title>
      <div class="flex flex-row justify-center">
        <svg-icon name="warning" color="#FF9902" size="24px" />
        <span class="ml-[12px] text-base font-semibold not-italic leading-[24px] text-[#333333]"
          >确认删除</span
        >
      </div>
    </template>
    <div class="text-[13px] font-normal not-italic leading-[22px] text-[#666666]">
      {{ content }}
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  content: {
    type: String,
    default: '',
  },
})
const visible = ref(false)
watch(
  () => props.show,
  () => {
    visible.value = props.show
  },
)
const emit = defineEmits(['onOk', 'input'])
const handleDelete = () => {
  emit('onOk')
}
const handleClose = () => {
  emit('input', false)
}
</script>
