<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { MODE_TYPE } from '../stores/constants'
import { useFlowStore } from '../stores/flow'
import { usePromptStore } from '../stores/prompt'
import { getParams } from '../utils'
import TipsIcon from '../components/tips-icon.vue'

const flowStore = useFlowStore()
const promptStore = usePromptStore()
const loading = ref(false)

const showBeta = computed(() => flowStore.flowData.mode === 'flow')
const handleSubmit = () => {
  loading.value = true
  if (flowStore.mode === MODE_TYPE.DESIGNER) {
    flowStore.saveData(true).finally(() => {
      loading.value = false
    })
  }
  if (flowStore.mode === MODE_TYPE.PROMPT) {
    const len = promptStore.messageContent.length
    if (len > 0 && promptStore.messageContent[len - 1].role !== 'user') {
      Message.warning('启用少样本模式后，最后一条对话必须是用户提问！')
      loading.value = false
      return
    }
    promptStore.saveData().finally(() => {
      loading.value = false
    })
  }
}

// biome-ignore lint/suspicious/noExplicitAny: off
const handleKeydown = (e: any) => {
  if (flowStore.mode === MODE_TYPE.VIEW) return
  const ctrlKey = navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey
  if (ctrlKey && e.keyCode === 83) {
    e.preventDefault()
    handleSubmit()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})

const saveStatus = computed(() => flowStore.saveStatus)
const isDesigner = computed(() => flowStore.mode === MODE_TYPE.DESIGNER)
const isPrompt = computed(() => flowStore.mode === MODE_TYPE.PROMPT)

const handleBack = () => {
  const params = getParams()
  const isFrame = !!window.frameElement || window.parent !== window
  const embedpage = isFrame ? '&embedpage=1' : ''
  let href = ''
  if (isDesigner.value) {
    href = `/std/42000301/08dc89df-f23b-4061-85de-f2f20054f617?mode=2&autoTitle=true&_mp=crumbs&oid=${params.oid}&spaceGUID=${params.space}&_title=技能${embedpage}`
  }
  if (isPrompt.value) {
    href = `/std/42001201/08dc8b7c-8383-4e0e-8739-c28f29f61e9f?mode=2&autoTitle=true&_mp=crumbs&oid=${params.oid}&spaceGUID=${params.space}&_title=提示词${embedpage}`
  }
  if (href) {
    window.open(href, '_blank')
  }
}

const title = computed(() => {
  if (isDesigner.value) return flowStore.flowData.name || '技能编排'
  if (isPrompt.value) return '提示词测试'
})
</script>

<template>
  <div class="designer-layout-header inline-block h-full w-full px-[20px] py-[14px]">
    <div
      class="float-left flex max-w-[calc(100%_-_320px)] flex-row items-center justify-center text-base font-normal not-italic leading-[32px] text-[#333]"
    >
      <span
        class="mr-[12px] min-w-[16px] cursor-pointer leading-[0] opacity-50 hover:opacity-100"
        @click="handleBack"
      >
        <svg-icon name="back" size="16px" />
      </span>
      <span
        :title="title"
        class="inline-block overflow-hidden overflow-ellipsis whitespace-nowrap"
        >{{ title }}</span
      >
      <svg-icon
        name="edit"
        size="16px"
        class="ml-[8px] min-w-[16px] cursor-pointer"
        @click="handleBack"
      ></svg-icon>
      <span class="designer-layout-header__beta min-w-[42px]" v-if="showBeta">
        <svg-icon name="beta" height="21px" width="42px" />
      </span>
      <tips-icon
        v-if="saveStatus.status"
        :message="saveStatus.message"
        :time="saveStatus.time"
        type="success"
      />
      <tips-icon
        v-if="!saveStatus.status && saveStatus.errorMessage"
        :message="saveStatus.errorMessage"
        time=""
        type="error"
      />
      <tips-icon
        v-if="!saveStatus.status && saveStatus.time"
        message="上次保存"
        :time="saveStatus.time"
        type="info"
      />
    </div>
    <div class="float-right flex flex-row items-center">
      <div
        class="ml-[16px] flex flex-row items-center text-[13px] font-normal not-italic leading-[22px]
          text-[#ffffff]"
      >
        <div
          class="mr-[16px] flex flex-row items-center text-xs font-normal not-italic leading-[20px] text-[#999999]"
        >
          <svg-icon name="keyboard" size="16px" class="mr-[4px]" />
          快捷保存: Ctrl+S
        </div>
        <a-button
          :loading="loading"
          type="text"
          @click="handleSubmit"
          class="rounded-[20px] bg-[#266EFF] text-white"
        >
          <template #icon>
            <svg-icon name="save" size="14px" color="white" v-if="!loading" />
          </template>
          <span class="text-[13px] text-white">保存</span>
        </a-button>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.designer-layout-header {
  &__beta {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
  }
  .arco-btn {
    .arco-icon {
      stroke: #fff;
    }
    .arco-btn-icon {
      line-height: 0;
      margin-right: 4px !important;
    }
    &.arco-btn-text:hover,
    &.arco-btn-text[type='button']:hover,
    &.arco-btn-text[type='submit']:hover {
      opacity: 0.8;
      background: #266eff;
    }
    &.arco-btn-loading {
      background: #266eff !important;
    }
  }
}
</style>
