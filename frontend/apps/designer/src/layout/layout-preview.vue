<script setup lang="ts">
import { useFlowStore } from '../stores/flow'

if (!window._gpt) window._gpt = {}
window._gpt.visible = false
// biome-ignore lint/suspicious/noExplicitAny: off
const flowStore: any = useFlowStore()
const handleClick = () => {
  if (window._gpt?.open) {
    window._gpt.debug = true
    const { id } = flowStore.data || {}
    if (!id) return
    window._gpt.open({
      skills: [id],
      defaultSkill: id,
    })
  }
}
</script>

<template>
  <div
    @click="handleClick"
    class="absolute right-[0] top-[20px] flex cursor-pointer flex-col items-center justify-center
      rounded-bl-[8px] rounded-br-[0] rounded-tl-[8px] rounded-tr-[0]
      bg-[linear-gradient(90deg,_#5C8AFF_0%,_#786CFF_100%)] px-[4px] py-[6px]
      [box-shadow:-4px_0_6px_-6px_#0000000f] [transition:0.3s] hover:opacity-90"
  >
    <svg-icon name="preview" size="16px" />
    <span
      class="mt-[4px] text-xs font-normal not-italic leading-[16px] tracking-[4px] text-[#ffffff]
        [writing-mode:vertical-rl]"
      >预览</span
    >
  </div>
</template>
