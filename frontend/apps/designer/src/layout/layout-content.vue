<script setup lang="ts">
import Tool from '../components/flow/sidebar/tool.vue'
import VueFlow from '../components/flow/vue-flow.vue'
import PropsForm from '../components/props-form.vue'
</script>

<template>
  <div class="w-[100%] h-[100%]">
    <div
      class="h-[100%] w-[64px] border-r-[1px] border-s-[#EAEAEA] float-left overflow-y-auto"
    >
      <Tool />
    </div>
    <div class="h-[100%] float-left layout-content_inner box-border">
      <div class="layout-content_flow float-left h-[100%]">
        <VueFlow />
      </div>
      <div
        class="layout-content_attribute float-left w-[45%] h-[100%] overflow-y-auto pr-[12px]"
      >
        <PropsForm />
      </div>
    </div>
  </div>
</template>

<style>
.layout-content_inner {
  width: calc(100% - 64px);
}
.layout-content_flow {
  width: calc(100% - 45%);
}
.layout-content_attribute {
  border-left: 1px solid #eaeaea;
}
</style>
