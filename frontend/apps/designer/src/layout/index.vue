<script setup lang="ts">
import { computed, onMounted } from 'vue'
import ErrorView from '../components/error.vue'
import VueFlow from '../components/flow/vue-flow.vue'
import Designer from '../page/designer.vue'
import Prompt from '../page/prompt.vue'
import { MODE_TYPE } from '../stores/constants'
import { useFlowStore } from '../stores/flow'
import LayoutHeader from './layout-header.vue'
import Agent from '../page/agent.vue'
import { useVerifiesStore } from '../stores/verifies'
import { useDesignerSdk } from '../stores/sdk'

const flowData = useFlowStore()
const verifiesStore = useVerifiesStore()
const sdk = useDesignerSdk()
const isDesigner = computed(() => [MODE_TYPE.DESIGNER, MODE_TYPE.PROMPT].includes(flowData.mode))
const isView = computed(() => flowData.mode === MODE_TYPE.VIEW)
const showError = computed(() => flowData.showError)
const data = computed(() => flowData.flowData)

onMounted(() => {
  if (flowData.mode === MODE_TYPE.DESIGNER) {
    flowData.getData().then((nodes: Node[]) => {
      sdk.onMounted()
      if (nodes?.length) {
        verifiesStore.verifyAll(nodes)
      }
    })
  }
})
</script>

<template>
  <ErrorView v-if="showError" />
  <div class="skill_layout h-[100%] w-[100%]" v-else-if="isDesigner">
    <div class="layout-header h-[60px] bg-[#F7F8FC] [border-bottom:1px_solid_#EAEAEA]">
      <LayoutHeader />
    </div>
    <div class="layout-content relative h-[calc(100%_-_60px)]">
      <Prompt v-if="flowData.mode === MODE_TYPE.PROMPT" />
      <template v-if="flowData.mode === MODE_TYPE.DESIGNER">
        <Designer v-if="data.mode === 'flow'" />
        <Agent v-if="data.mode === 'agent'" />
      </template>
    </div>
  </div>
  <template v-if="isView">
    <VueFlow />
  </template>
</template>
