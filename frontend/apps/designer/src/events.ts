export const createOpenPluginEvent = () => {
  return 'skyline:designer:plugin:open'
}
export const createPluginCallbackEvent = () => {
  return 'skyline:designer:plugin:callback'
}

export const createOpenMcpServiceEvent = () => {
  return 'skyline:designer:MCP:open'
}
export const createMcpServiceCallbackEvent = () => {
  return 'skyline:designer:MCP:callback'
}

export const createOpenKnowledgeEvent = () => {
  return 'skyline:designer:knowledge:open'
}
export const createKnowledgeCallbackEvent = () => {
  return 'skyline:designer:knowledge:callback'
}
export const createMountedEvent = () => {
  return 'skyline:designer:mounted'
}

// 触发自定义事件
export const emitEvent = <T = Record<string, unknown>>(name: string, data: T = {} as T) => {
  const event = new CustomEvent(name, {
    detail: data,
    bubbles: true,
    cancelable: true,
  })
  document.dispatchEvent(event)
}
