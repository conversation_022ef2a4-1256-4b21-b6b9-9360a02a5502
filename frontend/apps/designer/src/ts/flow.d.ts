interface Edge {
  code: string
  markerEnd: string
  source: string
  target: string
  type: string
  data?: Edge
}

interface EdgeMap {
  [key: string]: Edge
}

interface Node {
  id: string
  code: string
  type: string
  data: {
    label: string
    type: string
    code: string
    condition: Condition[]
    next: string
    end: string
    params: any
  }
}

interface Condition {
  field: string
  operator: string
  value: any
}

interface CardData {
  async?: boolean
  outputs: any
  width?: number
  height?: number
  _id: string
  name: string
  _actionName: string
  type: string
  inputs: {
    code: string
    type: string
    value: {
      type: string
      content: string
    }
  }[]
}

interface CardSelectorData extends CardData {
  conditions: SelectorCondition[]
}

interface CardPageData extends CardData {
  visible: boolean
  url: {
    type: string
    content: string
  }
  mount: string
}

interface CardDocumentData extends CardData {
  files: {
    type: string
    content: string
  }
  pattern: number
  imageRecognizeType: string
  ocrService: string
}

interface CardImageData extends CardData {
  files: {
    type: string
    content: string
  }
  pattern: number
  recognizeType: string
  ocrService: string
}

interface CardKnowledgeData extends CardData {
  pattern: number
}

interface CardAuxiliaryInput extends CardData {
  inputs: {
    code: string
    type: string
    value: {
      type: string
      content: string
    }
  }[]
  appCode: string
  formId: string
}

interface CardTextTemplate extends CardData {
  content: string
}

interface CardMessageData extends CardData {
  title: string
}

interface CardPlan extends CardData {
  id: string
}

interface CardNode {
  children: never[]
  parentNode: string | undefined
  id: string
  code: string
  type: string
  position: { x: number; y: number }
  data: CardData | CardPageData | CardDocumentData | CardImageData | CardKnowledgeData | CardAuxiliaryInput | CardTextTemplate | CardPlan | CardMessageData
}

interface CardSelectorNode extends CardData {
  data: CardSelectorData
}

interface InputParamValue {
  code: string
  value: {
    type: string
    content: string
  }
}