import { outputParamsConfig } from './../stores/configData';
import { FieldRule } from '@arco-design/web-vue'

// 定义选项类型
type Option = {
  hide: any
  disabled: boolean | undefined
  text: string
  value: string
}

// 定义编辑器属性类型
type EditorProps = {
  [key: string]: any // 这里使用 any 是因为没有具体定义所有可能的 props，可以根据实际情况细化
  options?: Option[] // 如果有特定的 options，则定义为可选属性
}

// 定义编辑器类型
type Editor = {
  type: string
  props?: EditorProps
  placeholder?: string
}

// 定义卡片配置项类型
type CardConfigItem = {
  id: string
  name: string
  label: string
  type: string
  defaultValue: any
  editor: Editor
  required?: boolean
  extra?: string
  disabled?: boolean
  rules?: FieldRule | FieldRule[]
  validateTrigger?: 'change' | 'input' | 'focus' | 'blur'
  hideLabel?: boolean
  tooltip?: string
  hide?: boolean
  events?: {name: string; type: string; callback: string}[]
}

type OutputParamsType = {
  name: string
  code: string
  type: string
  schema?: OutputParams[]
  describe?: string
  required: boolean
  schema?: OutputParamsType[]
  value: {
    content: string
    type: string
  }
}

interface FormFieldParams extends OutputParamsType {
  control: string
  controlType: string
  readonly: boolean
  thousands: boolean
  schema?: FormFieldParams[]
  options?: {text: string; value: string}[]
}

type OptionType = {
  label: string
  value: string
  supportType?: string[]
}