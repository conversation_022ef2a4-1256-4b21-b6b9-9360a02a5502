interface AgentData {
  prompt: {
    template: string
    inputs: PromptInputs[]
  }
  mcps: MCPData[]
  tools: ToolsData[]
  knowledgs: KnowledgeData[]
  inputs: {
    code: string
    name: string
    required: boolean
    type: string
  }[]
  uploadables: string
  useFileUpload: boolean
  useQRCodeUpload: boolean
  maxResultNumber: number
  maxUploadNumber: number

  // 携带上下文轮数
  maxContextTurnsNumber: number

  // 模型
  modelInstanceCode?: string

  // 生成多样性
  generationDiversity?: 'precise' | 'balance' | 'creative' | 'custom'

  // 模型执行设置
  executionSetting?: ExecutionSetting | null

  // 深度思考
  // enableThinking: boolean

  // 思考长度
  // thinkingBudget: number
}

interface MCPData {
  serviceGUID: string
  serviceCode: string
  serviceName: string
  serviceDescription: string
  toolGUID: string
  toolName: string
  toolDescription: string
}

interface ToolsData {
  pluginGUID: string
  toolGUID: string
  toolCode: string
  name: string
  path: string
  inputs: string
  outputs: string
  description: string
}

interface KnowledgeData {
  id: string
  code: string
  name: string
  description: string
  type: 1 | 2 | 3
}

interface InputsData {
  code: string
  name: string
  type: string
  default: string
}

interface PromptInputs {
  code: string
  value: {
    type: string
    content: string
  }
}

interface PromptField {
  code: string
  name: string
  type: string
  label: string
  title: string
  value: string
  field: string
}
