
declare module '@ipaas/doc-service'

enum ValueType {
  Ref = 'ref',
  Literal = 'literal',
}
interface GPTLauncherOpenOptions {
  // 技能列表，显示在聊天界面左下角的技能列表，不指定则使用 defaultSkill 指定
  skills?: string[]
  // 默认选中技能
  defaultSkill?: string
}

interface Window {
  _gpt?: {
    visible?: boolean
    debug?: boolean
    open?: (options: GPTLauncherOpenOptions) => void
  }
  _designerInit: (elementId: string, flowData: any) => void
}

interface InputParams {
  code: string
  mapValue: string
  name: string
  value: InputParamValue
}

interface PromptTestScenes extends InputParams {
  title: string
  field: string
  label: string
  value: string
}

interface PromptParams {
  paramCode: string
  paramName: string
  fieldType: string
  defaultValue: any | string
  isRequired: number
  isSystemParam: boolean
}

interface PromptParamsMap {
  [id: string]: PromptParams
}

declare type ChatMessageCard = {
  id: string
  type: string
  data: any
}

interface InputParamsMap {
  [id: string]: InputParams
}

interface MessageContent {
  role: string
  content: string | any
  index: number
}

interface SelectParamsType {
  [key: string]: string | number
  code: string
  name: string
  required: boolean
  value: string
  description: string
  type: string
}

interface EditorParamsType extends SelectParamsType {
  control: string
  schema: EditorParamsType[]
  value?: { content?: string }
}

interface SelectFieldParamsType {
  name: string
  value: string
  label: string
  code: string
  children: SelectFieldParamsType[]
}

interface ConfigDataType {
  type: string
  label?: string
  prop: string
  tips?: string
  defaultData?: any
  placeholder?: string
  required?: boolean
  width?: string
  readonly?: boolean
  options?: {
    label: string
    prop: string
    placeholder: string
    type: string
    default: string
    value?: string
  }[]
  props?: ConfigDataType[]
  rules?: {required: boolean, message: string}[]
  advancedconfig?: boolean
  hide?: boolean
  hideLabel?: boolean
  inputDisabled?: boolean
  config?: ConfigDataType[]
  columns?: {dataIndex: string; title: string}[]
  mode?: string
  minHeight?: string
  disabledInputs?: boolean
  maxLength?: number
  variables?: VariablesConfigType[]
  enableFillField?: boolean
}

interface VariablesConfigType {
  id: string
  label: string
  name: string
  type: string
  defaultValue: any,
  events: {
    name: string
    type: string,
    callback: string
  },
  editor: {
    type: string,
    props: Record<string, any>
    placeholder?: string
    options: {
      text: string
      value: string
    }[]
  },
  advancedconfig: boolean
  required?: boolean
  extra?: string
  disabled?: boolean
  rules?: FieldRule | FieldRule[]
  validateTrigger?: 'change' | 'input' | 'focus' | 'blur'
  hideLabel?: boolean
  tooltip?: string
  hide?: boolean
  events?: {name: string; type: string; callback: string}[]
}