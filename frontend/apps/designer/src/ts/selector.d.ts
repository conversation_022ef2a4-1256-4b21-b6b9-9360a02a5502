interface ExpressionRule {
  left: {
    code: string
    value: {
      type: string
      content: string
    }
  }
  operator: string
  right: {
    code: string
    value: {
      type: string
      content: string | number | boolean
    }
  }
}

interface ConditionExpression {
  type: 'and' | 'or'
  rules: ExpressionRule[]
}

interface SelectorCondition {
  enableEdit?: any
  title: string
  id: string
  target: string
  type: string
  priority: number
  expressions: ConditionExpression[]
  collapse: boolean
}

type Conditions = SelectorCondition[]

interface EdgeCondition extends SelectorCondition {
  style: number
}
