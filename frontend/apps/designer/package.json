{"name": "designer", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "pnpm run vite build", "buildAll": "vue-tsc && vite build", "dev": "pnpm run vite --force", "preview": "vite preview", "vite": "bun -b run ./node_modules/vite/bin/vite.js"}, "dependencies": {"@acme/card": "workspace:^", "@acme/core": "workspace:^", "@acme/markdown-editor": "workspace:^", "@arco-design/web-vue": "^2.56.3", "@ipaas/doc-service": "^5.5.20", "@modeling/template-editor": "1.0.5", "@vue-flow/background": "^1.3.2", "@vue-flow/core": "^1.42.1", "@vueuse/integrations": "^12.7.0", "axios": "^1.8.1", "deep-object-diff": "1.1.9", "dompurify": "^3.2.4", "highlight.js": "^11.11.1", "less": "^4.2.2", "pinia": "^2.3.0", "radash": "^12.1.0", "sortablejs": "^1.15.6", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vite": "^5.4.14", "vite-plugin-mock-dev-server": "^1.8.4", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.2.4"}}