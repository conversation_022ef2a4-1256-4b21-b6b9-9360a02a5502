import { transform } from '@swc/core'
import { file, write } from 'bun'
import JSZip from 'jszip'
import { rm } from 'node:fs/promises'
import { version } from './package.json' with { type: 'json' }
import pkg from './src/pk-config.json' with { type: 'json' }

pkg.version = version

const source = await file('./src/index.ts').text()

const result = await transform(source, {
  jsc: {
    target: 'es5',
    minify: {
      compress: true,
      mangle: true,
    },
  },
  minify: true,
})

const target = `define('${pkg.moduleName}',function(){
${result.code}
})`

console.log(pkg)
console.log()
console.log(target)

await rm('./dist', { recursive: true, force: true })
const zip = new JSZip()

zip.file('index.js', target)
zip.file('pk-config.json', JSON.stringify(pkg, null, 2))

const content = await zip.generateAsync({ type: 'blob' })

await write(`./dist/AILauncher-${pkg.version}.zip`, content)
