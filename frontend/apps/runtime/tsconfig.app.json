{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/*.vue", ".vite/*.d.ts"], "exclude": ["src/**/__tests__/*", "**/node_modules/**/*", "../../node_modules/**/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "skipLibCheck": true}, "traceResolution": true}