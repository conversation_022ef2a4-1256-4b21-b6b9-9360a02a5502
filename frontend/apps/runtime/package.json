{"name": "runtime", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "pnpm run vite --force", "build": "pnpm run vite build", "build-tc": "run-p type-check \"build-only {@}\" -- ", "preview": "vite preview", "test:unit": "vitest", "vite": "bun -b run ./node_modules/vite/bin/vite.js", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "biome lint", "format": "prettier --write src/", "analyze": "vite build --mode analyze", "postinstall": "node ./scripts/arco.mjs"}, "dependencies": {"@acme/core": "workspace:^", "@arco-design/web-vue": "^2.56.3", "@fingerprintjs/fingerprintjs": "^4.5.1", "@ipaas/doc-service": "^5.5.20", "@skyline-ai/widget": "workspace:^", "@skyline/ai": "workspace:^", "@ungap/structured-clone": "^1.3.0", "@vueuse/components": "^12.7.0", "@vueuse/core": "^12.7.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "fabric": "^6.6.1", "highlight.js": "^11.11.1", "js-base64": "^3.7.7", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-katex": "^2.0.3", "modern-screenshot": "^4.6.0", "number-precision": "^1.6.0", "pinia": "^2.3.0", "radash": "^12.1.0", "scule": "^1.3.0", "tailwind-merge": "^2.6.0", "ufo": "^1.5.4", "uqr": "^0.1.2", "uuid": "^10.0.0", "vue": "^3.5.13", "vue-json-viewer": "^3.0.4", "zod": "^3.24.2"}, "devDependencies": {"@iconify/tools": "^4.1.1", "@iconify/utils": "^2.3.0", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node20": "^20.1.4", "@types/crypto-js": "^4.2.2", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "@types/markdown-it-highlightjs": "^3.3.4", "@types/ungap__structured-clone": "^1.2.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "jsdom": "^24.1.3", "less": "^4.2.2", "npm-run-all2": "^7.0.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.3", "unplugin-auto-import": "^19.1.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.0", "vite": "^5.4.14", "vite-bundle-analyzer": "^0.16.0", "vite-manifest-parser": "^1.0.1", "vite-plugin-mock-dev-server": "^1.8.4", "vitest": "^2.1.8", "vue-tsc": "^2.2.4"}}