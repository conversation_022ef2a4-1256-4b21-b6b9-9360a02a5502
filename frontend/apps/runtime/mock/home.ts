import { MockHttpItem } from 'vite-plugin-mock-dev-server'

export const defineHomeResultResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')

  const questions = ['🔥 在复杂报表中，查询出来的数据不准要怎么处理？在复杂报表中，查询出来的数据', '📈 在仪表板中，跳转到子页面后，如何传递参数？', '🖥 在酷炫大屏中，表格组件如何配置冻结列？', '🖥 酷炫大屏、仪表板、数据集？', '🖥 表格、散点地图？', '🖥 表格、散点地图？', '🖥 在复杂报表中，查询出来的数据？']

  const d = {
    gptBuilderUrl: null,
    gptTenantUrl: null,
    gptEngineUrl: null,
    headers: null,
    assistant: {
      assistantGUID: 'f952b8c8-78a7-46f0-9ea7-6dea1c823938',
      assistantName: 'all_所有技能',
      assistantCode: 'ddd',
      isSystem: 0,
      describe: '',
      selfIntroduction: '',
      assistantType: 1,
      promptGUID: null,
      modelInstanceGUID: null,
      modelInstanceCode: null,
      modelInstanceName: null,
      promptCode: null,
      promptContext: null,
      introductoryPhrase: '',
      icon: '',
      skillConfig: 1,
      standalonePage: 1,
      accessUrl: '/gptbuilder/assistant/index.html?id=f952b8c8-78a7-46f0-9ea7-6dea1c823938',
      skillGUIDs: 'e481d207-8229-46f8-8cfb-aa000d6f38d6',
      skillText: 'test_知识库结构化输出',
      recommendedSkillGUID: null,
      recommendedSkillText: null,
      recommendedSkill: null,
      openCheck: 1,
      spaceGUID: '08dcab16-02b9-fed5-f934-d4d7d64041c7',
      initEvent: 'console.log("助手脚本来啦")',
    },
    skillList: [
      {
        skillGUID: '1880221b-e7f3-488e-a06d-636e3db7db16',
        skillCode: 'test_911',
        skillName: '【辅助录入】弹框列表填充',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '18be2325-348f-4c36-a426-bc9d25e77714',
        skillCode: 'test_1024_5_copy',
        skillName: '【成本】智能合同信息提取与录入_copy（插件+提示词）',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: '请上传【合同文档】，AI智能助手将为您提取合同信息并自动完成合同登记\n::: actions\n[上传文档](gpt://upload)\n:::',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '227b287c-52de-4828-a06e-2d64e8f04816',
        skillCode: 'test_1024_5',
        skillName: '【成本】智能合同信息提取与录入（插件+提示词+交互卡片）',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: '请上传【合同文档】，AI智能助手将为您提取合同信息并自动完成合同登记\n::: actions\n[上传文档](gpt://upload)\n:::',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '29f1cd71-14ff-4559-9741-83faaf54891f',
        skillCode: 'test_815_copy_copy_copy_copy',
        skillName: 'test_字符模板卡片_1010',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '30e1b54d-d020-4426-822f-51865b474e05',
        skillCode: 'test_n',
        skillName: 'test_多轮会话',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '38047249-29ba-411e-9f7a-6620cbaed69f',
        skillCode: 'qwenlong0001',
        skillName: '千问long验证',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '文档解析',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '5d5c09b1-6df3-4eef-b98c-b28d30e2e504',
        skillCode: 'tes_copy_copy_1',
        skillName: '【文档分析】test_文档信息提取',
        skillVersions: 'V006',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: 'http://mydoc:9070/api/v2/GetFile?documentId=068030c6-4b1e-42ba-b13c-4e09d2b1a615&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwNjgwMzBjNi00YjFlLTQyYmEtYjEzYy00ZTA5ZDJiMWE2MTUiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzQ5NTM3MDMxLCJpYXQiOjE3MzM5ODE0MzEsImp0aSI6ImIxZWYxOWQwLWNkYzgtNGJhMC1iOThiLWZkMDhhYmIzODY5NyJ9.ga_1aILa7XHdJmZSaKb_rmWfjFBxedVUFD5pTGwlEz0',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,xls,xlsx,txt,jpg,jpeg,bmp,gif,png,tif,webp',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '5fa6f3d7-dfb1-41ac-a8f7-75aafc7f1e38',
        skillCode: 'test_q1',
        skillName: '【嵌入卡片】测试嵌入页面',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '6738cf51-461f-4c9b-a383-5a993cb66fc7',
        skillCode: 'test_815_copy',
        skillName: 'test_字符模板卡片',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '6cb86900-359a-4026-8463-71ee25750cc6',
        skillCode: 'test_815_copy_copy',
        skillName: 'test_知识库结构化输出_109',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '72e8753e-5fd9-4073-b7bd-5d827ddf7428',
        skillCode: 'test',
        skillName: 'test电签',
        skillVersions: 'V013',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: true,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,jpg,jpeg,bmp,gif,png,tif,webp',
        welcome: '吼吼吼，我是电签小能手~',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '761a7d3b-be49-4682-bd34-0373de455805',
        skillCode: 'test_fzlr',
        skillName: '【辅助录入】多条数据',
        skillVersions: 'V003',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '76bc14dd-44e9-428e-82b2-395fe0976861',
        skillCode: 'test_905',
        skillName: '【测试】系统变量',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: true,
        uploadables: 'doc,docx,pdf,xls,xlsx,jpg,jpeg,bmp,gif,png,tif,webp',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '7a0f2a7c-9a01-4792-9035-b221bbf83a94',
        skillCode: 'test_yt',
        skillName: 'test_意图识别',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '8a98faa1-8c24-4099-b809-cbf9db9ca34e',
        skillCode: 'test_820',
        skillName: 'test_验证BUG',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: null,
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '8beb11a8-16ee-4e5b-8157-2cdac7ce11ae',
        skillCode: 'tes_copy_copy',
        skillName: 'test_文本提取【千问】',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: 'http://mydoc:9070/api/v2/GetFile?documentId=068030c6-4b1e-42ba-b13c-4e09d2b1a615&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwNjgwMzBjNi00YjFlLTQyYmEtYjEzYy00ZTA5ZDJiMWE2MTUiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzQ5NTM3MDMxLCJpYXQiOjE3MzM5ODE0MzEsImp0aSI6IjIwODBmZjQ2LTM0YmMtNGZlYS1iYWJhLTUzMzZiMTg2N2EwMCJ9.OkpB906MBDbLtGi1MZ_ol5zPLgV1hPkcfd7kM1gjgbM',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: null,
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '8ccd0c2b-ed30-4cbb-b9f8-4353113e16fd',
        skillCode: 'test_1204_1_copy',
        skillName: '【成本】合同文件信息提取_开始节点传参（提示词+提示词）',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: '请上传合同文件，我将即刻帮您提取关键信息。\n[上传文档](gpt://upload)',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '8de0d834-a33c-437c-b4ae-3021b282e863',
        skillCode: 'tes_copy',
        skillName: 'test_用户输入信息提取',
        skillVersions: 'V004',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: 'http://mydoc:9070/api/v2/GetFile?documentId=068030c6-4b1e-42ba-b13c-4e09d2b1a615&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwNjgwMzBjNi00YjFlLTQyYmEtYjEzYy00ZTA5ZDJiMWE2MTUiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzQ5NTM3MDMxLCJpYXQiOjE3MzM5ODE0MzEsImp0aSI6Ijk0MTcwNzE3LTFiYTktNGNmYS1hZTY1LWEwOTUzODMzM2E0OCJ9.UxXLa9odnLhrnuQZeRCYoUjI2wvWozs68L3rNxU_PlQ',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf',
        welcome: '*测试颜色主{.primary}* 测试颜色红{.red}\n\n测试颜色绿{.green} 测试颜色黄{.yellow} 测试颜色灰{.gray}',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '9914ef2e-f6d9-4aaf-ad6b-50bd679fe4d2',
        skillCode: 'test_1204_1',
        skillName: '【成本】合同文件信息提取（提示词）',
        skillVersions: 'V003',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: '请上传合同文件，我将即刻帮您提取关键信息。\n[上传文档](gpt://upload)',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: '9f494139-92cf-4e23-aa3b-75f0d937a24f',
        skillCode: 'sybjn',
        skillName: '少样本技能',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'a059fb00-8ef2-4577-a8ed-aa8855b8608f',
        skillCode: 'docAns',
        skillName: '文档分析',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'pdf',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'a391e0b7-2710-445b-9cbb-f8467608d0b5',
        skillCode: 'test_fzlr_copy_copy',
        skillName: '【辅助录入】弹窗列表自动填充',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'ad8007ba-68d8-44f6-85fb-d58240cceac0',
        skillCode: 'skill_kn_tj_developer',
        skillName: '【知识库】研发知识库-工作流',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '天际开发者研发手册，提供研发知识问答，辅助日常研发工作。',
        mode: 'flow',
        openDialogWindow: 1,
        icon: 'http://mydoc:9070/api/v2/GetFile?documentId=6532610a-2f22-41b8-8008-b8cbf72f94e3&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI2NTMyNjEwYS0yZjIyLTQxYjgtODAwOC1iOGNiZjcyZjk0ZTMiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzQ5NTM3MDMxLCJpYXQiOjE3MzM5ODE0MzEsImp0aSI6ImVmOWUxYWVmLTVmNWItNDIxMi1hZTg2LTZlOGFkNWJhODI5ZCJ9.msSP90r71Ykhw_XuBPM5s8yyAV1z1YK7HzjlQ6lJLO0',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: null,
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'b2533a14-f30f-41e0-9858-c85ca3e3125c',
        skillCode: 'test_815_copy_copy_copy',
        skillName: 'test_知识库结构化输出_109_copy',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'babd9055-d9ce-42bb-842e-fe234ffc6f94',
        skillCode: 'tes',
        skillName: 'test_文档分析',
        skillVersions: 'V004',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: 'http://mydoc:9070/api/v2/GetFile?documentId=068030c6-4b1e-42ba-b13c-4e09d2b1a615&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwNjgwMzBjNi00YjFlLTQyYmEtYjEzYy00ZTA5ZDJiMWE2MTUiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzQ5NTM3MDMxLCJpYXQiOjE3MzM5ODE0MzEsImp0aSI6ImMzYmRlOGM3LTdiNjItNDc1YS04NmI4LTgxMzM2NTExNjdlNyJ9.YqCsYLO4SlWN94qKnvitKgt0m31-rTh25g9Y_bQVRHM',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf,txt,zip,rar',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'bf26efac-c9c2-4811-80d8-7900293c2012',
        skillCode: 'test_815_1',
        skillName: '【辅助录入】表单辅助录入',
        skillVersions: 'V008',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf,txt,jpg,jpeg,bmp,gif,png,tif,webp',
        welcome: '# 测试颜色红{.red}\n测试颜色绿{.green}',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'c0b81bc5-266b-4db8-9f42-69f00117686c',
        skillCode: 'test_fzlr_copy',
        skillName: '【辅助录入】列表自动填充',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'cfc1153e-028b-4bfe-8c15-3c58dc7732c9',
        skillCode: 'test_1106_111',
        skillName: '11月验证BUG',
        skillVersions: 'V003',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'ddda059b-0544-4d18-85a2-d52bfa3d0910',
        skillCode: 'tes_1024',
        skillName: '【10月迭代】验证BUG',
        skillVersions: 'V002',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx,pdf,xls,xlsx,ppt,pptx,txt,jpg,jpeg,bmp,gif,png,tif,webp,avi,mpg,mpeg,mov,wav,ram,mp3,mp4',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'dfd285b5-9c7e-42c0-8eda-901b4028e8a1',
        skillCode: 'skill_1204',
        skillName: '合同智能录入_付款条件提取[MAX]_调优',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'e1eccea3-cdbb-4d53-9b02-78b59b53c487',
        skillCode: 'czmh_001_copy',
        skillName: '采购门户智能客服',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '提供采购门户相关疑问、数据查询服务，帮助400客服人员给用户答疑解惑。',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: true,
        uploadables: 'doc,docx,pdf,xls,xlsx,ppt,pptx,txt,jpg,jpeg,bmp,gif,png,tif,webp',
        welcome: '嗨，我是门户AI助手，任何问题我都可以为您答疑解惑！',
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'e481d207-8229-46f8-8cfb-aa000d6f38d6',
        skillCode: 'test_815',
        skillName: 'test_知识库结构化输出',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'e78091d9-77d4-44e6-8de8-279ff7e2d09c',
        skillCode: 'skill_kfff_copy',
        skillName: '智慧客服_用户问题优化',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: true,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'eea2aa3b-2e42-4380-ab4e-c03288ab7fba',
        skillCode: 'skil_card_1106',
        skillName: '【开发】交互卡片回填',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: true,
        hasImageAnalysisNode: false,
        uploadables: 'doc,docx',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'f79a3a5f-a4ff-4753-82a2-9843676ee026',
        skillCode: 'ai_generate_code',
        skillName: '「AI大赛」智能生成代码',
        skillVersions: 'V005',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
      {
        skillGUID: 'fccaa9cc-3ff8-43a0-826c-6ccf5a2b2760',
        skillCode: 'code_heper',
        skillName: '代码助手',
        skillVersions: 'V001',
        isSystem: 0,
        describe: '',
        mode: 'flow',
        openDialogWindow: 1,
        icon: '',
        questions: [],
        hasStartup: false,
        hasDocumentAnalysisNode: false,
        hasImageAnalysisNode: false,
        uploadables: '',
        welcome: null,
        skillCollectCount: 0,
        skillChatCount: 0,
      },
    ],
    defaultSkill: null,
    defaultquestions: [],
    skillCategoryList: [
      {
        skillCategoryGUID: '08dd19c3-1d80-4718-8adb-2c70c1fcaa30',
        spaceGUID: '08dcab16-02b9-fed5-f934-d4d7d64041c7',
        spaceCode: null,
        spaceName: null,
        skillCategoryName: '12312',
      },
    ],
    recommendedSkill: null,
  }

  d.skillList.forEach((v) => {
    v.questions = questions as any
  })

  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: d,
    }),
  )
}

export const defineHomeCollectResultResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [
        {
          skillGUID: '1880221b-e7f3-488e-a06d-636e3db7db16',
          skillCode: 'test_911',
          skillName: '【辅助录入】弹框列表填充',
          skillVersions: 'V001',
          isSystem: 0,
          describe: '',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: false,
          hasDocumentAnalysisNode: true,
          hasImageAnalysisNode: false,
          uploadables: 'doc,docx,pdf',
          welcome: null,
          skillCollectCount: 0,
          skillChatCount: 0,
        },
        {
          skillGUID: '38047249-29ba-411e-9f7a-6620cbaed69f',
          skillCode: 'qwenlong0001',
          skillName: '千问long验证',
          skillVersions: 'V001',
          isSystem: 0,
          describe: '文档解析',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: false,
          hasDocumentAnalysisNode: true,
          hasImageAnalysisNode: false,
          uploadables: 'doc,docx',
          welcome: null,
          skillCollectCount: 0,
          skillChatCount: 0,
        },
        {
          skillGUID: '5fa6f3d7-dfb1-41ac-a8f7-75aafc7f1e38',
          skillCode: 'test_q1',
          skillName: '【嵌入卡片】测试嵌入页面',
          skillVersions: 'V001',
          isSystem: 0,
          describe: '',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: false,
          hasDocumentAnalysisNode: false,
          hasImageAnalysisNode: false,
          uploadables: '',
          welcome: null,
          skillCollectCount: 0,
          skillChatCount: 0,
        },
        {
          skillGUID: '6738cf51-461f-4c9b-a383-5a993cb66fc7',
          skillCode: 'test_815_copy',
          skillName: 'test_字符模板卡片',
          skillVersions: 'V002',
          isSystem: 0,
          describe: '',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: false,
          hasDocumentAnalysisNode: false,
          hasImageAnalysisNode: false,
          uploadables: '',
          welcome: null,
          skillCollectCount: 0,
          skillChatCount: 0,
        },
        {
          skillGUID: '6cb86900-359a-4026-8463-71ee25750cc6',
          skillCode: 'test_815_copy_copy',
          skillName: 'test_知识库结构化输出_109',
          skillVersions: 'V001',
          isSystem: 0,
          describe: '',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: false,
          hasDocumentAnalysisNode: false,
          hasImageAnalysisNode: false,
          uploadables: '',
          welcome: null,
          skillCollectCount: 0,
          skillChatCount: 0,
        },
        {
          skillGUID: '72e8753e-5fd9-4073-b7bd-5d827ddf7428',
          skillCode: 'test',
          skillName: 'test电签',
          skillVersions: 'V013',
          isSystem: 0,
          describe: '',
          mode: 'flow',
          openDialogWindow: 1,
          icon: '',
          questions: [],
          hasStartup: true,
          hasDocumentAnalysisNode: false,
          hasImageAnalysisNode: false,
          uploadables: 'doc,docx,jpg,jpeg,bmp,gif,png,tif,webp',
          welcome: '吼吼吼，我是电签小能手~',
          skillCollectCount: 0,
          skillChatCount: 0,
        },
      ],
    }),
  )
}

export const definePrivacyAgreementContentResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        privacyAgreementGUID: '************************************',
        content: '<p>如果天际AI助手在公网开放给企业外部人员使用，建议开启“隐私协议”，用户必须同意隐私协议才允许使用天际AI助手（只需同意一次）</p><p><i>斜体</i></p><p><u>下划线</u></p><p><del>中划线</del></p><p><span style="background-color: rgb(171, 140, 228);">背景色</span></p><h4 style="font-size:16px;">标题</h4><p><br></p><p>123</p>',
        isEnabled: 1,
        version: 0,
        modifiedTime: '2025-02-24T15:04:56.000+08:00',
      },
    }),
  )
}
