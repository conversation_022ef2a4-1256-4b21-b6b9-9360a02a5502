import { defineMock } from 'vite-plugin-mock-dev-server'
import { createSkillStartupResponseMock, defineAssistantApplicationQueryByShareCodeResponseMock, defineAssistantChatDetailResponseMock, defineAssistantChatListResponseMock, defineAssistantContextResponseMock, defineAssistantInitEventToSkillAndAssistantResponseMock, defineAssistantLoadOptionDataResponseMock, defineAssistantQuestionResponseMock, defineAssistantSkillGuideResponseMock } from './config'
import { defineHomeCollectResultResponseMock, defineHomeResultResponseMock, definePrivacyAgreementContentResponseMock } from './home'
import { definePlanResultResponseMock } from './plan'

export default defineMock([
  {
    url: '/api/42000101/assistant/context',
    method: 'POST',
    delay: 0,
    response: defineAssistantContextResponseMock,
  },
  {
    url: '/api/42000101/assistant/contextByOnSite',
    method: 'POST',
    delay: 0,
    response: defineAssistantContextResponseMock,
  },
  {
    url: '/api/42000101/assistant/contextByOffSite',
    method: 'POST',
    delay: 0,
    response: defineAssistantContextResponseMock,
  },
  {
    url: '/api/42000101/assistant/question/:id',
    method: 'GET',
    delay: 300,
    response: defineAssistantQuestionResponseMock,
  },
  {
    url: '/api/42000101/assistant/loadOptionData',
    method: 'GET',
    delay: 300,
    response: defineAssistantLoadOptionDataResponseMock,
  },
  {
    url: '/api/42000101/assistant/skillGuide/:skillGUID',
    method: 'GET',
    delay: 1000,
    response: defineAssistantSkillGuideResponseMock,
  },
  {
    url: '/api/42000101/assistant/chatList',
    method: 'POST',
    delay: 0,
    response: defineAssistantChatListResponseMock,
  },
  {
    url: '/api/42000101/assistant/searchChatList',
    method: 'POST',
    delay: 0,
    response: defineAssistantChatListResponseMock,
  },
  {
    url: '/api/42000101/assistant/chatDetail',
    method: 'POST',
    delay: 100,
    response: defineAssistantChatDetailResponseMock,
  },
  {
    url: '/api/42000101/assistant/chatEdit',
    method: 'POST',
    delay: 0,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: null,
    },
  },

  {
    url: '/api/42000101/assistant/saveChatDetail',
    method: 'POST',
    delay: 0,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: null,
    },
  },
  {
    url: '/api/42000101/assistant/skill/getSkillStartupConfig',
    method: 'POST',
    delay: 0,
    response: createSkillStartupResponseMock,
  },
  {
    url: '/api/42000101/assistant/skill/ykfInit',
    method: 'POST',
    delay: 0,
    response: createSkillStartupResponseMock,
  },
  {
    url: '/api/42000101/assistant/chatLog/feedBack',
    method: 'POST',
    delay: 0,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: 'success',
    },
  },

  {
    url: '/api/42000101/assistant/application/queryByShareCode',
    method: 'POST',
    delay: 0,
    response: defineAssistantApplicationQueryByShareCodeResponseMock,
  },

  {
    url: '/api/42000101/assistant/uploadDocument',
    method: 'POST',
    delay: 3000,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        fileName: 'aaaaaaa.txt',
        fileSize: '12.2k',
        fileType: 'txt',
        downloadUrl: 'http://mydoc:9070/api/v2/GetFile?documentId=5a3bf0ec-4eba-459a-97f1-076d884f7687&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI1YTNiZjBlYy00ZWJhLTQ1OWEtOTdmMS0wNzZkODg0Zjc2ODciLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzM4NTYzOTcwLCJpYXQiOjE3MjMwMDgzNzAsImp0aSI6IjE3NTU5NGFjLWM0MTUtNGE3MC1iYmQ2LWI3OTRlZmRkMTkwNSJ9.HJPhcK8oDmbQ38ehrWzaSUOrTnhx_AbI6zpV_sT7ReM',
        documentGUID: '5a3bf0ec-4eba-459a-97f1-076d884f7687',
      },
    },
  },

  {
    url: '/api/42000101/assistant/uploadImage',
    method: 'POST',
    delay: 3000,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        fileName: 'a.png',
        fileSize: '12.4k',
        fileType: 'png',
        downloadUrl: 'http://mydoc:9070/api/v2/GetFile?documentId=5a3bf0ec-4eba-459a-97f1-076d884f7687&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI1YTNiZjBlYy00ZWJhLTQ1OWEtOTdmMS0wNzZkODg0Zjc2ODciLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzM4NTYzOTcwLCJpYXQiOjE3MjMwMDgzNzAsImp0aSI6IjE3NTU5NGFjLWM0MTUtNGE3MC1iYmQ2LWI3OTRlZmRkMTkwNSJ9.HJPhcK8oDmbQ38ehrWzaSUOrTnhx_AbI6zpV_sT7ReM',
        documentGUID: '5a3bf0ec-4eba-459a-97f1-076d884f7687',
      },
    },
  },

  {
    url: '/api/42000101/assistant/getDocumentInfo',
    method: 'POST',
    delay: 0,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        url: 'http://10.20.183.182:9070',
        token:
          'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJteXNvZnRFUlAiLCJyZWZ1c2VSaWdodHMiOiJbXSIsImlzQWRtaW4iOnRydWUsInRlbmFudENvZGUiOiJteXNvZnQiLCJ1c2VyTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsInVzZXJJZCI6IjQyMzBiYzZlLTY5ZTYtNDZhOS1hMzllLWI5MjlhMDZhODRlOCIsInVzZXJDb2RlIjoiYWRtaW4iLCJtb2JpbGVQaG9uZSI6IjEyMzEyMzEyMzEyIiwidGltZXpvbmVPZmZzZXQiOjQ4MC4wLCJ0ZW5hbnRJZCI6ImU4MmM3ZGYyLWU0MjEtNDVlOS05YjRlLWRjZWI4YTJjZjRkOSIsImV4cCI6MTcyNjA1OTUzMiwiaWF0IjoxNzI2MDQ4NzMyLCJqdGkiOiI0MGZmZDMwYi0wMWJiLTQzNzYtODFiMC01OWQ0ZThjODEwMzgifQ.U6QVcoF5jUNE7Kac1PLouoVNKff-k08qfqbPF4Wic1k',
        documentSizeLimit: 104857600,
        imageSizeLimit: 10485760,
        imagePixelsLimit: 1048576,
      },
    },
  },

  {
    url: '/api/42000101/assistant/initEventToSkillAndAssistant',
    method: 'POST',
    delay: 0,
    response: defineAssistantInitEventToSkillAndAssistantResponseMock,
  },

  {
    url: '/api/42001301/token/getToken',
    method: 'POST',
    delay: 0,
    body: {
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        url: 'http://mydoc:9070',
        token:
          'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aW1lem9uZU9mZnNldCI6NDgwLjAsImlzcyI6Im15c29mdEVSUCIsInRlbmFudElkIjoiOWFkMzExOWUtNjRiOC0xMWVkLWJmNzQtMDI0MmFjMTYxMjM0IiwicmVmdXNlUmlnaHRzIjoiW10iLCJpc0FkbWluIjpmYWxzZSwidGVuYW50Q29kZSI6ImdwdCIsImV4cCI6MTcyNjA0MjIxMCwidXNlck5hbWUiOiLmmI7mupDlupTnlKgiLCJpYXQiOjE3MjYwMzE0MTAsInVzZXJJZCI6IjQwY2YwYTA2LTVjZTUtNGZiYi1kZjE2LTA4ZDY3YWUxMTVkZiIsImp0aSI6IjY5ODhkMmIyLTM0MjEtNDgxMS1iNWUzLWM0ZjZhYjViMWRkNyIsInVzZXJDb2RlIjoiTXlzb2Z0In0.eQuwCPD79pNTJ4iP_3QOmS7fbpkQOSZBUzTige4SJnE',
        documentSizeLimit: 104857600,
        imageSizeLimit: 10485760,
        imagePixelsLimit: 1048576,
      },
    },
  },
  {
    url: '/api/42001401/plan/searchExecuteResult',
    method: 'POST',
    delay: 0,
    response: definePlanResultResponseMock,
  },
  {
    url: '/api/42000101/assistant/homePage',
    method: 'POST',
    delay: 0,
    response: defineHomeResultResponseMock,
  },
  {
    url: '/api/42000101/assistant/skillCollectQuery',
    method: 'POST',
    delay: 0,
    response: defineHomeCollectResultResponseMock,
  },
  {
    url: '/api/42001301/privacyAgreement/findNewCurrentVersionContent',
    method: 'POST',
    delay: 0,
    response: definePrivacyAgreementContentResponseMock,
  },
])
