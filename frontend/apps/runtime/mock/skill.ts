type Skill = {
  skillGUID: string
  skillCode: string
  skillName: string
  isSystem: 0 | 1
  icon: string
  describe: string
  question?: string | null
  hasStartup?: 1
  welcome?: string
  uploadables?: string
}

// 云客服
const myykfSkill: Skill = {
  skillGUID: 'f2df1516-575e-4be3-ac2c-61f27ed1a60c1',
  skillCode: 'myykf',
  skillName: '云客服',
  isSystem: 0,
  icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed1-0c14-da03-9aa4-c3e15f36258b.svg',
  question: null,
  hasStartup: 1,
  uploadables: 'jpg,jpeg,bmp,gif,png,tif,webp,txt,doc,docx,pdf,xls,xlsx,ppt,pptx,avi,mpg,mpeg,mov,wav,ram,mp3,mp4,xml,aaa,ccc,ggg',
  describe: '销售问答助手简介描述文字，销售问答助手简介描述文字，销售问答助手简介描述文字，销售问答助手简介描述文字',
}

const skills: Skill[] = [
  myykfSkill,
  {
    skillGUID: 'f2df1516-575e-4be3-ac2c-61f27ed1a60c',
    skillCode: 'skill_code2',
    skillName: '销售问答助手',
    isSystem: 0,
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed1-0c14-da03-9aa4-c3e15f36258b.svg',
    question: null,
    hasStartup: 1,
    describe: '有欢迎语',
    welcome: `👋🏻 Hi~我是您的智能助理~
通过本技能，可以咨询数见的三屏一表制作过程中，遇到的各类操作型问题。您可以在输入框中，对问题进行描述，为了提升您的咨询效率，描述请尽量包含如下内容：
- 提问的功能模块，如：酷炫大屏、仪表板、数据集；
- 如果是组件类的咨询，请描述组件的具体名称，如：表格、散点地图；
- 如果系统中出现了错误提示，请在描述中将错误信息也进行输入；

:::actions
[上传文档](gpt://upload) [移动端上传](gpt://upload/qrcode)
:::
`,
  },
  {
    skillGUID: 'f2df1516-575e-4be3-ac2c-61f27ed1a60e',
    skillCode: 'skill_code_s',
    skillName: '定价调价助手',
    isSystem: 0,
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed1-0c14-da03-9aa4-c3e15f36258b.svg',
    question: null,
    hasStartup: 1,
    describe: '定价调价助手, 有欢迎语，没有预置问题',
    welcome: `请上传认购相关协议，我将即刻帮你生成认购。
:::actions
[上传文档](gpt://upload) [移动端上传](gpt://upload/qrcode)
:::
`,
  },
  {
    skillGUID: '830f37e2-16f3-43f5-82a0-8815503bb56d',
    skillCode: 'xianliao3',
    skillName: '创建督办助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-e431-14f5-5226-88e13265c91b.svg',
    isSystem: 0,
    question: '请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。',
    describe: '创建督办助手',
  },
  {
    skillGUID: 'guid1',
    skillCode: 'xzzs4',
    skillName: '智能客服助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-f828-c748-c83d-19cbccb3acfd.svg',
    isSystem: 0,
    describe: '智能客服助手',
  },
  {
    skillGUID: 'guid2',
    skillCode: 'xzzs5',
    skillName: '合同分析助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed1-1c1e-4ff3-f93e-ff65c442b359.svg',
    question:
      '请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。',
    isSystem: 0,
    describe: '合同分析助手描述文字',
  },
  {
    skillGUID: 'guid3',
    skillCode: 'xianliao6',
    skillName: '定价风险识别',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fecf-feff-3591-343c-bb4455bbedf2.svg',
    isSystem: 0,
    describe: '在价格制定环节，能辅助操作人员自动识别价格的异常、折扣及付款方式测算，提高价格的准确性及合理性',
  },
  {
    skillGUID: 'guid4',
    skillCode: 'xianliao7',
    skillName: '流程审批助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-d508-5807-35cb-9612d3cf5074.svg',
    isSystem: 0,
    describe: '流程审批助手描述文字',
  },
  {
    skillGUID: 'guid5',
    skillCode: 'xianliao7',
    skillName: '无图标测试',
    icon: '',
    isSystem: 0,
    describe: '这个技能使用默认图标',
  },
  {
    skillGUID: 'guid6',
    skillCode: 'xianliao7',
    skillName: '流程审批助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-d508-5807-35cb-9612d3cf5074.svg',
    isSystem: 0,
    describe: '流程审批助手描述文字',
  },
  {
    skillGUID: 'guid7',
    skillCode: 'xianliao7',
    skillName: '流程审批助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-d508-5807-35cb-9612d3cf5074.svg',
    isSystem: 0,
    describe: '流程审批助手描述文字',
  },
  {
    skillGUID: 'guid8',
    skillCode: 'xianliao7',
    skillName: '流程审批助手',
    icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-d508-5807-35cb-9612d3cf5074.svg',
    isSystem: 0,
    describe: '流程审批助手描述文字',
  },
]

export const createSkills = () =>
  skills.map((v) => {
    return {
      ...v,
      uploadables: 'png,docx',
    }
  })

const shortcuts = [
  {
    id: 'guid1',
    code: 'shortcut_code_xxx1',
    name: '颜色消息',
    type: 'chat',
    description: '指令描述',
    chat: { message: '颜色消息' },
  },
  {
    id: 'guid1',
    code: 'shortcut_code_xxx1',
    name: '代码消息',
    type: 'chat',
    description: '指令描述',
    chat: { message: '代码消息' },
  },

  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '表单卡片',
    type: 'chat',
    description: '表单卡片',
    chat: { message: '表单卡片' },
  },

  // {
  //   id: 'guid',
  //   code: 'shortcut_code_xxx2',
  //   name: '图文消息',
  //   type: 'chat',
  //   description: '图文消息',
  //   chat: { message: '图文消息' },
  // },

  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '复杂消息',
    type: 'chat',
    description: '复杂消息',
    chat: { message: '复杂消息' },
  },

  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '流式文本',
    type: 'chat',
    description: '流式文本',
    chat: { message: '流式文本' },
  },
  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '嵌入卡片-微件',
    type: 'chat',
    description: '嵌入卡片-微件',
    chat: { message: '嵌入卡片-微件' },
  },
  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '嵌入卡片-页面',
    type: 'chat',
    description: '嵌入卡片-页面',
    chat: { message: '嵌入卡片-页面' },
  },
  {
    id: 'guid',
    code: 'shortcut_code_xxx2',
    name: '流式文本-错误排查',
    type: 'chat',
    description: '流式文本-错误排查',
    chat: { message: '流式文本-错误排查' },
  },

  // 云客服
  {
    id: 'guid1',
    code: 'shortcut_code_xxx1',
    name: '智能检查',
    type: 'chat',
    description: '指令描述',
    chat: { message: '智能检查' },
  },
  {
    id: 'guid1',
    code: 'shortcut_code_xxx1',
    name: '人工客服',
    type: 'chat',
    description: '指令描述',
    chat: { message: '转人工' },
  },
  {
    id: 'guid2',
    code: 'shortcut_code_xxx2',
    name: '我的工单',
    type: 'page',
    badge: { content: '待处理2' },
    description: '指令描述',
    page: { url: 'https://www.baidu.com' },
  },
  {
    id: 'guid2',
    code: 'shortcut_code_xxx2',
    name: '打开百度',
    type: 'page',
    description: '指令描述',
    page: { url: 'https://www.baidu.com', target: 'blank' },
  },
  {
    id: 'guid2',
    code: 'shortcut_code_xxx2',
    name: '数据报表',
    type: 'chat',
    description: '指令描述',
    chat: { message: '数据报表' },
  },
]

export const createSkillStartup = () => ({
  code: '0',
  message: '成功',
  success: true,
  error: null,
  data: {
    shortcuts,
    welcomeMsg: '',
  },
})
