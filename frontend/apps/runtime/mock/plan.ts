import { type MockHttpItem } from 'vite-plugin-mock-dev-server'

type PlanRuleStatus = 1 | 2 | 3

type PlanRuleType = 0 | 1
type PlanRuleCheckSource = 0 | 1

type PlanRuleResult = 1 | 2

type PlanRule = {
  ruleId: string
  planRuleInstanceGUID: string
  ruleName: string
  ruleType: PlanRuleType
  ruleCheckSource: PlanRuleCheckSource
  status: PlanRuleStatus
  result: PlanRuleResult
  tip: string
  overview: string
  details: string
}
type PlanRuleGroupStatus = 1 | 2
type PlanRuleGroupResult = 1 | 2

type PlanRuleGroup = {
  groupGUID: string
  groupName: string
  rules: PlanRule[]
  status: PlanRuleGroupStatus
  result: PlanRuleGroupResult
}

type PlanStatus = 1 | 2 | 3

type PlanResult = {
  planMode: number
  planVersion: string
  status: PlanStatus
  result: number
  resultCount: Record<string, number>
  resultSummary: string
  ruleGroups: PlanRuleGroup[]
}

let planCount = 0
let planResult: PlanResult | null = null
export const definePlanResultResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  // 第一次加载默认数据
  if (planCount === 0) {
    planResult = {
      planMode: 1,
      planVersion: '1.0',
      status: 1,
      result: 0,
      resultCount: {
        '0': 2,
        '1': 6,
        '2': 6,
      },
      resultSummary: '当前的价格方案存在异常顶顶顶顶顶',
      ruleGroups: [
        {
          groupGUID: '1234567890',
          groupName: '测试分组',
          status: 2,
          result: 2,
          rules: [
            {
              ruleId: '1234567890',
              planRuleInstanceGUID: '12345678901',
              ruleName: '测试规则1',
              ruleType: 0,
              ruleCheckSource: 0,
              status: 2,
              result: 2,
              tip: 'AI暂无法完成检查，请通过人工方式检查',
              overview: '测试规则概述hhhhhhhh',
              details: '测试规则详情测试规则详情测试测试规则详情测试规则详情测试规则详情测试规则详情测试规则详情规则详情测试规则详情测试规则详情测试规则详情',
            },
            {
              ruleId: '1234567897',
              planRuleInstanceGUID: '12345678902',
              ruleName: '测试规则2',
              ruleType: 0,
              ruleCheckSource: 0,
              status: 3,
              result: 2,
              tip: 'AI暂无法完成检查，请通过人工方式检查',
              overview: '测试规则概述2',
              details: '测试规则详情2测试规则详情测试规则详情测试规则详情测试规则详情测试规则详情',
            },
            {
              ruleId: '1234567897',
              planRuleInstanceGUID: '12345678903',
              ruleName: '测试规则3',
              ruleType: 0,
              ruleCheckSource: 0,
              status: 2,
              result: 2,
              tip: '',
              overview: '测试规则概述3',
              details: '测试规则详情3测试规则详情测试规则详情测试规则详情测试规则详情测试规则详情',
            },
          ],
        },
      ],
    }
    planCount++
  } else {
    // 多次请求接口只更新数据
    planCount++
    if (!planResult) {
      return
    }
    if (planCount === 5) {
      planCount = 0
      planResult.status = 2
      planResult.ruleGroups[0].status = 2
      planResult.ruleGroups[0].result = 1
      planResult.ruleGroups[0].rules.forEach((c) => (c.result = 1))
      // console.log(666,  planResult.ruleGroups[0])
    }
    planResult.ruleGroups[0].groupName = `测试分组${planCount}`
    planResult.ruleGroups[0].rules[0].ruleName = `测试规则${planCount}`
    planResult.ruleGroups[0].rules[0].details = `测试规则详情顶顶顶${planCount}`
  }
  const result = {
    planInstanceGUID: '1234567890',
    planGUID: '1234567890',
    instanceId: '32',
    ...planResult,
  }

  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [result],
    }),
  )
}
