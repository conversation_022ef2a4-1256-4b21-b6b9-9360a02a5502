import { sleep } from 'radash'
import { defineMock } from 'vite-plugin-mock-dev-server'
import { createBlocks, createCodes, createColors, createFixedFormCard, createFormCard, createFormStream2, createImage, createImageText, createMessageCard, createPageCardWithIframe, createPageCardWithWidget, createPlanCard, createProcess, createReportCard, createStream } from './agent'
import { createDoneMessage, createProcessMessage, createTextMessage } from './types'
export default defineMock({
  url: '/api/Agent/StreamingChatCompletion',
  method: 'POST',
  delay: 1000,
  async response(req, res, next) {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      Connection: 'keep-alive',
      'Cache-Control': 'no-cache',
    })
    // 开始处理
    const data = createProcess()
    console.log(req.body)

    if (req.body.next) {
      if (!req.body.nextId) {
        const message = '必须指定 nextId'
        data.push(createTextMessage(message))
        console.log(message)
        throw new Error(message)
      }
      data.push(createTextMessage('这个是下一步的消息'))
    } else {
      // 模拟响应
      switch (req.body.input) {
        case '流式文本':
          data.push(...createImageText())
          // data.push(...createText())
          // data.push(...createRevokeText())
          break
        case '流式文本-错误排查':
          data.push(...createFormStream2())
          break
        case '流式组合':
          data.push(...createStream())
          break
        case '颜色消息':
          data.push(...createColors())
          break
        case '代码消息':
          data.push(...createCodes())
          break
        case '图文消息':
          data.push(...createImage())
          break
        case '复杂消息':
          data.push(...createBlocks())
          break
        case '嵌入卡片-页面':
          data.push(createPageCardWithIframe())
          break
        case '嵌入卡片-微件':
          data.push(...createPageCardWithWidget())
          break
        case '表单卡片':
          data.push(...createFormCard())
          break
        case '表单卡片固定值':
          data.push(...createFixedFormCard())
          break
        case '消息卡片':
          data.push(...createMessageCard())
          break
        case '智能检查':
          data.push(createPlanCard())
          break
        case '数据报表':
          data.push(...createReportCard())
          break
        default:
          data.push(createTextMessage('未定义的消息'))
          break
      }
    }

    // 结束
    data.push(createProcessMessage('正在结束步骤：完成执行动作', 0))
    data.push(createDoneMessage(new Date().getTime().toString(), 0))

    // 推送数据
    while (true) {
      const item = data.shift()
      if (item) {
        res.write(`${item.code}:${item.body}\n`)
        await sleep(item?.time ?? 0)
      } else {
        res.end()
        break
      }
    }
  },
})
