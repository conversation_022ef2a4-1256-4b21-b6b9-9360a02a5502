import { encode } from 'js-base64'
import type { MockHttpItem } from 'vite-plugin-mock-dev-server'

export const assistantDefault = {
  id: '01942c5a-6713-721b-96d1-b1c24eb67269',
  name: 'AI智能助手',
}

export const assistantKF = {
  id: '01942c5a-6713-721b-96d1-b71616a5f6ed',
  name: 'AI智能客服',
}
export const assistantXZ = {
  id: '01942c5a-6713-721b-96d1-b91815c4c024',
  name: 'AI行政助手',
}

export const defineWorkbenchGetAssistantsResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')

  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [assistantDef<PERSON>, assistantK<PERSON>, assistantX<PERSON>],
    }),
  )
}

const token = encode(
  JSON.stringify({
    history: 1,
    accessToken: 'mock-access-token',
    shareCode: 'mock-share-code',
  }),
)

const assistants = [
  assistantDefault,
  {
    ...assistantKF,
    loader: `http://localhost:5174/gptbuilder/assistant/src/main.ts?token=${token}`,
  },
  assistantXZ,
]

export const defineWorkbenchGetAssistantResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')

  const { id } = req.params
  const data = assistants.find((v) => v.id === id)

  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data,
    }),
  )
}
