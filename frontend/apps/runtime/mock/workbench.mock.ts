import { defineMock } from 'vite-plugin-mock-dev-server'
import { defineWorkbenchGetAssistantResponseMock, defineWorkbenchGetAssistantsResponseMock } from './workbench/assistant'

export default defineMock([
  {
    url: '/api/42000101/assistant/getAssistants',
    method: 'GET',
    delay: 0,
    response: defineWorkbenchGetAssistantsResponseMock,
  },
  {
    url: '/api/42000101/assistant/getAssistant/:id',
    method: 'GET',
    delay: 0,
    response: defineWorkbenchGetAssistantResponseMock,
  },
])
