import type { FormCardData } from '@acme/core'

export const form = [
  {
    type: 'form',
    code: 'node1',
    next: '401f4036-9449-4726-a318-4178dbc3c73a',
    data: {
      inputs: [],
      data: [],
      content: '',
      templateId: '401f4036-9449-4726-a318-4178dbc3c7ef',
      props: [
        {
          name: 'fields',
          value: [],
        },
        {
          name: 'editable',
          value: true,
        },
        {
          name: 'mode',
          value: 'edit',
        },
        {
          name: 'columns',
          value: [
            {
              name: '报销主题',
              code: 'name',
              type: 'Textarea',
              required: true,
              readonly: false,
              columns: [],
            },
            {
              name: '敏感备注',
              code: 'decribe',
              type: 'InputNumber',
              required: false,
              readonly: false,
              columns: [],
            },
            {
              name: '报销明细',
              code: 'content',
              type: 'DataForm',
              required: false,
              readonly: false,
              columns: [
                {
                  name: '费用类型',
                  code: 'type',
                  type: 'Input',
                  required: false,
                  readonly: true,
                },
                {
                  name: '发生日期',
                  code: 'date',
                  type: 'DatePicker',
                  required: false,
                  readonly: false,
                },
                {
                  name: '报销金额',
                  code: 'amount',
                  type: 'InputNumber',
                  required: false,
                  readonly: false,
                },
                {
                  name: '消费人数',
                  code: 'people',
                  type: 'InputNumber',
                  required: true,
                  readonly: false,
                },
              ],
            },
          ],
        },
      ],
      title: '',
      outputs: [
        {
          code: 'name',
          name: '报销主题',
          type: 'string',
          required: true,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_imageAnalysis0_name',
          literalValue: '',
          value: {
            type: 'ref',
            content: 'NodeOutput_imageAnalysis0_name',
            syncMessage: null,
          },
          schema: [],
        },
        {
          code: 'decribe',
          name: '敏感备注',
          type: 'number',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_imageAnalysis0_decribe',
          literalValue: '',
          value: {
            type: 'ref',
            content: 'NodeOutput_imageAnalysis0_decribe',
            syncMessage: null,
          },
          schema: [],
        },
        {
          code: 'content',
          name: '报销明细',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_imageAnalysis0_content',
          literalValue: '',
          value: {
            type: 'ref',
            content: 'NodeOutput_imageAnalysis0_content',
            syncMessage: null,
          },
          schema: [
            {
              code: 'type',
              name: '费用类型',
              type: 'string',
              required: false,
              description: null,
              defaultValue: null,
              literalCode: '',
              literalValue: '',
              value: {
                type: 'ref',
                content: 'type',
                syncMessage: null,
              },
              schema: null,
            },
            {
              code: 'date',
              name: '发生日期',
              type: 'date',
              required: false,
              description: null,
              defaultValue: null,
              literalCode: '',
              literalValue: '',
              value: {
                type: 'ref',
                content: 'date',
                syncMessage: null,
              },
              schema: null,
            },
            {
              code: 'amount',
              name: '报销金额',
              type: 'number',
              required: true,
              description: null,
              defaultValue: null,
              literalCode: '',
              literalValue: '',
              value: {
                type: 'ref',
                content: 'amount',
                syncMessage: null,
              },
              schema: null,
            },
            {
              code: 'people',
              name: '消费人数',
              type: 'string',
              required: true,
              description: null,
              defaultValue: null,
              literalCode: '',
              literalValue: '',
              value: {
                type: 'ref',
                content: 'people',
                syncMessage: null,
              },
              schema: null,
            },
          ],
        },
        {
          code: 'other',
          name: '其它',
          type: 'string',
          required: true,
          description: '',
          defaultValue: null,
          literalCode: 'NodeInput_card0_other',
          literalValue: '123',
          value: {
            type: 'literal',
            content: '123',
            syncMessage: null,
          },
          schema: null,
        },
      ],
      nodeCode: 'card0',
      probResult: {
        '8': 2,
        '基本信息：': 2,
        字段名称: 2,
        示例数据: 2,
        '费用报销-商业板块绿地中心项目—业务招待费报销6000元': 1,
        报销主题: 2,
        '-2024-6-4': 2,
        公司抬头: 2,
        深圳市明源地产有限公司: 2,
        '绿地中心项目工作对接、资源洽谈申请招待费报销6000元.': 2,
        报销说明: 2,
        '招待3人，5人陪同': 2,
        敏感备注: 2,
        '包括吃饭2000元，洗脚2000 元，KTV2000 元': 2,
        预算检查结果: 2,
        未超标: 2,
        费控标准检查结果: 2,
        超标: 2,
        '报销明细：': 2,
        费用类型: 2,
        发生日期: 2,
        报销金额: 2,
        消费人数: 2,
        餐饮: 2,
        '2024-04-24': 2,
        '2125.66': 2,
        娱乐: 2,
        '4000.99': 2,
        '5000': 1,
        '2024-03-18': 0,
      },
      probRules: [
        {
          maxProb: 85,
          minProb: 0,
          priority: 0,
        },
        {
          maxProb: 95,
          minProb: 85,
          priority: 1,
        },
        {
          maxProb: 100,
          minProb: 95,
          priority: 2,
        },
      ],
    } as FormCardData,
  },
]

export const fixedForm = [
  {
    type: 'form',
    code: 'card2',
    data: {
      id: null,
      templateId: '401f4036-9449-4726-a318-4178dbc3c7ef',
      props: [
        { name: 'fields', value: [] },
        { name: 'editable', value: true },
        { name: 'mode', value: 'view' },
        {
          name: 'columns',
          value: [
            { name: '提示词_0-定金金额', code: 'amount', type: 'InputNumber', required: false, readonly: false, columns: [] },
            { name: 'test', code: 'test', type: 'Input', required: false, readonly: false, columns: [] },
            {
              name: '提示词_0-客户',
              code: 'cst',
              type: 'DataForm',
              required: false,
              readonly: false,
              columns: [
                { name: '身份证号', code: 'cardid', type: 'Input', required: false, readonly: false },
                { name: '姓名', code: 'cstname', type: 'Input', required: false, readonly: false },
              ],
            },
            {
              name: '提示词_0-客户',
              code: 'cst2',
              type: 'DataForm',
              required: false,
              readonly: false,
              columns: [
                { name: '联系电话', code: 'tel', type: 'Input', required: false, readonly: false },
                { name: '姓名', code: 'cstname2', type: 'Input', required: false, readonly: false },
                { name: '身份证号', code: 'cardid2', type: 'Input', required: false, readonly: false },
                { name: '联系电话', code: 'tel2', type: 'Input', required: false, readonly: false },
              ],
            },
          ],
        },
      ],
      nodeCode: 'card2',
      probResult: {},
      probRules: [
        { maxProb: 85.0, minProb: 0.0, priority: 0 },
        { maxProb: 95.0, minProb: 85.0, priority: 1 },
        { maxProb: 100.0, minProb: 95.0, priority: 2 },
      ],
      inputs: [
        { code: 'amount', name: '提示词_0-定金金额', type: 'number', required: false, description: null, defaultValue: null, literalCode: 'NodeOutput_promptTemplate0_amount', literalValue: '5000', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_amount', syncMessage: null }, schema: [] },
        { code: 'test', name: 'test', type: 'string', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card2_test', literalValue: '固定', value: { type: 'literal', content: '固定', syncMessage: null }, schema: [] },
        {
          code: 'cst',
          name: '提示词_0-客户',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_promptTemplate0_cst',
          literalValue: '[\n  {\n    "cardid": "123456789012345678",\n    "cstname": "张三"\n  }\n]',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_cst', syncMessage: null },
          schema: [
            { code: 'cardid', name: '身份证号', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cardid', syncMessage: null }, schema: null },
            { code: 'cstname', name: '姓名', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cstname', syncMessage: null }, schema: null },
          ],
        },
        {
          code: 'cst2',
          name: '提示词_0-客户',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_promptTemplate0_cst',
          literalValue: '[\n  {\n    "cardid": "123456789012345678",\n    "cstname": "张三",\n    "tel": "13800138000"\n  }\n]',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_cst', syncMessage: null },
          schema: [
            { code: 'tel', name: '联系电话', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'tel', syncMessage: null }, schema: null },
            { code: 'cstname2', name: '姓名', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cstname', syncMessage: null }, schema: null },
            { code: 'cardid2', name: '身份证号', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cardid', syncMessage: null }, schema: null },
            { code: 'tel2', name: '联系电话', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'tel', syncMessage: null }, schema: null },
          ],
        },
        { code: 'currentDocument', name: '当前文档', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocument', literalValue: null, value: { type: 'ref', content: 'System_Keyword_CurrentDocument', syncMessage: null }, schema: null },
        { code: 'docname', name: '提示词_0-文档名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: 'NodeOutput_promptTemplate0_docname', literalValue: '销售合同-2023', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_docname', syncMessage: null }, schema: null },
      ],
      data: [],
      title: '选择器分支1：',
      outputs: [
        { code: 'amount', name: '提示词_0-定金金额', type: 'number', required: false, description: null, defaultValue: null, literalCode: 'NodeOutput_promptTemplate0_amount', literalValue: '5000', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_amount', syncMessage: null }, schema: [] },
        { code: 'test', name: 'test', type: 'string', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card2_test', literalValue: '固定', value: { type: 'literal', content: '固定', syncMessage: null }, schema: [] },
        {
          code: 'cst',
          name: '提示词_0-客户',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_promptTemplate0_cst',
          literalValue: '[\n  {\n    "cardid": "123456789012345678",\n    "cstname": "张三"\n  }\n]',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_cst', syncMessage: null },
          schema: [
            { code: 'cardid', name: '身份证号', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cardid', syncMessage: null }, schema: null },
            { code: 'cstname', name: '姓名', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cstname', syncMessage: null }, schema: null },
          ],
        },
        {
          code: 'cst2',
          name: '提示词_0-客户',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: 'NodeOutput_promptTemplate0_cst',
          literalValue: '[\n  {\n    "cardid": "123456789012345678",\n    "cstname": "张三",\n    "tel": "13800138000"\n  }\n]',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_cst', syncMessage: null },
          schema: [
            { code: 'tel', name: '联系电话', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'tel', syncMessage: null }, schema: null },
            { code: 'cstname2', name: '姓名', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cstname', syncMessage: null }, schema: null },
            { code: 'cardid2', name: '身份证号', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'cardid', syncMessage: null }, schema: null },
            { code: 'tel2', name: '联系电话', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'tel', syncMessage: null }, schema: null },
          ],
        },
        { code: 'currentDocument', name: '当前文档', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocument', literalValue: null, value: { type: 'ref', content: 'System_Keyword_CurrentDocument', syncMessage: null }, schema: null },
        { code: 'docname', name: '提示词_0-文档名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: 'NodeOutput_promptTemplate0_docname', literalValue: '销售合同-2023', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_docname', syncMessage: null }, schema: null },
      ],
    } as FormCardData,
  },
]

export const form2 = [
  {
    type: 'form',
    next: null,
    code: 'card0',
    data: {
      id: null,
      templateId: '401f4036-9449-4726-a318-4178dbc3c7ef',
      props: [
        { name: 'fields', value: [] },
        { name: 'editable', value: false },
        { name: 'mode', value: 'edit' },
        {
          name: 'columns',
          value: [
            { name: '基础信息', code: 'group_nk5sod', type: 'GroupLine', required: false, readonly: false, columns: [] },
            { name: '合同名称', code: 'ContractName', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '合同类别', code: 'HtTypeName', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '项目名称', code: 'ProjectCostOwnerNames', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '签约日期', code: 'SignDate', type: 'DatePicker', required: false, readonly: false, columns: [] },
            { name: '是否总包合同', code: 'IsZfb', type: 'Input', required: false, readonly: false, thousands: false, options: [], columns: [] },
            { name: '合同主体信息', code: 'group_n344rp', type: 'GroupLine', required: false, readonly: false, columns: [] },
            { name: '甲方单位', code: 'JfProviderName', type: 'Input', required: false, readonly: false, columns: [] },
            {
              name: '乙方单位',
              code: 'YfProviderListShow',
              type: 'DataForm',
              required: false,
              readonly: false,
              columns: [
                { name: '乙方单位', code: 'YfProviderName', type: 'Input', required: false, readonly: false },
                { name: '税率', code: 'TaxRate', type: 'InputNumber', required: false, readonly: false },
              ],
            },
            { name: '丙方单位', code: 'BfProviderNames', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '收款单位', code: 'ReceivingProviderName', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '其他信息', code: 'group_ztjgur', type: 'GroupLine', required: false, readonly: false, columns: [] },
            { name: '进度款支付比例上限（%）', code: 'ProgressPayUpperLimitRate', type: 'InputNumber', required: false, readonly: false, columns: [] },
            { name: '是否预付备料款', code: 'HasPrepaidMateriel', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '产值完成率上限', code: 'CzCompleteUpRate', type: 'InputNumber', required: false, readonly: false, columns: [] },
            { name: '计划开工日期', code: 'PlanBeginDate', type: 'DatePicker', required: false, readonly: false, columns: [] },
            { name: '计划完工日期', code: 'PlanEndDate', type: 'DatePicker', required: false, readonly: false, columns: [] },
            { name: '工期', code: 'WorkPeriod', type: 'InputNumber', required: false, readonly: false, columns: [] },
            { name: '价格信息', code: 'group_nhhq3i', type: 'GroupLine', required: false, readonly: false, columns: [] },
            { name: '计价方式', code: 'PricingMethod', type: 'Input', required: false, readonly: false, columns: [] },
            { name: '合同金额', code: 'TotalAmount', type: 'InputNumber', required: false, readonly: false, thousands: true, columns: [] },
            { name: '付款条件', code: 'group_hmgxnw', type: 'GroupLine', required: false, readonly: false, columns: [] },
            {
              name: '付款条件',
              code: 'FkConditionList',
              type: 'DataForm',
              required: false,
              readonly: false,
              columns: [
                { name: '供应商名称', code: 'ProviderName', type: 'Input', required: false, readonly: false, thousands: false, options: [] },
                { name: '款项类别及名称', code: 'FKName', type: 'Input', required: false, readonly: false, thousands: false, options: [] },
                { name: '付款比例', code: 'FKRate', type: 'InputNumber', required: false, readonly: false, thousands: true, options: [] },
                { name: '付款金额', code: 'FKAmount', type: 'InputNumber', required: false, readonly: false, thousands: true, options: [] },
                { name: '付款日期', code: 'FkDate', type: 'DatePicker', required: false, readonly: false, thousands: false, options: [] },
                { name: '付款条件', code: 'FKCondition', type: 'Input', required: false, readonly: false, thousands: false, options: [] },
              ],
            },
          ],
        },
        { name: 'actions', value: [{ code: 'card-confirm', name: '应用并调整' }] },
      ],
      nodeCode: 'card0',
      probResult: {},
      probRules: [
        { maxProb: 85.0, minProb: 0.0, priority: 0 },
        { maxProb: 95.0, minProb: 85.0, priority: 1 },
        { maxProb: 100.0, minProb: 95.0, priority: 2 },
      ],
      inputs: [
        { code: 'group_nk5sod', name: '基础信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_nk5sod', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'ContractName', name: '合同名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ContractName', syncMessage: null }, schema: [] },
        { code: 'HtTypeName', name: '合同类别', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_HtTypeName', syncMessage: null }, schema: [] },
        { code: 'ProjectCostOwnerNames', name: '项目名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ProjectCostOwnerNames', syncMessage: null }, schema: [] },
        { code: 'SignDate', name: '签约日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_SignDate', syncMessage: null }, schema: [] },
        { code: 'IsZfb', name: '是否总包合同', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_IsZfb', syncMessage: null }, schema: [] },
        { code: 'group_n344rp', name: '合同主体信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_n344rp', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'JfProviderName', name: '甲方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_JfProviderName', syncMessage: null }, schema: [] },
        {
          code: 'YfProviderListShow',
          name: '乙方单位',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: '',
          literalValue: '',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_YfProviderList', syncMessage: null },
          schema: [
            { code: 'YfProviderName', name: '乙方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'YfProviderName', syncMessage: null }, schema: null },
            { code: 'TaxRate', name: '税率', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'TaxRate', syncMessage: null }, schema: null },
          ],
        },
        { code: 'BfProviderNames', name: '丙方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_BfProviderNames', syncMessage: null }, schema: [] },
        { code: 'ReceivingProviderName', name: '收款单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ReceivingProviderName', syncMessage: null }, schema: [] },
        { code: 'group_ztjgur', name: '其他信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_ztjgur', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'ProgressPayUpperLimitRate', name: '进度款支付比例上限（%）', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ProgressPayUpperLimitRate', syncMessage: null }, schema: [] },
        { code: 'HasPrepaidMateriel', name: '是否预付备料款', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_HasPrepaidMateriel', syncMessage: null }, schema: [] },
        { code: 'CzCompleteUpRate', name: '产值完成率上限', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_CzCompleteUpRate', syncMessage: null }, schema: [] },
        { code: 'PlanBeginDate', name: '计划开工日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PlanBeginDate', syncMessage: null }, schema: [] },
        { code: 'PlanEndDate', name: '计划完工日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PlanEndDate', syncMessage: null }, schema: [] },
        { code: 'WorkPeriod', name: '工期', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_WorkPeriod', syncMessage: null }, schema: [] },
        { code: 'group_nhhq3i', name: '价格信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_nhhq3i', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'PricingMethod', name: '计价方式', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PricingMethod', syncMessage: null }, schema: [] },
        { code: 'TotalAmount', name: '合同金额', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_TotalAmount', syncMessage: null }, schema: [] },
        { code: 'group_hmgxnw', name: '付款条件', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_hmgxnw', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        {
          code: 'FkConditionList',
          name: '付款条件',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: '',
          literalValue: '',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate1_FkConditionList', syncMessage: null },
          schema: [
            { code: 'ProviderName', name: '供应商名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'ProviderName', syncMessage: null }, schema: null },
            { code: 'FKName', name: '款项类别及名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKName', syncMessage: null }, schema: null },
            { code: 'FKRate', name: '付款比例', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKRate', syncMessage: null }, schema: null },
            { code: 'FKAmount', name: '付款金额', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKAmount', syncMessage: null }, schema: null },
            { code: 'FkDate', name: '付款日期', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FkDate', syncMessage: null }, schema: null },
            { code: 'FKCondition', name: '付款条件', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKCondition', syncMessage: null }, schema: null },
          ],
        },
        { code: 'currentDocument', name: '当前文档', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocument', literalValue: 'a9b4a8d2-3d5a-4fe2-bba9-688422ca7b6b', value: { type: 'ref', content: 'System_Keyword_CurrentDocument', syncMessage: null }, schema: null },
        { code: 'currentDocumentName', name: '当前文档名称', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocumentName', literalValue: '新海棚户区改造回迁商品房（E0301地块）项目工程总承包合同2018.4.28.docx', value: { type: 'ref', content: 'System_Keyword_CurrentDocumentName', syncMessage: null }, schema: null },
        { code: 'YfProviderList', name: '推理合同信息-乙方单位列表', type: 'array<object>', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_YfProviderList', syncMessage: null }, schema: null },
      ],
      title: '',
      outputs: [
        { code: 'group_nk5sod', name: '基础信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_nk5sod', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'ContractName', name: '合同名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ContractName', syncMessage: null }, schema: [] },
        { code: 'HtTypeName', name: '合同类别', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_HtTypeName', syncMessage: null }, schema: [] },
        { code: 'ProjectCostOwnerNames', name: '项目名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ProjectCostOwnerNames', syncMessage: null }, schema: [] },
        { code: 'SignDate', name: '签约日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_SignDate', syncMessage: null }, schema: [] },
        { code: 'IsZfb', name: '是否总包合同', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_IsZfb', syncMessage: null }, schema: [] },
        { code: 'group_n344rp', name: '合同主体信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_n344rp', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'JfProviderName', name: '甲方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_JfProviderName', syncMessage: null }, schema: [] },
        {
          code: 'YfProviderListShow',
          name: '乙方单位',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: '',
          literalValue: '',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate0_YfProviderList', syncMessage: null },
          schema: [
            { code: 'YfProviderName', name: '乙方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'YfProviderName', syncMessage: null }, schema: null },
            { code: 'TaxRate', name: '税率', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'TaxRate', syncMessage: null }, schema: null },
          ],
        },
        { code: 'BfProviderNames', name: '丙方单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_BfProviderNames', syncMessage: null }, schema: [] },
        { code: 'ReceivingProviderName', name: '收款单位', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ReceivingProviderName', syncMessage: null }, schema: [] },
        { code: 'group_ztjgur', name: '其他信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_ztjgur', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'ProgressPayUpperLimitRate', name: '进度款支付比例上限（%）', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_ProgressPayUpperLimitRate', syncMessage: null }, schema: [] },
        { code: 'HasPrepaidMateriel', name: '是否预付备料款', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_HasPrepaidMateriel', syncMessage: null }, schema: [] },
        { code: 'CzCompleteUpRate', name: '产值完成率上限', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_CzCompleteUpRate', syncMessage: null }, schema: [] },
        { code: 'PlanBeginDate', name: '计划开工日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PlanBeginDate', syncMessage: null }, schema: [] },
        { code: 'PlanEndDate', name: '计划完工日期', type: 'date', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PlanEndDate', syncMessage: null }, schema: [] },
        { code: 'WorkPeriod', name: '工期', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_WorkPeriod', syncMessage: null }, schema: [] },
        { code: 'group_nhhq3i', name: '价格信息', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_nhhq3i', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        { code: 'PricingMethod', name: '计价方式', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_PricingMethod', syncMessage: null }, schema: [] },
        { code: 'TotalAmount', name: '合同金额', type: 'number', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_TotalAmount', syncMessage: null }, schema: [] },
        { code: 'group_hmgxnw', name: '付款条件', type: 'group', required: false, description: null, defaultValue: null, literalCode: 'NodeInput_card0_group_hmgxnw', literalValue: '', value: { type: 'literal', content: '', syncMessage: null }, schema: [] },
        {
          code: 'FkConditionList',
          name: '付款条件',
          type: 'array<object>',
          required: false,
          description: null,
          defaultValue: null,
          literalCode: '',
          literalValue: '',
          value: { type: 'ref', content: 'NodeOutput_promptTemplate1_FkConditionList', syncMessage: null },
          schema: [
            { code: 'ProviderName', name: '供应商名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'ProviderName', syncMessage: null }, schema: null },
            { code: 'FKName', name: '款项类别及名称', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKName', syncMessage: null }, schema: null },
            { code: 'FKRate', name: '付款比例', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKRate', syncMessage: null }, schema: null },
            { code: 'FKAmount', name: '付款金额', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKAmount', syncMessage: null }, schema: null },
            { code: 'FkDate', name: '付款日期', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FkDate', syncMessage: null }, schema: null },
            { code: 'FKCondition', name: '付款条件', type: 'string', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'FKCondition', syncMessage: null }, schema: null },
          ],
        },
        { code: 'currentDocument', name: '当前文档', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocument', literalValue: 'a9b4a8d2-3d5a-4fe2-bba9-688422ca7b6b', value: { type: 'ref', content: 'System_Keyword_CurrentDocument', syncMessage: null }, schema: null },
        { code: 'currentDocumentName', name: '当前文档名称', type: 'string', required: true, description: '', defaultValue: null, literalCode: 'System_Keyword_CurrentDocumentName', literalValue: '新海棚户区改造回迁商品房（E0301地块）项目工程总承包合同2018.4.28.docx', value: { type: 'ref', content: 'System_Keyword_CurrentDocumentName', syncMessage: null }, schema: null },
        { code: 'YfProviderList', name: '推理合同信息-乙方单位列表', type: 'array<object>', required: false, description: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_YfProviderList', syncMessage: null }, schema: null },
      ],
    },
  },
]

export const form2Steam = `7:{"NodeOutput_promptTemplate0_ContractName":"建设工程"}
7:{"NodeOutput_promptTemplate0_ContractName":"合同"}
7:{"NodeOutput_promptTemplate0_HtTypeName":"工程类"}
7:{"NodeOutput_promptTemplate0_ProjectCostOwnerNames":"新海棚户"}
7:{"NodeOutput_promptTemplate0_ProjectCostOwnerNames":"区改造回迁"}
7:{"NodeOutput_promptTemplate0_ProjectCostOwnerNames":"商品房（E0"}
7:{"NodeOutput_promptTemplate0_ProjectCostOwnerNames":"301地块"}
7:{"NodeOutput_promptTemplate0_ProjectCostOwnerNames":"）项目"}
7:{"NodeOutput_promptTemplate0_SignDate":"201"}
7:{"NodeOutput_promptTemplate0_SignDate":"8-05"}
7:{"NodeOutput_promptTemplate0_SignDate":"-02"}
7:{"NodeOutput_promptTemplate0_IsZfb":"是"}
7:{"NodeOutput_promptTemplate0_JfProviderName":"海口联投"}
7:{"NodeOutput_promptTemplate0_JfProviderName":"新海置业有限公司"}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProvider":null}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中"}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":""}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位"}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":null}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":0}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":0,"YfTotalAmount":73}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":0,"YfTotalAmount":733267}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":0,"YfTotalAmount":*********.99}}}
7:{"NodeOutput_promptTemplate0_YfProviderList":{"index":0,"value":{"YfProviderName":"中建三局第二建设工程有限责任公司","YfProviderType":"施工单位","TaxRate":0,"YfTotalAmount":*********.99}}}
7:{"NodeOutput_promptTemplate0_ReceivingProviderName":"中"}
7:{"NodeOutput_promptTemplate0_ReceivingProviderName":"建三局第二"}
7:{"NodeOutput_promptTemplate0_ReceivingProviderName":"建设工程有限责任公司"}
7:{"NodeOutput_promptTemplate0_HasPrepaidMateriel":"否"}
7:{"NodeOutput_promptTemplate0_PlanBeginDate":"201"}
7:{"NodeOutput_promptTemplate0_PlanBeginDate":"8-05"}
7:{"NodeOutput_promptTemplate0_PlanBeginDate":"-01"}
7:{"NodeOutput_promptTemplate0_PlanEndDate":"20"}
7:{"NodeOutput_promptTemplate0_PlanEndDate":"19-0"}
7:{"NodeOutput_promptTemplate0_PlanEndDate":"3-31"}
7:{"NodeOutput_promptTemplate0_WorkPeriod":"3"}
7:{"NodeOutput_promptTemplate0_WorkPeriod":"35"}
7:{"NodeOutput_promptTemplate0_PricingMethod":"总价包"}
7:{"NodeOutput_promptTemplate0_PricingMethod":"干"}
7:{"NodeOutput_promptTemplate0_TotalAmount":"7332"}
7:{"NodeOutput_promptTemplate0_TotalAmount":"6714"}
7:{"NodeOutput_promptTemplate0_TotalAmount":"1.99"}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":11}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十天内，政府"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十天内，政府支付住宅团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十天内，政府支付住宅团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十天内，政府支付住宅团购总价的15%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":0,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得土地证或缴清土地出让金后三十天内，政府支付住宅团购总价的15%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":1100}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":11000007}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，政府支付住宅团购"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，政府支付住宅团购总价的15"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，政府支付住宅团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，政府支付住宅团购总价的15%","FKType":"进度"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":1,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"取得施工许可证或区政府认可进场施工后三十天内，政府支付住宅团购总价的15%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":1466667}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.7}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%","FKType":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":2,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":14666}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%","FKType":"进度"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":3,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":20,"FKAmount":146666761.73,"FkDate":null,"FKCondition":"施工至正负零后三十天内，政府支付住宅团购总价的20%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":733}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":7333338}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构5"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十天内，政府"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十天内，政府支付团购总价的"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十天内，政府支付团购总价的10%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十天内，政府支付团购总价的10%","FKType":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":4,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构50%后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":7}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后三十天内，政府支付团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后三十天内，政府支付团购总价的10%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":5,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至项目结构封顶后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":7333}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":6,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":7333}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":7,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":7333}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":8,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"进度款","FKRate":10,"FKAmount":73333380.87,"FkDate":null,"FKCondition":"施工至外立面装饰完成后三十天内，政府支付团购总价的10%","FKType":"进度款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":1100}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":11000007}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":9,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":1100000}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.2}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的1"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":10,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":11}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":11,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":1}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":11000}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":12,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"结算款","FKRate":15,"FKAmount":110000071.29,"FkDate":null,"FKCondition":"项目整体竣工综合验收并完成竣工结算后三十天内，政府支付团购总价的15%","FKType":"结算款"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"Provider":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":1466}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天内，政府支付"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天内，政府支付团购结算款的"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天内，政府支付团购结算款的2%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天内，政府支付团购结算款的2%","FKType":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":13,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":2,"FKAmount":14665342.84,"FkDate":null,"FKCondition":"项目竣工备案完成后三十天内，政府支付团购结算款的2%","FKType":"质保金"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":2}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":null}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付团购结算款的"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付团购结算款的3%"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付团购结算款的3%","FKType":""}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付团购结算款的3%","FKType":"质保金"}}}
7:{"NodeOutput_promptTemplate1_FkConditionList":{"index":14,"value":{"ProviderName":"中建三局第二建设工程有限责任公司","FKName":"质保金","FKRate":3,"FKAmount":21998014.26,"FkDate":null,"FKCondition":"项目竣工综合验收完毕后满两年之日起三十天内，政府支付团购结算款的3%","FKType":"质保金"}}}`
