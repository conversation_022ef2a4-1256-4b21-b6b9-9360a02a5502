import type { MockHttpItem } from 'vite-plugin-mock-dev-server'
import { createAssistantContext, createAssistantContextWithDebug } from './assistant'
import { chats1 } from './chat'
import { createSkillStartup } from './skill'

export const createSkillStartupResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.end(JSON.stringify(createSkillStartup()))
}

// Mock助手上下文
export const defineAssistantContextResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  if (req.body.mode === 'debug') {
    res.end(
      JSON.stringify({
        code: '0',
        message: '成功',
        success: true,
        error: null,
        data: createAssistantContextWithDebug(),
      }),
    )
  } else {
    res.end(
      JSON.stringify({
        code: '0',
        message: '成功',
        success: true,
        error: null,
        data: createAssistantContext(),
      }),
    )
  }
}

// Mock助手推荐问
export const defineAssistantQuestionResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  let data = ['🔥 在复杂报表中，查询出来的数据不准要怎么处理？在复杂报表中，查询出来的数据', '📈 在仪表板中，跳转到子页面后，如何传递参数？', '🖥 在酷炫大屏中，表格组件如何配置冻结列？', '🖥 酷炫大屏、仪表板、数据集？', '🖥 表格、散点地图？', '🖥 表格、散点地图？', '🖥 在复杂报表中，查询出来的数据？']

  console.log('req.params.id', req.params.id)
  if (req.params.id === 'f2df1516-575e-4be3-ac2c-61f27ed1a60e') {
    // f2df1516-575e-4be3-ac2c-61f27ed1a60e
    // 定价调价助手，不提供预置问题
    data = []
  }
  console.log('data', data)

  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        questions: data,
        enabled: true,
      },
    }),
  )
}

export const defineAssistantLoadOptionDataResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [
        {
          value: '4230bc6e-69e6-46a9-a39e-b929a06a84e8',
          text: '系统管理员',
        },
        {
          value: '950ae1e0-2e77-4e54-b95a-a689d6fdc509',
          text: '明源云服务账户',
        },
      ],
    }),
  )
}

export const defineAssistantSkillGuideResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        skillGUID: '8d033108-db85-4582-9733-a6e4b13f71c8',
        skillName: '技能测试【618】',
        icon: 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/3a12fed0-d508-5807-35cb-9612d3cf5074.svg',
        description: '技能说明技能说明技能说明技能说明技能说明',
        questions: [
          {
            skillQuestionsGUID: null,
            skillGUID: null,
            question: '技能说明技能说明1',
            sort: null,
          },
          {
            skillQuestionsGUID: null,
            skillGUID: null,
            question: '技能说明技能说明12',
            sort: null,
          },
        ],
        guide:
          '<p>使用指引<br></p><p><img src="http://*************:9070/api/v2/GetFile?documentId=e0d993bd-9281-4b13-ac34-6638e2f86b45&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJlMGQ5OTNiZC05MjgxLTRiMTMtYWMzNC02NjM4ZTJmODZiNDUiLCJUZW5hbnRDb2RlIjoibXlzb2Z0IiwiZXhwIjoxNzM4Mzk4ODMzLCJpYXQiOjE3MjI4NDMyMzMsImp0aSI6ImI0MDJmZTAwLTk0NDgtNDkyNS1hOTg1LTIwMjY5OTdlMTI0NyJ9.0EEe5g4neVHy5TmhkZ7F76AQwff_bj_M3HwitWK4vSA"><br></p><p><br></p>',
        examples: [
          {
            skillUserExampleGUID: null,
            skillGUID: null,
            userQuestion: '使用指引使用指引',
            assistantAnswer: '使用指引使用指引使用指引',
            sort: null,
          },
        ],
      },
    }),
  )
}

export const defineAssistantChatListResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: [
        {
          chatGUID: '85f93a17-568b-4b16-90ff-f36be454e8ca',
          skillGUID: 'ec35e33f-e9b0-4543-a640-be21c63ba4d1',
          assistantGUID: '7a3f4310-125c-11ef-83d7-00155d822d63',
          currentNodeGUID: 'c04bb71d-4629-4a8f-adbc-31640b92b615',
          currentOrchestrationGUID: 'c2de0421-9482-4086-aaa4-44ed9d9a8035',
          title: '转人工',
          isDeleted: 0,
        },
        {
          chatGUID: '60298767-ed3b-4037-b6a4-5264d0741437',
          skillGUID: 'd3f4e854-207f-4b2e-9cca-3cf89f6f9ba7',
          assistantGUID: '7a3f4310-125c-11ef-83d7-00155d822d63',
          currentNodeGUID: 'f1cbc3ee-c45b-451d-b10b-e7a8b76ff0ac',
          currentOrchestrationGUID: '46ab0204-2b17-4d75-9945-34960ff1e285',
          title: '新增合同',
          isDeleted: 0,
        },
      ],
    }),
  )
}

export const defineAssistantSearchChatListResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        list: [
          {
            chatGUID: '85f93a17-568b-4b16-90ff-f36be454e8ca',
            skillGUID: 'ec35e33f-e9b0-4543-a640-be21c63ba4d1',
            assistantGUID: '7a3f4310-125c-11ef-83d7-00155d822d63',
            currentNodeGUID: 'c04bb71d-4629-4a8f-adbc-31640b92b615',
            currentOrchestrationGUID: 'c2de0421-9482-4086-aaa4-44ed9d9a8035',
            title: '转人工',
            isDeleted: 0,
          },
          {
            chatGUID: '60298767-ed3b-4037-b6a4-5264d0741437',
            skillGUID: 'd3f4e854-207f-4b2e-9cca-3cf89f6f9ba7',
            assistantGUID: '7a3f4310-125c-11ef-83d7-00155d822d63',
            currentNodeGUID: 'f1cbc3ee-c45b-451d-b10b-e7a8b76ff0ac',
            currentOrchestrationGUID: '46ab0204-2b17-4d75-9945-34960ff1e285',
            title: '新增合同',
            isDeleted: 0,
          },
        ],
        total: 2,
        pageSize: 10,
        pageIndex: 0,
      },
    }),
  )
}

export const defineAssistantChatDetailResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: chats1,
    }),
  )
}

export const defineAssistantApplicationQueryByShareCodeResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: {
        applicationGUID: '08dcaa48-5267-4523-853e-f55a025540ba',
        applicationName: '测试',
        applicationCode: 'gpt5tzo1z8xj1ve0ps',
        description: null,
        icon: null,
        spaceGUID: '08dca713-3d6e-fbce-835e-6d2d2154296c',
        assistants: [
          {
            assistantGUID: '01942c5a-6713-721b-96d1-b1c24eb67269',
            assistantName: 'AI智能助手',
            assistantCode: 'system_assistant',
            isSystem: 0,
            describe: '',
            selfIntroduction: '',
            assistantType: 1,
            promptGUID: null,
            modelInstanceGUID: null,
            modelInstanceCode: null,
            modelInstanceName: null,
            promptCode: null,
            promptContext: null,
            introductoryPhrase: '',
            icon: '',
            skillConfig: 1,
            standalonePage: 1,
            accessUrl: '/gptbuilder/gpt/index.html?id=18ee69a2-7263-4d0b-8df7-efe2468a9a12',
            skillGUIDs: null,
            skillText: null,
            openCheck: 0,
            spaceGUID: '08dca713-3d6e-fbce-835e-6d2d2154296c',
          },
          {
            assistantGUID: '467ebe30-d3c4-4694-a92a-37d6c35048f0',
            assistantName: '系统助手1',
            assistantCode: 'system_other',
            isSystem: 0,
            describe: 'sdfs',
            selfIntroduction: 'sdf',
            assistantType: 1,
            promptGUID: null,
            modelInstanceGUID: null,
            modelInstanceCode: null,
            modelInstanceName: null,
            promptCode: null,
            promptContext: null,
            introductoryPhrase: 'sdf',
            icon: '[{"name":"图标.png","documentGuid":"2a2c11c4-7e48-44b8-a230-707701f61943","checked":0,"isDefault":0,"size":10859}]',
            skillConfig: 0,
            standalonePage: 0,
            accessUrl: '/gptbuilder/gpt/index.html?id=467ebe30-d3c4-4694-a92a-37d6c35048f0',
            skillGUIDs: null,
            skillText: null,
            openCheck: 0,
            spaceGUID: '08dca722-e29d-fe02-feb7-d56579aa2681',
          },
          {
            assistantGUID: '568eece9-36ab-4369-be5f-2d4295a11b9e',
            assistantName: '普通助手1',
            assistantCode: 'foo',
            isSystem: 0,
            describe: '12321321121',
            selfIntroduction: '3',
            assistantType: 1,
            promptGUID: null,
            modelInstanceGUID: null,
            modelInstanceCode: null,
            modelInstanceName: null,
            promptCode: null,
            promptContext: null,
            introductoryPhrase: '123',
            icon: '',
            skillConfig: 1,
            standalonePage: 1,
            accessUrl: '/gptbuilder/gpt/index.html?id=568eece9-36ab-4369-be5f-2d4295a11b9e',
            skillGUIDs: null,
            skillText: null,
            openCheck: 0,
            spaceGUID: '08dca713-3d6e-fbce-835e-6d2d2154296c',
          },
          {
            assistantGUID: '01942c5a-6713-721b-96d1-b71616a5f6ed',
            assistantName: 'AI智能客服',
            assistantCode: 'system_kefu',
            isSystem: 0,
            describe: 'AI智能客服',
            selfIntroduction: '3',
            assistantType: 1,
            promptGUID: null,
            modelInstanceGUID: null,
            modelInstanceCode: null,
            modelInstanceName: null,
            promptCode: null,
            promptContext: null,
            introductoryPhrase: '123',
            icon: '',
            skillConfig: 1,
            standalonePage: 1,
            accessUrl: '/gptbuilder/gpt/index.html?id=568eece9-36ab-4369-be5f-2d4295a11b9e',
            skillGUIDs: null,
            skillText: null,
            openCheck: 0,
            spaceGUID: '08dca713-3d6e-fbce-835e-6d2d2154296c',
          },
        ],
      },
    }),
  )
}

export const defineAssistantInitEventToSkillAndAssistantResponseMock: MockHttpItem['response'] = (req, res, next) => {
  res.statusCode = 200
  res.setHeader('Content-Type', 'application/json')
  console.log(req.body)
  const id = req.body.skillGUID
  if (id === 'f2df1516-575e-4be3-ac2c-61f27ed1a60e' || !id) {
    res.end(
      JSON.stringify({
        code: '0',
        message: '成功',
        success: true,
        error: null,
        data: "console.log($gpt);console.log('hello assistant');",
      }),
    )
    return
  }
  res.end(
    JSON.stringify({
      code: '0',
      message: '成功',
      success: true,
      error: null,
      data: `
      // 1️ 调用插件工具
      const res = await $gpt.tool.invoke('/ykf/app/agent/messageinfo/shortcuts')

      // 2️ 设置快捷指令
      if (res.shortcuts) {
        $gpt.skill.shortcuts(res.shortcuts)
      }

      // 3️ 打开强提醒页面
      if(res.evaluateUrl) {
        $gpt.page.open(res.evaluateUrl)
      }

      // 4️ 打开转人工页面
      if(res.restoreUrl) {
        $gpt.chat.open(res.restoreUrl)
      }
      // 5 欢迎语
      if(res.welcomeMsg){
        $gpt.chat.welcome(res.welcomeMsg)
      }
    `,
    }),
  )
}
