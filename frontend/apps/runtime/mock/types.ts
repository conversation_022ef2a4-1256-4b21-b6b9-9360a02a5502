export enum MessageType {
  text = 0,
  process = 1,
  error = 2,
  data = 3,
  done = 4,
  replace = 5,
  recovery = 6,
  stream = 7,
  reasoning = 8,
}

const createMessage = <T = any>(data: T, type: MessageType, consuming = 50) => {
  return {
    code: type,
    body: (typeof data === 'object' ? JSON.stringify(data) : data) as string,
    time: consuming,
  }
}

export const createDataMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.data, consuming)
}

export const createTextMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.text, consuming)
}

export const createReplaceMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.replace, consuming)
}

export const createDoneMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.done, consuming)
}

export const createProcessMessage = (text: string, consuming?: number) => {
  return createMessage({ text }, MessageType.process, consuming)
}

export const createTextMessages = (text: string, consuming = 10) => {
  return text.split('\n').map((v, i) => {
    if (i > 0) {
      v = `|n${v}`
    }
    return createTextMessage(v, consuming)
  })
}

export const createRecoveryMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.recovery, consuming)
}

export const createStreamMessage = <T>(data: T, consuming?: number) => {
  return createMessage(data, MessageType.stream, consuming)
}

const parseLine = (v: string) => {
  if (v.includes(':')) {
    const i = v.indexOf(':')
    const type = v.slice(0, i)
    const value = v.slice(i + 1)
    return { type: Number(type), value }
  }
  return { value: v }
}

export const createStreamMessages = <T = object[]>(data: T | string, consuming?: number) => {
  let v = data as object[]
  if (typeof data === 'string') {
    v = data.split('\n').map((v) => {
      const { type, value } = parseLine(v)
      if (type) {
        // 必须是 7
        if (type !== MessageType.stream) {
          throw new Error('unsupported type')
        }
      }
      return JSON.parse(value)
    })
  }

  if (!Array.isArray(v)) {
    throw new Error('unsupported type')
  }
  return v.map((v) => createStreamMessage(v, consuming))
}
