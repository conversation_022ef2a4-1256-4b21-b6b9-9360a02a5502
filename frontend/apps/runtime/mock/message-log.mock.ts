import { defineMock } from 'vite-plugin-mock-dev-server'

export default defineMock({
  url: '/api/Agent/GetChatMessageLog',
  //参数  msgId: string,
  method: 'POST',
  delay: 0,
  body: {
    data: {
      duration: 1.446,
      startTime: '2024-06-07T03:23:07',
      endTime: '2024-06-07T03:23:09',
      status: 'success',
      message: null,
      promptTokens: 236,
      completeTokens: 103,
      tokens: 339,
      inputs:
        '{"input":"请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。"}',
      outputs: '{}',
      chatMessageNodeLogDtos: [
        {
          chatMessageNodeLogGUID: '632285f1-4ad6-4566-9eab-e4fae9413729',
          chatGUID: 'b39e2410-0b04-4b3b-96d2-c7693cba4386',
          nodeGUID: '6ccd6001-953e-42ec-b8dd-50c367063386',
          batchGUID: '23248fec-3934-4d69-aac5-6faccc3b762e',
          name: '开始',
          description: null,
          index: 0,
          duration: 1.4350000000000001,
          startTimeMillisecond: 1387000,
          endTimeMillisecond: 1389000,
          startTime: '2024-06-07T03:23:07',
          endTime: '2024-06-07T03:23:09',
          status: 'success',
          message: null,
          promptTokens: 236,
          completeTokens: 103,
          tokens: 339,
          config:
            '{"OutputType":"variables","Inputs":[{"Code":"input","Name":"用户输入","Type":"string","Required":false,"Description":null,"DefaultValue":null,"LiteralCode":"","LiteralValue":"请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。","Value":{"Type":"ref","Content":"System_Input"}}],"Outputs":[{"Code":"AssociateTaskName","Name":"督办标题","Type":"string","Required":true,"Description":"用户输入督办概要","DefaultValue":null,"LiteralCode":"","LiteralValue":"","Value":null},{"Code":"Remark","Name":"事项说明","Type":"string","Required":true,"Description":"督办详细工作内容","DefaultValue":null,"LiteralCode":"","LiteralValue":"","Value":null},{"Code":"DutyUserName","Name":"主责人","Type":"string","Required":true,"Description":"督办对应工作的负责人","DefaultValue":null,"LiteralCode":"","LiteralValue":"","Value":null},{"Code":"ExpectedFinishDate","Name":"要求完成日期","Type":"date","Required":true,"Description":"督办要求完成日期","DefaultValue":null,"LiteralCode":"","LiteralValue":"","Value":null}]}',
          inputs:
            '{"input":"请谢国帅在2023年8月5号完成滨江东路房屋征拆量测算的工作，工作要求：根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。"}',
          outputs:
            '{"AssociateTaskName":"滨江东路房屋征拆量测算","Remark":"根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。","DutyUserName":"谢国帅","ExpectedFinishDate":"2023-08-05"}',
        },
        {
          chatMessageNodeLogGUID: '420ac6e3-600d-434b-a79e-21b0b3e82374',
          chatGUID: 'b39e2410-0b04-4b3b-96d2-c7693cba4386',
          nodeGUID: '2c4071ed-9b2d-46e9-a7bd-d25944dc6cd1',
          batchGUID: '23248fec-3934-4d69-aac5-6faccc3b762e',
          name: '交互卡片',
          description: null,
          index: 1,
          duration: 0.010999999999999999,
          startTimeMillisecond: 1389000,
          endTimeMillisecond: 1389000,
          startTime: '2024-06-07T03:23:09',
          endTime: '2024-06-07T03:23:09',
          status: 'success',
          message: null,
          promptTokens: 0,
          completeTokens: 0,
          tokens: 0,
          config:
            '{"OutputType":null,"Inputs":[{"Code":"AssociateTaskName0","Name":"督办标题","Type":"string","Required":true,"Description":null,"DefaultValue":null,"LiteralCode":"NodeOutput_start0_AssociateTaskName","LiteralValue":"滨江东路房屋征拆量测算","Value":{"Type":"ref","Content":"NodeOutput_start0_AssociateTaskName"}},{"Code":"DutyUserName0","Name":"主责人","Type":"string","Required":true,"Description":null,"DefaultValue":null,"LiteralCode":"NodeOutput_start0_DutyUserName","LiteralValue":"谢国帅","Value":{"Type":"ref","Content":"NodeOutput_start0_DutyUserName"}},{"Code":"Remark0","Name":"事项说明","Type":"string","Required":true,"Description":null,"DefaultValue":null,"LiteralCode":"NodeOutput_start0_Remark","LiteralValue":"根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。","Value":{"Type":"ref","Content":"NodeOutput_start0_Remark"}},{"Code":"ExpectedFinishDate0","Name":"要求完成日期","Type":"date","Required":true,"Description":null,"DefaultValue":null,"LiteralCode":"NodeOutput_start0_ExpectedFinishDate","LiteralValue":"2023-08-05","Value":{"Type":"ref","Content":"NodeOutput_start0_ExpectedFinishDate"}}],"Outputs":[]}',
          inputs:
            '{"AssociateTaskName0":"滨江东路房屋征拆量测算","DutyUserName0":"谢国帅","Remark0":"根据会议要求，对接征推部重新测算房屋征拆量，并重新出具效果图。","ExpectedFinishDate0":"2023-08-05"}',
          outputs: '{}',
        },
      ],
    },
    success: true,
    message: null,
  },
})
