import { MessageCardData, TextCardData } from '@acme/core'
import { fixedForm, form, form2, form2Steam } from './form'
import { createDataMessage, createProcessMessage, createRecoveryMessage, createReplaceMessage, createStreamMessage, createStreamMessages, createTextMessage, createTextMessages } from './types'

const text = `盖闻天地之数，有十二万九千六百岁为一元。
将一元分为十二会，乃子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥之十二支也。每会该一万八百岁。`

// `且就一日而论：子时得阳气，而丑则鸡鸣；寅不通光，而卯则日出；辰时食后，而巳则挨排；日午天中，而未则西蹉；申时晡，而日落酉，戌黄昏，而入定亥。
// 譬于大数，若到戌会之终，则天地昏曚而万物否矣。
// 再去五千四百岁，交亥会之初，则当黑暗，而两间人物俱无矣，故曰混沌。又五千四百岁，亥会将终，贞下起元，近子之会，而复逐渐开明。
// 邵康节曰：“冬至子之半，天心无改移。一阳初动处，万物未生时。”
// 到此，天始有根。
// 再五千四百岁，正当子会，轻清上腾，有日，有月，有星，有辰。
// 日、月、星、辰，谓之四象。
// 故曰，天开于子。
// 又经五千四百岁，子会将终，近丑之会，而逐渐坚实。
// 《易》曰：“大哉乾元！至哉坤元！万物资生，乃顺承天。”
// 至此，地始凝结。
// 再五千四百岁，正当丑会，重浊下凝，有水，有火，有山，有石，有土。
// 水、火、山、石、土，谓之五形。故曰，地辟于丑。
// 又经五千四百岁，丑会终而寅会之初，发生万物。
// 历曰：“天气下降，地气上升；天地交合，群物皆生。”
// 至此，天清地爽，阴阳交合。
// 再五千四百岁，正当寅会，生人，生兽，生禽，正谓天地人，三才定位。故曰，人生于寅。`

export const createText = () => {
  return createTextMessages(text)
}

// 图片未正常替换
export const createImageText = () => {
  return [
    createProcessMessage('知识库'),
    createReplaceMessage(
      '[{"key":"![图片1](图片1)","value":"![图片1](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d701-44d7-fd89-3b7740775fa5&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kNzAxLTQ0ZDctZmQ4OS0zYjc3NDA3NzVmYTUiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiJiZmRmYWZlMy1mMTM4LTQ2NzQtYTdmMi0yYzVlYjM2NzY0OGEifQ.UM_xD3dCYnNPV1yMFFo7LGs6XvTReh3Ej_Gz738QFMM&tenantCode=ompdmbsdslqcp)"},{"key":"![图片2](图片2)","value":"![图片2](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d701-fe9b-ff72-fc5b8349fcf5&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kNzAxLWZlOWItZmY3Mi1mYzViODM0OWZjZjUiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiJkYjMwNzI3YS1mYTA1LTRmYmMtYjQzZi1lMDBiZTYxZDM0ZjcifQ.wdNwWagyaHiYhWdESwy3-BLlL6AvraotDdhaG1Mo3AQ&tenantCode=ompdmbsdslqcp)"},{"key":"![图片3](图片3)","value":"![图片3](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d700-fd84-fb1a-bb23eb1e3ad5&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kNzAwLWZkODQtZmIxYS1iYjIzZWIxZTNhZDUiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI1OGYxMjBlMS01OGNhLTQ1ZTgtYTI4YS02OTVhNDQ4MDBlMTUifQ.6o9ozJE4SC6jPkfnO-2VujQVJ4LZUS_coK_uS_P-8CU&tenantCode=ompdmbsdslqcp)"},{"key":"![图片4](图片4)","value":"![图片4](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d70b-4389-87bf-cf43666fd746&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kNzBiLTQzODktODdiZi1jZjQzNjY2ZmQ3NDYiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI5YmI3MjA4NC01MmM0LTRjMmMtYWNmMy1mODY4N2YwNmU4MTIifQ.KmEraKa8zBLgvkDpumAlulOsVwL_yN5V0UIGsP7dW80&tenantCode=ompdmbsdslqcp)"},{"key":"![图片5](图片5)","value":"![图片5](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d7b3-f86e-f9da-6a12bf6b3577&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kN2IzLWY4NmUtZjlkYS02YTEyYmY2YjM1NzciLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI0YWE4YzJkOC0zZDNhLTQ4ZDUtODRkOC01MjUxZjkzODY0NTYifQ.hXZgcfkdFmRUsQwvPdcy9so555sWiRr3G3OyxFJhTDI&tenantCode=ompdmbsdslqcp)"},{"key":"![图片6](图片6)","value":"![图片6](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d7bb-4010-8307-6546e06661fb&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kN2JiLTQwMTAtODMwNy02NTQ2ZTA2NjYxZmIiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI4OTJiNDUwZS05YzhlLTRjODEtYjc5NS0zNzE5YzQzYjUxN2QifQ.ySsFQFZcbxZKPxFAMEpYETaq9gsTcGk1hDjf6flN8GA&tenantCode=ompdmbsdslqcp)"},{"key":"![图片7](图片7)","value":"![图片7](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d7bb-4281-853f-af95176a1612&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kN2JiLTQyODEtODUzZi1hZjk1MTc2YTE2MTIiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiJiZTg5M2Q3NC1kOWRhLTRhZTUtOTZkZi05Y2Q3MDEwOTE5M2IifQ.66PEHpS06a_PYNGh9rIHoy4EmwGrqsiERY0Pe7M2aoE&tenantCode=ompdmbsdslqcp)"},{"key":"![图片8](图片8)","value":"![图片8](https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-d7bb-4010-84aa-898199838b6b&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1kN2JiLTQwMTAtODRhYS04OTgxOTk4MzhiNmIiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI0NDYxM2M2MC1lMzVmLTRmMDgtOTg3OS00YWY1NTllMmNjZjkifQ.2b6cRmUVNNOZEnph1fBvnrhodsYWOAAXHsKrOmS6Uuk&tenantCode=ompdmbsdslqcp)"}]',
    ),
    createDataMessage(
      '[{"type":"source","data":[{"name":"云ERP-售楼管理系统V5.0_操作手册（下） (3)","size":"23815k","type":"word","url":"https://myerp.v55-standard.mycyjg.com:9443/share/40000101/api/v2/GetFile?documentId=08dd5d48-bfec-fa34-fd98-b52a18a50c33&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIwOGRkNWQ0OC1iZmVjLWZhMzQtZmQ5OC1iNTJhMThhNTBjMzMiLCJUZW5hbnRDb2RlIjoib21wZG1ic2RzbHFjcCIsImV4cCI6MTc1NzE0MTkyNiwiaWF0IjoxNzQxNTg2MzI2LCJqdGkiOiI5YjQ2NjQ5NC03NjhkLTRkN2MtODc0MS0zYTYyOWFlOGIxMzMifQ.FTKTNuQcbXGo6yjqcLKstpxJ3Fa2tx7R8REL8hTkXKQ&tenantCode=ompdmbsdslqcp"}]}]',
    ),
    createProcessMessage('提示词'),
    createTextMessage('Question'),
    createTextMessage('：'),
    createTextMessage('怎么'),
    createTextMessage('新增合同|n|nAnswer'),
    createTextMessage('：|n根据提供的上下'),
    createTextMessage('文信息，关于'),
    createTextMessage('如何新增合同的具体'),
    createTextMessage('步骤如下：|n|n1'),
    createTextMessage('. **选择签约'),
    createTextMessage('状态下需要办理售后服务'),
    createTextMessage('的房间**  |n'),
    createTextMessage('   在【交易'),
    createTextMessage('管理】-【'),
    createTextMessage('签订认购/合同'),
    createTextMessage('】模块中的销'),
    createTextMessage('控图中，'),
    createTextMessage('选定要办理的'),
    createTextMessage('房间；  |n  '),
    createTextMessage(' ![图片5]('),
    createTextMessage('图片5)|n|n2'),
    createTextMessage('. **进入合同'),
    createTextMessage('详情页**  |n'),
    createTextMessage('   选定房间'),
    createTextMessage('后，进入合同'),
    createTextMessage('详情页进行详细'),
    createTextMessage('操作。在此页面'),
    createTextMessage('可以进行以下操作'),
    createTextMessage('：|n|n3. **'),
    createTextMessage('调整合同信息**'),
    createTextMessage('  |n   - **'),
    createTextMessage('付款方式、折扣'),
    createTextMessage('信息、按揭'),
    createTextMessage('金额、按揭'),
    createTextMessage('银行等信息**'),
    createTextMessage(' 的调整。|n  '),
    createTextMessage(' - **附属房产'),
    createTextMessage('**：添加或'),
    createTextMessage('删除附属房产。|n'),
    createTextMessage('   - **权益'),
    createTextMessage('人信息**：'),
    createTextMessage('编辑手机号、通讯'),
    createTextMessage('地址、修改姓名'),
    createTextMessage('（证件信息不可'),
    createTextMessage('改，如需'),
    createTextMessage('变更证件信息，'),
    createTextMessage('需发起权益人'),
    createTextMessage('变更申请）。|n'),
    createTextMessage('   - **付款'),
    createTextMessage('情况**：手工'),
    createTextMessage('在供款明细'),
    createTextMessage('列表中进行〖'),
    createTextMessage('新增〗、〖'),
    createTextMessage('修改〗、〖'),
    createTextMessage('删除〗。如果'),
    createTextMessage('调整供款表'),
    createTextMessage('后导致应收款'),
    createTextMessage('之和与协议'),
    createTextMessage('总价不符，系统'),
    createTextMessage('会在〈付款情况'),
    createTextMessage('〉标签页，'),
    createTextMessage('应收款表格下'),
    createTextMessage('，显示协议总价'),
    createTextMessage('与房款合计'),
    createTextMessage('差额为红色'),
    createTextMessage('，需手工调整'),
    createTextMessage('供款明细表'),
    createTextMessage('，使两者相'),
    createTextMessage('等。|n   |n  '),
    createTextMessage(' ![图片1]('),
    createTextMessage('图片1)|n  '),
    createTextMessage(' ![图片2]('),
    createTextMessage('图片2)|n  '),
    createTextMessage(' ![图片3]('),
    createTextMessage('图片3)|n  '),
    createTextMessage(' ![图片4]('),
    createTextMessage('图片4)|n|n4'),
    createTextMessage('. **生成售后服务'),
    createTextMessage('**  |n   '),
    createTextMessage('签订正式'),
    createTextMessage('合同后，后续'),
    createTextMessage('会有一系列的'),
    createTextMessage('售后服务工作，例如'),
    createTextMessage('办理产权、办理'),
    createTextMessage('入伙。如果'),
    createTextMessage('付款方式为按'),
    createTextMessage('揭，需要办理'),
    createTextMessage('按揭或公积金'),
    createTextMessage('手续。合同保存'),
    createTextMessage('时自动生成销售'),
    createTextMessage('服务，如销售'),
    createTextMessage('服务变动后，'),
    createTextMessage('可直接点击〖'),
    createTextMessage('生成销售服务〗'),
    createTextMessage('自动将销售服务'),
    createTextMessage('传递到售后进程中'),
    createTextMessage('。|n|n   备'),
    createTextMessage('注说明：|n  '),
    createTextMessage(' - 生成的服务'),
    createTextMessage('进程，其承诺'),
    createTextMessage('办理时间、承诺'),
    createTextMessage('完成时间可在业务'),
    createTextMessage('参数中进行设置'),
    createTextMessage('。|n   - '),
    createTextMessage('只有包含按'),
    createTextMessage('揭、公积金的'),
    createTextMessage('付款方式，生成'),
    createTextMessage('的销售服务进程中'),
    createTextMessage('才有“按揭'),
    createTextMessage('贷款服务”和'),
    createTextMessage('“公积金贷款服务'),
    createTextMessage('”。|n   -'),
    createTextMessage(' 在合同管理编辑'),
    createTextMessage('界面的〈售后服务'),
    createTextMessage('〉标签页可以'),
    createTextMessage('查看该客户对应'),
    createTextMessage('需办理哪些后续'),
    createTextMessage('的服务。|n|n总结：|n'),
    createTextMessage('为了新增合同，请'),
    createTextMessage('按照上述步骤操作'),
    createTextMessage('，确保所有相关信息'),
    createTextMessage('填写准确，并根据'),
    createTextMessage('实际情况调整合同细节'),
    createTextMessage('。如有其他问题'),
    createTextMessage('，欢迎继续咨询'),
    createTextMessage('。|n|n![图片5'),
    createTextMessage('](图片5)'),
  ]
}

export const createRevokeText = () => {
  return [
    createProcessMessage('选择器'),
    createProcessMessage('查询FAQ'),
    createProcessMessage('提取是否启用客服'),
    createProcessMessage('提取是否转人工'),
    createProcessMessage('提取转人工页面'),
    createProcessMessage('提取FAQ查询结果'),
    createProcessMessage('是否启用客服'),
    createProcessMessage('是否转人工'),
    createProcessMessage('AI问答'),
    createTextMessage('根据文档'),
    createTextMessage('内容，配置套'),
    createTextMessage('打模板有两种方式'),
    createTextMessage('：|n|n1. 新'),
    createTextMessage('增套打模板'),
    createTextMessage('：|n- 进'),
    createTextMessage('入【项目准备'),
    createTextMessage('】-【套'),
    createTextMessage('打设置】页面'),
    createTextMessage('|n- 选择'),
    createTextMessage('项目点击【新增'),
    createTextMessage('】|n-'),
    createTextMessage('编辑模板名称'),
    createTextMessage('、应用场景等信息'),
    createTextMessage('n- 点'),
    createTextMessage('击【保存】'),
    createTextMessage('按钮完成设置 ['),
    createTextMessage('1102'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项'),
    createRecoveryMessage(''),
    createTextMessage('根据您的'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1)'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印'),
    createTextMessage('|n4)'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。'),
    createTextMessage('[110'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。[1102020'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 [11'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](link-'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 [110201'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](link-3251'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2.'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](link-32519970'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：'),
    createTextMessage('|n- 进入'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link'),
    createTextMessage('-325'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link-3251997'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打'),
    createRecoveryMessage(''),
    createTextMessage('根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link-32519970001'),
    createRecoveryMessage(''),
    createTextMessage('根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2.',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n-',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目',
    ),
    createTextMessage('已有的模板，'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n-',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 [110',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 ',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n!',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目',
    ),
    createTextMessage('点击【引入模板'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](link-3',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](link-32519',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后',
    ),
    createTextMessage('，点击【确定'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](link-325199700',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。[110',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link-',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link-3251',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](',
    ),
    createTextMessage('link-32'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](link-32519970',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](link-325199',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](link-3251997100',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](link',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n!',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](link-325',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-3',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](link-3251997',
    ),
    createTextMessage('1000'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-32519',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-325199710',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-32',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)',
    ),
    createTextMessage('|n|n需要注意的是：|n1'),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-325199',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)|n|n需要注意的是：|n1. 如果打印认购',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](link-3251997100',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)|n|n需要注意的是：|n1. 如果打印认购签约单或单',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据您的问题，我为您介绍两种配置套打模板的方法：|n|n1. 设置本项目下的套打模板：|n您可以进入【项目准备】-【套打设置】页面，选择项目点击【新增】，编辑模板名称、应用场景等信息，点击【保存】按钮即可完成设置。|n|n以下是操作界面示意图：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n|n2. 引入其他项目已有的套打模板：|n如果您想使用其他项目已有的模板，可以通过引入功能实现。操作路径是：进入【项目准备】-【套打设置】页面，选择项目点击【引入模板】，选择要引入的模板后，点击【确定】按钮即可。|n|n以下是引入模板的操作界面：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)|n|n需要注意的是：|n1. 如果打印认购签约单或单据时提示没有',
    ),
    createRecoveryMessage(''),
    createTextMessage(
      '根据文档内容，配置套打模板有两种方式：|n|n1. 新增套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【新增】|n- 编辑模板名称、应用场景等信息|n- 点击【保存】按钮完成设置 |n|n注意事项：|n1) 打印认购签约单或单据时，如果没有套打模板需要先新增模板|n2) 套打的新增和调整需要联系人工客服处理|n3) 设置套打模板后才能进行置业计划、认购书、合同、收据等打印|n4) 需要相应功能权限，如无权限请联系管理员授权 |n|n2. 引入其他项目套打模板：|n- 进入【项目准备】-【套打设置】页面|n- 选择项目点击【引入模板】|n- 选择要引入的模板|n- 点击【确定】按钮完成操作 |n|n相关图片：|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/111_7672877726.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=KvJ2BrLV%2Feri9HP912%2FIu8JYeCk%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_1512987050.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=YFYgCjt6PNZ9bQQoKrDtBY%2BCSyA%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/112_7717058656.png?Expires=1741572708&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=ri%2BG6e90I1o185rHHiGuDVjjL04%3D)|n![](http://doc2bot-bucket-cloud.oss-cn-shanghai.aliyuncs.com/doc2bot/media/1241439/113_7593372602.png?Expires=1741572709&OSSAccessKeyId=LTAI5tS8c7BGUtQGKD4dX7Gk&Signature=r3RfeagRtqrg0g2oZ2CF3ywnKoo%3D)',
    ),
  ]
}

export const createStream = () => {
  return [
    ...createText(),
    // createFormCard(),
    createTextCard(),
    ...createMessageCard(),
  ]
}

export const createFormStream2 = () => {
  return [createDataMessage(form2, 100), ...createStreamMessages(form2Steam)]
}

export const createTodoFormCard = () => {
  return createDataMessage(form, 100)
}

export const createReportCard = () => {
  const data = [
    {
      type: 'source',
      data: [
        {
          name: '1.签约进度分析报表',
          size: '11.4k',
          type: 'preview',
          url: 'https://arco.design/vue/docs/start',
        },
        {
          name: '2.房间销售报表',
          size: '11.4k',
          type: 'preview',
          url: 'https://blog.csdn.net/i042416/article/details/135707674',
        },
        {
          name: '3.产成品销售情况报表',
          size: '11.4k',
          type: 'preview',
          url: 'https://vuejs.org/',
        },
        {
          name: '4.产成品销售情况报表',
          size: '11.4k',
          type: 'preview',
          url: 'https://ant-design.antgroup.com/index-cn',
        },
        {
          name: '5.产成品销售情况报表',
          size: '11.4k',
          type: 'preview',
          url: 'https://arco.design/vue/docs/start',
        },
      ],
    },
  ]

  return [createProcessMessage('选择器'), createProcessMessage('查询FAQ'), createProcessMessage('提取是否启用客服'), createProcessMessage('提取是否转人工'), createProcessMessage('提取转人工页面'), createProcessMessage('提取FAQ查询结果'), createProcessMessage('是否启用客服'), createProcessMessage('是否转人工'), createProcessMessage('AI问答'), createDataMessage(data, 100)]
}

export const createFormCard = () => {
  const k = 'NodeOutput_imageAnalysis0_content'
  return [
    createDataMessage(form, 100),

    createStreamMessage({ NodeOutput_imageAnalysis0_name: null }, 3000),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '费' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '用' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '报销' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '-' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '商业' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '板块绿' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '地' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '中' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '心项' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '目—' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '业务' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '招待费' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '报销' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_name: '6000元' }),

    createStreamMessage({ NodeOutput_imageAnalysis0_decribe: null }),
    createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '1442' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '443' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '5.8' }),
    createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '7' }),
    // createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '洗脚2000 元，' }),
    // createStreamMessage({ NodeOutput_imageAnalysis0_decribe: 'KTV2000 ' }),
    // createStreamMessage({ NodeOutput_imageAnalysis0_decribe: '元' }),

    createStreamMessage({ [k]: { index: 0, value: { type: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金' } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: '2024' } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: '2024-', amount: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: '2024-0', amount: 62226 } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: '2024-03-18', amount: 622265604 } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', date: '2024-03-18', amount: 6222656040006 } } }),
    createStreamMessage('{"NodeOutput_imageAnalysis0_content":{"index":0,"value":{"type":"现金","date":"2024-03-18","amount":6222656040006356}}}'),
    createStreamMessage('{"NodeOutput_imageAnalysis0_content":{"index":0,"value":{"type":"现金","date":"2024-03-18","amount":6222656040006356421}}}'),

    createStreamMessage({ [k]: { index: 1, value: { type: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账' } } }, 1000),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: '2024' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: '2024' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: '2024-0' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: '2024-0' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', date: '2024-03-1' } } }, 2000), // 卡顿了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款', date: '2024-03-18' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款', date: '2024-03-18' } } }, 1000), // 又卡了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 1500 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 15000 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 15000123.23 } } }, 2000), // 卡顿了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 15000123.2312333 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 15000123.2312333 } } }, 1000), // 又卡了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', date: '2024-03-18', amount: 15000123.2312333 } } }),
  ]
}

export const createFixedFormCard = () => {
  return [createDataMessage(fixedForm, 100)]
}

// { code: 'amount', name: '提示词_0-定金金额', type: 'number', required: false,  literalCode: 'NodeOutput_promptTemplate0_amount', literalValue: '5000', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_amount', syncMessage: null }, schema: [] },

export const createMessageCard = () => {
  const data = [
    {
      type: 'message',
      code: 'message0',
      data: {
        inputs: [
          {
            code: 'author0',
            name: '作者',
            type: 'string',
            required: false,
            value: { type: 'ref', content: 'NodeOutput_promptTemplate0_author' },
          },
          {
            code: 'style0',
            name: '风格',
            type: 'string',
            required: false,
            value: { type: 'ref', content: 'NodeOutput_promptTemplate0_style' },
          },
          {
            code: 'content0',
            name: '内容',
            type: 'string',
            required: false,
            value: { type: 'ref', content: 'NodeOutput_promptTemplate0_content' },
          },
        ],
        content: `**卡片一：流式输出**

作者：{{$author0}}
风格：{{$style0}}
内容：{{$content0}}
时间：2024-10-16
用户输入：写一首七言律诗`,
      } as MessageCardData,
    },
  ]
  const k = 'NodeOutput_promptTemplate0_arry'
  return [
    // 卡片布局
    createDataMessage(data, 100),
    // 卡片数据

    createStreamMessage({ NodeOutput_promptTemplate0_author: '无名氏' }),
    createStreamMessage({ NodeOutput_promptTemplate0_style: '七言律诗' }),
    createStreamMessage({ NodeOutput_promptTemplate0_content: '秋风瑟' }),
    createStreamMessage({ NodeOutput_promptTemplate0_content: '瑟卷黄沙' }),
    createStreamMessage({ NodeOutput_promptTemplate0_style: text }),

    createStreamMessage({ NodeOutput_promptTemplate0_code: null }),
    createStreamMessage({ NodeOutput_promptTemplate0_code: '2310' }),
    createStreamMessage({ NodeOutput_promptTemplate0_code: '2310' }),
    createStreamMessage({ NodeOutput_promptTemplate0_code: '0500' }),
    createStreamMessage({ NodeOutput_promptTemplate0_code: '1' }),

    createStreamMessage({ [k]: { index: 0, value: { type: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金' } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', name: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', name: '定金' } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', name: '定金', amount: null } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', name: '定金', amount: 500 } } }),
    createStreamMessage({ [k]: { index: 0, value: { type: '现金', name: '定金', amount: 5000 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: '首期' } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: '首期', amount: null } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: '首期定金', amount: 1500 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: '首期定金付款', amount: 15000 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账', name: '首期定金付款', amount: 15000123.23 } } }, 2000), // 卡顿了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款', name: '首期定金付款', amount: 15000123.2312333 } } }),
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款', name: '首期定金付款', amount: 15000123.2312333 } } }, 1000), // 又卡了
    createStreamMessage({ [k]: { index: 1, value: { type: '转账付款贷款首付', name: '首期定金付款', amount: 15000123.2312333 } } }),

    // ...text.split('').map((v, i) => {
    //   return createStreamMessage(
    //     { NodeOutput_promptTemplate0_style: v },
    //     100,
    //   )
    // }),
  ]
}

export const createTextCard = () => {
  const data = [
    {
      type: 'text',
      data: {
        inputs: [
          {
            code: 'System_Input',
            name: 's',
            type: 'string',
            required: false,
            value: {
              type: 'ref',
              content: 'System_Input',
            },
          },
        ],
        content: '{{$System_Input}}这是自己写的 ',
        outputs: [
          {
            code: 'input0',
            name: 's',
            type: 'string',
            required: false,
            literalCode: 'System_Input',
            literalValue: '2',
            value: { type: 'ref', content: 'System_Input' },
          },
        ],
      } as TextCardData,
    },
  ]

  return [createDataMessage(data, 100), createStreamMessage({ System_Input: '根据文档识别出以下变更变更场景，您可以执行：\n\n:::actions\n[权益人变更](gpt://card/action?$name=card-confirm&modifyApplyTypeName=&#26435;益人变更)\n[退房变更](gpt://card/action?$name=card-confirm&modifyApplyTypeName=&#36864;房变更)\n' })]
}

// 创建嵌入卡片消息
export const createPageCardWithIframe = () => {
  return createDataMessage([
    {
      type: 'card',
      data: {
        id: '08dc64ca-b4b8-4c56-8225-302339e29baf',
        props: [
          // { name: 'mount', value: 'chat' }, // 聊天区域
          // { name: 'mount', value: 'page' }, // 页面区域
          { name: 'mount', value: 'message' }, // 消息区域
          { name: 'visible', value: true },
          {
            name: 'url',
            value: 'https://vite.dev',
          },
        ],
      },
    },
  ])
}

const createPageCardWithWidgetConfig = () => {
  return createDataMessage([
    {
      type: 'card',
      next: '95ecb6c8-9d4d-416e-86e8-0854d7f50b97',
      data: {
        id: '08dc64ca-b4b8-4c56-8225-302339e29baf',
        props: [
          { name: 'mount', value: 'message' },
          { name: 'url', value: 'http://localhost:11001/src/main.ts' },
          // { name: 'url', value: 'http://localhost:5174/gptbuilder/assistant/dist/index.js' },
          { name: 'visible', value: true },
          { name: 'actions', value: [{ code: 'card-confirm', name: '' }] },
        ],
        inputs: [{ code: null, name: null, type: null, required: false, description: null, paramType: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'literal', content: 'http://localhost:11001/src/main.ts', syncMessage: null }, schema: null }],
        outputs: [
          { code: 'chatGUID', name: '提示词_0-会话 GUID', type: 'string', required: false, description: null, paramType: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_chatGUID', syncMessage: null }, schema: null },
          { code: 'skillGUID', name: '提示词_0-技能 GUID', type: 'string', required: false, description: null, paramType: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_skillGUID', syncMessage: null }, schema: null },
          { code: 'arguments', name: '提示词_0-参数', type: 'array<object>', required: false, description: null, paramType: null, defaultValue: null, literalCode: '', literalValue: '', value: { type: 'ref', content: 'NodeOutput_promptTemplate0_arguments', syncMessage: null }, schema: null },
        ],
        nodeCode: 'card0',
      },
    },
  ])
}

export const createPageCardWithWidget = () => {
  return [
    createPageCardWithWidgetConfig(),

    createStreamMessage({ NodeOutput_imageAnalysis0_name: null }, 3000),
    createStreamMessage({ NodeOutput_promptTemplate0_chatGUID: '基' }),
    createStreamMessage({ NodeOutput_promptTemplate0_chatGUID: '本' }),
    createStreamMessage({ NodeOutput_promptTemplate0_chatGUID: '信息' }),
    createStreamMessage({ NodeOutput_promptTemplate0_skillGUID: '客户' }),
    createStreamMessage({ NodeOutput_promptTemplate0_skillGUID: '信' }),
    createStreamMessage({ NodeOutput_promptTemplate0_skillGUID: '息' }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: null } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: '协议' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: '协议编号' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: '协议编号', value: null } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: '协议编号', value: 'Agreement' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 0, value: { key: '协议编号', value: 'AgreementNo' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 1, value: {} } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 1, value: { key: '订单类型' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 1, value: { key: '订单类型' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 1, value: { key: '订单类型', value: null } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 1, value: { key: '订单类型', value: 'OrderType' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: {} } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: { key: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: { key: '认购日期' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: { key: '认购日期', value: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: { key: '认购日期', value: 'QS' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 2, value: { key: '认购日期', value: 'QSDate' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: {} } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: { key: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: { key: '审核日期' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: { key: '审核日期', value: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: { key: '审核日期', value: 'Au' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 3, value: { key: '审核日期', value: 'AuditDate' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: {} } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: { key: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: { key: '审核状态' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: { key: '审核状态', value: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: { key: '审核状态', value: 'Audit' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 4, value: { key: '审核状态', value: 'AuditStatus' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: {} } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: { key: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: { key: '审核人姓名' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: { key: '审核人姓名', value: '' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: { key: '审核人姓名', value: 'Audit' } } }),
    createStreamMessage({ NodeOutput_promptTemplate0_arguments: { index: 5, value: { key: '审核人姓名', value: 'AuditName' } } }),
  ]
}

// 创建颜色消息
export const createColors = () => {
  return createTextMessages(`
[百度](http://www.baidu.com)
|n# 测试颜色自定义{style="color:#D24BD2;"}
|n# 测试颜色主{.primary}
|n# 测试颜色红{.red}
|n# 测试颜色绿{.green}
|n# 测试颜色黄{.yellow}
|n# 测试颜色灰{.gray}
|n[上传](gpt://upload)
|n[推荐问](gpt://chat?message=你好你好你好){.red}|n`)
}

// 创建代码消息
export const createCodes = () => {
  return createTextMessages(`\`\`\`javascript
function() {|n    const a = 1;}`)
}
// 创建智能检查
export const createPlanCard = () => {
  return createDataMessage(
    [
      {
        type: 'plan',
        next: '08dc64ca-b4b8-4c56-8225-302339e29bc5',
        data: {
          id: '08dc64ca-b4b8-4c56-8225-302339e29baf',
        },
      },
    ],
    1000,
  )
}

export const createProcess = () => {
  return [createProcessMessage('正在解析语义', 5e2), createProcessMessage('正在匹配知识库', 5e2), createProcessMessage('正在帮您总结数据', 5e2)]
}

export const createImage = () => {
  return [
    createReplaceMessage([
      {
        key: '![图片1](图片1)',
        value:
          '![图片1](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=9436a2e6-9cc5-44a1-b9b0-79457dc7ff1f&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI5NDM2YTJlNi05Y2M1LTQ0YTEtYjliMC03OTQ1N2RjN2ZmMWYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6IjdiN2Q1NjQ3LTBhYmItNDAwNy05NDk3LTM5ZGZhZGIwMGYwMyJ9.SUyYIVTTAwzSd14MTHLwWoLwE3Y2HPoZfCfjpNv2yME)',
      },
      {
        key: '![图片2](图片2)',
        value:
          '![图片2](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=3d81a70a-fcfd-4b91-a646-9a26a92cccdf&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIzZDgxYTcwYS1mY2ZkLTRiOTEtYTY0Ni05YTI2YTkyY2NjZGYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6IjU1ZTg2MWMyLWNlY2YtNDZhZC1iNDQ3LTU2YTVjZmMwMDM1NSJ9.CRaHjrLoewx9avdbLrYdnb8Wkdg1gYjeiD-cjeMmV7E)',
      },
      {
        key: '![图片3](图片3)',
        value:
          '![图片3](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=fd7dee07-a28e-44d2-a5e7-e53019bc7fc0&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJmZDdkZWUwNy1hMjhlLTQ0ZDItYTVlNy1lNTMwMTliYzdmYzAiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6IjEzYzllOTFhLWU2NGItNDRlMC1iMzhjLWEzYjZkYTE0ZGViNiJ9.xgdusFNxFmjjC91l94UJiwRv1a2WDD0mzXreTNXh8f0)',
      },
      {
        key: '![图片4](图片4)',
        value:
          '![图片4](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=d10faef2-0f2c-484c-bbb8-2c8b9b8ec0cd&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJkMTBmYWVmMi0wZjJjLTQ4NGMtYmJiOC0yYzhiOWI4ZWMwY2QiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6ImQxMTlkYWIxLWRjMTAtNGY2Zi1hZDhlLTFjZmZjYTVmYzU2MCJ9.LpYS6G_FBNixx8XKBnM9O4CMvE6FUKnhzWo_7hryx_g)',
      },
      {
        key: '![图片5](图片5)',
        value:
          '![图片5](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=538a0a54-dbec-42b5-8214-2dc76931c43d&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI1MzhhMGE1NC1kYmVjLTQyYjUtODIxNC0yZGM3NjkzMWM0M2QiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6Ijg4YTQ1YTQxLTZhMGUtNGM0My1hMGY5LTliYWIwY2E2MTkzOCJ9.Yhg1m2l4HYPUTABjkAjeWRTgAXFAi_-PJ29Y91FittU)',
      },
      {
        key: '![图片6](图片6)',
        value:
          '![图片6](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=36a0a38d-6c38-4fe1-a909-4aacd4615225&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIzNmEwYTM4ZC02YzM4LTRmZTEtYTkwOS00YWFjZDQ2MTUyMjUiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk2MTM5LCJpYXQiOjE3MzE3NDA1MzksImp0aSI6ImZlYTRiYmMxLTNkZjMtNDM5OC05NTI4LTI3NTYyNjc1NmI3MCJ9.DA6wpT7wdQiGtlIvbAe32VNjaDsuwy2cEIXt4WABlY4)',
      },
    ]),
    ...createTextMessages(`
根据您的
查询
，
这里是关于已审批
待执行的变更
的操作指南：|n|n###
  查询已审批待
执行的变更|n|n
- **入口**
：进入【交易
管理】-【
提醒催办】
- 【变更代办
】。|n  -
  *![图片1](图片1)*
|n  |n####
操作步骤：|n1
. **查看变更
详情**：点击
您想要了解的
房间链接，可以
详细查看该次
变更的具体内容。|n
    - *![图片2](图片2)*|n2. **沟通及
跟进记录**：
在线下与客户
完成必要的沟通之后
，在系统中相应
位置记录沟通的结果和后续的跟进
情况。|n   -
  *![图片3](图片3)*
|n   |n另外还
提到了关于定价
管理和付款方式方案
修订的信息，如果您
也感兴趣的话：|n|n
- 若要对某个
付款方式方案进行
调整，请通过以下
路径操作：【
定价管理】-
【付款方式】
，找到对应方案
后点击“修订
”，即可对该方案
做出修改并保存
提交审批。|n
  - *![图片4](图片4)*|n  -
  完成编辑
后记得点击保存
，并发起审批流程
。|n    - *
![图片5](图片5)*|n
    |n若在过程中
决定不继续当前
操作，可以选择点击
【取消】按钮
来关闭正在编辑
的页面。|n|n此外
，关于货值
相关数据获取说明
如下：|n-
版本基本信息按照
提供的模板显示。|n
- 货
值明细包括但不限于
房源套数、车位数量、总面积
、平均价格等
关键指标。这些
信息中，面积
和单价需手动
输入，而总
货值则是基于
这些基础数据计算
得出（例如，
对于住宅是用
面积乘以单价
；对于车位，则
是个数乘以
单价）。|n
  - *![图片6](图片6)*|n|n希望以上
信息能够帮助到
您！如果有更
具体的方面需要咨询
，欢迎随时提问
。`),

    createDataMessage([
      {
        type: 'message',
        code: 'card0',
        data: {
          content:
            '\n提示词输出：\n根据您的查询，这里是关于已审批待执行的变更的操作指南：\n\n### 查询已审批待执行的变更\n\n- **入口**：进入【交易管理】-【提醒催办】- 【变更代办】。\n  - *![图片1](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=9436a2e6-9cc5-44a1-b9b0-79457dc7ff1f&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI5NDM2YTJlNi05Y2M1LTQ0YTEtYjliMC03OTQ1N2RjN2ZmMWYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6IjFmNWJlOWNhLWZlYTktNDE4MS1iNjU4LTcyNmY1YzE2OGUxOCJ9.o2AQ4cr9s1ae90gmX45ozTHm_9z7U4zy8rUzUN5dvGI)*\n  \n#### 操作步骤：\n1. **查看变更详情**：点击您想要了解的房间链接，可以详细查看该次变更的具体内容。\n   - *![图片2](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=3d81a70a-fcfd-4b91-a646-9a26a92cccdf&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIzZDgxYTcwYS1mY2ZkLTRiOTEtYTY0Ni05YTI2YTkyY2NjZGYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6ImRiZDViNDRhLTE1YTktNGM5NC1hYTRiLTQ5NDQyZjc1ZDZkMiJ9.YnVdHMX1Rcp3kAyEvvZRUCZr4JLescWtkjNC00gePEE)*\n2. **沟通及跟进记录**：在线下与客户完成必要的沟通之后，在系统中相应位置记录沟通的结果和后续的跟进情况。\n   - *![图片3](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=fd7dee07-a28e-44d2-a5e7-e53019bc7fc0&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJmZDdkZWUwNy1hMjhlLTQ0ZDItYTVlNy1lNTMwMTliYzdmYzAiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6ImJmODRmZTUxLThjZGEtNGVlZi1hZTBlLWQ0YTJhYjczMjhkYyJ9.fcVA_0gCq8uJ5kY-btgXnAfhsGAe9RNKy_jkpQQlCtE)*\n   \n另外还提到了关于定价管理和付款方式方案修订的信息，如果您也感兴趣的话：\n\n- 若要对某个付款方式方案进行调整，请通过以下路径操作：【定价管理】-【付款方式】，找到对应方案后点击“修订”，即可对该方案做出修改并保存提交审批。\n  - *![图片4](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=d10faef2-0f2c-484c-bbb8-2c8b9b8ec0cd&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJkMTBmYWVmMi0wZjJjLTQ4NGMtYmJiOC0yYzhiOWI4ZWMwY2QiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6IjQ5NTBjZjQ0LTM1NWQtNGEyMS1hNTE3LWY2YjQyMTJjYmY4MyJ9.ZHgjUqtcHxoY5rtHokOMUDjnJw9hNds4YLMRXXETDSI)*\n  - 完成编辑后记得点击保存，并发起审批流程。\n    - *![图片5](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=538a0a54-dbec-42b5-8214-2dc76931c43d&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI1MzhhMGE1NC1kYmVjLTQyYjUtODIxNC0yZGM3NjkzMWM0M2QiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6IjM5ZTBlNzhlLWJmYzYtNGQxYS1hNTY2LThjYzFmYmY5MzQxNSJ9.Oz8iKgUNe3X_8Oi2Eb7g15ktebLfCLhOgs_nxBjyz6M)*\n    \n若在过程中决定不继续当前操作，可以选择点击【取消】按钮来关闭正在编辑的页面。\n\n此外，关于货值相关数据获取说明如下：\n- 版本基本信息按照提供的模板显示。\n- 货值明细包括但不限于房源套数、车位数量、总面积、平均价格等关键指标。这些信息中，面积和单价需手动输入，而总货值则是基于这些基础数据计算得出（例如，对于住宅是用面积乘以单价；对于车位，则是个数乘以单价）。\n  - *![图片6](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=36a0a38d-6c38-4fe1-a909-4aacd4615225&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIzNmEwYTM4ZC02YzM4LTRmZTEtYTkwOS00YWFjZDQ2MTUyMjUiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6ImE1ZDc0NzllLTM1OWEtNDVkOC04YzQyLWIxYjZmMmY0NDI4MSJ9.pE0lVnIxO6samIlW8dnfmilsOnl6ojA8X83ZVhfys6Q)*\n\n希望以上信息能够帮助到您！如果有更具体的方面需要咨询，欢迎随时提问。\n\n-- ---------------------------\n\n固定值：1\n\n--------------------------\n\n知识库输出：\n查询已审批待执行的变更\n\n入口：【交易管理】-【提醒催办】- 【变更代办】\n\n*![图片1](图片1)*\n\n操作：\n\n1、点击对应房间，查看变更详情\n![图片2](图片2)\n\n2、线下与客户沟通后，记录对应的跟进情况\n\n![图片3](图片3)\r\n**入口：**【定价管理】-【付款方式】点击方案，点击修订,对方案进行编辑；\n\n![图片4](图片4)\n操作：\n\n对方案进行编辑，点击保存，并发起审批；\n\n![图片5](图片5)\r\n点击【取消】：点击关闭货值详情页。\n\n取数：\n\n1.版本基本信息：信息如原型所示。\n\n2.货值明细：全盘货值（房源套数，车位个数，面积，均价，货值）。\n\n`                     `房源套数，车位个数，面积，均价手动填写\n`                     `货值为面积\\*均价或个数\\*均价（车位）\n\n![图片6](图片6)\n\n',
          inputs: [
            {
              code: 'output0',
              name: '默认输出',
              type: 'string',
              required: false,
              description: null,
              defaultValue: null,
              literalCode: 'NodeOutput_promptTemplate0_output',
              literalValue:
                '根据您的查询，这里是关于已审批待执行的变更的操作指南：\n\n### 查询已审批待执行的变更\n\n- **入口**：进入【交易管理】-【提醒催办】- 【变更代办】。\n  - *![图片1](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=9436a2e6-9cc5-44a1-b9b0-79457dc7ff1f&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiI5NDM2YTJlNi05Y2M1LTQ0YTEtYjliMC03OTQ1N2RjN2ZmMWYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6IjFmNWJlOWNhLWZlYTktNDE4MS1iNjU4LTcyNmY1YzE2OGUxOCJ9.o2AQ4cr9s1ae90gmX45ozTHm_9z7U4zy8rUzUN5dvGI)*\n  \n#### 操作步骤：\n1. **查看变更详情**：点击您想要了解的房间链接，可以详细查看该次变更的具体内容。\n   - *![图片2](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=3d81a70a-fcfd-4b91-a646-9a26a92cccdf&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiIzZDgxYTcwYS1mY2ZkLTRiOTEtYTY0Ni05YTI2YTkyY2NjZGYiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6ImRiZDViNDRhLTE1YTktNGM5NC1hYTRiLTQ5NDQyZjc1ZDZkMiJ9.YnVdHMX1Rcp3kAyEvvZRUCZr4JLescWtkjNC00gePEE)*\n2. **沟通及跟进记录**：在线下与客户完成必要的沟通之后，在系统中相应位置记录沟通的结果和后续的跟进情况。\n   - *![图片3](https://doc.xchzmyha7.mingyuanyun.com:8443/api/v2/GetFile?documentId=fd7dee07-a28e-44d2-a5e7-e53019bc7fc0&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJmZDdkZWUwNy1hMjhlLTQ0ZDItYTVlNy1lNTMwMTliYzdmYzAiLCJUZW5hbnRDb2RlIjoib21wbXlzcWxic3l6IiwiZXhwIjoxNzQ3Mjk1Mzg3LCJpYXQiOjE3MzE3Mzk3ODcsImp0aSI6ImJmODRmZTUxLThjZGEtNGVlZi1hZTBlLWQ0YTJhYjczMjhkYyJ9.fcVA_0gCq8uJ5kY-btgXnAfhsGAe9RNKy_jkpQQlCtE)*\n   \n另外还提到了关于定价管理和付款方式方案修订的信息，如果您也感兴趣的话：\n\n- 若要对某个付款方式方案进行调整，请通过以下路',
            },
          ],
        },
      },
    ]),
  ]
}

export const createBlocks = () => {
  return [
    //
    // ...createMessageCard(),
    // ...createText(),
    // ...createFormCard(),
    ...createTextCard(),
    // ...createMessageCard(),
  ]
}
