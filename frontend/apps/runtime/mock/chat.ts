export const chats1 = [
  {
    batchGUID: 'aec059c5-a07d-4261-87f7-d7e521523395',
    chatGUID: '49f997f9-a8d9-4f82-9018-c2bf55f729fd',
    chatTime: '2024-08-09T17:25:04.000+08:00',
    question: '0:头像好帅\n',
    feedBack: 0,
    num: 0,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3394952f-6259-41d0-98cf-cc58c6481ea8',
        role: 'user',
        content: '0:头像好帅\n',
        nodeGUID: '27eb212b-0d50-4c0f-a8bf-98cf03f5b70f',
        chatGUID: null,
        index: 0,
        batchGUID: 'aec059c5-a07d-4261-87f7-d7e521523395',
        images: [
          {
            chatMessageGUID: null,
            chatMessageFileGUID: '5da52127-d351-43f6-b837-eb18457990c3',
            id: 'c4230c22-5d4e-4871-9233-022b8a649dfe',
            name: 'icon.png',
            downloadUrl:
              'http://mydoc:9070/api/v2/GetFile?documentId=c4230c22-5d4e-4871-9233-022b8a649dfe&mydocToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJBcHBLZXkiOiJkMzFiNDA3NTE4NTM0OWYxODMzYzVhMGM1MjlkYWQ1ZiIsImlzcyI6Im15c29mdEVSUCIsIkRvY3VtZW50SWQiOiJjNDIzMGMyMi01ZDRlLTQ4NzEtOTIzMy0wMjJiOGE2NDlkZmUiLCJUZW5hbnRDb2RlIjoiZ3B0IiwiZXhwIjoxNzM4NzQ3NDk3LCJpYXQiOjE3MjMxOTE4OTcsImp0aSI6IjIwZWY3ZWIwLWU5MTQtNDkzZi05OThjLWQ2Y2FjN2YyMzY5ZiJ9.S6P2Awq9LbB_AwvWv3gBpKi2z4m8lGvqdLeYzj-jv2s',
            size: '209.0k',
            status: 'done',
            type: 'png',
          },
        ],
        documents: null,
      },
    ],
  },
]

// 长聊天记录
export const chats2 = [
  {
    batchGUID: '0c65f474-d02e-4adc-8654-d24dd10e5eb5',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:付款申请发起，提示超累计应付进度款\n',
    feedBack: 0,
    num: 0,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3761fde0-7cba-46dc-8a5a-87b230db3cf3',
        role: 'user',
        content: '0:付款申请发起，提示超累计应付进度款\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 0,
        batchGUID: '0c65f474-d02e-4adc-8654-d24dd10e5eb5',
      },
    ],
  },
  {
    batchGUID: '0e8f5404-de84-4ac4-8959-198e4861fbe4',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:付款申请发起，提示超累计应付进度款\n',
    feedBack: 0,
    num: 1,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ae84283f-6475-4741-bbbe-d8e8a9b61ece',
        role: 'user',
        content: '0:付款申请发起，提示超累计应付进度款\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 1,
        batchGUID: '0e8f5404-de84-4ac4-8959-198e4861fbe4',
      },
    ],
  },
  {
    batchGUID: 'e5b70700-1014-47a8-b4e3-47200dc21ded',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:付款申请发起，提示超累计应付进度款\n',
    feedBack: 1,
    num: 3,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'de621e49-4c8a-43cc-8484-f5fadb82929b',
        role: 'user',
        content: '0:付款申请发起，提示超累计应付进度款\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 2,
        batchGUID: 'e5b70700-1014-47a8-b4e3-47200dc21ded',
      },
      {
        chatMessageGUID: '6180ab22-c2f5-4530-a16a-e4f4a64de678',
        role: 'assistant',
        content:
          '0:<p><span style="font-size: 14px;">发起付款申请，提示超出累计应付进度款，建议您参考以下说明：</span></p><p><span style="font-size: 14px;">原因一：是因为设置了进度款支付比例，付款申请的金额超出了应付进度款支付比例。</span></p><p><span style="font-size: 14px;">需要调整进度款支付比例，或者取消进度款管控后重新发起。</span></p><p><br/></p><p style="white-space: normal;"><span style="font-size: 14px;"><span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">原因二：</span>是因为合同启用了应付进度款。</span></p><p style="white-space: normal;"><span style="font-size: 14px;">1、合同未结算，可以新增进度款，需要将应付进度款上报且审核通过；累计付款申请金额，不能大于应付进度款金额。<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">2、合同已结算或者结算中，两个方法：<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">⑴打回结算单，删除结算单，新增进度款，然后再重新做结算。<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">⑵付款申请的款项类型先选预付款发起。</span></p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 3,
        batchGUID: 'e5b70700-1014-47a8-b4e3-47200dc21ded',
      },
    ],
  },
  {
    batchGUID: '9c54e9a2-6944-4963-931b-58ca0996f43f',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:付款申请发起，提示超累计应付进度款\n',
    feedBack: 0,
    num: 5,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9fc63316-ca67-4467-ae90-cf16584e2d31',
        role: 'user',
        content: '0:付款申请发起，提示超累计应付进度款\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 4,
        batchGUID: '9c54e9a2-6944-4963-931b-58ca0996f43f',
      },
      {
        chatMessageGUID: 'dd362e5d-1eba-4b60-9fce-23dc9a4ea549',
        role: 'assistant',
        content:
          '0:<p><span style="font-size: 14px;">发起付款申请，提示超出累计应付进度款，建议您参考以下说明：</span></p><p><span style="font-size: 14px;">原因一：是因为设置了进度款支付比例，付款申请的金额超出了应付进度款支付比例。</span></p><p><span style="font-size: 14px;">需要调整进度款支付比例，或者取消进度款管控后重新发起。</span></p><p><br/></p><p style="white-space: normal;"><span style="font-size: 14px;"><span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">原因二：</span>是因为合同启用了应付进度款。</span></p><p style="white-space: normal;"><span style="font-size: 14px;">1、合同未结算，可以新增进度款，需要将应付进度款上报且审核通过；累计付款申请金额，不能大于应付进度款金额。<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">2、合同已结算或者结算中，两个方法：<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">⑴打回结算单，删除结算单，新增进度款，然后再重新做结算。<br/></span></p><p style="white-space: normal;"><span style="font-size: 14px;">⑵付款申请的款项类型先选预付款发起。</span></p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 5,
        batchGUID: '9c54e9a2-6944-4963-931b-58ca0996f43f',
      },
    ],
  },
  {
    batchGUID: '2c3fea42-be64-410a-a4a0-7585741ce914',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:设计变更审批单总包单位选错了，走完流程了还能改吗？\n',
    feedBack: 1,
    num: 7,
    chatMessageDtoList: [
      {
        chatMessageGUID: '41d1899c-90f7-403c-9f8a-fe1d87395699',
        role: 'user',
        content: '0:设计变更审批单总包单位选错了，走完流程了还能改吗？\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 6,
        batchGUID: '2c3fea42-be64-410a-a4a0-7585741ce914',
      },
      {
        chatMessageGUID: '6938753b-de8b-46a4-a645-b18bf292b9b6',
        role: 'assistant',
        content:
          '0:<p><span style="text-wrap: nowrap;">修改设计变更，步骤如下：</span></p><p><span style="text-wrap: nowrap;">1、未审核的设计变更，可以直接编辑修改。</span></p><p><span style="text-wrap: nowrap;">2、审核中的设计变更，可以联系审批人打回流程或者发起人作废流程，修改信息后，重新发起。</span></p><p><span style="">3、已审核的设计变更，不能进行修改。</span></p><p><span style="">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3.1如果做错了金额，或者是选错了所属合同，建议联系系统管理员，通过线下模式驳回设计变更，使设计变更为未审核状态，修改后再重新发起审批。</span></p><p><span style="">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3.2如果是要给设计变更补录合同，请通过更多操作-补录合同进行处理。</span></p><p><br/></p><p><span style="">注：设计变更发起审批，提示单据金额（含税）未完全归集，是因为设计变更界面的成本归集处需要填写变更归集金额，直至未归集金额为0&nbsp;，才能发起审批。如下图：</span></p><p style="white-space: normal;"><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1708910810.png" title="image-1708910810.png" alt="image.png"/></p><p style="white-space: normal;"><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1712915556.png" title="image-1712915556.png" alt="image.png"/></p><p><br/></p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 7,
        batchGUID: '2c3fea42-be64-410a-a4a0-7585741ce914',
      },
    ],
  },
  {
    batchGUID: 'a3eabcd1-c2ce-44f9-94de-dbf7b9393278',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:项目需要新增套打配置，如何处理\n',
    feedBack: 0,
    num: 9,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'f0d762ae-dd49-4c73-b56c-1d3bb93285c4',
        role: 'user',
        content: '0:项目需要新增套打配置，如何处理\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 8,
        batchGUID: 'a3eabcd1-c2ce-44f9-94de-dbf7b9393278',
      },
      {
        chatMessageGUID: '87535cb1-9f02-48d9-bd0c-c8779706c5d9',
        role: 'assistant',
        content:
          '0:<p style="white-space: normal;"><span style="font-size: 14px;">您好，提示无打印模板数据，</span>原因是系统中还没设置对应的套打模板或配置有误。如果已经设置，需先查看自己的打印场景是否设置有误，做对应调整。</p><p><span style="font-size: 14px; color: rgb(255, 0, 0);">设置<span style="font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">套打模板</span></span><span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">，步骤如下：</span></p><p>1、需在项目准备--套打设置--查看是否有设置套打模板，如果没有设置需要新增或者引入其他项目的（通用的情况下）；</p><p>2、点击〖新增〗，打开“套打设置”页面，录入模板名称，选择应用场景（认购、签约、收款等场景），选择报表文件等字段后点击“保存”。或者直接引入系统已存在的模板。</p><p><strong>注：</strong></p><p>1、若没有权限可以联系系统管理员，或者引入其他项目模板使用。</p><p>2、若房间已经转签约了，在合同界面上打印就不会显示认购模板了，只会显示签约模板。</p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332862.png" title="image-1718332862.png" alt="image.png"/></p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332879.png" title="image-1718332879.png" alt="image.png"/></p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 9,
        batchGUID: 'a3eabcd1-c2ce-44f9-94de-dbf7b9393278',
      },
    ],
  },
  {
    batchGUID: '267cae96-1648-4ac1-b58f-03137dd4cfb2',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:认购怎么转签约\n',
    feedBack: 1,
    num: 11,
    chatMessageDtoList: [
      {
        chatMessageGUID: '97b4117b-f144-4559-90ae-58820c1cf599',
        role: 'user',
        content: '0:认购怎么转签约\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 10,
        batchGUID: '267cae96-1648-4ac1-b58f-03137dd4cfb2',
      },
      {
        chatMessageGUID: '4702dfb9-28fd-4737-a72f-86cd7aec6d29',
        role: 'assistant',
        content:
          '0:<p>您好，认购转签约，建议您参考以下步骤：</p><p>1、转签约</p><p>在【销售管理】&gt;【交易管理】&gt;【签订认购合同】下找到认购单，选择房间后，点击右侧的&lt;签订合同&gt;按钮，维护信息后保存即可。</p><p><br/>2、转网签</p><p>在<span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">【销售管理】&gt;</span>【交易管理】&gt;【签订认购合同】下找到签约单，选择房间后，点击右侧的&lt;网签&gt;按钮，维护信息后保存即可。</p><p><br/></p><p>注意：认购单不论是已审核状态或者是未审核状态，都可以转签约。</p><p>具体操作您可以参考以下图片：</p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1650615488.png" title="image-1650615488.png" alt="image.png"/></p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1646123441.png" title="image-1646123441.png" alt="image.png"/></p><!--网签--><!--签订合同-->\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 11,
        batchGUID: '267cae96-1648-4ac1-b58f-03137dd4cfb2',
      },
    ],
  },
  {
    batchGUID: '7eb203f4-4f66-42ac-b263-ee205f94f7bc',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:test\n',
    feedBack: 0,
    num: 13,
    chatMessageDtoList: [
      {
        chatMessageGUID: '560d0ac5-a36e-4c20-938b-ae0042638061',
        role: 'user',
        content: '0:test\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 12,
        batchGUID: '7eb203f4-4f66-42ac-b263-ee205f94f7bc',
      },
      {
        chatMessageGUID: '3f85f024-8c36-4ea9-9ce3-72c0060d557d',
        role: 'assistant',
        content:
          '0:对不起，我无法回答您的问题，请您换个方式提问。作为一个AI机器人，我是基于大量知识进行训练的，而这些知识是不断更新和扩充的，随着时间的推移我会变得越来越聪明\n',
        nodeGUID: '6860a03e-31ed-4b65-903a-7c2d14624e1b',
        chatGUID: null,
        index: 13,
        batchGUID: '7eb203f4-4f66-42ac-b263-ee205f94f7bc',
      },
    ],
  },
  {
    batchGUID: '61c4856b-c55a-461e-8a90-be5ed92acfc3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:转人工\n',
    feedBack: 0,
    num: 15,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'a72e76d4-a04d-4ce2-9090-bc26ff75d219',
        role: 'user',
        content: '0:转人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 14,
        batchGUID: '61c4856b-c55a-461e-8a90-be5ed92acfc3',
      },
      {
        chatMessageGUID: '1a55de6e-4c1f-45eb-8e35-7d411a090152',
        role: 'assistant',
        content:
          '0:我可以为您解答部分问题哦，您可以直接输入您的问题，比如：已进行退房审核的房间怎么进行调价？\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 15,
        batchGUID: '61c4856b-c55a-461e-8a90-be5ed92acfc3',
      },
    ],
  },
  {
    batchGUID: '7eeb5b7f-9d0d-4021-9888-34e54ca92cec',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:已进行退房审核的房间怎么进行调价？\n',
    feedBack: 1,
    num: 17,
    chatMessageDtoList: [
      {
        chatMessageGUID: '98838f7c-26be-4c9d-96c3-d35aee511c8b',
        role: 'user',
        content: '0:已进行退房审核的房间怎么进行调价？\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 16,
        batchGUID: '7eeb5b7f-9d0d-4021-9888-34e54ca92cec',
      },
      {
        chatMessageGUID: '0bfdda8a-5f9c-4cb4-91cc-bb108f0fec85',
        role: 'assistant',
        content:
          '0:<p>退房中的房间无法进行价格调整，做完退房审核后，再去进行调价即可。</p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 17,
        batchGUID: '7eeb5b7f-9d0d-4021-9888-34e54ca92cec',
      },
    ],
  },
  {
    batchGUID: '5f33a508-244c-4f13-8e33-1f38a8d03435',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:转人工\n',
    feedBack: 0,
    num: 19,
    chatMessageDtoList: [
      {
        chatMessageGUID: '7c2bb8be-ca02-4c5d-9380-9655f1be0859',
        role: 'user',
        content: '0:转人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 18,
        batchGUID: '5f33a508-244c-4f13-8e33-1f38a8d03435',
      },
      {
        chatMessageGUID: '793f1a36-05d2-46b8-9310-7e7347f3fb85',
        role: 'assistant',
        content:
          '3:[{"type":"card","data":{"id":"08dc64ca-b4b8-4c56-8225-302339e29baf","props":[{"name":"mount","value":"chat"},{"name":"visible","value":true},{"name":"url","value":"https://kfmerge.fdcyun.com/chat?sk=d97cda44fe89de98177f63ee03a4cae9"}]}}]\n',
        nodeGUID: '8ca632a1-ca0f-4bb7-9b6e-c7c961aa23c5',
        chatGUID: null,
        index: 19,
        batchGUID: '5f33a508-244c-4f13-8e33-1f38a8d03435',
      },
    ],
  },
  {
    batchGUID: 'dab0ca40-6be3-4de2-9c90-6007ab21938f',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 20,
    chatMessageDtoList: [
      {
        chatMessageGUID: '5b712db4-5fee-4d6b-95f1-00df63d89cc1',
        role: 'notice',
        content: '您前面有0人正在排队，预计需等待1分钟，',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 20,
        batchGUID: 'dab0ca40-6be3-4de2-9c90-6007ab21938f',
      },
    ],
  },
  {
    batchGUID: 'f9363a43-ddc1-43be-9f63-17cb541c7c6d',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 21,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'd9c40763-9d83-4f67-bcf2-d454f3259441',
        role: 'notice',
        content:
          '尊敬的林一一，您好，【孙晶云】为您服务，请问有什么可以帮到您？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 21,
        batchGUID: 'f9363a43-ddc1-43be-9f63-17cb541c7c6d',
      },
    ],
  },
  {
    batchGUID: '8088bea9-930f-465c-a4a6-4791b5c91e10',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '已进行退房审核的房间怎么进行调价？',
    feedBack: 0,
    num: 22,
    chatMessageDtoList: [
      {
        chatMessageGUID: '2e306574-99ae-4f45-9acc-6fff8821e1a2',
        role: 'user',
        content: '已进行退房审核的房间怎么进行调价？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 22,
        batchGUID: '8088bea9-930f-465c-a4a6-4791b5c91e10',
      },
    ],
  },
  {
    batchGUID: '7215e38f-1e28-428f-b4e6-b070047d2200',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 23,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9c5aa373-3aac-4dff-ac08-dfab3f54613e',
        role: 'assistant',
        content:
          '<h1 class="card-title">进行退房审核的房间怎么进行调价</h1><p>退房中的房间无法进行价格调整，做完退房审核后，再去进行调价即可。</p>',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 23,
        batchGUID: '7215e38f-1e28-428f-b4e6-b070047d2200',
      },
    ],
  },
  {
    batchGUID: '3667c322-1249-479f-8eca-f218b8c6a81a',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '这是图片么',
    feedBack: 0,
    num: 24,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'bb2f178f-09df-4f93-9d74-2f0aaa382a8c',
        role: 'user',
        content: '这是图片么',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 24,
        batchGUID: '3667c322-1249-479f-8eca-f218b8c6a81a',
      },
    ],
  },
  {
    batchGUID: '875e0e33-9320-4e63-b2c1-9f10d8ebc486',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '回复 下文字看看',
    feedBack: 0,
    num: 25,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'fcd15ed0-3c36-4215-b3f5-3129e70a4f3c',
        role: 'user',
        content: '回复 下文字看看',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 25,
        batchGUID: '875e0e33-9320-4e63-b2c1-9f10d8ebc486',
      },
    ],
  },
  {
    batchGUID: '6bb7e058-43e9-41b0-bed8-621857cac2b3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 26,
    chatMessageDtoList: [
      {
        chatMessageGUID: '6d011121-a91e-4d75-82ae-fe6405688425',
        role: 'assistant',
        content: '不是图片\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 26,
        batchGUID: '6bb7e058-43e9-41b0-bed8-621857cac2b3',
      },
    ],
  },
  {
    batchGUID: '93c8e0ba-9b83-4a53-bcf8-75254312ff70',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: ':blush:',
    feedBack: 0,
    num: 27,
    chatMessageDtoList: [
      {
        chatMessageGUID: '729863c2-ae6a-4f0f-9aa9-d32f4b54f3db',
        role: 'user',
        content: ':blush:',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 27,
        batchGUID: '93c8e0ba-9b83-4a53-bcf8-75254312ff70',
      },
    ],
  },
  {
    batchGUID: 'fb6e0c8c-00fa-41d6-ba07-e41089ff890e',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 28,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3f3e0568-5ad4-4d1d-b72b-4729119da536',
        role: 'assistant',
        content: '文字\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 28,
        batchGUID: 'fb6e0c8c-00fa-41d6-ba07-e41089ff890e',
      },
    ],
  },
  {
    batchGUID: 'f1caef5d-0b58-4b9f-a7fc-6df0d1257027',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '好的',
    feedBack: 0,
    num: 29,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'e21e9f20-a4f5-4058-968e-d163cf90989d',
        role: 'user',
        content: '好的',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 29,
        batchGUID: 'f1caef5d-0b58-4b9f-a7fc-6df0d1257027',
      },
    ],
  },
  {
    batchGUID: '96f98e30-c75f-4981-9118-ce288205d4bb',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '[图片]',
    feedBack: 0,
    num: 30,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'b37ce5c7-8a2e-4cf5-9a6f-8f3b34af7bf9',
        role: 'user',
        content: '[图片]',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 30,
        batchGUID: '96f98e30-c75f-4981-9118-ce288205d4bb',
      },
    ],
  },
  {
    batchGUID: '9f6292ec-9d93-4476-8e3e-38b59b9e0bb1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 31,
    chatMessageDtoList: [
      {
        chatMessageGUID: '72320017-9a69-45ef-a2c7-89257d4a1a5c',
        role: 'assistant',
        content: '测试测试\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 31,
        batchGUID: '9f6292ec-9d93-4476-8e3e-38b59b9e0bb1',
      },
    ],
  },
  {
    batchGUID: 'aaf0242b-ef0b-4afd-ade1-c93935686bc2',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '[IE和EDGE浏览器基本设置-1678414710.docx]',
    feedBack: 0,
    num: 32,
    chatMessageDtoList: [
      {
        chatMessageGUID: '35683183-7b61-49ea-9955-baac31935aad',
        role: 'user',
        content: '[IE和EDGE浏览器基本设置-1678414710.docx]',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 32,
        batchGUID: 'aaf0242b-ef0b-4afd-ade1-c93935686bc2',
      },
    ],
  },
  {
    batchGUID: 'a1c0c93e-2fb3-48dc-9fae-26344dd51758',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '要不要远程？',
    feedBack: 0,
    num: 33,
    chatMessageDtoList: [
      {
        chatMessageGUID: '569b93c8-8f5e-4540-8639-913f4da63cf1',
        role: 'user',
        content: '要不要远程？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 33,
        batchGUID: 'a1c0c93e-2fb3-48dc-9fae-26344dd51758',
      },
    ],
  },
  {
    batchGUID: '0c1c7838-fbd1-4d54-8e7f-ca0df17659af',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 34,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ab6d252d-54f5-4779-986c-c2f6a054c3e4',
        role: 'assistant',
        content: '说下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 34,
        batchGUID: '0c1c7838-fbd1-4d54-8e7f-ca0df17659af',
      },
    ],
  },
  {
    batchGUID: 'a5a76208-6acb-43fe-89da-8471bafe5991',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 35,
    chatMessageDtoList: [
      {
        chatMessageGUID: '8b273ee7-f9b2-4630-8404-5c19918ca0e8',
        role: 'assistant',
        content: '试下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 35,
        batchGUID: 'a5a76208-6acb-43fe-89da-8471bafe5991',
      },
    ],
  },
  {
    batchGUID: '5e09ddbd-9185-4efd-8a82-98e4870dc7b9',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question:
      '我的识别码:257752248\n使用向日葵即可对我发起远程协助\n向日葵下载地址:http://url.oray.com/tGJdas/\n',
    feedBack: 0,
    num: 36,
    chatMessageDtoList: [
      {
        chatMessageGUID: '51a1d9bc-d1f6-42b6-8e28-dd9a09fb03de',
        role: 'user',
        content:
          '我的识别码:257752248\n使用向日葵即可对我发起远程协助\n向日葵下载地址:http://url.oray.com/tGJdas/\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 36,
        batchGUID: '5e09ddbd-9185-4efd-8a82-98e4870dc7b9',
      },
    ],
  },
  {
    batchGUID: '1a2f1203-996d-4e98-aeac-041c5827c177',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '是这样玩么',
    feedBack: 0,
    num: 37,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ce1f3c54-1fb7-4b7b-b1ad-8737d337fd04',
        role: 'user',
        content: '是这样玩么',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 37,
        batchGUID: '1a2f1203-996d-4e98-aeac-041c5827c177',
      },
    ],
  },
  {
    batchGUID: 'aa9f2c96-cbb8-47e9-97e7-************',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '257752248',
    feedBack: 0,
    num: 38,
    chatMessageDtoList: [
      {
        chatMessageGUID: '13aadcdd-c9d9-4478-b4a1-754708decfb9',
        role: 'user',
        content: '257752248',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 38,
        batchGUID: 'aa9f2c96-cbb8-47e9-97e7-************',
      },
    ],
  },
  {
    batchGUID: 'd5047caf-4aeb-40bb-bb31-145116e557b4',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 39,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'c73f5c37-340c-4b8d-996c-ae030041eb44',
        role: 'assistant',
        content: '可以\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 39,
        batchGUID: 'd5047caf-4aeb-40bb-bb31-145116e557b4',
      },
    ],
  },
  {
    batchGUID: 'a427a68e-21b4-4686-b0c6-6c2b62ddecd9',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 40,
    chatMessageDtoList: [
      {
        chatMessageGUID: '340eff97-b116-4663-820f-78ad7c74c29c',
        role: 'assistant',
        content: '我这边发起试下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 40,
        batchGUID: 'a427a68e-21b4-4686-b0c6-6c2b62ddecd9',
      },
    ],
  },
  {
    batchGUID: '6ec20768-836d-4058-96d5-58cf3e1998e3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 41,
    chatMessageDtoList: [
      {
        chatMessageGUID: '0abb013f-5c74-4e19-bc93-1c6fd1f584b0',
        role: 'notice',
        content: '对方想要控制您的电脑？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 41,
        batchGUID: '6ec20768-836d-4058-96d5-58cf3e1998e3',
      },
    ],
  },
  {
    batchGUID: '82c17f72-1912-4a6e-9729-8b4c33ac2aa1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 42,
    chatMessageDtoList: [
      {
        chatMessageGUID: '678f227f-2f42-4c76-98af-5cdf01958617',
        role: 'notice',
        content: '您已接受对方请求远程连接您的电脑',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 42,
        batchGUID: '82c17f72-1912-4a6e-9729-8b4c33ac2aa1',
      },
    ],
  },
  {
    batchGUID: 'cea670c1-7f52-4546-9f40-dadfbea6aed3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 43,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'e8bc0aa5-e569-46f7-803a-20297c46f80b',
        role: 'assistant',
        content: '收到没\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 43,
        batchGUID: 'cea670c1-7f52-4546-9f40-dadfbea6aed3',
      },
    ],
  },
  {
    batchGUID: 'f5762e99-fb90-42d6-afff-848817a843c6',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question:
      '![图片](//mysoft-robot.oss-cn-hangzhou.aliyuncs.com/ykfupload/ykf_5bebd46835e0d/20240801/1722483926_53491_image.png)',
    feedBack: 0,
    num: 44,
    chatMessageDtoList: [
      {
        chatMessageGUID: '5d6749c9-321b-4a60-b297-33bfcdec3b26',
        role: 'user',
        content:
          '![图片](//mysoft-robot.oss-cn-hangzhou.aliyuncs.com/ykfupload/ykf_5bebd46835e0d/20240801/1722483926_53491_image.png)',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 44,
        batchGUID: 'f5762e99-fb90-42d6-afff-848817a843c6',
      },
    ],
  },
  {
    batchGUID: 'd9475af7-c543-4841-9bb1-b7f985b0e286',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '是这样么',
    feedBack: 0,
    num: 45,
    chatMessageDtoList: [
      {
        chatMessageGUID: '4c3c71a5-9833-47b4-9d46-d3aec589842b',
        role: 'user',
        content: '是这样么',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 45,
        batchGUID: 'd9475af7-c543-4841-9bb1-b7f985b0e286',
      },
    ],
  },
  {
    batchGUID: 'eb5d0d83-2ed2-4d08-9363-eca785efe117',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 46,
    chatMessageDtoList: [
      {
        chatMessageGUID: '66f5657d-5c8d-49a5-81a9-22bda4ba3e92',
        role: 'assistant',
        content: '没下载过会弹出这个提示\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 46,
        batchGUID: 'eb5d0d83-2ed2-4d08-9363-eca785efe117',
      },
    ],
  },
  {
    batchGUID: '6cae5b50-b25e-47e1-9113-53fdcc263c4c',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '我有的话还要下载？',
    feedBack: 0,
    num: 47,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'dbc51699-63c1-44cb-b517-0ec926a6b58c',
        role: 'user',
        content: '我有的话还要下载？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 47,
        batchGUID: '6cae5b50-b25e-47e1-9113-53fdcc263c4c',
      },
    ],
  },
  {
    batchGUID: '0c745db4-6346-4b57-b708-63f0aa2a12cd',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 48,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9392f446-4516-43dd-86bb-b75459e5a841',
        role: 'assistant',
        content: '这个是专属版本',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 48,
        batchGUID: '0c745db4-6346-4b57-b708-63f0aa2a12cd',
      },
    ],
  },
  {
    batchGUID: '99eaa33b-2e1e-47ae-9a91-46121564ae8e',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 49,
    chatMessageDtoList: [
      {
        chatMessageGUID: '97b9c1e2-a943-4ca5-aa35-079ea46dcf1d',
        role: 'assistant',
        content: '你先下载试下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 49,
        batchGUID: '99eaa33b-2e1e-47ae-9a91-46121564ae8e',
      },
    ],
  },
  {
    batchGUID: '822ae671-a2bf-42d7-8115-ce5ef5739a31',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '我试试，但是这个窗口已经关了',
    feedBack: 0,
    num: 50,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9536e405-f336-45c8-9aeb-ce9deaf27054',
        role: 'user',
        content: '我试试，但是这个窗口已经关了',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 50,
        batchGUID: '822ae671-a2bf-42d7-8115-ce5ef5739a31',
      },
    ],
  },
  {
    batchGUID: '4fccbc88-54ac-41d5-8cd8-6563c93e59d2',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 51,
    chatMessageDtoList: [
      {
        chatMessageGUID: '7f679639-d61e-4f34-8be1-416b184e9b38',
        role: 'assistant',
        content: '我再发一下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 51,
        batchGUID: '4fccbc88-54ac-41d5-8cd8-6563c93e59d2',
      },
    ],
  },
  {
    batchGUID: 'c3730684-136f-4518-b7c6-e32a3e8c7fa4',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 52,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'd267b498-89f9-40e9-9f51-61874f7610c5',
        role: 'notice',
        content: '对方想要控制您的电脑？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 52,
        batchGUID: 'c3730684-136f-4518-b7c6-e32a3e8c7fa4',
      },
    ],
  },
  {
    batchGUID: '363c0cc4-7158-4a4c-9aec-45d0d910bcb3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 53,
    chatMessageDtoList: [
      {
        chatMessageGUID: '937d8ad2-f504-471d-a722-14f26445e2ce',
        role: 'notice',
        content: '您已接受对方请求远程连接您的电脑',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 53,
        batchGUID: '363c0cc4-7158-4a4c-9aec-45d0d910bcb3',
      },
    ],
  },
  {
    batchGUID: '91e50c07-2eff-49c5-96c4-a1ec3462edda',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '好',
    feedBack: 0,
    num: 54,
    chatMessageDtoList: [
      {
        chatMessageGUID: '6ca39d48-483b-4692-a269-d628be745c4e',
        role: 'user',
        content: '好',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 54,
        batchGUID: '91e50c07-2eff-49c5-96c4-a1ec3462edda',
      },
    ],
  },
  {
    batchGUID: '447aa23d-7593-47e4-a6c2-20853a323005',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '图片里点下载，界面就空白了',
    feedBack: 0,
    num: 55,
    chatMessageDtoList: [
      {
        chatMessageGUID: '27ce4197-0bfb-4b47-bc15-86c64eb8756a',
        role: 'user',
        content: '图片里点下载，界面就空白了',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 55,
        batchGUID: '447aa23d-7593-47e4-a6c2-20853a323005',
      },
    ],
  },
  {
    batchGUID: 'b22e85b3-63c0-4756-afe6-d999ea60515b',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 56,
    chatMessageDtoList: [
      {
        chatMessageGUID: '0a844750-f15c-499a-8b5a-472ee95c9923',
        role: 'assistant',
        content: '截图看下\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 56,
        batchGUID: 'b22e85b3-63c0-4756-afe6-d999ea60515b',
      },
    ],
  },
  {
    batchGUID: '4bf1a511-fb4b-4141-acb2-fd19b8d0c9f1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question:
      '我的识别码:198393734\n使用向日葵即可对我发起远程协助\n向日葵下载地址:http://url.oray.com/tGJdas/\n',
    feedBack: 0,
    num: 57,
    chatMessageDtoList: [
      {
        chatMessageGUID: '42023efd-027b-411f-be35-6d9c41a28350',
        role: 'user',
        content:
          '我的识别码:198393734\n使用向日葵即可对我发起远程协助\n向日葵下载地址:http://url.oray.com/tGJdas/\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 57,
        batchGUID: '4bf1a511-fb4b-4141-acb2-fd19b8d0c9f1',
      },
    ],
  },
  {
    batchGUID: 'cd4f4832-e447-46e8-a3dc-edbe373a8149',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question:
      '![图片](//mysoft-robot.oss-cn-hangzhou.aliyuncs.com/ykfupload/ykf_5bebd46835e0d/20240801/1722484182_10624_image.png)',
    feedBack: 0,
    num: 58,
    chatMessageDtoList: [
      {
        chatMessageGUID: '7c42c8a5-dba1-4b9d-9129-e38c363dd38b',
        role: 'user',
        content:
          '![图片](//mysoft-robot.oss-cn-hangzhou.aliyuncs.com/ykfupload/ykf_5bebd46835e0d/20240801/1722484182_10624_image.png)',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 58,
        batchGUID: 'cd4f4832-e447-46e8-a3dc-edbe373a8149',
      },
    ],
  },
  {
    batchGUID: 'a1438ba7-**************-598bd9a674b1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '现在远程上来了么',
    feedBack: 0,
    num: 59,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3d161f32-a604-4f7f-8304-0d5e26faeb82',
        role: 'user',
        content: '现在远程上来了么',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 59,
        batchGUID: 'a1438ba7-**************-598bd9a674b1',
      },
    ],
  },
  {
    batchGUID: 'cfeaa2f7-9a39-45fd-a631-dd81b3ed3e1f',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 60,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3c34d24c-ba2c-4772-8179-b8f7e98dd6c8',
        role: 'notice',
        content:
          '林一一您好，现在咨询量较大，请暂时耐心等待，我会尽快回复您！感谢您的理解！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 60,
        batchGUID: 'cfeaa2f7-9a39-45fd-a631-dd81b3ed3e1f',
      },
    ],
  },
  {
    batchGUID: 'bf181b7a-9e83-4440-b7b7-6177bcaba4d6',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '退出人工',
    feedBack: 0,
    num: 61,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'fbaa44c6-6bf3-4f86-a27d-436517589d46',
        role: 'user',
        content: '退出人工',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 61,
        batchGUID: 'bf181b7a-9e83-4440-b7b7-6177bcaba4d6',
      },
    ],
  },
  {
    batchGUID: '352fea93-f2dd-4b1b-a601-32ff26b3be78',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 62,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'fdc8259c-d007-483a-9086-5dd5c1decd5c',
        role: 'notice',
        content:
          '林一一您好，现在咨询量较大，请暂时耐心等待，我会尽快回复您！感谢您的理解！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 62,
        batchGUID: '352fea93-f2dd-4b1b-a601-32ff26b3be78',
      },
    ],
  },
  {
    batchGUID: 'f273d5ae-9b8d-4130-baaf-d6ca8c0cc4c2',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 63,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'f1c3a943-90de-4522-8bbe-d47e405036c3',
        role: 'notice',
        content:
          '会话已结束，感谢您的垂询，祝您生活愉快。如您在后续使用过程中遇到问题，可随时与我们联系，我们将竭诚为您服务！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 63,
        batchGUID: 'f273d5ae-9b8d-4130-baaf-d6ca8c0cc4c2',
      },
    ],
  },
  {
    batchGUID: '22b7cdb1-857c-44ef-9850-bacc3eb2e032',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:再进入下人工\n',
    feedBack: 0,
    num: 65,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'c1cd6b59-3e56-435c-a33c-c10eae37bdc9',
        role: 'user',
        content: '0:再进入下人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 64,
        batchGUID: '22b7cdb1-857c-44ef-9850-bacc3eb2e032',
      },
      {
        chatMessageGUID: 'fd682696-590f-4f5a-bab1-a1c22c4ea7d1',
        role: 'assistant',
        content:
          '0:对不起，我无法回答您的问题，请您换个方式提问。作为一个AI机器人，我是基于大量知识进行训练的，而这些知识是不断更新和扩充的，随着时间的推移我会变得越来越聪明\n',
        nodeGUID: '6860a03e-31ed-4b65-903a-7c2d14624e1b',
        chatGUID: null,
        index: 65,
        batchGUID: '22b7cdb1-857c-44ef-9850-bacc3eb2e032',
      },
    ],
  },
  {
    batchGUID: '63454419-ddd8-4e81-8733-a1d408e0a29d',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:人工\n',
    feedBack: 0,
    num: 67,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'b3f860a6-9e56-4246-8d2b-947e01b6bf56',
        role: 'user',
        content: '0:人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 66,
        batchGUID: '63454419-ddd8-4e81-8733-a1d408e0a29d',
      },
      {
        chatMessageGUID: 'f931d3ea-de08-4e98-a118-45d3e45576ef',
        role: 'assistant',
        content:
          '0:我可以为您解答部分问题哦，您可以直接输入您的问题，比如：已进行退房审核的房间怎么进行调价？\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 67,
        batchGUID: '63454419-ddd8-4e81-8733-a1d408e0a29d',
      },
    ],
  },
  {
    batchGUID: '8118d30d-bc71-4172-941c-5bb088634386',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:转人工\n',
    feedBack: 0,
    num: 69,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9a582486-f8b3-4554-8a14-969c62fd770a',
        role: 'user',
        content: '0:转人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 68,
        batchGUID: '8118d30d-bc71-4172-941c-5bb088634386',
      },
      {
        chatMessageGUID: 'c204add4-7541-4ca3-99fe-22af2565145b',
        role: 'assistant',
        content:
          '3:[{"type":"card","data":{"id":"08dc64ca-b4b8-4c56-8225-302339e29baf","props":[{"name":"mount","value":"chat"},{"name":"visible","value":true},{"name":"url","value":"https://kfmerge.fdcyun.com/chat?sk=d97cda44fe89de98177f63ee03a4cae9"}]}}]\n',
        nodeGUID: '8ca632a1-ca0f-4bb7-9b6e-c7c961aa23c5',
        chatGUID: null,
        index: 69,
        batchGUID: '8118d30d-bc71-4172-941c-5bb088634386',
      },
    ],
  },
  {
    batchGUID: '9aeb8d9f-d454-4393-b732-34799a710e63',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 70,
    chatMessageDtoList: [
      {
        chatMessageGUID: '60f0d7a1-52fd-40ca-8b18-70e55d85e1a4',
        role: 'notice',
        content: '您前面有0人正在排队，预计需等待1分钟，',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 70,
        batchGUID: '9aeb8d9f-d454-4393-b732-34799a710e63',
      },
    ],
  },
  {
    batchGUID: 'a4fc005c-5428-417d-9571-58c8b8b12bf5',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 71,
    chatMessageDtoList: [
      {
        chatMessageGUID: '04623cbe-bf87-4dc1-9d1d-0a57f977000a',
        role: 'notice',
        content:
          '尊敬的林一一，您好，【孙晶云】为您服务，请问有什么可以帮到您？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 71,
        batchGUID: 'a4fc005c-5428-417d-9571-58c8b8b12bf5',
      },
    ],
  },
  {
    batchGUID: '9ccb1538-dbbf-46e9-aad0-39759bd55956',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 72,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9bd3efd0-a137-484c-a01f-2f2020db7f0f',
        role: 'assistant',
        content: '消息1\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 72,
        batchGUID: '9ccb1538-dbbf-46e9-aad0-39759bd55956',
      },
    ],
  },
  {
    batchGUID: '33db1dfa-685c-48c6-8943-0d7038401442',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 73,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'f12ffbdb-a9b1-45dd-a90c-e01d0f15f9ee',
        role: 'assistant',
        content: '消息2\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 73,
        batchGUID: '33db1dfa-685c-48c6-8943-0d7038401442',
      },
    ],
  },
  {
    batchGUID: '70ede84a-f123-44ad-8073-17e82f0d6552',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 74,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'bb745c5d-caeb-4d66-9d39-87752f3cc6d3',
        role: 'assistant',
        content:
          '您好，认购合同无法编辑（如上传附件、修改界面字段、点击更多操作等），请参考以下步骤排查：\n\n首先，确认编辑路径，通过【交易管理】-【签订认购合同】进入编辑页面。\n\n然后按照以下操作排查原因。\n\n原因一：交易单中存在审批中或者审批后未执行的交易变更\n\n需待变更执行后才能编辑合同，或者在【交易管理】>【销售变更】下作废交易变更。\n\n\n\n原因二：交易单是关闭状态\n\n交易单关闭，该交易单是无法编辑的，建议在激活的交易单上编辑。\n\n\n\n原因三：交易单是审核状态\n\n需在【交易管理】>【销控管理】>[批量审核]>[已审核]页签下，根据房间名称查询已审核的交易单，勾选后，点击左上角的<取消审核>按钮，或者【交易管理】-【签订认购合同】进入编辑页面，【更多操作】-【取消审核】\n\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 74,
        batchGUID: '70ede84a-f123-44ad-8073-17e82f0d6552',
      },
    ],
  },
  {
    batchGUID: '9ca3b15f-71ac-474e-b086-857d039114a8',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 75,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'e2961fa8-536a-45ca-81e0-1024f1262866',
        role: 'assistant',
        content: '[图片]',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 75,
        batchGUID: '9ca3b15f-71ac-474e-b086-857d039114a8',
      },
    ],
  },
  {
    batchGUID: '*************-486a-90a9-820d15b4d96c',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 76,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ed3a53ed-6892-4598-861a-e013d3ae6c3a',
        role: 'notice',
        content:
          '会话已结束，感谢您的垂询，祝您生活愉快。如您在后续使用过程中遇到问题，可随时与我们联系，我们将竭诚为您服务！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 76,
        batchGUID: '*************-486a-90a9-820d15b4d96c',
      },
    ],
  },
  {
    batchGUID: 'd3d338e5-85f2-43a9-a153-cc112daa87d7',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:成本协同\n',
    feedBack: 1,
    num: 78,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'db547217-4483-45e5-8fde-a49f2ea25d6f',
        role: 'user',
        content: '0:成本协同\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 77,
        batchGUID: 'd3d338e5-85f2-43a9-a153-cc112daa87d7',
      },
      {
        chatMessageGUID: '6d5e1200-45e0-4576-887b-b14fcc3b966d',
        role: 'assistant',
        content:
          '0:<p style="white-space: normal;"><strong><span style="color: rgb(255, 0, 0);">合同成本归集有两类使用场景：</span></strong></p><p style="white-space: normal;">1、一类为正常情况：</p><p style="white-space: normal;">客户需要在签订合同过程中，实时进行成本归集，系统为此设置一个参数为“启用合同控制”，这时启用，就需要强控归集成本，不强控归集成本则不启用。</p><p style="white-space: normal;">2、一类为非正常情况：</p><p style="white-space: normal;">用户已归集的合同成本出现错误，例如<span style="color: rgb(255, 0, 0);"><strong>选错合约规划或者科目归集错误</strong></span>，需要调整。</p><p style="white-space: normal;">用户已审核未结算的<strong><span style="color: rgb(255, 0, 0);">合同未选择合约规划</span></strong>，需要添加。</p><p style="white-space: normal;"><strong>注：</strong></p><p style="white-space: normal;">1、当项目级参数“是否启用合同控制”由不启用调整为启用后，需要将合同的合约规划重新归集。</p><p style="white-space: normal;">2、建议严格控制合约规划重新归集的权限分配。</p><p style="white-space: normal;">3、当合约规划金额分摊错误时，可采用合约规划重新归集功能进行重新调整</p><p><br/></p><p><strong><span style="color: rgb(255, 0, 0);">合同进行成本归集，步骤如下：</span></strong></p><p>1、先进入【成本管理】&gt;【成本归集】&gt;[合同归集]页签，通过搜索找到需要成本归集的合同。选中合同，点击[归集]，打开合同成本归集页面。</p><p>2、如果不调整合同的合约规划，可直接调整合同各科目的归集金额；如果需要调整合约规划，可点击[选择合约规划]，重新选择合约规划。<br/>注意：切换价税分离，可选择录入含税金额或不含税金额。</p><p>3、点击[下一步]，进入补充合同重新归集步骤。</p><p>4、可点击[按合同归集比例同步]直接按合同归集比例自动同步所有补充合同的成本归集，对于个别补充合同需要单独调整成本归集的，可点击[归集]按钮。</p><p>5、点击[下一步]，进入分包合同成本归集步骤。在分包合同列表中选中分包合同，点击[归集]按钮，重新归集分包合同成本。</p><p>6、点击[下一步]，进入合同执行明细归集步骤。</p><p>7、可点击[按合同和补充合同归集比例同步]，直接按合同和补充合同归集比例自动同步所有合同执行明细的成本归集，对于个别需要单独调整成本归集的，可点击[归集]按钮。</p><p>8、最后等全部单据归集完成后，点击[保存]即可。</p><p><br/></p><p style="text-wrap: wrap;"><strong>注：</strong></p><p style="text-wrap: wrap;">1、如果要<span style="color: rgb(255, 0, 0);"><strong>清除归集</strong></span>，可以在这里直接点击清除归集。</p><p style="text-wrap: wrap;">2、成本归集系统还没有批量处理的功能，归集或者清除归集需要一个一个的处理。</p><p style="text-wrap: wrap;">3、<span style="color: rgba(0, 0, 0, 0.9); font-family: &quot;Segoe UI VSS (Regular)&quot;, &quot;Segoe UI&quot;, -apple-system, BlinkMacSystemFont, Roboto, &quot;Helvetica Neue&quot;, Helvetica, Ubuntu, Arial, NextIcon, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 13px;">不能多个合同一起归集，可以一个合同下的单据批量归集。</span></p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1715047550.png" title="image-1715047550.png" alt="image.png"/></p><p><br/></p><p><br/></p>\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 78,
        batchGUID: 'd3d338e5-85f2-43a9-a153-cc112daa87d7',
      },
    ],
  },
  {
    batchGUID: '7eafc073-0b6c-4c21-b21b-83164f08f259',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:test\n',
    feedBack: 0,
    num: 80,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'a2411d16-294c-42f7-af00-a1d7d2a88419',
        role: 'user',
        content: '0:test\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 79,
        batchGUID: '7eafc073-0b6c-4c21-b21b-83164f08f259',
      },
      {
        chatMessageGUID: 'd7459ddc-2900-441a-8a42-0afb26b8794d',
        role: 'assistant',
        content:
          '0:对不起，我无法回答您的问题，请您换个方式提问。作为一个AI机器人，我是基于大量知识进行训练的，而这些知识是不断更新和扩充的，随着时间的推移我会变得越来越聪明\n',
        nodeGUID: '6860a03e-31ed-4b65-903a-7c2d14624e1b',
        chatGUID: null,
        index: 80,
        batchGUID: '7eafc073-0b6c-4c21-b21b-83164f08f259',
      },
    ],
  },
  {
    batchGUID: 'ff9d5623-f025-4e67-88db-fb21a831a13b',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:人工\n',
    feedBack: 0,
    num: 82,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'e59096ae-bf0d-4614-a116-ea0f51d47acc',
        role: 'user',
        content: '0:人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 81,
        batchGUID: 'ff9d5623-f025-4e67-88db-fb21a831a13b',
      },
      {
        chatMessageGUID: '456bf8df-7d67-463a-b801-f344953f80eb',
        role: 'assistant',
        content:
          '0:我可以为您解答部分问题哦，您可以直接输入您的问题，比如：已进行退房审核的房间怎么进行调价？\n',
        nodeGUID: '08ec2fe4-2b56-49ef-8f26-42347b2a5f76',
        chatGUID: null,
        index: 82,
        batchGUID: 'ff9d5623-f025-4e67-88db-fb21a831a13b',
      },
    ],
  },
  {
    batchGUID: '2c3685ff-fe59-482b-bdcb-97ec5905c9cd',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:转人工服务\n',
    feedBack: 0,
    num: 84,
    chatMessageDtoList: [
      {
        chatMessageGUID: '0e123088-ae3f-4cf9-9d29-a252ef71a3d5',
        role: 'user',
        content: '0:转人工服务\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 83,
        batchGUID: '2c3685ff-fe59-482b-bdcb-97ec5905c9cd',
      },
      {
        chatMessageGUID: 'fb860b50-ba91-4fe3-84c4-3d55f4041954',
        role: 'assistant',
        content:
          '3:[{"type":"card","data":{"id":"08dc64ca-b4b8-4c56-8225-302339e29baf","props":[{"name":"mount","value":"chat"},{"name":"visible","value":true},{"name":"url","value":"https://kfmerge.fdcyun.com/chat?sk=d97cda44fe89de98177f63ee03a4cae9"}]}}]\n',
        nodeGUID: '8ca632a1-ca0f-4bb7-9b6e-c7c961aa23c5',
        chatGUID: null,
        index: 84,
        batchGUID: '2c3685ff-fe59-482b-bdcb-97ec5905c9cd',
      },
    ],
  },
  {
    batchGUID: '148667f0-4a0d-4048-ac52-6d9ba4951d1e',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 85,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ca7fb882-6368-46df-9102-012baa1a09b2',
        role: 'notice',
        content: '您前面有0人正在排队，预计需等待1分钟，',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 85,
        batchGUID: '148667f0-4a0d-4048-ac52-6d9ba4951d1e',
      },
    ],
  },
  {
    batchGUID: '607b9dd7-9354-465e-8706-51aa070f66e7',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 86,
    chatMessageDtoList: [
      {
        chatMessageGUID: '70cd4bed-585b-47d2-8004-************',
        role: 'notice',
        content:
          '尊敬的林一一，您好，【孙晶云】为您服务，请问有什么可以帮到您？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 86,
        batchGUID: '607b9dd7-9354-465e-8706-51aa070f66e7',
      },
    ],
  },
  {
    batchGUID: '5a5f8b02-793f-4ab3-b9b0-d9b922b196dd',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '验证会话丢失的问题，请回复 ',
    feedBack: 0,
    num: 87,
    chatMessageDtoList: [
      {
        chatMessageGUID: '69687c84-78e2-4904-b2eb-e568f88d1fd6',
        role: 'user',
        content: '验证会话丢失的问题，请回复 ',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 87,
        batchGUID: '5a5f8b02-793f-4ab3-b9b0-d9b922b196dd',
      },
    ],
  },
  {
    batchGUID: '4c68776f-ce85-4bae-9f2a-5638bb9d4ff8',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 88,
    chatMessageDtoList: [
      {
        chatMessageGUID: '3d7f7059-0696-4b97-a802-553ccd856faf',
        role: 'assistant',
        content: '111\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 88,
        batchGUID: '4c68776f-ce85-4bae-9f2a-5638bb9d4ff8',
      },
    ],
  },
  {
    batchGUID: '6cd3324b-23a2-40bd-a513-bb02584eb751',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 89,
    chatMessageDtoList: [
      {
        chatMessageGUID: '8982bc58-96e5-4701-99bf-3f518156efef',
        role: 'assistant',
        content: '222\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 89,
        batchGUID: '6cd3324b-23a2-40bd-a513-bb02584eb751',
      },
    ],
  },
  {
    batchGUID: 'fdb091fd-36c3-49e3-a197-22f4e35f6048',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 90,
    chatMessageDtoList: [
      {
        chatMessageGUID: '72225702-0f41-4d1f-9948-6d05f4cf8324',
        role: 'assistant',
        content:
          '<h1 class="card-title">修改认购书模板</h1><p style="white-space: normal;"><span style="font-size: 14px;">您好，提示无打印模板数据，</span>原因是系统中还没设置对应的套打模板或配置有误。如果已经设置，需先查看自己的打印场景是否设置有误，做对应调整。</p><p><span style="font-size: 14px; color: rgb(255, 0, 0);">设置<span style="font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">套打模板</span></span><span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">，步骤如下：</span></p><p>1、需在项目准备--套打设置--查看是否有设置套打模板，如果没有设置需要新增或者引入其他项目的（通用的情况下）；</p><p>2、点击〖新增〗，打开“套打设置”页面，录入模板名称，选择应用场景（认购、签约、收款等场景），选择报表文件等字段后点击“保存”。或者直接引入系统已存在的模板。</p><p><strong>注：</strong></p><p>1、若没有权限可以联系系统管理员，或者引入其他项目模板使用。</p><p>2、若房间已经转签约了，在合同界面上打印就不会显示认购模板了，只会显示签约模板。</p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332862.png" title="image-1718332862.png" alt="image.png"/></p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332879.png" title="image-1718332879.png" alt="image.png"/></p>',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 90,
        batchGUID: 'fdb091fd-36c3-49e3-a197-22f4e35f6048',
      },
    ],
  },
  {
    batchGUID: '55e67a80-1807-439c-ab8d-f0cab2a79170',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 91,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'ac1865ef-1c10-4773-a4a8-7211e92bd1fe',
        role: 'assistant',
        content: '333\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 91,
        batchGUID: '55e67a80-1807-439c-ab8d-f0cab2a79170',
      },
    ],
  },
  {
    batchGUID: '2e5d2ce9-0aa0-4b28-a7f7-0e3459bb8937',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: null,
    feedBack: 0,
    num: 92,
    chatMessageDtoList: [
      {
        chatMessageGUID: '77d39f5f-af9a-43c9-9905-c522137e9c55',
        role: 'notice',
        content:
          '会话已结束，感谢您的垂询，祝您生活愉快。如您在后续使用过程中遇到问题，可随时与我们联系，我们将竭诚为您服务！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 92,
        batchGUID: '2e5d2ce9-0aa0-4b28-a7f7-0e3459bb8937',
      },
    ],
  },
  {
    batchGUID: '050f0c5e-3371-4ba5-9d45-12d221552584',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:设计变更审批单总包单位选错了，走完流程了还能改吗？\n',
    feedBack: 2,
    num: 94,
    chatMessageDtoList: [
      {
        chatMessageGUID: '6bcc9a2f-d197-4a85-8101-63ca94ac9f6a',
        role: 'user',
        content: '0:设计变更审批单总包单位选错了，走完流程了还能改吗？\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 93,
        batchGUID: '050f0c5e-3371-4ba5-9d45-12d221552584',
      },
      {
        chatMessageGUID: 'a11a2976-a6db-4abc-b7eb-98631e5e20d1',
        role: 'assistant',
        content: '0:无机器人服务\n',
        nodeGUID: '70fa5324-a023-4d3d-a1c6-ba4d45aad36a',
        chatGUID: null,
        index: 94,
        batchGUID: '050f0c5e-3371-4ba5-9d45-12d221552584',
      },
    ],
  },
  {
    batchGUID: '13031756-0a73-45b3-be15-a3103d1567b6',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:22.000+08:00',
    question: '0:转人工\n',
    feedBack: 0,
    num: 96,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'a67c781c-2357-4275-b7fb-a8625da84755',
        role: 'user',
        content: '0:转人工\n',
        nodeGUID: '97b12843-e746-4a5b-892d-d2b345c2d71d',
        chatGUID: null,
        index: 95,
        batchGUID: '13031756-0a73-45b3-be15-a3103d1567b6',
      },
      {
        chatMessageGUID: '89577aaf-c980-445b-b2a0-4df96f16958a',
        role: 'assistant',
        content:
          '3:[{"type":"card","data":{"id":"08dc64ca-b4b8-4c56-8225-302339e29baf","props":[{"name":"mount","value":"chat"},{"name":"visible","value":true},{"name":"url","value":"https://kfmerge.fdcyun.com/chat?sk=d97cda44fe89de98177f63ee03a4cae9"}]}}]\n',
        nodeGUID: 'ca3f7773-c1f7-45d4-b436-82741ca0e749',
        chatGUID: null,
        index: 96,
        batchGUID: '13031756-0a73-45b3-be15-a3103d1567b6',
      },
    ],
  },
  {
    batchGUID: '80badd95-b7e2-4709-8550-e1046772f8f1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:23.000+08:00',
    question: null,
    feedBack: 0,
    num: 97,
    chatMessageDtoList: [
      {
        chatMessageGUID: '5c84b387-6380-4a2f-8086-f17c180a188a',
        role: 'notice',
        content: '您前面有0人正在排队，预计需等待1分钟，',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 97,
        batchGUID: '80badd95-b7e2-4709-8550-e1046772f8f1',
      },
    ],
  },
  {
    batchGUID: '157711e5-9406-43c3-9f13-f5f43a9f07c4',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:28.000+08:00',
    question: null,
    feedBack: 0,
    num: 98,
    chatMessageDtoList: [
      {
        chatMessageGUID: '018fbb38-dcf4-4303-9acb-3870371f4353',
        role: 'notice',
        content:
          '尊敬的林一一，您好，【孙晶云】为您服务，请问有什么可以帮到您？',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 98,
        batchGUID: '157711e5-9406-43c3-9f13-f5f43a9f07c4',
      },
    ],
  },
  {
    batchGUID: 'edef7186-67ac-4754-9ca0-3e6f0098fbc3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:33:45.000+08:00',
    question: '现在是关闭了弱化转人工，是吧',
    feedBack: 0,
    num: 99,
    chatMessageDtoList: [
      {
        chatMessageGUID: '7df0e434-cbd2-46fe-9421-1cab3c7afb54',
        role: 'user',
        content: '现在是关闭了弱化转人工，是吧',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 99,
        batchGUID: 'edef7186-67ac-4754-9ca0-3e6f0098fbc3',
      },
    ],
  },
  {
    batchGUID: 'd56f747d-bb32-476b-b24c-5517d3750a0c',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T15:35:46.000+08:00',
    question: null,
    feedBack: 0,
    num: 100,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'a403665f-b7e6-4b57-8ff3-36fb6c778410',
        role: 'notice',
        content:
          '林一一您好，现在咨询量较大，请暂时耐心等待，我会尽快回复您！感谢您的理解！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 100,
        batchGUID: 'd56f747d-bb32-476b-b24c-5517d3750a0c',
      },
    ],
  },
  {
    batchGUID: '9c143335-f073-4810-ae4f-d37e9c2c1c4e',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:19:30.000+08:00',
    question: '在了',
    feedBack: 0,
    num: 101,
    chatMessageDtoList: [
      {
        chatMessageGUID: '2bb87ecf-8fdd-4035-ac86-98d17c2492f0',
        role: 'user',
        content: '在了',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 101,
        batchGUID: '9c143335-f073-4810-ae4f-d37e9c2c1c4e',
      },
    ],
  },
  {
    batchGUID: '08bf80d3-1de0-4aae-bb86-4f633c2f3c19',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:19:34.000+08:00',
    question: '来重现了',
    feedBack: 0,
    num: 102,
    chatMessageDtoList: [
      {
        chatMessageGUID: '6bfb757c-fc8b-4a62-8868-caf17f37df43',
        role: 'user',
        content: '来重现了',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 102,
        batchGUID: '08bf80d3-1de0-4aae-bb86-4f633c2f3c19',
      },
    ],
  },
  {
    batchGUID: '6c324a1e-e9f5-4232-b9e8-cdb6d5ba1e59',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:19:42.000+08:00',
    question: null,
    feedBack: 0,
    num: 103,
    chatMessageDtoList: [
      {
        chatMessageGUID: '0681b4ed-f399-4ae7-847b-be228a0c5d0d',
        role: 'assistant',
        content: 'lai\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 103,
        batchGUID: '6c324a1e-e9f5-4232-b9e8-cdb6d5ba1e59',
      },
    ],
  },
  {
    batchGUID: '2d428d64-6809-423f-98f7-500f6c48322a',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:19:45.000+08:00',
    question: null,
    feedBack: 0,
    num: 104,
    chatMessageDtoList: [
      {
        chatMessageGUID: '9ebd3867-6b2c-4906-8f9b-790793faa0b6',
        role: 'assistant',
        content: '1111\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 104,
        batchGUID: '2d428d64-6809-423f-98f7-500f6c48322a',
      },
    ],
  },
  {
    batchGUID: '86a8a874-8e5c-4178-9998-15a4bfe125d1',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:20:08.000+08:00',
    question: null,
    feedBack: 0,
    num: 105,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'f855d64b-945b-4cb8-8a27-96a5f650e322',
        role: 'assistant',
        content: '重现消息丢失的对吧',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 105,
        batchGUID: '86a8a874-8e5c-4178-9998-15a4bfe125d1',
      },
    ],
  },
  {
    batchGUID: 'f9b5ab32-03af-4e1f-a17e-c3c0f23471e8',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:20:12.000+08:00',
    question: '对',
    feedBack: 0,
    num: 106,
    chatMessageDtoList: [
      {
        chatMessageGUID: '58b367f7-8fe1-40ce-b6f2-2ee7705d9ffe',
        role: 'user',
        content: '对',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 106,
        batchGUID: 'f9b5ab32-03af-4e1f-a17e-c3c0f23471e8',
      },
    ],
  },
  {
    batchGUID: '23e87228-c8ee-4dfd-90b1-a71fa735b3a3',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:20:15.000+08:00',
    question: '重现一下',
    feedBack: 0,
    num: 107,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'cc75332c-4fa9-44cb-a45f-9ae479d996fb',
        role: 'user',
        content: '重现一下',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 107,
        batchGUID: '23e87228-c8ee-4dfd-90b1-a71fa735b3a3',
      },
    ],
  },
  {
    batchGUID: 'c19d0786-1490-4841-8244-3caaff418e0d',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:20:40.000+08:00',
    question: null,
    feedBack: 0,
    num: 108,
    chatMessageDtoList: [
      {
        chatMessageGUID: '30895afb-a120-4f47-8915-efcc22e48d0d',
        role: 'assistant',
        content:
          '<h1 class="card-title">修改认购书模板</h1><p style="white-space: normal;"><span style="font-size: 14px;">您好，提示无打印模板数据，</span>原因是系统中还没设置对应的套打模板或配置有误。如果已经设置，需先查看自己的打印场景是否设置有误，做对应调整。</p><p><span style="font-size: 14px; color: rgb(255, 0, 0);">设置<span style="font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">套打模板</span></span><span style="color: rgb(51, 51, 51); font-family: &quot;Microsoft YaHei&quot;; font-size: 14px;">，步骤如下：</span></p><p>1、需在项目准备--套打设置--查看是否有设置套打模板，如果没有设置需要新增或者引入其他项目的（通用的情况下）；</p><p>2、点击〖新增〗，打开“套打设置”页面，录入模板名称，选择应用场景（认购、签约、收款等场景），选择报表文件等字段后点击“保存”。或者直接引入系统已存在的模板。</p><p><strong>注：</strong></p><p>1、若没有权限可以联系系统管理员，或者引入其他项目模板使用。</p><p>2、若房间已经转签约了，在合同界面上打印就不会显示认购模板了，只会显示签约模板。</p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332862.png" title="image-1718332862.png" alt="image.png"/></p><p><img src="https://new-in-elearning-sl.oss-cn-hangzhou.aliyuncs.com/prod/png/image-1718332879.png" title="image-1718332879.png" alt="image.png"/></p>',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 108,
        batchGUID: 'c19d0786-1490-4841-8244-3caaff418e0d',
      },
    ],
  },
  {
    batchGUID: '01a8962a-d2e2-4999-a91d-3611b411ae6e',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:21:08.000+08:00',
    question: null,
    feedBack: 0,
    num: 109,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'faee1c05-2838-4ea9-af3a-ba15c78624e8',
        role: 'assistant',
        content: '已发送\n',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 109,
        batchGUID: '01a8962a-d2e2-4999-a91d-3611b411ae6e',
      },
    ],
  },
  {
    batchGUID: '9371c40a-3a97-4596-a9ca-5e35617deef2',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:21:18.000+08:00',
    question: '你结束',
    feedBack: 0,
    num: 110,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'b88bd3f1-d53c-4cef-91d1-1e55b5da8bac',
        role: 'user',
        content: '你结束',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 110,
        batchGUID: '9371c40a-3a97-4596-a9ca-5e35617deef2',
      },
    ],
  },
  {
    batchGUID: 'c8b10884-1f15-46ae-b9de-ea137aba3227',
    chatGUID: '54903bdb-1a28-4176-95f9-09191beb409d',
    chatTime: '2024-08-01T19:21:26.000+08:00',
    question: null,
    feedBack: 0,
    num: 111,
    chatMessageDtoList: [
      {
        chatMessageGUID: 'a2ae3039-bfe7-42f5-9433-3779ad895199',
        role: 'notice',
        content:
          '会话已结束，感谢您的垂询，祝您生活愉快。如您在后续使用过程中遇到问题，可随时与我们联系，我们将竭诚为您服务！',
        nodeGUID: '1cc647c4-8abe-4999-9346-0835179dc104',
        chatGUID: null,
        index: 111,
        batchGUID: 'c8b10884-1f15-46ae-b9de-ea137aba3227',
      },
    ],
  },
]
