import injectJs from '@acme/vite-plugin-inject-js'
import { cleanupSVG, parseSVG, SVG } from '@iconify/tools'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import autoprefixer from 'autoprefixer'
import { fileURLToPath, URL } from 'node:url'
import tailwindcss from 'tailwindcss'
import { FileSystemIconLoader } from 'unplugin-icons/loaders'
import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import { ArcoResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv, type Plugin } from 'vite'
import { analyzer } from 'vite-bundle-analyzer'
import { mockDevServerPlugin } from 'vite-plugin-mock-dev-server'

//按需加载
import AutoImport from 'unplugin-auto-import/vite'

//分析工具
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  let mockServer: Plugin | undefined = undefined
  if (mode === 'development' && env.VITE_USE_MOCK_SERVER === '1') {
    console.log('use mock server')
    mockServer = mockDevServerPlugin() as unknown as Plugin
  }

  return {
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag) => tag.includes('-'),
          },
        },
      }),
      vueJsx(),
      Icons({
        compiler: 'vue3',
        defaultClass: 'text-[16px]',
        customCollections: {
          gpt: FileSystemIconLoader('./src/assets/icons', (content) => {
            const svg = new SVG(content)

            cleanupSVG(svg)

            parseSVG(svg, (item) => {
              if (item.tagName === 'svg') {
                item.element.attribs.width = '1em'
                item.element.attribs.height = '1em'
                item.element.attribs.fill = 'currentColor'
              }
            })

            return svg.toString()
          }),
        },
      }),
      mockServer,
      //为了方便建模调用，这里我们读取Html把所有的JS和CSS全部打包到一个JS中，方便引入
      injectJs({
        index: {
          preBuild: () => {
            return `var origin = '';var gpt = document.getElementById('skyline-gpt');if(gpt){origin=gpt.getAttribute('data-gpt-base-url');}`
          },
          attrValue: (key: string, value: string) => {
            if (key === 'src' || key === 'href') {
              return `origin+'${value}?source=loader'`
            }
            return `'${value}'`
          },
        },
        loader: {
          preBuild: () => {
            return 'var s = new URL(import.meta.url);s.searchParams.set("source", "loader");'
          },
          attrValue: (key, value) => {
            if (key === 'src' || key === 'href') {
              return `s.href.replace('/gptbuilder/assistant/loader.js', '${value}')`
            }
            return `'${value}'`
          },
        },
      }),
      Components({
        deep: false,
        dirs: [],
        dts: '.vite/components.d.ts',
        resolvers: [
          IconsResolver({
            customCollections: ['gpt'],
          }),
          ArcoResolver({
            importStyle: false,
            resolveIcons: true,
            sideEffect: true,
          }),
        ],
      }),
      AutoImport({
        dts: '.vite/auto-imports.d.ts',
        imports: ['vue', '@vueuse/core', '@vueuse/head'],
      }),
      mode === 'analyze' ? analyzer() : undefined,
    ],
    //打包目录共用资源
    base: '/gptbuilder/assistant',
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
      preprocessorOptions: {
        less: {
          modifyVars: {
            'arco-theme-tag': ':host',
          },
          javascriptEnabled: true,
        },
      },
    },
    build: {
      outDir: '../../../data/statis/gptbuilder/assistant/',
      emptyOutDir: true,
      manifest: 'manifest.json',
      target: 'esnext', // 设置目标环境为不支持模块的ES版本
      minify: true,
      modulePreload: false, // 关闭 modulePreload
      // 避免使用 manualChunks，因为它会导致 Vite 无法进行 tree-shaking。多使用 import() 动态导入
      // rollupOptions: {
      //   output: {
      //     manualChunks: (id) => {},
      //   },
      // },
    },
    server: {
      // https: true,
      port: 5174,

      //启动全域服务
      host: '0.0.0.0',
      //写这个可以在建模中直接访问IP，让所有的静态资源变成IP地址
      origin: 'http://***********:5173',
      proxy: {
        '/api': {
          // target: "http://**********:44357",
          // target: "http://**********:5000/",//军龙环境
          // target: 'http://***********:8080/', //万桥环境
          target: 'http://***********:9300',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, "")
        },
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
  }
})
