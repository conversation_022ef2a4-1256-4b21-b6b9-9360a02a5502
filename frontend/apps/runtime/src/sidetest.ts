import gpt from '@skyline/ai'

const button = document.querySelector('button')
if (button) {
  button.addEventListener('click', async () => {
    gpt.setContext({
      userCode: 'aa',
      metadata: JSON.stringify({
        aa: 'bb',
      }),
    })
    gpt.open({
      chatId: '123',
      auto: false,
      onCardAction(event) {
        console.log('onCardAction', event)
      },
      data: {
        foo: 'bar',
        bar: 'foo',
      },
      context: {
        input: '你好',
        images: ['fa2b7bc2-2fe6-48e5-8951-6af7d1979441'],
        files: [{ id: '8ba37545-3590-407d-a569-5f3c517334c0', name: '问题.doc' }],
      },
    })
    // window._gpt.debug = false
    // window
    //     ._gpt
    //     .open({ skills: ["xianliao7"], defaultSkill: "xianliao7", context: { 'input': "你好" } })
  })
}
