import { fetchEngine } from '@/services/request'
import { useConfigState } from '@/stores'

export const fetchMessageLog = async (id: string) => {
  try {
    const store = useConfigState()
    const response = await fetchEngine(
      `/Agent/GetChatMessageLog?messageGuid=${id}`,
      {
        method: 'POST',
        headers: {
          ...store.headers,
        },
      },
    )
    if (!response.ok) {
      throw new Error(`数据请求异常: ${response.status}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error('数据请求异常：', error)
    throw error
  }
}
