import type { SystemContext } from '@skyline/ai/internal'
import type { AssistantSession } from './session'
import type { AssistantSkill } from './skill'

export enum CardActionStatus {
  Normal = 'normal',
  Running = 'running',
  Success = 'success',
  Fail = 'fail',
}

// 助手对象
export type Assistant = {
  id: string
  //助手名称
  name: string
  //助手icon
  icon: string
  //欢迎语
  introductoryPhrase: string
  //自我介绍
  selfIntroduction: string
  //技能相关
  skillList: AssistantSkill[]
  //收藏技能
  collectSkills: AssistantSkill[]
  //会话：
  sessions: AssistantSession[]
  currentSessionId: string
  context?: SystemContext
  autoUpload: boolean
  disableAutoScroll: boolean
  listScrollViewRef: HTMLElement | null
  //显示页面，页面模式默认为 agent
  showPageName: 'agent' | 'chat'
  cardActionStatus: { [id: string]: CardActionStatus }
  //是否能使用
  isEnabled: boolean
  // 分页请求chatlist的页码
  sessionsPageIndex: number,
  // 分页请求chatlist的分页大小
  sessionsPageSize: number,
  // 请求chatlist的总数
  sessionsTotal?: number | null
}
