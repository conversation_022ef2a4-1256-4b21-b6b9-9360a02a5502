import { fetchBuilder } from '@/services/request'

type SkillQuestionsResponseData = {
  questions: string[]
  enabled: boolean
}

export const fetchSkillQuestions = async (id: string) => {
  try {
    const response = await fetchBuilder(`/assistant/question/${id}`)
    if (!response.ok) {
      throw new Error(`数据请求异常: ${response.status}`)
    }
    const data = await response.json()
    return data.data as SkillQuestionsResponseData
  } catch (error) {
    console.error('数据请求异常：', error)
    throw error
  }
}
