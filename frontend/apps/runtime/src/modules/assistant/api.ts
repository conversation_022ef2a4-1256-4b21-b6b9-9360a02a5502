import { fetchBuilder } from '@/services/request'
import { contextToArguments } from '@/stores/utils/context'
import type { SystemContext } from '@skyline/ai/internal'

export const fetchAssistantContext = async (
  params: {
    assistantGUIDs: string[]
    skills: string[]
    context: SystemContext
  },
  isRelease: boolean,
) => {
  try {
    const bodyData = {
      assistantGUIDs: params.assistantGUIDs.filter((id) => !!id),
      skills: params.skills.filter((id) => !!id),
      arguments: contextToArguments(params.context),
    }
    const url = isRelease ? '/assistant/contextByOffSite' : '/assistant/contextByOnSite'
    const response = await fetchBuilder(url, {
      method: 'POST',
      body: JSON.stringify(bodyData),
      headers: {
        'Content-Type': 'application/json',
      },
    })
    if (!response.ok) {
      throw new Error(`数据请求异常: ${response.status}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error('数据请求异常：', error)
    throw error
  }
}
