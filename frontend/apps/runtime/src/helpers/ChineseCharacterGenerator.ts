//胡言乱语生成器
class ChineseCharacterGenerator {
  static generateRandomCharacter() {
    const characters = [
      '我',
      '你',
      '他',
      '她',
      '它',
      '们',
      '是',
      '的',
      '在',
      '有',
      '一',
      '个',
      '不',
      '了',
      '人',
      '这',
      '个',
      '上',
      '大',
      '来',
      '国',
      '和',
      '们',
      '到',
      '说',
      '时',
      '要',
      '就',
      '出',
      '会',
      '可',
      '也',
      '你',
      '对',
      '生',
      '能',
      '而',
      '子',
      '那',
      '得',
      '于',
      '着',
      '下',
      '自',
      '之',
      '年',
      '过',
      '发',
      // 添加更多的汉字到数组中
    ]

    const randomIndex = Math.floor(Math.random() * characters.length)
    return characters[randomIndex]
  }

  static generateRandomString(min: number, max: number) {
    const len = min + Math.floor(Math.random() * (max - min))
    let randomString = ''
    for (let i = 0; i < len; i++) {
      randomString += this.generateRandomCharacter()
    }
    return randomString
  }
}

export default ChineseCharacterGenerator
