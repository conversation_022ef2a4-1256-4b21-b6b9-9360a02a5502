import { useAssistantAction } from '@/stores'
import { getCardOriginalParams, type CardParamObject, type CardParams } from '@acme/core'

type UseNextOptions = {
  messageId: string
}

export type NextOptions = {
  updated?: boolean
  nextId?: string
  data: CardParamObject
  nodeCode: string
  outputs: CardParams
}

export const useNext = ({ messageId }: UseNextOptions) => {
  const assistantAction = useAssistantAction()

  const next = ({ data, nextId, updated, nodeCode, outputs }: NextOptions) => {
    assistantAction.sendSystemMessage('操作已完成')

    assistantAction.onCardAction({
      sender: 'button',
      event: 'click',
      data: {
        name: 'card-confirm',
        message: {
          id: messageId,
          data,
        },
      },
    })

    // 手动触发下一步，没有nextId时，不触发下一步
    if (!nextId) {
      return
    }

    let args: Record<string, string> = {}

    // 生成更新数据
    if (updated) {
      args = getCardOriginalParams(data, outputs, nodeCode)
    }

    //表单进行下一步
    assistantAction.sendMessage({
      next: true,
      nextId,
      inputArgs: args,
      type: 'trigger',
    })
  }

  return {
    next,
  }
}
