export const useScroll = () => {
  const scrollState = reactive({
    userScrolled: false,
    disableAutoScroll: false,
  })
  let scrollContainer: HTMLElement | null = null
  const handleScroll = (e: any) => {
    if (!scrollState.userScrolled) return
    const scrollHeight = e.target.scrollHeight
    const height = e.target.scrollTop + e.target.clientHeight
    const diffHeight = scrollHeight - height
    scrollState.disableAutoScroll = diffHeight >= 10
    if (!scrollState.disableAutoScroll) {
      scrollState.userScrolled = false
    }
  }
  const onWheel = () => {
    scrollState.userScrolled = true
  }
  const onListenScroll = (tableInnerRef: HTMLElement) => {
    scrollContainer = tableInnerRef as HTMLElement
    scrollContainer.addEventListener('scroll', handleScroll)
    scrollContainer.addEventListener('wheel', onWheel)
  }
  const scrollToBottom = () => {
    if (scrollState.disableAutoScroll) return
    if (!scrollContainer) return
    scrollContainer.scrollTop = scrollContainer.scrollHeight
  }
  onUnmounted(() => {
    if (!scrollContainer) return
    scrollContainer.removeEventListener('scroll', handleScroll)
    scrollContainer.removeEventListener('wheel', onWheel)
  })
  return { onListenScroll, scrollToBottom }
}
