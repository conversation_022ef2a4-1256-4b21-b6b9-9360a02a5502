import { usePopupStore } from '@/assistant/stores'
import { createAssistantSwitchEvent, emitEvent } from '@/events'
import type { CardActionEvent, ChatMessage, ChatMessageCard } from '@/modules'
import { setAssistantStorage } from '@/services/storage'
import { useAssistantAction, useAssistantState, useConfigState } from '@/stores'
import { getMessageText, getParams, getQueryString } from '@/utils'
import { getCardOriginalParams, getCardParams } from '@acme/core'

const useProtocolUpload = () => {
  const store = useAssistantAction()
  const configStore = useConfigState()
  const popupStore = usePopupStore()
  const assistantStore = useAssistantState()
  const call = (action: string) => {
    if (!assistantStore.currentSkill?.uploadables) return
    store.setAutoUpload(true)
    if (action.startsWith('gpt://upload/qrcode')) {
      configStore.dialogState.scan = true
      return
    }
    if (popupStore.container) {
      popupStore.container.querySelector('.arco-upload')?.dispatchEvent(new MouseEvent('click'))
    }
  }
  return { call }
}

const useProtocolChat = () => {
  const store = useAssistantAction()
  const call = (action: string) => {
    const content = getQueryString('message', new URL(action).search)
    if (content) {
      store.sendMessage({ message: content })
    } else {
      console.error('参数错误')
    }
  }
  return { call }
}

const useProtocolCardAction = () => {
  const store = useAssistantAction()
  const configStore = useConfigState()
  const call = (action: string, message?: ChatMessage, card?: ChatMessageCard) => {
    const onCardAction = window._gpt?.onCardAction || configStore.openOptions?.onCardAction

    // 不再判断是否存在 onCardAction, 无论是否通过 gpt.open 打开，都需要执行后续的逻辑
    // typeof onCardAction !== 'function' ||
    if (!message) {
      return
    }
    const params = getParams(action)
    let data: Record<string, string> = {}
    Object.keys(params).forEach((key) => {
      if (!key.startsWith('$')) {
        data[key] = params[key]
      }
    })

    if (typeof onCardAction === 'function') {
      let content = getMessageText(message.id)

      // 默认为超链接协议
      const actionData: CardActionEvent = {
        sender: 'link',
        event: 'click',
        data: {
          name: 'protocol',
          message: {
            id: message.id,
            content,
            data,
          },
        },
      }

      // 通过卡片动作发起的，修改sender 为 'button', name 为 'card-confirm'
      if (params['$name'] === 'card-confirm') {
        actionData.sender = 'button'
        actionData.data.name = 'card-confirm'
      }
      // 再触发动作事件
      onCardAction(actionData)
    }

    // 无论是否通过 gpt.open 打开，都需要执行后续的逻辑
    if (params['$name'] === 'card-confirm') {
      store.sendSystemMessage('操作已完成')
      // 如果 data 内修改了 outputs 的参数，先修改参数，再触发下一步
      let inputArgs: Record<string, string> = {}
      if (card?.data?.outputs?.length) {
        const outputs = getCardParams(card.data.outputs)
        inputArgs = getCardOriginalParams(data, outputs, card.code)
      }
      if (!card?.next) return
      // 执行下一步
      store.sendMessage({
        next: true,
        // 把 nextId 带上
        nextId: card?.next || '',
        // 确认带上参数后是否可以修改当前卡片的输出参数
        inputArgs,
        type: 'trigger',
      })
    }
  }

  return { call }
}

const useProtocolAssistant = () => {
  const call = (action: string) => {
    const params = getParams(action)
    try {
      if (typeof params.context === 'string') {
        params.context = JSON.parse(params.context)
      }
      setAssistantStorage(params)
      emitEvent(createAssistantSwitchEvent(), params)
    } catch (error) {
      console.error('参数错误')
    }
  }
  return { call }
}

export const useProtocol = () => {
  const { call: callProtocolUpload } = useProtocolUpload()
  const { call: callProtocolChat } = useProtocolChat()
  const { call: callProtocolCardAction } = useProtocolCardAction()
  const { call: callProtocolAssistant } = useProtocolAssistant()

  return {
    callProtocolUpload,
    callProtocolChat,
    callProtocolCardAction,
    callProtocolAssistant,
  }
}
