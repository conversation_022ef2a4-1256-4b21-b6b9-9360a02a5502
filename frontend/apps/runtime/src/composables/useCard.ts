import { useAssistantAction, useConfigAction } from '@/stores'
import type { CardProps, FormDataMap, FormDataValue } from '@acme/core'

type Props = {
  data: {
    data: FormDataMap[]
  }
  messageId: string
}

export const useFormBinding = () => {
  const assistantAction = useAssistantAction()
  const onFormatData = (data: FormDataMap[]) => {
    if (!data || !data?.length) return {}
    const output: Record<string, FormDataValue> = {}
    data.forEach((item) => {
      if (item.code) {
        output[item.code] = item.value
      }
    })
    return output
  }
  const run = (e: Props) => {
    assistantAction.onCardAction({
      sender: 'node',
      event: 'execute',
      data: {
        name: 'form-binding',
        message: {
          id: e.messageId,
          data: onFormatData(e.data.data),
        },
      },
    })
  }
  return { run }
}

export const usePageOpen = () => {
  const configAction = useConfigAction()
  const run = (e: CardProps) => {
    if (e?.url) {
      // 页面打开
      if (e?.mount === 'page') {
        configAction.openExtPage(e?.url as string)
      }
      // 聊天区域打开
      if (e?.mount === 'chat') {
        configAction.openChatPage(e?.url as string)
      }
    }
  }
  return { run }
}
