import {
  createAssistantChangeEvent,
  createAssistantConfigSetEvent,
  createAssistantOpenEvent,
  createAssistantShowEvent,
} from '@/events'
import {
  CardActionStatus,
  fetchSaveChatDetail,
  fetchSkillStartup,
  type CardActionEvent,
  type ChatMessageRole,
  type ErrorEvent,
  type SkillRunningEvent,
} from '@/modules'
import { isLoginPage } from '@/utils'
import {
  SIGNAL,
  signal,
  signalListener,
  signalListenerClear,
  type SkillShortcut,
} from '@skyline/ai/internal'
import { defineStore } from 'pinia'
import { useAssistantState } from '../state/assistantState'
import { useConfigState } from '../state/configState'
import { useAssistantAction } from './assistantAction'
import { useConfigAction } from './configAction'

const hasValue = (d: any) => {
  return typeof d !== 'undefined'
}

export const useApiAction = defineStore('apiAction', () => {
  const configState = useConfigState()
  const configAction = useConfigAction()
  const assistantState = useAssistantState()
  const assistantAction = useAssistantAction()
  const actionConfigShow = () => {
    configState.init = true
  }
  const actionPageOpen = (options: string | { url: string }) => {
    const url = typeof options === 'string' ? options : options?.url
    configAction.openExtPage(url)
  }
  const actionPageClose = () => {
    configAction.openExtPage('')
  }
  const actionChatOpen = (options: string | { url: string }) => {
    const url = typeof options === 'string' ? options : options?.url
    configAction.openChatPage(url)
  }
  const actionChatClose = () => {
    configAction.closeChatPage()
  }
  const actionOpenCardActionSuccessCallback = (
    event: Record<string, string>,
    source: Window | null,
  ) => {
    if (event.id) {
      assistantState.cardActionStatus[event.id] = CardActionStatus.Success
    }
  }
  const actionOpenCardActionFailCallback = (
    event: Record<string, string>,
    source: Window | null,
  ) => {
    if (event.id) {
      assistantState.cardActionStatus[event.id] = CardActionStatus.Fail
    }
  }
  const actionChatMessageSync = (options: {
    messages: { content: string; role: ChatMessageRole }[]
  }) => {
    const messages: { content: string; role: ChatMessageRole; source: string }[] = []
    options.messages.forEach((msg) => {
      const role = msg.role === 'notice' ? 'system' : <ChatMessageRole>msg.role
      const source = role === 'assistant' ? 'human' : ''
      assistantAction.addMessage({
        message: msg.content,
        role,
        source,
      })
      messages.push({
        ...msg,
        source,
      })
    })
    fetchSaveChatDetail({
      sessionId: assistantState.currentSessionId,
      messages: messages,
    })
  }
  const actionChatMessageSend = (msg: string) => {
    assistantAction.sendSystemMessage(msg)
  }
  const actionChatWelcomeMessageSend = (msg: string) => {
    assistantAction.addWelcomeMessage(msg)
  }
  const actionToolInvoke = async (data: { id: string | undefined }, source: Window | undefined) => {
    const { data: res } = await fetchSkillStartup(assistantAction.getContext(), data.id)
    signal(SIGNAL.ActionToolInvokeResult, res, source)
  }
  const actionShortcuts = async (data: SkillShortcut | SkillShortcut[]) => {
    if (Array.isArray(data)) {
      assistantAction.setShortcuts(data)
    } else {
      assistantAction.setShortcuts([data])
    }
  }
  const bind = () => {
    unbind()
    try {
      signalListener(SIGNAL.ActionConfigShow, actionConfigShow)
      //页面相关
      signalListener(SIGNAL.ActionPageOpen, actionPageOpen)
      signalListener(SIGNAL.ActionPageClose, actionPageClose)
      //对话框相关
      signalListener(SIGNAL.ActionChatOpen, actionChatOpen)
      //对话框相关
      signalListener(SIGNAL.ActionChatClose, actionChatClose)
      // onCardAction 执行完毕回调
      signalListener(
        SIGNAL.ActionOpenCardActionSuccessCallback,
        actionOpenCardActionSuccessCallback,
      )
      // onCardAction 执行失败回调
      signalListener(SIGNAL.ActionOpenCardActionFailCallback, actionOpenCardActionFailCallback)
      //对话框-发送消息
      signalListener(SIGNAL.ActionChatMessageSync, actionChatMessageSync)
      //发送消息
      signalListener(SIGNAL.ActionChatMessageSend, actionChatMessageSend)
      //发送欢迎消息
      signalListener(SIGNAL.ActionChatWelcomeMessageSend, actionChatWelcomeMessageSend)
      //发送消息
      signalListener(SIGNAL.ActionToolInvoke, actionToolInvoke)

      //发送消息
      signalListener(SIGNAL.ActionShortcuts, actionShortcuts)
    } catch (error) {
      console.log('GPT API 绑定失败')
    }
  }
  const unbind = () => {
    try {
      signalListenerClear(SIGNAL.ActionConfigShow, actionConfigShow)
      signalListenerClear(SIGNAL.ActionPageOpen, actionPageOpen)
      signalListenerClear(SIGNAL.ActionPageClose, actionPageClose)
      signalListenerClear(SIGNAL.ActionChatOpen, actionChatOpen)
      signalListenerClear(SIGNAL.ActionChatClose, actionChatClose)
      signalListenerClear(
        SIGNAL.ActionOpenCardActionSuccessCallback,
        actionOpenCardActionSuccessCallback,
      )
      signalListenerClear(SIGNAL.ActionOpenCardActionFailCallback, actionOpenCardActionFailCallback)
      signalListenerClear(SIGNAL.ActionChatMessageSync, actionChatMessageSync)
      signalListenerClear(SIGNAL.ActionChatMessageSend, actionChatMessageSend)
      signalListenerClear(SIGNAL.ActionChatWelcomeMessageSend, actionChatWelcomeMessageSend)
      signalListenerClear(SIGNAL.ActionToolInvoke, actionToolInvoke)
      signalListenerClear(SIGNAL.ActionShortcuts, actionShortcuts)
    } catch (error) {}
  }

  const actionOpen = createAssistantOpenEvent()
  const onActionOpen = (event: CustomEvent) => {
    const { options, source } = event.detail
    if (options) {
      if (options.hasOnCardAction) {
        options.onCardAction = (event: CardActionEvent) => {
          signal(SIGNAL.ActionOpenCardAction, event, source)
        }
      }
      if (options.hasOnError) {
        options.onError = (event: ErrorEvent) => {
          signal(SIGNAL.ActionOpenErrorCallback, event, source)
        }
      }
      if (options.hasOnSkillRunning) {
        options.onSkillRunning = (event: SkillRunningEvent) => {
          signal(SIGNAL.ActionOpenSkillRunning, event, source)
        }
      }
      if (options.hasOnClose) {
        options.onClose = () => {
          signal(SIGNAL.ActionOpenCloseCallback, undefined, source)
        }
      }
      if (options.hasOnChat) {
        options.onChat = (event: { id: string }) => {
          signal(SIGNAL.ActionOpenChatCallback, event, source)
        }
      }
      if (options.hasOnSkillStop) {
        options.onSkillStop = () => {
          signal(SIGNAL.ActionOpenChatStopCallback, undefined, source)
        }
      }
      assistantAction.openAssistant(options)
    }
  }

  //全局配置相关
  const actionConfigSet = createAssistantConfigSetEvent()
  const onActionConfigSet = (event: CustomEvent) => {
    const config = event.detail.options
    if (hasValue(config.name)) {
      assistantState.name = config.name || ''
    }
    if (hasValue(config.introductoryPhrase)) {
      assistantState.introductoryPhrase = config.introductoryPhrase || ''
    }
    if (hasValue(config.icon)) {
      assistantState.icon = config.icon
    }
    if (hasValue(config.skills)) {
      // assistantState.skillList = config.skills
      if (!configState.openOptions) {
        configState.openOptions = {}
      }
      if (config.skills === null) {
        configState.openOptions = undefined
      } else {
        configState.openOptions.skills = config.skills || []
      }
    }
    if (hasValue(config.selfIntroduction)) {
      assistantState.selfIntroduction = config.selfIntroduction || ''
      assistantAction.newSession({})
    }

    if (config.context) {
      assistantAction.setContext(config.context)
    }

    //SDK模式下对于现实隐藏的定义不一样
    if (hasValue(config.visible)) {
      if (config.visible && isLoginPage()) {
        return
      }
      if (configState.params.initBySdk) {
        //@ts-ignore
        configState.sdkShow = config.visible
      } else {
        //@ts-ignore
        configState.init = config.visible
      }
    }
  }

  const actionShow = createAssistantShowEvent()
  const onVisible = (event: CustomEvent) => {
    configAction.setOpen(event.detail.visible)
  }

  const assistantChange = createAssistantChangeEvent()
  const onAssistantChange = (event: CustomEvent) => {
    unbind()
    unbindEvent()
    if (configState.openOptions) {
      configState.openOptions.defaultSkill = ''
      configState.openOptions.skills = []
    }
  }

  const bindEvent = () => {
    unbindEvent()
    document.addEventListener(actionOpen, onActionOpen as EventListener)
    document.addEventListener(actionConfigSet, onActionConfigSet as EventListener)
    document.addEventListener(actionShow, onVisible as EventListener)
    document.addEventListener(assistantChange, onAssistantChange as EventListener)
  }
  const unbindEvent = () => {
    document.removeEventListener(actionOpen, onActionOpen as EventListener)
    document.removeEventListener(actionConfigSet, onActionConfigSet as EventListener)
    document.removeEventListener(actionShow, onVisible as EventListener)
    document.removeEventListener(assistantChange, onAssistantChange as EventListener)
  }

  const emitConfigMounted = () => signal(SIGNAL.NotifyConfigMounted)
  const emitConfigError = () => signal(SIGNAL.NotifyConfigError)

  const emitScreenshotResolve = (data: any, host: Window) =>
    signal(SIGNAL.ActionMediaScreenshotResult, data, host)

  const onScreenshot = (callback: (data: void, source: Window) => void) => {
    signalListener(SIGNAL.ActionMediaScreenshot, callback)
  }
  const offScreenshot = (callback: (data: void, source: Window) => void) => {
    signalListenerClear(SIGNAL.ActionMediaScreenshot, callback)
  }

  return {
    bind,
    unbind,
    bindEvent,
    unbindEvent,
    emitConfigMounted,
    emitConfigError,
    onScreenshot,
    offScreenshot,
    emitScreenshotResolve,
    onActionConfigSet,
  }
})
