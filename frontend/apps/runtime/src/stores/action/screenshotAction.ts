import type { CroppingImage } from '@/modules'
import { defineStore } from 'pinia'
import { useApiAction, useAssistantAction, useScreenshotState } from '../'

export const useScreenshotAction = defineStore('useScreenshotAction', () => {
  const state = useScreenshotState()
  const assistantAction = useAssistantAction()
  const apiAction = useApiAction()
  const setVisible = (v: boolean) => {
    state.croppingImg = null
    state.visible = v
  }

  const setFullImg = (img: string) => {
    state.fullImg = img
  }

  const setCroppingImg = (imgObj: CroppingImage | null) => {
    if (!imgObj) {
      state.visible = false
      return
    }
    state.croppingImg = { ...(state.croppingImg || {}), ...imgObj }
  }

  function base64ToFile(base64Data: string, filename: string) {
    // 将base64的数据部分提取出来
    const parts = base64Data.split(';base64,')
    const contentType = parts[0].split(':')[1]
    const raw = window.atob(parts[1])

    // 将原始数据转换为Uint8Array
    const rawLength = raw.length
    const uInt8Array = new Uint8Array(rawLength)
    for (let i = 0; i < rawLength; ++i) {
      uInt8Array[i] = raw.charCodeAt(i)
    }

    // 使用Blob和uInt8Array创建一个新的Blob对象
    const blob = new Blob([uInt8Array], { type: contentType })

    // 创建File对象
    const file = new File([blob], filename, { type: contentType })

    return file
  }
  const setScreenshotImg = (img: string, width: number, height: number) => {
    state.screenshotImg = img
    const host = getFromApi()
    if (host) {
      apiAction.emitScreenshotResolve({ image: img, width, height }, host)
      setFromApi(null)
    } else {
      assistantAction.uploadFile(base64ToFile(img, `screenshot${new Date().getTime()}.png`))
    }
  }

  const setFromApi = (isApi: Window | null) => {
    // state.fromApi = isApi
    //@ts-ignore
    window.screenshotFromApi = isApi
  }
  const getFromApi = () => {
    //@ts-ignore
    return window.screenshotFromApi
  }

  return {
    setVisible,
    setFullImg,
    setCroppingImg,
    setScreenshotImg,
    setFromApi,
  }
})
