import hljs from 'highlight.js/lib/core'
import MarkdownIt from 'markdown-it'
import MarkdownItContainer from 'markdown-it-container'
import MarkdownItHighlight from 'markdown-it-highlightjs/core'

// 按需加载语言包
import batch from '@/utils/languages/batch'
import bash from 'highlight.js/lib/languages/bash'
import powershell from 'highlight.js/lib/languages/powershell'
import shell from 'highlight.js/lib/languages/shell'

import cshtmlRazor from '@/utils/languages/cshtml-razor'
import csharp from 'highlight.js/lib/languages/csharp'

import c from 'highlight.js/lib/languages/c'
import cpp from 'highlight.js/lib/languages/cpp'

import css from 'highlight.js/lib/languages/css'
import less from 'highlight.js/lib/languages/less'

import csv from '@/utils/languages/csv'
import markup from '@/utils/languages/markup'
import json from 'highlight.js/lib/languages/json'
import xml from 'highlight.js/lib/languages/xml'
import yaml from 'highlight.js/lib/languages/yaml'

import java from 'highlight.js/lib/languages/java'

import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'

import python from 'highlight.js/lib/languages/python'

import sql from 'highlight.js/lib/languages/sql'
import { onSanitizeHtml } from './sanitizeHtml'

const registerLanguages = () => {
  hljs.registerLanguage('bash', bash)

  hljs.registerLanguage('sh', shell)
  hljs.registerLanguage('shell', shell)

  hljs.registerLanguage('powershell', powershell)

  hljs.registerLanguage('bat', batch)
  hljs.registerLanguage('batch', batch)

  hljs.registerLanguage('csharp', csharp)
  hljs.registerLanguage('dotnet', csharp)
  hljs.registerLanguage('aspnet', csharp)
  hljs.registerLanguage('cs', csharp)

  hljs.registerLanguage('razor', cshtmlRazor)
  hljs.registerLanguage('cshtml', cshtmlRazor)

  hljs.registerLanguage('c', c)
  hljs.registerLanguage('cpp', cpp)

  hljs.registerLanguage('less', less)
  hljs.registerLanguage('css', css)

  hljs.registerLanguage('json', json)

  hljs.registerLanguage('csv', csv)

  hljs.registerLanguage('yaml', yaml)
  hljs.registerLanguage('yml', yaml)

  hljs.registerLanguage('xml', xml)
  hljs.registerLanguage('svg', markup)
  hljs.registerLanguage('markup', markup)
  hljs.registerLanguage('mathml', markup)
  hljs.registerLanguage('ssml', markup)
  hljs.registerLanguage('rss', markup)
  hljs.registerLanguage('atom', markup)

  hljs.registerLanguage('java', java)

  hljs.registerLanguage('python', python)

  hljs.registerLanguage('sql', sql)

  hljs.registerLanguage('javascript', javascript)
  hljs.registerLanguage('js', javascript)

  hljs.registerLanguage('typescript', typescript)
  hljs.registerLanguage('ts', typescript)
}

const languages = [
  // 自定义支持
  'aspnet',
  'batch',
  'csv',
  'java',
  'json',
  'jsx',
  'less',
  'powershell',
  'python',
  'sql',

  // bash
  'bash',
  'sh',
  'shell',

  // c#
  'csharp',
  'dotnet',
  'cs',

  // cshtml
  'cshtml',
  'razor',

  // typescript
  'typescript',
  'ts',
  // yaml
  'yaml',
  'yml',

  // c/c++
  'c',
  'cpp',

  'javascript',
  'js',
  'css',

  // xml 系列
  'markup',
  'html',
  'mathml',
  'svg',
  'xml',
  'ssml',
  'atom',
  'rss',
  // prism 自带
]

const markdownLangs = [
  'abap',
  'actionscript-3',
  'ada',
  'adoc',
  'angular-html',
  'angular-ts',
  'apache',
  'apex',
  'apl',
  'applescript',
  'ara',
  'asciidoc',
  'asm',
  'astro',
  'awk',
  'ballerina',
  'bash',
  'bat',
  'batch',
  'be',
  'beancount',
  'berry',
  'bibtex',
  'bicep',
  'blade',
  'c',
  'c#',
  'c++',
  'cadence',
  'cdc',
  'clarity',
  'clj',
  'clojure',
  'closure-templates',
  'cmake',
  'cmd',
  'cobol',
  'codeowners',
  'codeql',
  'coffee',
  'coffeescript',
  'common-lisp',
  'console',
  'coq',
  'cpp',
  'cql',
  'crystal',
  'cs',
  'csharp',
  'css',
  'csv',
  'cue',
  'cypher',
  'd',
  'dart',
  'dax',
  'desktop',
  'diff',
  'docker',
  'dockerfile',
  'dotenv',
  'dream-maker',
  'edge',
  'elisp',
  'elixir',
  'elm',
  'emacs-lisp',
  'erb',
  'erl',
  'erlang',
  'f',
  'f#',
  'f03',
  'f08',
  'f18',
  'f77',
  'f90',
  'f95',
  'fennel',
  'fish',
  'fluent',
  'for',
  'fortran-fixed-form',
  'fortran-free-form',
  'fs',
  'fsharp',
  'fsl',
  'ftl',
  'gdresource',
  'gdscript',
  'gdshader',
  'genie',
  'gherkin',
  'git-commit',
  'git-rebase',
  'gjs',
  'gleam',
  'glimmer-js',
  'glimmer-ts',
  'glsl',
  'gnuplot',
  'go',
  'gql',
  'graphql',
  'groovy',
  'gts',
  'hack',
  'haml',
  'handlebars',
  'haskell',
  'haxe',
  'hbs',
  'hcl',
  'hjson',
  'hlsl',
  'hs',
  'html',
  'html-derivative',
  'http',
  'hxml',
  'hy',
  'imba',
  'ini',
  'jade',
  'java',
  'javascript',
  'jinja',
  'jison',
  'jl',
  'js',
  'json',
  'json5',
  'jsonc',
  'jsonl',
  'jsonnet',
  'jssm',
  'jsx',
  'julia',
  'kotlin',
  'kql',
  'kt',
  'kts',
  'kusto',
  'latex',
  'lean',
  'lean4',
  'less',
  'liquid',
  'lisp',
  'lit',
  'log',
  'logo',
  'lua',
  'luau',
  'make',
  'makefile',
  'markdown',
  'marko',
  'matlab',
  'md',
  'mdc',
  'mdx',
  'mediawiki',
  'mermaid',
  'mojo',
  'move',
  'nar',
  'narrat',
  'nextflow',
  'nf',
  'nginx',
  'nim',
  'nix',
  'nu',
  'nushell',
  'objc',
  'objective-c',
  'objective-cpp',
  'ocaml',
  'pascal',
  'perl',
  'perl6',
  'php',
  'plsql',
  'po',
  'postcss',
  'pot',
  'potx',
  'powerquery',
  'powershell',
  'prisma',
  'prolog',
  'properties',
  'proto',
  'protobuf',
  'ps',
  'ps1',
  'pug',
  'puppet',
  'purescript',
  'py',
  'python',
  'ql',
  'qml',
  'qmldir',
  'qss',
  'r',
  'racket',
  'raku',
  'razor',
  'rb',
  'reg',
  'regex',
  'regexp',
  'rel',
  'riscv',
  'rs',
  'rst',
  'ruby',
  'rust',
  'sas',
  'sass',
  'scala',
  'scheme',
  'scss',
  'sh',
  'shader',
  'shaderlab',
  'shell',
  'shellscript',
  'shellsession',
  'smalltalk',
  'solidity',
  'soy',
  'sparql',
  'spl',
  'splunk',
  'sql',
  'ssh-config',
  'stata',
  'styl',
  'stylus',
  'svelte',
  'swift',
  'system-verilog',
  'systemd',
  'tasl',
  'tcl',
  'templ',
  'terraform',
  'tex',
  'tf',
  'tfvars',
  'toml',
  'ts',
  'ts-tags',
  'tsp',
  'tsv',
  'tsx',
  'turtle',
  'twig',
  'typ',
  'typescript',
  'typespec',
  'typst',
  'v',
  'vala',
  'vb',
  'verilog',
  'vhdl',
  'vim',
  'viml',
  'vimscript',
  'vue',
  'vue-html',
  'vy',
  'vyper',
  'wasm',
  'wenyan',
  'wgsl',
  'wiki',
  'wikitext',
  'wl',
  'wolfram',
  'xml',
  'xsl',
  'yaml',
  'yml',
  'zenscript',
  'zig',
  'zsh',
  '文言',
].reduce((r, v) => ((r[v] = true), r), {} as Record<string, boolean>)

export const fixMarkdownLanguageMeta = (text: string) => {
  const isLanguageMeta = text in markdownLangs
  if (isLanguageMeta && !text.endsWith('\n')) {
    text += '\n'
  }
  return text
}

const md = MarkdownIt({
  html: true,
  linkify: true,
  breaks: true,
})

registerLanguages()

md.use(MarkdownItContainer, 'actions', {
  validate(params: string) {
    // 完全匹配 actions
    return params.trim().match(/^actions$/)
  },
  render(tokens: { nesting: number }[], idx: number) {
    if (tokens[idx].nesting === 1) {
      // 使用 .gpt-card-actions 样式包裹内部内容
      return '<div class="gpt-card-actions">\n'
    } else {
      return '</div>\n'
    }
  },
})
  .use(MarkdownItHighlight, {
    hljs,
    inline: true,
    langPrefix: 'hljs language-',
  })
  .use((md) => {
    md.core.ruler.before('normalize', 'sanitize', function purify_dom(state) {
      state.src = onSanitizeHtml(state.src)
    })
  })
// .use(MarkdownItKatex)

const p = location.pathname

md.use(preWrapperPlugin)

const use = async () => {
  md.renderer.rules.link_open = function (tokens, idx, options, _env, self) {
    // 修改tokens来添加target属性
    const tag = tokens[idx]
    if (tag) {
      const linkUrl = tag.attrGet('href')
      if (linkUrl && (linkUrl.startsWith('http://') || linkUrl.startsWith('https://'))) {
        tag.attrPush(['target', '_blank'])
      } else if (linkUrl && linkUrl.startsWith('gpt://')) {
        tag.attrPush(['gpt-action', linkUrl])
        tag.attrSet('href', 'javascript:void(0);')
      }
    }

    // 调用默认的渲染器
    return self.renderToken(tokens, idx, options)
  }
  md.renderer.rules.text = function (tokens, idx, options, _env, self) {
    const text = tokens[idx].content
    if (idx < 1) return text
    const isLink = tokens[idx - 1]?.type === 'link_open'
    if (isLink) {
      return `<span data-type="message-btn" class="gpt-message-btn">${text}</span>`
    } else {
      return text
    }
  }
  md.renderer.rules.code_inline = function (tokens, idx) {
    const token = tokens[idx]
    // 转义 HTML 尖括号
    const content = token.content.replace(/</g, '&lt;').replace(/>/g, '&gt;')
    return `<code>${content}</code>`
  }
  md.renderer.rules.code_block = function (tokens, idx) {
    const token = tokens[idx]
    // 转义 HTML 尖括号
    const content = token.content.replace(/</g, '&lt;').replace(/>/g, '&gt;')
    return `<pre><code>${content}</code></pre>`
  }
}
use()

// 用户输入，不解析html
const disableHtmlMd = MarkdownIt({
  html: false,
  linkify: true,
  breaks: true,
})
disableHtmlMd.use(MarkdownItHighlight, {
  hljs,
  inline: true,
  langPrefix: 'hljs language-',
})
disableHtmlMd.use(preWrapperPlugin)

export const renderMarkdown = (content: string, role?: string) => {
  const isUser = role === 'user'
  const currentMd = isUser ? disableHtmlMd : md
  const res = currentMd.render((content || '').trim())
  return res
}

const isCodeGeneration =
  p.startsWith('/coding/business-component') ||
  p.startsWith('/modeling/designer') ||
  location.hostname === 'localhost'

const iconSVG = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="1" y="2" width="10" height="4.5" rx="0.6" stroke="currentColor" stroke-width="1.2"/><rect x="1" y="9.5" width="10" height="4.5" rx="0.6" stroke="currentColor" stroke-width="1.2"/><path d="M15.5 6L12.5 8L15.5 10" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/></svg>`
const copySVG = `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="4.80005" y="4.79999" width="9.6" height="9.6" rx="0.6" stroke="currentColor" stroke-width="1.2"/><path d="M11.2001 2.59998V2.19998C11.2001 1.8686 10.9315 1.59998 10.6001 1.59998H2.2001C1.86873 1.59998 1.6001 1.8686 1.6001 2.19998V10.6C1.6001 10.9313 1.86873 11.2 2.2001 11.2H2.6001" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/></svg>`

const getCodeToolbar = (lang: string) => {
  let toolbarStr = `<div class="gpt-code-block_toolbar"><div class="gpt-code-block_language">${lang}</div><div class="gpt-code-block_actions">`
  if (isCodeGeneration) toolbarStr += `<button class="gpt-code-generation">${iconSVG}</button>`
  toolbarStr += `<button class="gpt-code-generation" data-action="copy" title="复制">${copySVG}</button>`
  return toolbarStr + '</div></div>'
}
export function preWrapperPlugin(md: MarkdownIt) {
  // biome-ignore lint/style/noNonNullAssertion: <explanation>
  const fence = md.renderer.rules.fence!
  md.renderer.rules.fence = (...args: [any, any, any, any, any]) => {
    const [tokens, idx] = args
    const token = tokens[idx]
    // remove title from info
    token.info = token.info.replace(/\[.*\]/, '')

    const lang = extractLang(token.info)
    // 解决不支持的语言类型 prismjs 报错
    if (lang && !languages.includes(lang)) {
      token.info = ''
    }
    let rawCode = ''
    try {
      rawCode = fence(...args)
    } catch (err) {}
    const toolbar = getCodeToolbar(lang)
    return rawCode.replace(
      /<pre[^>]*>/,
      // `<pre style="position: relative"><span class="lang">${lang}</span><button title="Copy Code" class="code-copy copy"></button>`,
      `<pre class="hljs language-${lang} gpt-code-block">${toolbar}`,
    )
  }

  const defaultRender =
    md.renderer.rules.table_open ||
    function (tokens, idx, options, env, self) {
      return '<table>\n'
    }
  md.renderer.rules.table_open = (tokens, idx, options, env, self) => {
    return (
      `<div class="gpt-table-container">
      <button class="gpt-table-container_copy gpt-code-generation" data-action="copy-table" title="复制">
      ${copySVG}
      </button>` + defaultRender(tokens, idx, options, env, self)
    )
  }
  md.renderer.rules.table_close = (tokens, idx, options, env, self) => {
    return '</table>\n' + '</div>'
  }
}

export function extractTitle(info: string) {
  return info.match(/\[(.*)\]/)?.[1] || extractLang(info) || 'txt'
}

export const MD_CLASS = `
prose prose-slate prose-sm dark:prose-invert prose-headings:mb-4
prose-headings:mt-2 prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg
prose-h4:text-base prose-h5:text-base prose-h6:text-base prose-p:mb-1
prose-ol:my-1 prose-ul:my-1 prose-ul:pl-0.5 prose-li:my-1 prose-ol:ml-6
prose-p:mt-[4px] prose-p:mb-[4px] prose-pre:my-2 prose-pre:w-full overflow-hidden break-words`

const extractLang = (info: string) => {
  return info
    .trim()
    .replace(/:(no-)?line-numbers({| |$).*/, '')
    .toLowerCase()
}
