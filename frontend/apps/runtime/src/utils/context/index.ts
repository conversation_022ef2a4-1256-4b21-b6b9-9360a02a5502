import type { SystemContext } from '@skyline/ai/internal'
import { getFingerprint } from '../fingerprint'
import getContextFromProdiders from './providers'

export default (): SystemContext => {
  const context: SystemContext = {}
  const c = getContextFromProdiders()
  if (c) {
    Object.assign(context, c)
  }

  context.pageUrl = location.href

  const fingerprint = getFingerprint()
  if (!context.userGuid) {
    context.userGuid = fingerprint
  }
  if (!context.userCode) {
    context.userCode = fingerprint
  }
  if (!context.userName) {
    context.userName = fingerprint
  }
  if (!context.customerId) {
    context.customerId = fingerprint
  }
  if (!context.metadata) {
    context.metadata = ''
  }

  return context
}

export const pruneContext = (context: SystemContext) => {
  const ctx = context as Record<string, string>
  Object.keys(ctx).forEach((key) => {
    if (!ctx[key]) {
      delete ctx[key]
    }
  })

  return context
}
