<script setup lang="ts">
import { SupportedImageExtName } from '@/services/const'
import { useAssistantAction, useAssistantState, useConfigState } from '@/stores'
import { PlatformType, detectPlatform } from '@/utils/deviceType'
import toast from '@/utils/vue/toast'
import { ImagePreview, Tooltip } from '@arco-design/web-vue'
import { storeToRefs } from 'pinia'
import FileItem from './FileItem.ce.vue'
import { usePopupStore } from '@/assistant/stores'
import { onClickOutside } from '@vueuse/core'
import MessageForm from './MessageForm.vue'

const popupStore = usePopupStore()
const store = useAssistantState()
const action = useAssistantAction()
const config = useConfigState()
const isActive = ref<boolean>(false)
const inputRef = ref<HTMLTextAreaElement | null>(null)
const enterTip = ref<string>('')
const previewImg = ref<string>('')
const visibleForm = ref(false)
const formData = ref<Record<string, string>>({})
const editorFormRef = useTemplateRef('editorForm')
const messageInputRef = useTemplateRef('messageInputRef')
const previewImgVisible = computed<boolean>({
  get() {
    return !!previewImg.value
  },
  set(value) {
    if (value === false) {
      previewImg.value = ''
    }
  },
})
const disabled = computed(
  () => config.disableChat || store.isWaiting || !store.isEnabled || config.dialogState.agreement,
)

//快速发送
enterTip.value = (detectPlatform() === PlatformType.MAC ? '⌘' : 'Ctrl') + ' + Enter'
const { currentSession } = storeToRefs(store)
const inputRow = ref(1)
const isEnable = computed(() => {
  if (Object.keys(formData.value).length > 0) return true
  if (currentSession.value) {
    const { input, documentFiles, imageFiles } = currentSession.value
    if (documentFiles?.some((c) => c.status === 'uploading')) {
      return false
    }
    if (imageFiles?.some((c) => c.status === 'uploading')) {
      return false
    }
    return (
      input || (documentFiles && documentFiles.length > 0) || (imageFiles && imageFiles.length > 0)
    )
  }
  return false
})

const sendMessage = () => {
  if (disabled.value) return
  if (!store.currentSession?.selectedSkillId) {
    toast.show('请先选择技能')
    return
  }

  if (editorFormRef.value?.validate) {
    editorFormRef.value.validate().then((res) => {
      if (res) {
        addMessage()
      }
    })
  } else {
    addMessage()
  }
}

const addMessage = () => {
  if (isEnable.value && currentSession.value) {
    currentSession.value.input = currentSession.value.input.trim()

    action.sendMessage({
      message: currentSession.value.input,
      inputArgs: { ...toRaw(formData.value) },
    })
    formData.value = {}
    visibleForm.value = false
    //输入完成后，对话框需要自动变更高度
    nextTick(() => {
      autoHeight()
    })
  }
}
const onEnter = (e: KeyboardEvent) => {
  if (disabled.value) return
  if (currentSession.value?.input) {
    if (e.shiftKey || e.ctrlKey || e.metaKey) {
      currentSession.value.input += '\r\n'
      nextTick(() => {
        autoHeight()
      })
    } else {
      sendMessage()
    }
  }
}

//这里主要的作用是，当对话框需要换行时，自动伸展高度
const autoHeight = () => {
  if (inputRef.value) {
    inputRef.value.style.height = 'auto'
    inputRef.value.style.height = `${inputRef.value.scrollHeight}px`
  }
}
//监听，如果有其他组件设置了message，就把光标移动，并且自动切换高度
watch(
  currentSession,
  (cur, pre) => {
    autoHeight()
  },
  { deep: true },
)
const unsubscribe = store.$onAction(
  ({
    name, // 动作名称
    after, // 动作返回或解决后的钩子
  }) => {
    if (name === 'actionSetInput') {
      after(() => {
        nextTick(() => {
          autoHeight()
          if (inputRef.value) {
            inputRef.value.focus()
          }
        })
      })
    }
  },
)
onUnmounted(() => {
  unsubscribe()
})
onMounted(() => {
  const con = popupStore.container
  if (con) {
    con.addEventListener('paste', function (event: any) {
      if (disabled.value) return
      if (event.target && (event.target as any).id === 'message-input') {
        if (event.clipboardData) {
          var items = event.clipboardData.items
          var item = items[0]
          if (item && item?.kind === 'file') {
            var blob = item.getAsFile()
            if (
              blob &&
              store.currentSkill?.uploadables.split(',').some((c) => blob?.name.endsWith(c))
            ) {
              action.uploadFile(blob)
              event.stopPropagation()
              event.preventDefault()
            }
          }
        }
      }
    })
    // 处理文件拖入
    con.addEventListener('dragover', function (e: any) {
      if (disabled.value) return
      e.stopPropagation()
      e.preventDefault()
      if (e.dataTransfer) {
        e.dataTransfer.dropEffect = 'copy'
      }
    })

    // 处理文件放下
    con.addEventListener('drop', function (e: any) {
      if (disabled.value) return
      e.stopPropagation()
      e.preventDefault()
      if (e.dataTransfer) {
        // 获取拖入的文件列表
        var files = e.dataTransfer.files
        var blob = files[0]

        if (
          blob &&
          store.currentSkill?.uploadables.split(',').some((c) => blob?.name.endsWith(c))
        ) {
          action.uploadFile(blob)
        }
      }
    })
  }
})

const inputPlaceholder = computed(() => {
  if (config.dialogState.agreement) return '同意协议后方可使用'
  if (config.disableChat) return '当前AI技能不支持对话，请按指引操作'
  return store.introductoryPhrase
})

const isAllowInput = computed(() => {
  if (
    config.openOptions &&
    config.openOptions.hasOwnProperty('interactive') &&
    typeof config.openOptions?.interactive === 'boolean'
  ) {
    return config.openOptions.interactive
  }
  return store.currentSkill?.interactive
})
const isAllowParams = computed(() => !!config.params.designer)
const placeholder = computed(() => {
  if (isAllowInput.value) {
    return isActive ? '' : inputPlaceholder.value
  }
  return store.currentSkill ? '该技能不支持对话' : '请选择技能'
})
const onFocus = () => {
  isActive.value = true
  visibleForm.value = true
}
onClickOutside(messageInputRef, () => {
  visibleForm.value = false
})
</script>

<template>
  <label
    ref="messageInputRef"
    for="message-input"
    :class="{ 'gpt-message-input_disabled': disabled }"
  >
    <div class="input-area" :class="{ active: isActive }" v-if="currentSession">
      <div class="input-area-back" :class="{ active: isActive }">
        <MessageForm ref="editorForm" :visible="isAllowParams && visibleForm" :value="formData" />
        <div
          class="input-file-list"
          v-if="currentSession.documentFiles && currentSession.documentFiles.length > 0"
        >
          <FileItem
            v-for="(documentFile, index) in currentSession.documentFiles"
            :documentFile="documentFile"
            :single="currentSession.documentFiles.length === 1"
            @clear="() => !disabled && action.clearFile(index)"
          ></FileItem>
        </div>
        <div class="input-row">
          <textarea
            ref="inputRef"
            id="message-input"
            type="text"
            :placeholder="placeholder"
            :disabled="disabled || !store.currentSkill || !currentSession.inputEnabled"
            @focus="onFocus"
            @blur="isActive = false"
            v-model="currentSession.input"
            :rows="inputRow"
            @keydown.enter.prevent="onEnter"
            :readonly="!isAllowInput"
          ></textarea>
          <Tooltip
            content="停止生成"
            position="top"
            :popup-container="popupStore.container!"
            content-class="gpt-tooltip-content"
            arrow-class="gpt-tooltip-arrow"
            v-if="store.showStopButton"
          >
            <button class="input-stop-answer active" @click.prevent="() => action.stopMessage()">
              <span class="input-stop-answer-white"></span>
            </button>
          </Tooltip>
          <button
            v-else
            class="input-send"
            :class="{ active: isEnable }"
            @click="sendMessage"
            :title="enterTip"
          >
            <IGptSend class="text-[16px]"></IGptSend>
          </button>
        </div>
      </div>
    </div>
  </label>
  <ImagePreview
    v-if="previewImgVisible"
    :src="previewImg"
    :popup-container="popupStore.container!"
    v-model:visible="previewImgVisible"
  />
</template>
