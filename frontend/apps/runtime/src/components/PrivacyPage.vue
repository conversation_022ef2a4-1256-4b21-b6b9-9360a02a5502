<script lang="ts" setup>
import { useConfigState } from '@/stores'
import ToolBar from '@/components/Toolbar.ce.vue'
import MessageInput from '@/components/MessageInput.vue'
import PageHeader from '@/components/PageHeader.ce.vue'
import PrivacyIcon from '@/components/icons/PrivacyIcon.vue'

const config = useConfigState()
const handleView = () => {
  config.dialogState.agreementContent.visible = true
}
</script>

<template>
  <div class="gpt-content justify-between">
    <PageHeader />
    <div
      class="mx-[16px] my-[20px] flex h-full flex-col items-center justify-center rounded-[8px] bg-[#fff]"
    >
      <div class="inline-block">
        <PrivacyIcon />
      </div>
      <div class="mt-[4px] text-lg font-normal not-italic leading-[26px] text-[#333333]">
        欢迎使用
      </div>
      <div class="mt-[8px] text-[13px] font-normal not-italic leading-[22px] text-[#999999]">
        为了给您提供更好的服务，请先阅读并同意隐私协议
      </div>
      <div
        @click="handleView"
        class="gpt-linear-gradient-btn mt-[20px] w-[102px] cursor-pointer items-center gap-[4px] rounded-[4px]
          px-[12px] py-[5px]"
      >
        <span class="text-center text-[13px] font-normal not-italic leading-[22px] text-[#ffffff]"
          >隐私政策详情</span
        >
      </div>
    </div>
    <ToolBar />
    <MessageInput />
  </div>
</template>
