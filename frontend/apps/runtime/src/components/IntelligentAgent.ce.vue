<script lang="ts" setup>
import { getIntelligentAgent, onSkillCollect } from '@/services/request'
import { useAssistantAction, useAssistantState } from '@/stores'
import { generateConsistentRandom, getTextWidth } from '@/utils'
import ToastApi from '@/utils/vue/toast'
import type { Component, Reactive } from 'vue'
import { Tooltip, Spin, InputSearch, Dropdown } from '@arco-design/web-vue'
import { SkillDefaultIcons } from '@/components/icons'
import { usePopupStore } from '@/assistant/stores'

const popupStore = usePopupStore()

interface SkillType {
  skillGUID: string
  skillName: string
  describe: string
  skillChatCount: number
  skillCollectCount: number
  icon?: string
  collect: boolean
  randomIcon?: Component
}
interface AgentMenu {
  skillCategoryGUID: string
  skillCategoryName: string
}
interface AgentData {
  recommendedSkill?: SkillType
  skillCategoryList?: AgentMenu[]
  skillList?: SkillType[]
  menuList?: AgentMenu[]
  moreMenus?: AgentMenu[]
}
const assistant = useAssistantState()
const action = useAssistantAction()
const isLoading = ref(false)
const scrollContainer = ref(null)
const agentMenu = ref(null)
const stickyData = reactive({
  isSticky: false,
})
const options = reactive({
  skillCategoryGUID: '',
  skillName: '',
})
const agentData: Reactive<AgentData> = reactive({
  recommendedSkill: undefined,
  skillCategoryList: [],
  skillList: [],
  menuList: [],
  moreMenus: [],
})

const getData = async () => {
  try {
    isLoading.value = true
    const { data } = await getIntelligentAgent({
      ...options,
      assistantGUID: assistant.id,
    })
    agentData.skillCategoryList = data?.skillCategoryList
    const rSkill = data?.recommendedSkill
    let index = -1
    agentData.skillList = data?.skillList?.map((item: SkillType, key: number) => {
      if (rSkill?.skillGUID === item.skillGUID) {
        index = key
      }
      item.randomIcon = SkillDefaultIcons[generateConsistentRandom(item.skillGUID)]
      return item
    })
    if (rSkill && !rSkill?.icon) {
      rSkill.randomIcon = SkillDefaultIcons[generateConsistentRandom(rSkill.skillGUID)]
    }
    if (index !== -1 && rSkill && agentData.skillList) {
      agentData.skillList[index] = rSkill
    }
    agentData.recommendedSkill = rSkill
    isLoading.value = false
    getMoreMenus()
  } catch (err) {
    isLoading.value = false
  }
}
const getMoreMenus = () => {
  if (!scrollContainer.value) return
  let boxWidth = (scrollContainer.value as HTMLElement).clientWidth
  // 设置默认宽度
  if (boxWidth <= 0) boxWidth = 928
  const allWidth = 54
  const searchWidth = 320
  const maxWidth = boxWidth - allWidth - searchWidth
  let width = 0
  let moreIndex = agentData.skillCategoryList?.findIndex((item) => {
    width += getMenuWidth(item.skillCategoryName)
    return width > maxWidth
  })
  moreIndex = moreIndex && moreIndex > 1 ? moreIndex - 1 : -1
  const data = JSON.parse(JSON.stringify(agentData.skillCategoryList))
  if (moreIndex !== -1) {
    agentData.menuList = data.slice(0, moreIndex)
    agentData.moreMenus = data.slice(moreIndex)
  } else {
    agentData.menuList = data
    agentData.moreMenus = []
  }
}
const getMenuWidth = (text: string) => {
  const marginLeft = 16
  const padding = 26
  const maxWidth = 110 + 16
  let width = getTextWidth(text) + marginLeft + padding
  if (width > maxWidth) {
    return maxWidth
  }
  return width
}
const onselect = (value: string) => {
  options.skillCategoryGUID = value
  getData()
}
const handleScroll = () => {
  if (!agentMenu.value) return
  stickyData.isSticky = (agentMenu.value as HTMLElement).getBoundingClientRect().top === 0
}
const onResize = () => {
  getMoreMenus()
}
watch(() => [options.skillName, assistant.id], getData)
onMounted(() => {
  getData()
  window.addEventListener('resize', onResize)
  if (!scrollContainer.value) return
  ;(scrollContainer.value as HTMLElement).addEventListener('scroll', handleScroll)
  return () => {
    window.removeEventListener('resize', onResize)
    if (!scrollContainer.value) return
    ;(scrollContainer.value as HTMLElement).removeEventListener('scroll', handleScroll)
  }
})

const onCollect = async (item: SkillType) => {
  item.collect = !assistant.collectSkillIdList.includes(item.skillGUID)
  if (item.collect) {
    item.skillCollectCount += 1
  } else {
    item.skillCollectCount -= 1
  }
  await onSkillCollect({
    skillGUID: item.skillGUID,
    collect: item.collect,
  })
  await action.getCollectSkills()
  ToastApi.show(item.collect ? '收藏成功' : '已取消收藏')
}

const rSkill = computed(() => {
  if (!agentData.recommendedSkill?.skillGUID) return null
  return agentData.recommendedSkill
})

const rsCollectStatus = computed(() => {
  if (!rSkill.value?.skillGUID) return false
  return assistant.collectSkillIdList.includes(rSkill.value.skillGUID)
})

const activeMore = computed(() => {
  if (!agentData.moreMenus?.length) return false
  return agentData.moreMenus?.some((item) => item.skillCategoryGUID === options?.skillCategoryGUID)
})

const handleUseSkill = (id: string) => {
  assistant.showPageName = 'chat'
  action.toggleSkillById(id)
}
watch(
  () => assistant.collectSkillIdList,
  (v) => {
    agentData.skillList?.forEach((item: SkillType) => {
      const oldCollect = item.collect
      item.collect = v?.includes(item.skillGUID)
      if (oldCollect && !item.collect && item.skillCollectCount > 0) {
        item.skillCollectCount -= 1
      }
    })
  },
)
</script>

<template>
  <div class="gpt-content gpt-content_agent" ref="scrollContainer">
    <div class="gpt-content_agent-sticky" v-if="stickyData.isSticky"></div>
    <Spin :loading="isLoading" :size="28">
      <div class="gpt-intelligent-agent">
        <div class="gpt-intelligent-agent_title">发现智能体</div>
        <div class="gpt-intelligent-agent_recommend" v-if="rSkill?.skillGUID">
          <div class="agent_recommend-icon">
            <IGptFireFill class="text-[16px]" />
            推荐
          </div>
          <div class="agent_recommend-title">
            <span class="agent_recommend-title-icon">
              <img v-if="rSkill.icon" :src="rSkill.icon" alt="" />
              <component v-else :is="rSkill.randomIcon" class="text-[32px]" />
            </span>
            <span class="agent_recommend-title-name" :title="rSkill.skillName">{{
              rSkill.skillName
            }}</span>
          </div>
          <div class="agent_recommend-describe" :title="rSkill.describe">
            {{ rSkill.describe || '暂无描述' }}
          </div>
          <div class="agent_recommend-tools">
            <div class="agent_recommend-tools-btn" @click="handleUseSkill(rSkill.skillGUID)">
              <IGptMagicFill class="text-[16px]"></IGptMagicFill>
              立即使用
            </div>
            <span>
              <IGptFireFill class="text-[16px]" />
              <span class="text-[#999999]">{{ rSkill.skillChatCount }}</span>
            </span>
            <Tooltip
              :content="rsCollectStatus ? '取消收藏' : '收藏'"
              position="top"
              :popup-container="popupStore.container!"
              content-class="gpt-tooltip-content"
              arrow-class="gpt-tooltip-arrow"
            >
              <span class="agent_content-item-tools-collect" @click.stop="onCollect(rSkill)">
                <IGptCollect v-if="rSkill.collect" class="text-[16px]" />
                <IGptCollectNone v-else class="text-[16px]" />
                <span class="text-[#999999]">{{ rSkill.skillCollectCount }}</span>
              </span>
            </Tooltip>
          </div>
        </div>
        <div class="gpt-intelligent-agent_menu" ref="agentMenu">
          <div class="agent_menu-list">
            <div
              class="agent_menu-item"
              @click="onselect('')"
              :class="{ 'agent_menu-item_active': !options.skillCategoryGUID }"
            >
              <span>全部</span>
            </div>
            <div
              v-if="agentData?.menuList?.length"
              class="agent_menu-item"
              :class="{
                'agent_menu-item_active': options.skillCategoryGUID === item.skillCategoryGUID,
              }"
              @click="onselect(item.skillCategoryGUID)"
              v-for="(item, key) in agentData.menuList"
              :key="'menu_' + key"
            >
              <span :title="item.skillCategoryName">{{ item.skillCategoryName }}</span>
            </div>
            <Dropdown
              position="bottom"
              :popup-container="popupStore.container!"
              content-class="gpt-dropdown_agent-menu"
            >
              <div
                class="agent_menu-item"
                :class="{ 'agent_menu-item_active': activeMore }"
                v-if="agentData.moreMenus?.length"
              >
                <IGptMore class="text-[20px]" />
              </div>
              <template #content>
                <template v-for="item in agentData.moreMenus">
                  <Dropdown.Option
                    :value="item.skillCategoryGUID"
                    @click="onselect(item.skillCategoryGUID)"
                  >
                    <div
                      class="agent_menu-item"
                      :title="item.skillCategoryName"
                      :class="{
                        'agent_menu-item_active':
                          options.skillCategoryGUID === item.skillCategoryGUID,
                      }"
                    >
                      <span>{{ item.skillCategoryName }}</span>
                    </div>
                  </Dropdown.Option>
                </template>
              </template>
            </Dropdown>
          </div>
          <div class="agent_menu-search">
            <InputSearch size="large" placeholder="搜索智能体" v-model="options.skillName" />
          </div>
        </div>
        <div class="gpt-intelligent-agent_content" v-if="agentData?.skillList?.length">
          <div
            class="agent_content-item"
            v-for="(item, index) in agentData.skillList"
            :key="'item_' + index"
          >
            <div class="agent_content-item-inner" @click="handleUseSkill(item.skillGUID)">
              <div class="agent_content-item-skill">
                <div class="agent_content-item-skill-icon">
                  <img v-if="item.icon" :src="item.icon" alt="" />
                  <component v-else :is="item.randomIcon" class="text-[48px]" />
                </div>
                <div class="agent_content-item-skill-info">
                  <div class="agent_content-item-skill-title" :title="item.skillName">
                    {{ item.skillName }}
                  </div>
                  <div class="agent_content-item-skill-describe" :title="item.describe">
                    {{ item.describe }}
                    <span v-if="!item.describe" class="text-[#999999]">暂无描述</span>
                  </div>
                </div>
              </div>
              <div class="agent_content-item-tools">
                <span>
                  <IGptFireFill class="text-[16px]" />
                  <span class="text-[#999999]">{{ item.skillChatCount }}</span>
                </span>
                <Tooltip
                  :content="item.collect ? '取消收藏' : '收藏'"
                  position="top"
                  :popup-container="popupStore.container!"
                  content-class="gpt-tooltip-content"
                  arrow-class="gpt-tooltip-arrow"
                >
                  <span class="agent_content-item-tools-collect" @click.stop="onCollect(item)">
                    <IGptCollect v-if="item.collect" class="text-[16px]" />
                    <IGptCollectNone v-else class="text-[16px]" />
                    <span class="text-[#999999]">{{ item.skillCollectCount }}</span>
                  </span>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="gpt-intelligent-agent_empty" v-else-if="!isLoading">
          <div class="">
            <IGptEmpty class="text-[92px]" />
            <div
              class="mt-[8px] text-center text-[13px] font-normal not-italic leading-[22px] text-[#999999]"
            >
              暂无智能体
            </div>
          </div>
        </div>
      </div>
    </Spin>
  </div>
</template>

<style lang="less">
.ai-container {
  .gpt-intelligent-agent {
    padding: 36px 16px 20px 16px;
    width: 100%;
    min-height: 100%;
  }

  .gpt-intelligent-agent_title {
    color: #333333;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
  }

  .gpt-intelligent-agent_recommend {
    margin-top: 24px;
    height: 238px;
    width: 100%;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    background: url('data:image/jpeg;base64,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')
      no-repeat;
    background-size: 100% 100%;

    .agent_recommend-icon {
      display: flex;
      padding: 3px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      position: absolute;
      border-radius: 0 0 8px 0;
      background: linear-gradient(107deg, #e4b7ff 0%, #c04cff 100%);
      color: #fff;
      left: 0;
      top: 0;
    }
    .agent_recommend-title {
      margin-top: 65px;
    }
    .agent_recommend-title-icon {
      width: 32px;
      height: 32px;
      margin-right: 8px;
      display: inline-block;
      float: left;
      border-radius: 6px;
      overflow: hidden;
      img {
        width: 100%;
        height: auto;
      }
    }
    .agent_recommend-title-name {
      color: #333333;
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 28px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .agent_recommend-describe {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      color: #666666;
      text-overflow: ellipsis;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-top: 12px;
    }
    .agent_recommend-tools {
      position: absolute;
      bottom: 16px;
      height: 36px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      > span {
        margin-left: 12px;
        display: inline-flex;
        color: #ddd;
        align-items: center;
        svg {
          margin-right: 4px;
        }
      }
    }
    .agent_recommend-tools-btn {
      display: flex;
      padding: 7px 12px;
      justify-content: center;
      align-items: center;
      gap: 6px;
      border-radius: 20px;
      color: #ffffff;
      height: 36px;
      cursor: pointer;
    }
  }

  .history-header-button,
  .agent_recommend-tools-btn {
    background: linear-gradient(90deg, #5c8aff 0%, #786cff 100%);
    &:hover {
      transition: 0.3s;
      background: linear-gradient(90deg, #7da1ff 0%, #938aff 100%);
    }
  }

  .gpt-intelligent-agent_menu {
    padding-top: 24px;
    padding-bottom: 14px;
    width: 100%;
    display: inline-block;
    position: sticky;
    top: 0;

    .agent_menu-item {
      display: inline-block;
      padding: 7px 12px;
      gap: 10px;
      border-radius: 20px;
      background: #fff;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      cursor: pointer;
      border: 1px solid transparent;
      max-width: 110px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      box-sizing: border-box;

      > span {
        color: #333333;
      }

      + .agent_menu-item {
        margin-left: 16px;
      }

      &.agent_menu-item_active {
        border: 1px solid #5c8aff;
        background: linear-gradient(90deg, #dee7ff 0%, #e4e2ff 100%);

        span {
          color: transparent;
          font-weight: 600;
          background: linear-gradient(90deg, #5c8aff 0%, #786cff 100%);
          background-clip: text;
          -webkit-background-clip: text;
        }
      }
      &:hover {
        transition: 0.3s;
        background: linear-gradient(90deg, #dee7ff 0%, #e4e2ff 100%);
      }
    }
    .agent_menu-search {
      width: 280px;
      display: inline-block;
      box-sizing: border-box;
      float: right;
    }
    .agent_menu-search > span {
      width: 100%;
      display: flex;
      align-items: center;
      border-radius: 20px;
      background: #fff;
      border: 1px solid transparent;
      input {
        padding: 7px 16px;
      }
    }
    .agent_menu-list {
      display: inline-block;
      height: 39px;
      overflow: hidden;
      float: left;
    }
  }

  .history-search-btn,
  .agent_menu-search {
    &:hover .arco-input-wrapper,
    .arco-input-focus {
      transition: 0.3s;
      border: 1px solid #5c8aff !important;
    }
  }

  .gpt-intelligent-agent_content {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
    .agent_content-item {
      display: flex;
      flex: 33.33%;
      max-width: 33.33%;
      height: 136px;
      padding: 0 8px 16px 8px;
    }
    .agent_content-item-inner {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      border: 1px solid #eaeaea;
      background: #fff;
      padding: 16px 16px 8px 16px;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      &:hover {
        transition: 0.3s;
        box-shadow: 0 4px 10px 0 #0000001a;
      }
    }
    .agent_content-item-skill {
      display: inline-block;
      height: 68px;
    }
    .agent_content-item-skill-icon {
      width: 48px;
      height: 48px;
      padding-right: 0;
      display: inline-block;
      float: left;
      overflow: hidden;
      margin-bottom: 20px;
      border-radius: 6px;
      margin-right: 12px;
      img {
        width: 48px;
        height: auto;
      }
    }
    .agent_content-item-skill-info {
      width: calc(100% - 60px);
      display: inline-block;
    }
    .agent_content-item-skill-title {
      height: 24px;
      color: #333333;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .agent_content-item-skill-describe {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      color: #666666;
      text-overflow: ellipsis;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-top: 4px;
    }
    .agent_content-item-tools {
      overflow: hidden;
      color: #ddd;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-top: 6px;
      > span {
        margin-right: 12px;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        svg {
          margin-right: 4px;
        }
      }
    }
  }
  .gpt-intelligent-agent_empty {
    width: 100%;
    height: calc(100% - 102px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120px 0;
  }
  .agent_content-item-tools-collect {
    cursor: pointer;
  }

  .gpt-content.gpt-content_agent {
    .arco-spin,
    .arco-spin-loading {
      height: 100%;
    }
    .arco-spin-mask {
      background-color: transparent;
    }
  }
}

.gpt-popover_agent-menu {
  display: flex;
  width: 120px;
  padding: 4px 0;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  background: #fff;
  box-shadow: 0 3px 8px 0 #0000001a;
  .arco-popover-content {
    width: 100%;
    padding-bottom: 4px;
    max-height: 380px;
    overflow-y: auto;
  }
  .agent_menu-item {
    width: 100%;
    padding: 5px 12px;
    height: 32px;
    overflow: hidden;
    color: #333333;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    box-sizing: border-box;
    &:hover,
    &.active,
    &.agent_menu-item_active {
      transition: 0.3s;
      background: #f2f4f6;
    }
    &.agent_menu-item_active {
      span {
        background: linear-gradient(90deg, #5c8aff 0%, #786cff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }
    }
  }
}
.gpt-content_agent-sticky {
  position: fixed;
  height: 76px;
  left: 0;
  right: 0;
  top: 0;
  width: 100%;
  background-color: #f2f2f8;
  background-image: var(--gpt-header-image);
  background-size: 100% 48px;
  background-repeat: no-repeat;
}
.gpt-dropdown_agent-menu {
  .agent_menu-item {
    cursor: pointer;
    max-width: 110px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    box-sizing: border-box;

    > span {
      color: #333333;
    }

    &.agent_menu-item_active {
      span {
        background: linear-gradient(90deg, #5c8aff 0%, #786cff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
      }
    }
  }
}
</style>
