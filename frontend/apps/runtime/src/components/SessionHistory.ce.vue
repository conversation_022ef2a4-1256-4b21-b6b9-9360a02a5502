<script setup lang="ts">
import type { AssistantSession, AssistantSkill } from '@/modules'
import { onSkillCollect } from '@/services/request'
import { setNavigationVisible } from '@/services/storage'
import { InputSearch, Tooltip, Button, Popover, List } from '@arco-design/web-vue'
import { useAssistantAction, useAssistantState, useSkillGuideAction } from '@/stores'
import ToastApi from '@/utils/vue/toast'
import { Dropdown, Modal, Input } from '@arco-design/web-vue'
import {
  IconCheck,
  IconClose,
  IconDelete,
  IconEdit,
  IconLeft,
  IconMore,
} from '@arco-design/web-vue/es/icon'
import { usePopupStore } from '@/assistant/stores'

const { showHistory } = defineProps({
  showHistory: {
    type: Boolean,
  },
})

const action = useAssistantAction()
const state = useAssistantState()
const skillAction = useSkillGuideAction()
const searchTxt = ref('')
const editText = reactive({
  id: '',
  name: '',
  loading: false,
  deleteId: '',
})
const inputRef = ref<any>(null)
const deleteModalVisible = ref(false)
const emit = defineEmits(['update:visible'])
const handleSelect = (item: AssistantSession, op: any) => {
  if (op === 'delete') {
    editText.deleteId = item.id
    editText.name = item.name
    deleteModalVisible.value = true
  } else if (op === 'rename') {
    editText.id = item.id
    editText.name = item.name
    nextTick(() => {
      if (inputRef.value && inputRef.value[0].focus) {
        inputRef.value[0].focus()
      }
    })
  }
}
const deleteAll = () => {
  editText.deleteId = ''
  editText.name = '【所有历史回话】'
  deleteModalVisible.value = true
}
const deleteModalClose = () => {
  deleteModalVisible.value = false
  editText.deleteId = ''
}
const deleteItem = async () => {
  return await action.deleteSession(editText.deleteId)
}
const renameItem = async () => {
  editText.loading = true
  await action.renameSession(editText.id, editText.name)
  editText.loading = false
  editText.id = ''
}

const handleClear = () => {
  state.showPageName = 'chat'
  action.selectSkillById('')
}
const sectionList = computed(() => {
  return state.sessions.filter((c) => c.name.includes(searchTxt.value))
})
const handleOpenAgent = () => {
  // 正在查看技能说明返回智能体时需要先关闭技能说明
  skillAction.close()
  state.showPageName = 'agent'
}

// 避免在展开收起的时候再次请求
const afterToggle = ref(false)
const setAfterToggle = () => {
  afterToggle.value = true
  setTimeout(() => {
    afterToggle.value = false
  }, 10)
}

const handleToggle = () => {
  emit('update:visible', !showHistory)
  setAfterToggle()
  setNavigationVisible(!showHistory)
}
const handleCancelCollect = async (item: AssistantSkill) => {
  if (!item.skillGUID) return
  await onSkillCollect({ skillGUID: item.skillGUID, collect: false })
  await action.getCollectSkills()
  ToastApi.show('已取消收藏')
}
const handleUseSkill = (id?: string) => {
  if (!id) return
  state.showPageName = 'chat'
  action.toggleSkillById(id)
}
const popupStore = usePopupStore()

let resizeObserver: ResizeObserver | null = null
const historyListRef = ref()
const historyListHeight = ref(500)
const setInitWidth = () => {
  if (historyListRef.value) {
    historyListHeight.value = historyListRef.value.offsetHeight // 初始化容器宽度
  }
}

onMounted(() => {
  setInitWidth()

  // 使用 ResizeObserver 监听父级容器大小变化
  resizeObserver = new ResizeObserver(() => {
    setInitWidth()
  })
  resizeObserver.observe(historyListRef.value)
})

const finished = computed(() => {
  return typeof state.sessionsTotal === 'number' && state.sessionsTotal <= state.sessions.length
})

const loading = ref(false)

// 列表是否滚动过，避免初始化触发listReachBottom
const listHasScrolled = ref(false)
const listScroll = () => {
  listHasScrolled.value = true
}

const listReachBottom = async () => {
  if (finished.value || !listHasScrolled.value || afterToggle.value) return
  loading.value = true
  await action.getChatList()
  loading.value = false
}

onBeforeUnmount(() => {
  // 组件卸载时销毁 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
})
</script>

<template>
  <div class="gpt-history" :class="{ active: !showHistory }">
    <div class="history-content_inner" v-show="showHistory">
      <div class="history-header">
        <div class="history-header-button gpt-agent-ai" @click="handleOpenAgent">
          <IGptAgent class="text-[18px]" />
          <span>发现智能体</span>
        </div>
        <div class="history-header-button" @click="handleClear">
          <IGptNewChat class="text-[18px]" />
          新建对话
        </div>
      </div>
      <div class="history-header-title">历史对话</div>
      <div class="history-search">
        <div class="history-search-btn">
          <InputSearch size="large" placeholder="搜索历史对话" v-model="searchTxt" />
        </div>
        <div class="history-search-clear">
          <Tooltip
            content="清空历史对话"
            position="right"
            :popup-container="popupStore.container!"
            content-class="gpt-tooltip-content"
            arrow-class="gpt-tooltip-arrow"
          >
            <Button shape="circle" size="large" @click="deleteAll">
              <IconDelete />
            </Button>
          </Tooltip>
        </div>
      </div>
      <div class="history-list" ref="historyListRef">
        <List
          :virtualListProps="{
            height: historyListHeight,
          }"
          :loading="loading"
          :data="sectionList"
          @scroll="listScroll"
          @reachBottom="listReachBottom"
        >
          <template #item="{ item }">
            <div
              class="list-item"
              :class="{
                active: state.showPageName !== 'agent' && item.id === state.currentSessionId,
              }"
              @click="() => action.selectSession(item.id)"
            >
              <div class="lite-item-text" v-if="editText.id === item.id">
                <Input
                  v-model="editText.name"
                  ref="inputRef"
                  @keydown.enter="renameItem"
                  @keydown.esc="editText.id = ''"
                >
                  <template #suffix>
                    <Button type="text" @click="renameItem">
                      <IconCheck />
                    </Button>
                    <Button type="text" @click="editText.id = ''">
                      <IconClose />
                    </Button>
                  </template>
                </Input>
              </div>
              <Popover
                content-class="gpt-popover-content"
                :popup-container="popupStore.container!"
                position="bottom"
                v-else="editText.id"
              >
                <div class="lite-item-text">
                  {{ item.name || '新会话' }}
                </div>
                <template #content>
                  <span>{{ item.name || '新会话' }}</span>
                </template>
              </Popover>
              <div class="lite-item-action" @click.prevent.stop>
                <Dropdown
                  @select="(op) => handleSelect(item, op)"
                  :popup-container="popupStore.container!"
                >
                  <Button type="text"><IconMore /></Button>
                  <template #content>
                    <Dropdown.Option value="rename">
                      <template #icon>
                        <IconEdit />
                      </template>
                      <template #default>重命名</template>
                    </Dropdown.Option>
                    <Dropdown.Option value="delete">
                      <template #icon>
                        <IconDelete class="text-danger" />
                      </template>
                      <template #default><span class="text-danger">删除</span></template>
                    </Dropdown.Option>
                  </template>
                </Dropdown>
              </div>
            </div>
          </template>

          <template #empty>
            <div class="no-data" v-if="sectionList.length === 0">
              {{ searchTxt === '' ? '暂无历史会话' : '暂无搜索结果' }}
            </div>
          </template>
        </List>
      </div>
      <div class="history-header-title mt-[20px]">我的智能体</div>
      <div class="gpt-self-collect">
        <template v-for="item in state.collectSkills">
          <div class="gpt-self-collect-item" @click="handleUseSkill(item.skillGUID)">
            <span class="gpt-self-collect-item-icon">
              <img v-if="item.icon" :src="item.icon" alt="" />
              <component v-else :is="item.randomIcon" class="text-[24px]" />
            </span>
            <Popover
              :popup-container="popupStore.container!"
              content-class="gpt-popover-content"
              position="bottom"
            >
              <span class="gpt-self-collect-item-text" :title="item.skillName">{{
                item.skillName
              }}</span>
              <template #content>
                <span>{{ item.skillName }}</span>
              </template>
            </Popover>

            <Popover
              position="bottom"
              :popup-container="popupStore.container!"
              content-class="gpt-popover-content gpt-popover_agent-menu"
              :arrow-style="{ display: 'none' }"
              :content-style="{ padding: 0, maxWidth: '88px' }"
            >
              <span class="gpt-self-collect-item-more">
                <IGptMore class="text-[16px]" />
              </span>
              <template #content>
                <div class="agent_menu-item" @click="handleCancelCollect(item)">
                  <span>取消收藏</span>
                </div>
              </template>
            </Popover>
          </div>
        </template>
      </div>
    </div>

    <div class="drawer-side-button" @click="handleToggle">
      <IconLeft></IconLeft>
    </div>

    <Modal
      v-model:visible="deleteModalVisible"
      @cancel="deleteModalClose"
      :on-before-ok="deleteItem"
      unmountOnClose
      :simple="true"
      modal-class="gpt-modal-delete"
      title-align="start"
      :popup-container="popupStore.container!"
    >
      <template #title>
        <div class="gpt-modal-title">
          <IGptWarningFill class="text-[24px]" />
          <span class="gpt-modal-title-text">确认删除对话？</span>
        </div>
      </template>
      <div class="gpt-modal-content">删除后，聊天技能将不可恢复。</div>
    </Modal>
  </div>
</template>

<style lang="less">
.gpt-history {
  width: 248px;
  height: 100%;
  padding: 20px;
  border-radius: 0 16px 16px 0;
  border-right: #fff;
  background-color: #ffffffcc;
  box-sizing: border-box;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1;
  padding-top: 60px;
  border: 1px solid var(--usage-bg---color-bg-grey-0, #f3f4f9);
  box-shadow: 0 4px 10px 0 #0000001a;

  .history-header {
    .history-header-button {
      width: 100%;
      display: flex;
      padding: 9px 12px;
      justify-content: center;
      align-items: center;
      border-radius: 20px;
      overflow: hidden;
      color: #ffffff;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      cursor: pointer;
      margin-bottom: 20px;
      background: linear-gradient(90deg, #5c8aff, #786cff);
      > svg {
        margin-right: 8px;
      }
      &.gpt-agent-ai {
        background: linear-gradient(90deg, #eff3ff 0%, #f1f0ff 100%);
        color: #266eff;
        span {
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          background: linear-gradient(90deg, #5c8aff 0%, #786cff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          color: transparent;
        }
        &:hover {
          transition: 0.3s;
          background: linear-gradient(90deg, #dee7ff 0%, #e4e2ff 100%);
        }
      }
    }
  }
  .history-header-title {
    color: #999999;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 12px;
  }
  .history-search {
    display: flex;
    flex-direction: row;
    .history-search-btn {
      .arco-input-wrapper {
        background-color: #ffffff;
        border-radius: 20px;
        border: 1px solid #eaeaea;
        background: #fff;
      }
    }
    .history-search-clear {
      margin-left: 4px;
      font-size: 16px;
      .arco-btn:hover {
        background: #fff1f1;
        color: #ff4c4c;
      }
    }
  }
  .history-list {
    flex: 1;
    margin-top: 10px;
    overflow: hidden;
    min-height: 120px;
    padding-right: 0 !important;

    .arco-list-wrapper {
      overflow: hidden;
      height: 100%;
    }
    .arco-spin,
    .arco-list-spin {
      display: block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .list-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      display: flex;
      padding: 4px 7px;
      justify-content: space-between;
      align-self: stretch;
      border-radius: 8px;
      border: 1px solid #eaeaea;
      margin-bottom: 8px;
      cursor: pointer;
      .lite-item-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        line-height: 32px;
      }
      .lite-item-action {
        .arco-btn {
          padding-left: 0;
          padding-right: 0;
          color: #999;
        }
      }
      &:hover,
      &.active {
        border: 1px solid #5c8aff;
        background: linear-gradient(90deg, #eff3ff 0%, #f1f0ff 100%);
        .arco-btn {
          color: #266eff;
        }
      }
    }
    .no-data {
      color: #999999;
      padding: 10px 8px;
      font-size: 13px;
    }
    .no-data-dev {
      color: #999999;
      text-align: center;
      margin-top: 40px;
    }
  }
  .history-list,
  .gpt-self-collect {
    margin-right: -6px;
    padding-right: 6px;
  }

  .history-content_inner {
    width: 205px;
    display: flex;
    height: 100%;
    flex-direction: column;
  }

  .gpt-self-collect {
    max-height: 280px;
    overflow-y: auto;
  }

  .gpt-self-collect-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 6px;
    cursor: pointer;

    + .gpt-self-collect-item {
      margin-top: 8px;
    }

    .gpt-self-collect-item-icon {
      width: 24px;
      height: 24px;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .gpt-self-collect-item-text {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      color: #333333;
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-left: 8px;
      width: 139px;
    }

    .gpt-self-collect-item-more svg {
      visibility: hidden;
    }

    &:hover {
      transition: 0.3s;
      border-radius: 8px;
      background: linear-gradient(90deg, #eff3ff 0%, #f1f0ff 100%);
      .gpt-self-collect-item-more svg {
        visibility: visible;
      }
    }
  }
}

.ai-container {
  .gpt-history {
    div.drawer-side-button {
      left: 252px;
      position: fixed;
    }

    &.active {
      transition: all 0.3s ease;
      background-color: transparent;
      width: 0px;
      padding: 0;

      div.drawer-side-button {
        left: 4px;
      }
    }
  }
}
</style>
