<script setup lang="ts">
import { useScreenshotAction, useScreenshotState } from '@/stores'
import { Modes, Whiteboard } from '../utils/whiteboard'
import type { CroppingImage } from '@/modules'

const screenshotStore = useScreenshotState()
const screenshotAction = useScreenshotAction()

const canvasRef = ref<HTMLCanvasElement>()

const modeData: any = computed(() => Modes)

const imgObj = ref<CroppingImage | any>({
  top: 0,
  left: 0,
  width: 0,
  height: 0,
})

const whiteboard = new Whiteboard({
  onCropping: (data: CroppingImage) => {
    if (!data.img) {
      imgObj.value = { ...imgObj.value, ...data }
      return
    }
    whiteboard.off()
    screenshotAction.setCroppingImg(data)
    imgObj.value = data
  },
})

onMounted(async () => {
  if (!canvasRef.value) return
  whiteboard.bind(canvasRef.value, window.innerWidth, window.innerHeight)
  whiteboard.background(screenshotStore.fullImg)
  whiteboard.setMode(modeData.value.CROPPING)
})

watch(
  () => imgObj.value,
  () => {
    whiteboard.setCroppingBox(imgObj.value)
  },
  { deep: true },
)

const onSetMode = (mode: string) => {
  whiteboard.setMode(mode)
}

const handleSubmit = () => {
  whiteboard.onCropping(imgObj.value)
  nextTick(() => {
    screenshotAction.setScreenshotImg(imgObj.value.img, imgObj.value.width, imgObj.value.height)
    screenshotAction.setCroppingImg(null)
  })
}

const handleCancel = () => {
  screenshotAction.setCroppingImg(null)
}
</script>

<template>
  <div class="screenshot">
    <canvas ref="canvasRef" />
    <div
      v-if="screenshotStore.croppingImg"
      class="screenshot-toolbar"
      :style="{
        zIndex: 99999999999999999,
        position: 'absolute',
        left: imgObj.left + 'px',
        top: imgObj?.top + imgObj.height + 'px',
      }"
    >
      <span class="screenshot-toolbar-item" @click="onSetMode(modeData.RECTANGLE)">
        <IGptRect class="text-[18px]" />
      </span>
      <span class="screenshot-toolbar-item" @click="onSetMode(modeData.LINE)">
        <IGptArrow class="text-[18px]" />
      </span>
      <span class="screenshot-toolbar-item screenshot-toolbar-splice" @click="handleSubmit">
        <IGptSubmit class="text-[18px]" />
      </span>
      <span class="screenshot-toolbar-item" @click="handleCancel">
        <IGptCancel class="text-[18px]" />
      </span>
    </div>
  </div>
</template>
<style lang="less">
.screenshot {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 999999999;
  background: #e5e7eb;
}
.screenshot-toolbar {
  padding: 6px 8px;
  background-color: #2e3238;
  display: inline-block;
  border-radius: 4px;
}
.screenshot-toolbar-splice {
  position: relative;
  &:before {
    content: '';
    border-left: 1px solid rgba(255, 255, 255, 0.5);
    width: 1px;
    height: 16px;
    display: inline-block;
    position: absolute;
    left: 0;
  }
  svg {
    margin-left: 16px;
  }
}
.screenshot-toolbar-item {
  cursor: pointer;
  float: left;
  + .screenshot-toolbar-item {
    margin-left: 16px;
  }
}
</style>
