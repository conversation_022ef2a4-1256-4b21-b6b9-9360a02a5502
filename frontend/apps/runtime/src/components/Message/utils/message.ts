import type { ChatMessage } from '@/modules'
import { validCard } from '@/stores/utils/card'
import { hasFilesValue } from './file'

export const hasMessageContent = (message: ChatMessage) => {
  // 停止生成
  if (message.status === 'stop') return true
  // 深度思考内容
  if (message.reasoning) return true
  // 流式输出中
  if (message.process && !message.processEnd && message.status === 'pending') return true
  // 思考中
  if (message.status === 'waiting') return true
  // 异常
  if (message.status === 'error' || message.isError) return true
  // 文本-例如提示词输出
  let hasContent = !!message.content
  // 输出结束，判断是否有内容
  if (message.status === 'done') hasContent = !!message.content?.trim()
  return (
    hasContent ||
    validCard(message.cards) ||
    hasFilesValue(message.imageFiles) ||
    hasFilesValue(message.documentFiles)
  )
}

export const getAssistantMessages = (messages: ChatMessage[]) => {
  return messages.filter((message) => message.role === 'assistant' && hasMessageContent(message))
}

export const getMessageContentClass = (message: ChatMessage) => {
  let className = `message-${message.role} `
  className +=
    hasFilesValue(message.imageFiles) || hasFilesValue(message.documentFiles) ? 'file ' : ''
  return className
}
function escapeRegExp(string: string) {
  return string.replace(/[()\[\]]/g, '\\$&')
}
function replaceAll(str: string, find: string, replace: string) {
  return str.replace(new RegExp(escapeRegExp(find), 'g'), replace)
}

export const getMessageContent = (msg: ChatMessage): string => {
  let result = msg.content
  if (msg.replace) {
    for (let index = 0; index < msg.replace.length; index++) {
      const element = msg.replace[index]
      result = replaceAll(result, element.key, element.value)
    }
  }
  return result
}
