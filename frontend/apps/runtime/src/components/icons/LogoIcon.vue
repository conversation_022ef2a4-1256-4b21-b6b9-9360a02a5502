<script lang="ts" setup>
import { Guid } from '@/utils/guid'

const props = defineProps<{ size?: number }>()
const { size = 32 } = props

const w = computed(() => `${size}px`)
const h = computed(() => `${size}px`)
const linearId1 = computed(() => `paint1_linear_${Guid()}`)
const linearId0 = computed(() => `paint0_linear_${Guid()}`)
</script>

<template>
  <svg :width="w" :height="h" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M25.7646 23.0366C26.4467 22.287 26.9984 21.4104 27.3791 20.436C28.3069 18.0583 28.8256 14.6361 28.6412 12.09C28.4042 8.81471 26.2742 5.96457 23.1778 4.77229C20.1588 3.61023 14.9096 2.81719 11.6821 3.03649C10.0748 3.14623 8.57023 3.69609 7.31055 4.58258L25.7646 23.0366Z"
      :fill="`url(#${linearId0})`"
    />
    <path
      d="M7.31057 4.58259C5.97482 5.52262 4.91446 6.84117 4.29904 8.41483C3.37132 10.7925 2.85257 14.2147 3.03696 16.7608C3.23093 19.4505 4.70247 21.8552 6.93783 23.2868C8.27741 24.146 9.3423 25.3446 10.4004 26.5355C10.8376 27.0276 11.2737 27.5185 11.7275 27.9835C12.2562 28.5248 12.9357 28.8574 13.6489 28.9632C14.3557 29.0681 15.0956 28.9501 15.7545 28.5917C16.3221 28.283 16.8806 27.9452 17.4407 27.6064C18.8074 26.7797 20.1831 25.9476 21.7206 25.5209C24.2802 24.8098 26.3978 22.9478 27.3792 20.436C26.9985 21.4104 26.4467 22.287 25.7646 23.0366L7.31057 4.58259Z"
      :fill="`url(#${linearId1})`"
    />
    <path
      d="M14.5848 22.4724C12.8495 22.2094 11.2098 21.8088 10.0862 21.3742C8.0163 20.5707 6.5801 18.6205 6.42502 16.4048C6.28782 14.4504 6.70262 11.7041 7.41216 9.87524C8.21537 7.80302 10.1641 6.36483 12.3776 6.20896C13.579 6.12502 15.2645 6.22649 16.9998 6.4895C18.7351 6.7525 20.3747 7.15113 21.4983 7.58773C23.5682 8.39114 25.0044 10.3414 25.1595 12.5571C25.2967 14.5135 24.8819 17.2577 24.1724 19.0866C23.3692 21.1589 21.4205 22.597 19.2069 22.7529C18.0056 22.8369 16.32 22.7354 14.5848 22.4724Z"
      fill="white"
    />
    <rect
      x="18.7554"
      y="10.9935"
      width="2.82922"
      height="5.25427"
      rx="1.41461"
      transform="rotate(8 18.7554 10.9935)"
      fill="#2B3043"
    />
    <rect
      x="11.1509"
      y="9.92453"
      width="2.82922"
      height="5.25427"
      rx="1.41461"
      transform="rotate(8 11.1509 9.92453)"
      fill="#2B3043"
    />
    <defs>
      <linearGradient
        :id="linearId0"
        x1="10"
        y1="6.5"
        x2="24"
        y2="21.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#8F45FF" />
        <stop offset="0.504614" stop-color="#FF2A4E" />
        <stop offset="0.951402" stop-color="#2E67FF" />
      </linearGradient>
      <linearGradient
        :id="linearId1"
        x1="8.5"
        y1="6"
        x2="21.5"
        y2="22.5"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#8F45FF" />
        <stop offset="0.548289" stop-color="#00FFF7" />
        <stop offset="0.947462" stop-color="#2E67FF" />
      </linearGradient>
    </defs>
  </svg>
</template>
