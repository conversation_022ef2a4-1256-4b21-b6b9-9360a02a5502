<script lang="ts" setup>
import { agreeAgreement } from '@/services/request'
import dayjs from 'dayjs'
import { useAssistantState, useConfigState } from '@/stores'
import { Modal, Button } from '@arco-design/web-vue'
import { usePopupStore } from '@/assistant/stores'
import { renderMarkdown } from '@/utils/markdown'

const popupStore = usePopupStore()
const config = useConfigState()
const agreeLoading = ref(false)
const assistantState = useAssistantState()

const details = computed(() => {
  return renderMarkdown(config.dialogState.agreementContent?.content || '')
})
const modifiedTime = computed(() => {
  let date: string | any = config.dialogState.agreementContent?.modifiedTime || ''
  date = dayjs(date)
  if (date.isValid()) {
    date = date.format('YYYY-MM-DD HH:mm:ss')
  }
  return date
})
const visible = computed(() => {
  return config.dialogState.agreementContent.visible
})

const agree = () => {
  agreeLoading.value = true
  agreeAgreement({ context: assistantState.context ?? {} }).then(() => {
    agreeLoading.value = false
    config.dialogState.agreementContent.visible = false
    config.dialogState.agreement = false
  })
}
const close = () => {
  config.dialogState.agreementContent.visible = false
}
</script>

<template>
  <Modal
    :visible="visible"
    :mask-closable="false"
    width="90%"
    :render-to-body="false"
    title-align="start"
    modal-class="gpt-dialog_privacy-agreement"
    :popup-container="popupStore.container!"
    :body-style="{ padding: '0 20px' }"
    @cancel="close"
  >
    <template #title>
      <span class="gpt-dialog_privacy-agreement_title">使用助手前请先阅读并同意隐私协议</span>
    </template>
    <div class="gpt-privacy-agreement_content max-h-[calc(100vh-240px)] min-h-[200px]">
      <div class="gpt-privacy-agreement_title">明源云天际AI开发平台用户隐私协议</div>
      <div class="gpt-privacy-agreement_info">
        <span>发布日期：{{ modifiedTime }}</span>
        <!-- <span>版本号：</span> -->
      </div>
      <div class="gpt-privacy-agreement_details" v-html="details"></div>
    </div>
    <template #footer>
      <div class="gpt-privacy-agreement_footer">
        <Button @click="close" class="gpt-privacy-agreement_cancel">退出</Button>
        <Button
          type="primary"
          class="gpt-linear-gradient-btn"
          :loading="agreeLoading"
          @click="agree"
          >同意协议</Button
        >
      </div>
    </template>
  </Modal>
</template>

<style lang="less">
.gpt-dialog_privacy-agreement {
  border-radius: 12px;
  .gpt-dialog_privacy-agreement_title {
    color: #000000;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  .gpt-privacy-agreement_content {
    padding-top: 20px;
  }
  .gpt-privacy-agreement_title {
    height: 24px;
    color: #333333;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
  }
  .gpt-privacy-agreement_info {
    margin-top: 12px;
    color: #999999;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    > span {
      margin-right: 12px;
    }
  }
  .gpt-privacy-agreement_details {
    margin-top: 12px;
    margin-bottom: 12px;
    word-break: break-all;
  }
  .gpt-privacy-agreement_footer {
    height: 48px;
    padding: 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    button {
      margin: 0 6px;
      width: 80px;
      border-radius: 4px;
    }
  }
  .arco-modal-footer {
    padding: 0;
    border-top: 1px solid #eaeaea;
  }
  .gpt-privacy-agreement_cancel {
    background: transparent;
    border: 1px solid #dddddd;
  }
  .arco-modal-header {
    border-bottom: 1px solid #eaeaea;
  }
}
</style>
