<script setup lang="ts">
import { getViewValueByType } from '@/utils'
import type { FormColumn } from '@acme/core'
import { usePopupStore } from '@/assistant/stores'
import { useScroll } from '@/composables/useScroll'
import TableCellView from './TableCellView.ce.vue'
import EditorFormItem from './EditorFormItem.ce.vue'
import { Tooltip } from '@arco-design/web-vue'

const popupStore = usePopupStore()
interface TableColumnsType extends FormColumn {
  title: string
  dataIndex: string
  type: string
  readonly: boolean
  required: boolean
  thousands?: boolean
}
type Props = {
  isEdit: boolean
  columns: FormColumn[]
  name: string
  probMap: Record<string, number | undefined>[] | any
  required: boolean
  formData: Record<string, any>
  code: string
  isDone: boolean
}
const props = defineProps<Props>()
const columns: Ref<TableColumnsType[]> = ref([])
const data = computed(() => {
  return props.formData?.[props.code]
})
const tableInnerRef = useTemplateRef('tableInnerRef')
const { scrollToBottom, onListenScroll } = useScroll()
onMounted(() => {
  const col: TableColumnsType[] = []
  props.columns?.forEach((item) => {
    col.push({
      ...item,
      title: item.name,
      dataIndex: item.code,
    })
  })
  columns.value = col
  onListenScroll(tableInnerRef.value as HTMLElement)
})

watch(() => data.value, scrollToBottom)

const showEmpty = computed(() => {
  return props.isDone && !data.value?.length
})

const getCellClass = (code: string, index: number) => {
  const level = props.probMap?.[index]?.[code]
  if (level === 0) return 'gpt-prob-red'
  if (level === 1) return 'gpt-prob-warn'
  // if (level === 2) return 'gpt-prob-blue'
  return ''
}

const defaultRow = computed(() => {
  const row: Record<string, string> = {}
  columns.value.forEach((column) => {
    row[column.dataIndex] = ''
  })
  return row
})

const tableData = computed(() => {
  const { code, isDone } = props
  if (props.formData[code]?.length > 0 || isDone) {
    return props.formData[code] ?? []
  }
  return [defaultRow]
})

const getText = (column: TableColumnsType, rowIndex: number) => {
  const v = tableData.value[rowIndex]?.[column.dataIndex]
  if (props.isDone) return getViewValueByType(column, v)
  return v
}

const handleDeleteSubForm = (data: Record<string, any>[], index: number) => {
  if (!props.isEdit || !props.isDone) {
    return
  }
  data.splice(index, 1)
}
</script>

<template>
  <div class="gpt-data-table">
    <div class="gpt-data-table_title">{{ name }}</div>
    <div class="gpt-data-table_inner" ref="tableInnerRef">
      <table>
        <thead>
          <tr>
            <th v-for="(column, key) in columns" :key="'col' + key">
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in tableData" :key="'row' + rowIndex">
            <td v-for="(column, key) in columns" :key="'col' + key">
              <span
                v-if="isEdit"
                class="gpt-table-cell_prob"
                :class="getCellClass(column.dataIndex, rowIndex)"
              ></span>
              <template v-if="isEdit && !column.readonly">
                <EditorFormItem
                  :item="column"
                  :isEdit="isEdit"
                  :data="tableData[rowIndex]"
                  :field="`${code}[${rowIndex}].${column.dataIndex}`"
                  :isDone="isDone"
                  :auto-size="{
                    minRows: 1,
                    maxRows: 3,
                  }"
                  :hide-label="true"
                />
              </template>
              <TableCellView
                v-else
                :disabledWrap="['InputNumber', 'DatePicker'].includes(column.type)"
                :value="getText(column, rowIndex)"
                :isDone="isDone"
              />
            </td>
            <Tooltip content="删除" :popup-container="popupStore.container!">
              <span
                v-if="isEdit && isDone"
                @click="handleDeleteSubForm(tableData, rowIndex)"
                class="gpt-table-cell_delete"
              >
                <IGptTrash class="text-[12px]" />
              </span>
            </Tooltip>
          </tr>
          <tr v-if="showEmpty">
            <td :colspan="columns.length" class="text-center">
              <span
                class="inline-block px-[8px] py-[3px] text-[13px] font-normal not-italic leading-[22px] text-[#999999]"
                >暂无数据</span
              >
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style lang="less">
.ai-container {
  .gpt-data-table {
    padding: 0 12px 12px 12px;

    .gpt-data-table_title {
      color: #333333;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }

    .arco-picker,
    .arco-input-wrapper,
    .arco-textarea {
      padding: 3px 8px 3px 12px;
      border-radius: 4px;
      border: 1px solid #ddd;
      background: #fff;
      box-sizing: border-box;
      color: #333333;

      .arco-input {
        height: 22px;
        padding: 0;
      }
    }

    .arco-textarea-wrapper {
      display: inline;
    }

    .gpt-data-table_cell-view {
      display: inline-flex;
      padding: 4px 8px 4px 12px;
      color: #333333;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .gpt-data-table_inner {
    min-height: 40px;
    max-height: 350px;
    min-width: 100px;
    max-width: 100%;
    overflow: auto;
    margin-top: 12px;
    .arco-form-item {
      margin-bottom: 0;
    }
  }
  .gpt-data-table table {
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    background-color: #fff;
    width: 100%;

    tr + tr {
      border-top: 1px solid #e5e7eb;
    }

    td + td,
    th + th {
      border-left: 1px solid #e5e7eb;
    }

    thead tr {
      border-bottom: 1px solid #e5e7eb;
      background-color: #fbfbfb;

      th {
        padding: 7px 12px;
        min-width: 52px;
        max-width: 140px;
        color: #333333;
        font-size: 13px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        text-align: left;
      }
    }

    tr {
      position: relative;
      &:hover {
        .gpt-table-cell_delete {
          opacity: 1;
          transition: opacity 0.3s ease;
        }
      }
    }

    td {
      min-width: 52px;
      max-width: 140px;
      padding: 4px;
      position: relative;
    }
    .gpt-table-cell_delete {
      display: flex;
      align-items: center;
      justify-content: center;
      position: sticky;
      width: 16px;
      height: 16px;
      top: 0;
      right: 0;
      opacity: 0;
      border-bottom-left-radius: 4px;
      background-color: #c5c5c5;
      color: #fff;
      transition: opacity 0.3s ease;
      cursor: pointer;
      z-index: 2;
      svg {
        font-size: 12px;
      }
    }
  }
}
</style>
