<script lang="ts" setup>
import { useAssistantState } from '@/stores'

defineProps<{
  visible: boolean
}>()
const emit = defineEmits(['toBottom'])
const store = useAssistantState()
const handleToBottom = () => {
  emit('toBottom')
}
</script>

<template>
  <div v-if="visible" class="height-0 relative flex w-full items-center justify-center">
    <span
      @click="handleToBottom"
      class="absolute bottom-[16px] flex h-[36px] w-[36px] cursor-pointer items-center justify-center gap-[4px]
        rounded-[40px] border-[1px] border-solid border-[#EAEAEA] bg-[#FFF] px-[8px] py-[4px] text-[#999999]
        [box-shadow:0_4px_10px_0_#0000001a] hover:text-[#266EFF]"
    >
      <IGptLoadingRound
        v-if="store.isWaiting"
        class="absolute left-[0] top-[0] animate-spin rounded-full text-[36px]"
      />
      <IGptToBottom class="text-[18px]" />
    </span>
  </div>
</template>
