<script setup lang="ts">
import ToastApi from '@/utils/vue/toast'
import EditorFormItem from './EditorFormItem.ce.vue'
import { type FormColumn, type ProbMap, type ProbRule } from '@acme/core'
import { Form } from '@arco-design/web-vue'

type Props = {
  formData: Record<string, any>
  fields: FormColumn[]
  isEdit: boolean
  probMap: ProbMap
  probRules: ProbRule[]
  showProb: boolean
  isDone: boolean
}
const props = defineProps<Props>()
const emit = defineEmits(['input'])
const editorForm = ref<{
  validate: (call: Function) => Promise<undefined | Record<string, any>>
  resetFields: () => void
}>({ validate: () => new Promise(() => {}), resetFields: () => {} })
const collapsedItems: Ref<Record<string, boolean>> = ref({})

const probStyle = [
  {
    color: 'red',
    text: '高风险',
  },
  {
    color: 'warn',
    text: '中风险',
  },
  {
    color: 'blue',
    text: '无风险',
  },
]
const probList = computed(() => {
  if (!props.showProb) return []
  return props.probRules
    ?.filter((item) => item.maxProb !== 100)
    ?.map((item) => {
      return {
        ...(probStyle?.[item.priority] || {}),
        ...item,
      }
    })
})

const getValidate = (call: Function = () => {}) => {
  const subGrid = props.fields.filter(
    (item) => item.type === 'DataTable' && !item.readonly && item.required,
  )
  const item = subGrid.find(
    (item) => !props.formData[item.code] || !props.formData[item.code]?.length,
  )
  if (item && item.code) {
    ToastApi.show(`子列表[${item.name}]为必填`, 2000, 'warning')
    return new Promise((resolve) => resolve(true))
  }
  return editorForm.value?.validate(call)
}

const resetFields = () => {
  editorForm.value?.resetFields()
}

const onToggleGroup = (index: number, collapsed: boolean) => {
  props.fields.some((item, key) => {
    if (key > index) {
      if (item.type === 'GroupLine') return true
      collapsedItems.value[item.code] = collapsed
    }
    return false
  })
}

defineExpose({
  getValidate,
  resetFields,
})
</script>

<template>
  <div class="gpt-form-confidence" v-if="probList?.length">
    <span class="gpt-form-confidence_label">置信度：</span>
    <span
      v-for="(item, key) in probList"
      :key="'prob' + key"
      class="gpt-form-confidence_option"
      :class="'gpt-confidence_' + item.color"
      >{{ item.text }} ({{ item.minProb }}%~{{ item.maxProb }}%)</span
    >
  </div>
  <Form
    class="gpt-editor-form"
    ref="editorForm"
    :model="formData"
    :style="{ width: '100%' }"
    label-align="left"
    :label-col-props="{ span: 8, offset: 0 }"
    :wrapper-col-props="{ span: 16, offset: 0 }"
  >
    <EditorFormItem
      v-for="(item, index) in fields"
      :key="'field' + index"
      :item="item"
      :isEdit="isEdit"
      :showProb="showProb"
      :probList="probList"
      :probMap="probMap"
      :data="formData"
      :field="item.code"
      :isDone="isDone"
      :hidden="collapsedItems[item.code]"
      @toggleGroup="(collapsed: boolean) => onToggleGroup(index, collapsed)"
    />
  </Form>
</template>

<style lang="less">
@gpt-prop-red: #ff4c4c;
@gpt-prop-warn: #ff9902;
@gpt-prop-blue: #1cc78d;
.ai-container {
  .gpt-form-confidence {
    width: 100%;
    height: 36px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eaeaea;
    padding: 0 12px 0 0;

    > span {
      display: inline-block;
      margin-left: 12px;
      color: #333333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }

  .gpt-form-confidence_option {
    position: relative;

    &:before {
      content: ' ';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 2px;
      margin-right: 6px;
    }

    &.gpt-confidence_red:before {
      background-color: @gpt-prop-red;
    }

    &.gpt-confidence_warn:before {
      background-color: @gpt-prop-warn;
    }

    &.gpt-confidence_blue:before {
      background-color: @gpt-prop-blue;
    }
  }

  .gpt-editor-form {
    .arco-row {
      min-height: 32px;
      padding: 4px 12px 4px 20px;
      margin-bottom: 4px;
      position: relative;
      box-sizing: border-box;

      &[data-type='1'] .arco-form-item-content {
        border-color: #ddd;
      }

      &:before {
        content: ' ';
        display: inline-block;
        width: 2px;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
      }

      &.gpt-form-confidence_blue:before {
        background-color: @gpt-prop-blue;
      }

      &.gpt-form-confidence_red:before {
        background-color: @gpt-prop-red;
      }

      &.gpt-form-confidence_warn:before {
        background-color: @gpt-prop-warn;
      }

      &.gpt-form-confidence_blue:hover {
        background-color: rgba(28, 199, 141, 0.1);
        .arco-form-item-content {
          border-color: @gpt-prop-blue;
        }
      }

      &.gpt-form-confidence_red:hover {
        background-color: rgba(255, 76, 76, 0.1);
        .arco-form-item-content {
          border-color: @gpt-prop-red;
        }
      }

      &.gpt-form-confidence_warn:hover {
        background-color: rgba(255, 153, 2, 0.1);
        .arco-form-item-content {
          border-color: @gpt-prop-warn;
        }
      }
    }

    .gpt-form-item_view {
      padding-left: 12px;
      padding-right: 12px;
    }
    .gpt-form-item_view,
    .gpt-data-table_cell-view,
    .arco-textarea-mirror {
      max-height: 140px;
      overflow-y: auto;
    }
    // 换行
    .gpt-form-item_view,
    .gpt-data-table_cell-view {
      white-space: normal;
    }

    .gpt-form-item_disabled-wrap {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .arco-input-wrapper,
    .arco-picker,
    .arco-textarea {
      background-color: #fff;
      border-radius: 4px;
      box-sizing: border-box;
    }

    .arco-picker,
    .arco-textarea {
      padding: 4px 12px;
    }

    .arco-form-item-label {
      color: #666;
      position: relative;
      .arco-form-item-label-required-symbol {
        position: absolute;
        left: -12px;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
      }
    }

    .arco-form-item-content {
      border: 1px solid transparent;
      color: #333;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
    }
    .gpt-editor-form_readonly {
      .arco-form-item-content {
        border: 0;
      }
      .gpt-form-item_view {
        padding: 0;
      }
    }

    .arco-picker {
      width: 100%;
    }

    .gpt-data-table + .arco-row,
    .gpt-editor-form_sub-form + .arco-row {
      margin-top: 12px;

      &:after {
        content: '';
        display: inline-block;
        border-top: 1px dashed #ddd;
        position: absolute;
        left: 0;
        right: 0;
        top: -12px;
      }
    }

    .arco-form-item_radiogroup {
      .arco-form-item-content {
        border: 0;
      }
    }
    textarea {
      overflow: auto !important;
    }
  }

  .form-card-body-edit {
    min-width: 300px;
  }

  .gpt-data-table {
    .condidence-tips-style(@color) {
      position: absolute;
      border: 4px solid @color;
      border-left-color: transparent;
      border-bottom-color: transparent;
      right: 0;
      top: 0;
      &:hover {
        .arco-picker,
        .arco-input-wrapper,
        .arco-textarea {
          border-color: @color;
        }
      }
    }
    .arco-table-cell {
      position: relative;
    }
    .gpt-table-cell_prob {
      visibility: hidden;
      &.gpt-prob-red {
        visibility: visible;
        .condidence-tips-style(@gpt-prop-red);
      }
      &.gpt-prob-warn {
        visibility: visible;
        .condidence-tips-style(@gpt-prop-warn);
      }
      &.gpt-prob-blue {
        visibility: visible;
        .condidence-tips-style(@gpt-prop-blue);
      }
    }
  }

  // 处理单选按钮样式异常
  .arco-radio-group {
    .arco-radio-icon {
      border-width: 2px;
    }
  }
}
</style>
