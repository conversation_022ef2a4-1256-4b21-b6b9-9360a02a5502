<script setup lang="ts">
import type { AssistantSkill } from '@/modules'
import { useAssistantAction, useAssistantState, useConfigState } from '@/stores'
import { getRandomElements } from '@/utils/arrayExt'
import { IconRefresh } from '@arco-design/web-vue/es/icon'
import LogoIcon from '@/components/icons/LogoIcon.vue'
const state = useAssistantState()
const action = useAssistantAction()
const configState = useConfigState()
// const limit = 6
const dependency = ref(0)
const genSkillList = () => {
  // 改变依赖的值来强制计算属性重新计算
  dependency.value++
}
const limit = computed<number>(() => {
  return configState.mode === 'page' && window.innerWidth > 768 ? 6 : 4
})
const skillList = computed(() => {
  dependency.value
  return getRandomElements<AssistantSkill>(state.skillEnableList, limit.value)
})

const questionList = computed(() => {
  dependency.value
  return getRandomElements<AssistantSkill>(
    state.skillEnableList.filter((c) => c.question),
    5,
  )
})

const isLarge = computed(() => {
  return (configState.mode === 'page' ? window.innerWidth : configState.sideWidth) > 760
})

const selectSkillAndSendMessage = (skillId: string, message: string) => {
  action.selectSkillById(skillId, message || '')
}
</script>
<template>
  <div class="recommend-skill-card" v-if="state.skillEnableList.length > 0">
    <div class="recommend-skill-card-title">
      <div class="recommend-skill-card-title-text">你可以选择一个技能：</div>
      <div
        class="recommend-skill-card-title-btn"
        @click="genSkillList"
        v-show="state.skillEnableList.length > limit"
      >
        <IconRefresh size="18" />换一换
      </div>
    </div>
    <div
      class="recommend-skill-card-body"
      :class="{
        large: isLarge,
      }"
    >
      <div
        class="recommend-skill-item"
        v-for="item in skillList"
        @click="() => action.selectedRecommendSkillId(item.id)"
      >
        <div class="recommend-skill-item-title">
          <LogoIcon
            class="recommend-skill-item-title-icon text-[24px]"
            v-if="!item.icon"
            :size="24"
          />
          <span class="recommend-skill-item-title-icon" v-else>
            <img :src="item.icon" />
          </span>
          <span class="recommend-skill-item-title-name">{{ item.name }}</span>
        </div>
        <div class="recommend-skill-item-desc" :title="item.desc" v-if="item.desc">
          {{ item.desc }}
        </div>
      </div>
    </div>
  </div>
  <div class="recommend-skill-question" v-if="questionList.length > 0">
    <div class="recommend-skill-question-title">还可以这样问我：</div>
    <div class="recommend-skill-question-list">
      <p
        v-for="item in questionList"
        @click="selectSkillAndSendMessage(item.id, item.question || '')"
      >
        {{ item.question }}
      </p>
    </div>
  </div>
</template>
