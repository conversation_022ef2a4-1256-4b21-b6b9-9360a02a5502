<script lang="ts" setup>
import { StreamPipeline, type <PERSON><PERSON>ara<PERSON>, type MessageCardData } from '@acme/core'
import RenderTemplate from '../components/RenderTemplate.ce.vue'

const { data, pipeline = new StreamPipeline() } = defineProps<{
  data: MessageCardData
  replace?: { key: string; value: string }[]
  pipeline?: StreamPipeline
  messageId: string
}>()

const emit = defineEmits(['action'])

const onAction = ({ event }: { event: MouseEvent }) => {
  emit('action', { event })
}
const configMap = computed(() => {
  const res: Record<string, string> = {}
  data?.inputs?.forEach((item) => {
    if (item.value?.content) res[item.code] = item.value?.content
  })
  return res
})
const inputData = computed(() => {
  const res: Record<string, unknown> = {}
  data.inputs?.forEach((item) => {
    res[item.code] = item.literalValue || ''
  })
  return res
})
</script>

<template>
  <RenderTemplate
    :data="{ ...data, config: configMap, replace: replace || [] }"
    :pipeline="pipeline"
    :value="inputData"
    @action="onAction"
  />
</template>
