<script setup lang="ts">
import { useAssistantAction } from '@/stores'
const action = useAssistantAction()
type Props = {
  data: Array<string>
}

defineProps<Props>()
</script>
<template>
  <hr class="card-line" />
  <ul class="recommend-card">
    <li class="recommend-card-title">推荐提问:</li>
    <li
      v-for="item in data"
      class="recommend-card-item"
      :key="item"
      @click="action.sendMessage({ message: item })"
    >
      {{ item }}
    </li>
  </ul>
</template>

<style lang="less">
.recommend-card {
  display: block;
  padding: 0;
  margin: 10px 0;
  list-style-type: none;
}
.recommend-card-title {
  font-weight: bolder;
  list-style: none;
  line-height: 2em;
}
.recommend-card-item {
  list-style: none;
  cursor: pointer;
  color: #266eff;
  line-height: 1.5em;
}
</style>
