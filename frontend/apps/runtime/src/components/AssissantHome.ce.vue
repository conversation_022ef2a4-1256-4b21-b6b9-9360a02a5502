<script lang="ts" setup>
import { useAssistantState } from '@/stores'
import Home from './Home/Home.ce.vue'
import ChatPage from '@/components/ExtPage/ChatPage.vue'
import MessageLog from '@/components/MessageLog/index.ce.vue'
import ActionBar from '@/components/ActionBar.vue'
import MessageInput from '@/components/MessageInput.vue'
import MessageList from '@/components/MessageList/index.ce.vue'
import ToolBar from '@/components/Toolbar.ce.vue'
import { useConfigState } from '@/stores'
import ChatShareBar from './Chat/ChatShareBar.vue'

const config = useConfigState()
const assistant = useAssistantState()
</script>

<template>
  <div class="gpt-content gpt-asssistant-home">
    <Home v-if="!assistant.currentSkill" />

    <MessageList v-else />

    <div class="gpt-asssistant-footer" v-if="!config.isSharing">
      <ActionBar />
      <ToolBar />
      <MessageInput />
    </div>
    <ChatShareBar v-else class="absolute bottom-0 w-full" />

    <MessageLog />
    <ChatPage />
  </div>
</template>

<style lang="less">
.ai-container {
  &.gpt-side {
    .gpt-asssistant-home {
      padding-top: 48px;
    }
  }
  .gpt-asssistant-home {
    max-width: 960px;
    width: calc(100% - 248px);
    margin: 0 auto;
    background-image: none;
    background-color: transparent;
  }
}
</style>
