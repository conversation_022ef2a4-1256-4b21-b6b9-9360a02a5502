<script setup lang="ts">
import { useAssistantState, useConfigAction, useConfigState } from '@/stores'
import LogoIcon from '@/components/icons/LogoIcon.vue'

//主要功能在关闭按钮
const config = useConfigState()
const store = useAssistantState()
const configAction = useConfigAction()
const showPrivacy = computed(() => {
  const { agreement, agreementContent } = config.dialogState
  return agreement && !agreementContent.visible
})
const showClose = computed(() => {
  return config.params.mode === 'side' && showPrivacy.value
})
const visible = computed(() => {
  return config.params.mode === 'page' || config.params.mode === 'log' || showClose.value
})
const headerStyle = computed(() => {
  if (showPrivacy.value) return { height: '61px' }
  return {}
})
const onClose = () => {
  configAction.closeSidebar()
}
</script>

<template>
  <header class="header" :style="headerStyle">
    <template v-if="visible">
      <div class="left">
        <div class="flex min-w-[24px] items-center justify-center bg-cover">
          <LogoIcon v-if="!store.icon" class="icon" :size="24" />
          <img v-else :src="store.icon" class="icon" />
        </div>
        <div class="title">
          {{ store.name }}
          <span v-if="config.debugMode" class="debug-txt">[调试模式]</span>
        </div>
      </div>
    </template>
    <div class="right" v-if="showClose" @click="onClose">
      <div class="close-btn">
        <IGptClose class="text-[18px]" />
      </div>
    </div>
  </header>
</template>
