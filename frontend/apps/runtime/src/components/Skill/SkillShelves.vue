<script setup lang="ts">
import { useAssistantAction, useAssistantState, useSkillGuideAction } from '@/stores'
import { IconRight } from '@arco-design/web-vue/es/icon'
import SkillName from './SkillName.vue'
import SkillQuestions from './SkillQuestions.vue'
import SkillWelcome from './SkillWelcome.vue'
import MessageBox from '@/components/Message/MessageBox.vue'

const action = useAssistantAction()
const assistantState = useAssistantState()
const guideAction = useSkillGuideAction()

const skill = computed(() => assistantState.currentSkill)
const welcome = computed(() => skill.value?.welcome ?? '')

const questions = computed(() => skill.value?.questions ?? [])

const sendMessage = (message: string) => {
  action.sendMessage({ message })
}

const onMore = () => {
  const id = skill.value?.id
  if (!id) {
    return
  }
  guideAction.open(id)
}

const errorTips = computed(() => {
  let text = assistantState.currentSkill?.name
  text = text ? `【${text}】` : ''
  return `您没有${text}的使用权限，请联系系统管理员授权。`
})
</script>

<template>
  <div class="w-[93%] space-y-[8px]">
    <template v-if="assistantState.isEnabled">
      <MessageBox class="space-y-[8px] px-[16px] py-[12px]">
        <SkillName class="py-[6px]" :icon="skill?.icon" :name="skill?.name">
          <template #actions>
            <div
              class="inline-flex cursor-pointer items-center whitespace-nowrap py-2 text-[13px] text-[#999]
                hover:text-[#266EFF]"
              @click="onMore"
            >
              <span>使用指引</span><IconRight size="14" />
            </div>
          </template>
        </SkillName>
        <SkillWelcome v-if="welcome" :welcome="welcome" />
      </MessageBox>
      <SkillQuestions v-if="questions.length" :data="questions" @click="sendMessage" />
    </template>
    <template v-else>
      <MessageBox class="space-y-[8px] px-[16px] py-[12px]">
        <div class="message-content_error">
          <IGptWarningFill class="text-[16px] text-[#FF9902]" />
          {{ errorTips }}
        </div>
      </MessageBox>
    </template>
  </div>
</template>
