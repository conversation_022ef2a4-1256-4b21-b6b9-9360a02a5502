import { useConfigState } from '@/stores'
import { withFetchToken } from '@/utils/token'
import { withFetchSignature } from '@acme/core'

export const withAssistantFetchHeaders = (request: RequestInit) => {
  const store = useConfigState()

  withFetchToken(request, {
    isRelease: store.isRelease,
    accessToken: store.accessToken,
    token: store.params.authToken,
    applicationCode: store.applicationCode,
    releaseCode: store.releaseCode,
  })

  withFetchSignature(request)

  return request
}
