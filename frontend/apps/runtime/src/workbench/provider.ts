import type { ConfigParams } from '@/modules'
import { WorkbenchConfig } from './config'
import { createWorkbenchElement } from './element'

type WorkbenchProviderOptions = {
  name: string
  params: ConfigParams
}

// 工作台类
export class WorkbenchProvider {
  name: string
  config: WorkbenchConfig

  constructor({ name, params }: WorkbenchProviderOptions) {
    this.name = name

    this.config = new WorkbenchConfig({ params })
  }

  // 渲染 WebComponent 组件
  render() {
    return createWorkbenchElement({
      name: this.name,
      props: {
        config: this.config,
      },
    })
  }
}
