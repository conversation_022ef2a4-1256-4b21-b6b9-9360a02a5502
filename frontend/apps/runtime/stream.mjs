import MarkdownIt from 'markdown-it'

// const md = new MarkdownIt()

// md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
//   //   const hrefIndex = tokens[idx].attrIndex('href');
//   //   const href = tokens[idx].attrs[hrefIndex][1];

//   // 你可以在这里添加额外的逻辑来决定是否应该添加target="_blank"
//   // 例如，检查链接是否是指向外部域名等

//   // 修改tokens来添加target属性
//   tokens[idx].attrPush(['target', '_blank'])

//   // 调用默认的渲染器
//   return self.renderToken(tokens, idx, options)
// }

// 自定义渲染函数，用于检查链接的完整性
function customLinkRenderer(tokens, idx, options, env, self) {
  const token = tokens[idx]
  const href = token.attrGet('href')
  console.log(token)
  // 检查链接是否完整（这里只是一个简单的示例，你可以根据需要添加更复杂的逻辑）
  if (!/^https?:\/\//.test(href) && token.children) {
    // 如果链接不完整，可以选择不渲染它（例如，只渲染链接文本）

    return self.renderInlineAsText(token.children, options)
  }

  // 如果链接完整，则使用默认的渲染逻辑
  return self.renderInlineAsText(tokens, idx, options)
}

// 初始化 markdown-it 实例并覆盖默认的 link_open 和 link_close 渲染规则
const md = new MarkdownIt('commonmark', { html: true })
md.renderer.rules.text = customLinkRenderer
md.renderer.rules.link_close = function () {
  return ''
}

// 使用 markdown-it 实例进行渲染
const result = md.render('[点击这里查看示例](https://example.co)') // 正常渲染
const incompleteResult = md.render('[点击这里查看示例](example.co') // 只渲染链接文本

console.log(result)
console.log(incompleteResult)
