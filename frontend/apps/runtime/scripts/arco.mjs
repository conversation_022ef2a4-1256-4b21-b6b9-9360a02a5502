import fs from 'node:fs'
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const triggerFilename = 'node_modules/@arco-design/web-vue/es/trigger/trigger.js'

const triggerFilepath = resolve(__dirname, '..', triggerFilename)

const code = fs.readFileSync(triggerFilepath, 'utf-8')

const before = {
  keywords: `if (((_a = firstElement.value) == null ? void 0 : _a.contains(e.target)) || ((_b = popupRef.value) == null ? void 0 : _b.contains(e.target))) {
        return;
      }`,
  position: 0,
  index: -1,
}

const after = {
  keywords: `      for (const item of childrenRefs) {`,
  position: 0,
  index: -1,
}

before.index = code.indexOf(before.keywords)
if (before.index > -1) {
  before.position = before.index + before.keywords.length
}

after.index = code.indexOf(after.keywords)
if (after.index > -1) {
  after.position = after.index
}

const patch = `
// clickoutside patch
var path = e.path
if (typeof e.composedPath === 'function') {
  path = e.composedPath()
}
if (Array.isArray(path)) {
   if (path.some((c) => c && c.nodeType && _b.contains(c))) {
     return
   }
}
`

const err = new Error('aroc 组件库已更新，需重新设计 clickoutside 修复方案')

if (
  before.position > 0 &&
  after.position > 0 &&
  before.position <= after.position
) {
  const x = code.substring(before.position, after.position).trim()
  const c = !x.includes('e.composedPath()')
  if (!x || c) {
    // 未插入
    const bf = code.substring(0, before.position)
    const af = code.substring(after.position)

    const newCode = bf + patch + af
    fs.writeFileSync(triggerFilepath, newCode)
  } else if (x.includes('clickoutside patch')) {
    console.log('已应用 clickoutside 补丁')
  } else {
    throw err
  }
} else {
  throw err
}
