import typography from '@tailwindcss/typography'
import { type Config } from 'tailwindcss'
import animate from 'tailwindcss-animate'

const round = (num: number) =>
  num
    .toFixed(7)
    .replace(/(\.[0-9]+?)0+$/, '$1')
    .replace(/\.0$/, '')
const rem = (px: number) => `${round(px / 16)}rem`

export default {
  darkMode: ['class'],
  safelist: ['dark'],
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        danger: '#ff4c4c',
      },
      typography: {
        xs: {
          css: {
            // 段落字体大小
            fontSize: rem(13),
            lineHeight: round(22 / 13),
          },
        },
      },
    },
  },
  plugins: [animate, typography],
  corePlugins: {
    preflight: false,
    container: false,
  },
} satisfies Config
