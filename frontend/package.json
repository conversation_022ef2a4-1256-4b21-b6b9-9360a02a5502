{"name": "frontend", "version": "1.0.0", "private": true, "license": "ISC", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "format": "npx prettier --write --cache .", "preinstall": "npx -y only-allow pnpm", "lint": "biome lint", "prepare": "cd .. && husky frontend/.husky", "release": "lerna <PERSON>", "release:next": "lerna publish --dist-tag next", "release:next:retry": "lerna publish from-package --dist-tag next", "release:retry": "lerna publish from-package", "test": "turbo run test"}, "lint-staged": {"*.{ts,tsx,vue}": "npx prettier --write --cache"}, "devDependencies": {"@acme/card": "workspace:^", "@acme/vite-plugin-inject-js": "workspace:^", "@biomejs/biome": "^1.9.4", "@skyline/ai": "workspace:^", "@types/bun": "~1.1.13", "@types/node": "^22.13.5", "bun": "~1.1.45", "husky": "^9.1.7", "lerna": "~8.1.9", "lint-staged": "^15.4.3", "prettier": "^3.5.2", "prettier-plugin-classnames": "^0.7.6", "prettier-plugin-merge": "^0.7.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.8", "prettier-plugin-tailwindcss": "^0.6.11", "tsup": "^8.4.0", "turbo": "^2.4.4", "typescript": "^5.7.3"}, "packageManager": "pnpm@8.15.9", "engines": {"node": ">= 16.0.0"}, "pnpm": {"neverBuiltDependencies": ["canvas"], "allowedDeprecatedVersions": {"@humanwhocodes/config-array": "0.11.14 || 0.13.0", "@humanwhocodes/object-schema": "2.0.3", "are-we-there-yet": "2.0.0", "eslint": "^7.32.0 || ^8.57.1", "gauge": "3.0.2", "glob": "7.2.3", "inflight": "1.0.6", "npmlog": "5.0.1", "resolve-url": "0.2.1", "rimraf": "3.0.2", "source-map-resolve": "0.5.3", "source-map-url": "0.4.1", "stable": "0.1.8", "urix": "0.1.0"}, "overrides": {"fabric>jsdom": "^24.0.0", "mkdist>esbuild": "^0.25.0", "tsup>esbuild": "^0.25.0", "unbuild>esbuild": "^0.25.0", "vite-plugin-mock-dev-server>esbuild": "^0.25.0", "vite>esbuild": "^0.25.0"}, "packageExtensions": {"fabric": {"peerDependencies": {"canvas": "^2.11.2", "jsdom": "^24.1.1"}, "peerDependenciesMeta": {"canvas": {"optional": true}, "jsdom": {"optional": true}}}}}}