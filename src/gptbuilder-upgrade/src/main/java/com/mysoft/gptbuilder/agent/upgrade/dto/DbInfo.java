package com.mysoft.gptbuilder.agent.upgrade.dto;


public class DbInfo {
    /**
     * 数据库id
     */
    private String id;
    /**
     * 数据库类型
     */
    private String type;
    /**
     * 数据库地址
     */

    private String host;
    /**
     * 数据库端口
     */

    private Integer port;
    /**
     * 数据库名称
     */

    private String dbName;
    /**
     * 数据库密码
     */

    private String password;
    /**
     * 数据库用户名
     */

    private String username;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public DbInfo() {
    }

    public DbInfo(String id, String type, String host, Integer port, String dbName, String password, String username) {
        this.id = id;
        this.type = type;
        this.host = host;
        this.port = port;
        this.dbName = dbName;
        this.password = password;
        this.username = username;
    }

    @Override
    public String toString() {
        return "DbInfo{" +
                "id='" + id + '\'' +
                ", type='" + type + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", dbName='" + dbName + '\'' +
                ", password='" + password + '\'' +
                ", username='" + username + '\'' +
                '}';
    }
}
