<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mysoft.gptbuilder.agent.service.dao.eval.EvalTaskRecordDao">

    <update id="updateRunCountByGuid" parameterType="java.lang.String">
        UPDATE gpt_evaltaskrecord
        SET RunCount = RunCount + 1
        WHERE EvalTaskRecordGUID = #{guid}
    </update>

</mapper>