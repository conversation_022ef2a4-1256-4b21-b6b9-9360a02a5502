package com.mysoft.gptbuilder.agent.service.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.gptbuilder.agent.model.dto.plugin.ApiParam;
import com.mysoft.gptbuilder.agent.model.dto.plugin.PluginApiInfoDto;
import com.mysoft.gptbuilder.agent.model.dto.plugin.PluginInfoDto;
import io.swagger.v3.core.util.Yaml;
import io.swagger.v3.oas.models.*;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.*;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.parser.OpenAPIV3Parser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OpenApiConvertApiInfo {
    OpenAPI openAPI;

    List<String> saveSchemaRef = new ArrayList<>();

    static String paramHideExtensionKey = "x-param-hide";

    static String paramAttachmentExtensionKey = "x-param-attachment";

    static String pathItemIdkey = "x-path-item-id";

    Map<String, Integer> reuseSchemaRef = new HashMap<>();

    public OpenApiConvertApiInfo(OpenAPI openAPI) {
        this.openAPI = openAPI;
    }


    /**
     * 根据给定的 YAML 字符串构建 OpenApiConvertApiInfo 实例。
     *
     * @param yaml 表示 OpenAPI 定义的 YAML 字符串。
     * @return 返回一个包含解析后的 OpenAPI 信息的 OpenApiConvertApiInfo 实例。
     */
    public static OpenApiConvertApiInfo build(String yaml) {
        // 从 YAML 字符串解析 OpenAPI 对象
        OpenAPI openAPI = new OpenAPIV3Parser().readContents(yaml, null, null).getOpenAPI();
        return new OpenApiConvertApiInfo(openAPI);
    }

    /**
     * 获取API列表信息。
     * 该方法不接受任何参数，遍历openAPI中的路径，为每个路径上的操作生成一个PluginApiInfoDto对象，并收集到一个列表中返回。
     *
     * @return List<PluginApiInfoDto> 返回一个包含所有API信息的列表。
     */
    public List<PluginApiInfoDto> getApiList() {
        // 初始化结果列表
        List<PluginApiInfoDto> result = new ArrayList<>();
        // 遍历openAPI中的路径
        if (this.openAPI == null || this.openAPI.getPaths() == null) {
            return result;
        }
        this.openAPI.getPaths().forEach((k, v) -> {
            // 获取当前路径的操作
            Operation operation = getMethod(v);
            // 将路径和操作信息转换为PluginApiInfoDto对象
            if (operation != null) {
                PluginApiInfoDto dto = convertApiInfo(k, operation);
                // 将转换后的API信息添加到结果列表中
                result.add(dto);
            }
        });
        return result;
    }


    /**
     * 获取指定插件API的信息列表。
     *
     * @param codes API代码的列表，用于筛选特定的API。
     * @return PluginApiInfoDto列表，包含了匹配的API的详细信息。
     */
    public List<PluginApiInfoDto> getPluginApiInfo(List<String> codes) {
        List<PluginApiInfoDto> result = new ArrayList<>();
        // 遍历所有路径，查找与给定代码列表匹配的API
        this.openAPI.getPaths().forEach((k, v) -> {
            if (codes.contains(k)) {
                // 获取对应路径的operation对象
                PluginApiInfoDto dto = convertOperation(k, v);
                if (dto != null) {
                    result.add(dto);
                }
            }
        });
        return result;
    }

    /**
     * 获取插件的API信息列表。
     * 此方法会遍历openAPI中的所有路径，并为每个路径上的操作生成一个PluginApiInfoDto对象，最后将这些对象添加到结果列表中返回。
     *
     * @return List<PluginApiInfoDto> 包含所有插件API信息的列表。
     */
    public List<PluginApiInfoDto> getPluginApiInfo() {
        // 初始化结果列表
        List<PluginApiInfoDto> result = new ArrayList<>();
        // 遍历openAPI中的所有路径
        this.openAPI.getPaths().forEach((k, v) -> {
            // 获取当前路径的操作
            PluginApiInfoDto dto = convertOperation(k, v);
            if (dto != null) {
                result.add(dto);
            }
        });
        return result;
    }

    private PluginApiInfoDto convertOperation(String path, PathItem pathItem) {
        Operation operation = getMethod(pathItem);
        if (operation != null) {
            // 将路径和操作转换为PluginApiInfoDto对象
            PluginApiInfoDto dto = convertApiInfo(path, operation);
            // 转换操作中的参数信息到PluginApiInfoDto中
            convertParams(operation, dto, pathItem.getParameters());
            // 转换操作的响应信息到PluginApiInfoDto中
            convertResponse(operation, dto);
            // 转换API的元数据
            convertMetadata(path, pathItem, dto);
            // 将转换后的信息添加到结果列表中
            return dto;
        }
        return null;
    }

    /**
     * 获取插件的第一个API信息。
     * 此方法会遍历OpenAPI定义中的路径，获取第一个路径项，并尝试获取其对应的操作方法信息。
     * 如果找到了操作方法，则会填充API信息、参数和响应到PluginApiInfoDto对象中并返回。
     * 如果未找到操作方法，则返回一个空的PluginApiInfoDto对象。
     *
     * @return PluginApiInfoDto 包含API信息的对象，如果未找到操作方法则为空对象。
     */
    public PluginApiInfoDto getPluginFirstApiInfo() {
        // 获取OpenAPI定义中的第一个路径项
        Map.Entry<String, PathItem> firstEntry = this.openAPI.getPaths().entrySet().iterator().next();
        // 尝试获取路径项对应的操作方法
        Operation operation = getMethod(firstEntry.getValue());
        // 如果未找到操作方法，返回空的PluginApiInfoDto对象
        if (operation == null) {
            return new PluginApiInfoDto();
        }

        // 将路径和操作方法信息转换为PluginApiInfoDto对象
        PluginApiInfoDto dto = convertApiInfo(firstEntry.getKey(), operation);
        // 转换操作方法的参数信息到PluginApiInfoDto对象
        convertParams(operation, dto, null);
        // 转换操作方法的响应信息到PluginApiInfoDto对象
        convertResponse(operation, dto);
        return dto;
    }

    /**
     * 根据给定的 PathItem 对象，获取其第一个非空的方法操作。
     * 该方法按顺序检查 POST、GET、PUT、DELETE 和 PATCH 方法，
     * 并返回第一个找到的非空方法操作。
     *
     * @param pathItem 代表 API 路径项的 PathItem 对象。
     * @return 返回找到的第一个非空的方法操作；如果没有找到任何非空方法操作，则返回 null。
     */
    private static Operation getMethod(PathItem pathItem) {
        // 顺序检查 PathItem 对象中的 POST、GET、PUT、DELETE 和 PATCH 方法，返回第一个非空的方法操作
        if (pathItem.getPost() != null) {
            return pathItem.getPost();
        } else if (pathItem.getGet() != null) {
            return pathItem.getGet();
        } else if (pathItem.getPut() != null) {
            return pathItem.getPut();
        } else if (pathItem.getDelete() != null) {
            return pathItem.getDelete();
        } else if (pathItem.getPatch() != null) {
            return pathItem.getPatch();
        }
        // 如果所有方法都为空，则返回 null
        return null;
    }


    /**
     * 将给定的代码和操作信息转换为插件API信息DTO。
     *
     * @param path      API的唯一标识代码。
     * @param operation 包含API操作详情的对象，如摘要和描述。
     * @return 返回一个PluginApiInfoDto对象，包含API的代码、名称、描述。
     */
    private PluginApiInfoDto convertApiInfo(String path, Operation operation) {
        Object code = path;
        if (operation.getExtensions() != null && operation.getExtensions().get(pathItemIdkey) != null) {
            code = operation.getExtensions().get(pathItemIdkey);
        }
        PluginApiInfoDto dto = new PluginApiInfoDto();
        dto.setCode(code.toString()); // 设置API的代码
        dto.setPath(path);
        dto.setOperationId(operation.getOperationId());
        dto.setName(StringUtils.isBlank(operation.getSummary()) ? path : operation.getSummary()); // 设置API的名称
        dto.setDescribe(operation.getDescription()); // 设置API的描述
        return dto;
    }

    /**
     * 将操作参数转换为插件API信息DTO。
     * 该方法负责处理两种情况：如果有请求体（requestBody），则将其内容转换为DTO的参数；如果没有请求体，但有参数列表，则直接将参数添加到DTO中。
     *
     * @param operation 包含API操作信息的对象，可能包含请求体或参数列表。
     * @param dto       用于收集和存储转换后的API参数信息的DTO对象。
     */
    private void convertParams(Operation operation, PluginApiInfoDto dto,List<Parameter> parameters) {
        List<ApiParam> params = new ArrayList<>();
        // 如果操作包含请求体，则遍历请求体中的内容，并尝试将其转换为DTO的参数
        if (operation.getRequestBody() != null) {
            operation.getRequestBody().getContent().forEach((k1, v1) -> {
                Schema<?> schema = getRefSchema(v1.getSchema());
                // 如果schema是对象类型，则进行转换
                if (schema instanceof ObjectSchema) {
                    v1.setSchema(schema);
                    // 转换对象schema，并更新DTO的参数
                    List<ApiParam> apiParams = objectSchemaConvert((ObjectSchema) schema);
                    apiParams.forEach(f->{ f.setParamType("body"); });
                    params.addAll(apiParams);
                }
            });
        }
        // 如果操作不包含请求体，但包含参数列表，则直接将参数添加到DTO
        if(!Objects.isNull(parameters)){
            List<ApiParam> apiParams = parametersConvert(parameters);
            params.addAll(apiParams);
        }
        if(CollectionUtils.isEmpty(dto.getParams()) && operation.getParameters() != null){
            List<ApiParam> apiParams = parametersConvert(operation.getParameters());
            params.addAll(apiParams);
        }
        dto.setParams(params);
    }

    /**
     * 将操作响应转换为插件API信息。
     * 该方法主要解析操作可能的响应，聚焦于200响应码的内容，提取出数据结构，并将其存储在PluginApiInfoDto中。
     *
     * @param operation 指定的操作，包含API的响应信息。
     * @param dto       用于收集和存储API信息的目标对象。
     */
    private void convertResponse(Operation operation, PluginApiInfoDto dto) {
        // 检查操作是否有响应信息
        if (operation.getResponses() != null) {
            // 获取200响应码的内容
            Content content = operation.getResponses().get("200").getContent();
            // 尝试获取媒体类型
            MediaType mediaType = getMediaType(content);
            // 如果没有找到媒体类型，直接返回
            if (mediaType == null) {
                return;
            }
            dto.setContentType(content.keySet().toArray()[0].toString());
            // 获取媒体类型的Schema
            Schema<?> schema = getRefSchema(mediaType.getSchema());
            mediaType.setSchema(schema);
            // 判断schema是否为ObjectSchema类型
            if (schema instanceof ObjectSchema && schema.getProperties() != null) {
                ObjectSchema objectSchema = (ObjectSchema) schema;
                // 尝试获取并解析"data"属性的引用Schema
                Schema<?> refSchema = objectSchema.getProperties().get("data");
                if (refSchema != null) {
                    refSchema = getRefSchema(refSchema);
                } else {
                    refSchema = objectSchema;
                }
                // 如果解析出的引用Schema是ObjectSchema类型，则将其转换并存储在dto中
                if (refSchema instanceof ObjectSchema) {
                    objectSchema = (ObjectSchema) refSchema;
                    dto.setResults(objectSchemaConvert(objectSchema));
                } else if (refSchema instanceof ArraySchema) {
                    ArraySchema arraySchema = (ArraySchema) refSchema;
                    ApiParam apiParam = new ApiParam();
                    apiParam.setName("body"); // 设置参数名称
                    apiParam.setDescribe(getDescribe(refSchema, "返回结果")); // 设置参数描述
                    apiParam.setType(arraySchema.getType());
                    apiParam.setAttachment(getParamIsAttachment(arraySchema.getExtensions()));
                    apiParam.setChild(arraySchemaConvert(arraySchema));
                    apiParam.setShow(true);
                    dto.setResults(Arrays.asList(apiParam));
                }else {
                    // 如果不是ObjectSchema类型，则将该schema作为其他schema处理并添加到dto
                    dto.setResults(Arrays.asList(addOtherSchema(refSchema)));
                }
            } else {
                // 如果schema不是ObjectSchema类型，则直接将其作为其他schema处理并添加到dto
                dto.setResults(Arrays.asList(addOtherSchema(mediaType.getSchema())));
            }
        }
    }

    /**
     * 获取引用的 Schema 对象。
     * 该方法用于解析一个 Schema 对象，如果该对象是一个引用（即具有 "$ref" 属性），则尝试从 OpenAPI 组件中获取对应的引用 Schema。
     * 如果给定的 Schema 为 null 或不是一个引用，直接返回原 Schema。
     *
     * @param schema 待解析的 Schema 对象。
     * @return 如果找到引用的 Schema，则返回引用的 Schema 对象；如果没有找到或输入为 null，则返回原 Schema 或 null。
     */
    private Schema<?> getRefSchema(Schema<?> schema, Map<String, Integer> reuseSchemaRef) {
        // 检查输入 Schema 是否为 null
        if (schema == null) {
            return null;
        }
        // 尝试获取 "$ref" 属性，判断是否为引用
        String ref = schema.get$ref();
        // 如果 "$ref" 属性为空或格式不正确，则认为不是引用，返回原 Schema
        if (StringUtils.isBlank(ref)) {
            return schema;
        }
        // 从 "$ref" 属性中提取引用的 Schema 名称
        String schemaName = ref.substring(ref.lastIndexOf("/") + 1);
        if (this.openAPI.getComponents() == null || this.openAPI.getComponents().getSchemas() == null){
            return schema;
        }
        // 尝试从 OpenAPI 组件的 Schema 集合中获取引用的 Schema
        Schema<?> refSchema = this.openAPI.getComponents().getSchemas().get(schemaName);
        // 如果找到引用的 Schema，则返回之；否则返回原 Schema
        if (refSchema != null) {
            saveSchemaRef.add(schemaName);

            // 若存在复用，拷贝一份，并外部修改引用关系，避免覆盖
            if (reuseSchemaRef != null) {
                if (reuseSchemaRef.containsKey(schemaName)) {
                    Schema<?> newSchema = new ObjectSchema();
                    BeanUtils.copyProperties(refSchema, newSchema);
                    Integer nextIndex = reuseSchemaRef.get(schemaName) + 1;
                    String newSchemaName = schemaName + "$" + nextIndex;
                    // 增加新引用
                    this.openAPI.getComponents().getSchemas().put(newSchemaName, newSchema);
                    // 更新外部引用
                    schema.set$ref("#/components/schemas/" + newSchemaName);
                    reuseSchemaRef.put(schemaName, nextIndex);
                    return newSchema;
                } else {
                    reuseSchemaRef.put(schemaName, 0);
                }
            }
            return refSchema;
        }
        return schema;
    }

    private Schema<?> getRefSchema(Schema<?> schema) {
        return getRefSchema(schema, null);
    }


    /**
     * 获取给定内容的媒体类型。
     * 该方法首先尝试获取指定为"application/json"的媒体类型，如果不存在，则尝试获取通配符类型"
     *//*"，
     * 如果仍然不存在，则获取内容中任意一个键值对的值作为媒体类型。最后，检查获取到的媒体类型的架构是否为空，
     * 如果为空，则返回null；否则，返回该媒体类型。
     *
     * @param content 包含媒体类型信息的内容映射。键为媒体类型字符串，值为对应的媒体类型对象。
     * @return 媒体类型对象，如果无法获取有效的媒体类型则返回null。
     */
    private static MediaType getMediaType(Content content) {
        // 检查内容是否为null
        if (content == null) {
            return null;
        }
        // 尝试获取"application/json"的媒体类型
        MediaType mediaType = content.get("application/json");
        // 如果未获取到，尝试获取通配符类型"*/*"的媒体类型
        if (mediaType == null) {
            mediaType = content.get("*/*");
        }
        // 如果仍未获取到，获取内容中任意一个键值对的值作为媒体类型
        if (mediaType == null) {
            mediaType = content.entrySet().iterator().next().getValue();
        }
        // 检查获取到的媒体类型的架构是否为空
        if (mediaType.getSchema() == null) {
            return null;
        }
        return mediaType;
    }

    /**
     * 向给定的PluginApiInfoDto对象添加其他架构信息。
     *
     * @param schema 用于获取API参数名称和描述的Schema对象。如果为空，则不进行任何操作。
     */
    private ApiParam addOtherSchema(Schema<?> schema) {
        if (schema == null) {
            return null;
        }
        List<ApiParam> apiParams = new ArrayList<>();
        ApiParam apiParam = new ApiParam();
        if (StringUtils.isBlank(schema.getName())) {
            apiParam.setName("output");
            apiParam.setDescribe(getDescribe(schema, "返回内容"));
            apiParam.setType(schema.getType());
        } else {
            apiParam.setName(schema.getName());
            apiParam.setDescribe(getDescribe(schema,null));
            apiParam.setType(schema.getType());
        }
        apiParam.setShow(true);
        apiParam.setAttachment(getParamIsAttachment(schema.getExtensions()));
        apiParams.add(apiParam);
        return apiParam;
    }


    /**
     * 将给定的路径和路径项转换为OpenAPI元数据，并将其存储在DTO中。
     *
     * @param path     指定的路径字符串。
     * @param pathItem 与路径相关联的路径项对象。
     * @param dto      用于存储转换后OpenAPI元数据的DTO对象。
     */
    private void convertMetadata(String path, PathItem pathItem, PluginApiInfoDto dto) {
        OpenAPI currentOpenAPI = new OpenAPI();
        // 将路径和路径项添加到OpenAPI对象中
        currentOpenAPI.path(path, pathItem);
        List<String> uniqueItemsList = this.saveSchemaRef.stream()
                .distinct()
                .collect(Collectors.toList());

        Components components = new Components();
        components.setSchemas(new HashMap<>());
        currentOpenAPI.setComponents(components);

        if (this.openAPI.getComponents() != null && this.openAPI.getComponents().getSchemas() != null) {
            uniqueItemsList.stream().forEach(ref -> {
                Schema<?> refSchema = this.openAPI.getComponents().getSchemas().get(ref);
                components.getSchemas().put(ref, refSchema);
            });
        }


        try {
            // 将OpenAPI对象转换为YAML字符串
            String yaml = Yaml.pretty().writeValueAsString(currentOpenAPI);
            // 将YAML字符串存储在DTO中
            dto.setMetaData(yaml);
        } catch (JsonProcessingException e) {
            // 处理JSON处理异常，打印堆栈跟踪
            e.printStackTrace();
        }
    }


    /**
     * 将对象模式（ObjectSchema）转换为API参数列表（List<ApiParam>）。
     *
     * @param objectSchema 包含属性及其描述的对象模式。
     * @return 转换后的API参数列表，每个参数包含名称和描述。
     */
    private List<ApiParam> objectSchemaConvert(ObjectSchema objectSchema) {
        // 初始化API参数列表
        List<String> required = objectSchema.getRequired() == null ? Collections.emptyList() : objectSchema.getRequired();

        // 遍历对象模式中的每个属性，创建并添加ApiParam实例
        if (StringUtils.isNotBlank(objectSchema.get$ref())) {
            Schema<?> schema = getRefSchema(objectSchema);
            return propertiesConvert(schema.getProperties(), required);
        } else {
            return propertiesConvert(objectSchema.getProperties(), required);
        }
    }

    private List<ApiParam> propertiesConvert(Map<String, Schema> properties, List<String> required) {
        List<ApiParam> apiParams = new ArrayList<>();
        if (properties == null) {
            return apiParams;
        }

        properties.forEach((k1, v1) -> {
            ApiParam apiParam = new ApiParam();
            apiParam.setType(v1.getType());
            apiParam.setName(k1); // 设置参数名称
            apiParam.setDescribe(getDescribe(v1, null)); // 设置参数描述
            apiParam.setRequired(required.contains(k1));
            apiParam.setShow(getParamIsShow(v1.getExtensions()));
            apiParam.setAttachment(getParamIsAttachment(v1.getExtensions()));
            if (v1 instanceof ArraySchema) {
                apiParam.setChild(arraySchemaConvert((ArraySchema) v1));
            } else if (v1 instanceof ObjectSchema) {
                apiParam.setChild(objectSchemaConvert((ObjectSchema)v1));
            } else if (v1.getType() == null && StringUtils.isNotBlank(v1.get$ref())) {
                Schema<?> refSchema = getRefSchema(v1);
                if (refSchema instanceof ObjectSchema) {
                    apiParam.setChild(objectSchemaConvert((ObjectSchema)refSchema));
                    apiParam.setType(refSchema.getType());
                    apiParam.setDescribe(getDescribe(refSchema, null));
                    apiParam.setAttachment(getParamIsAttachment(refSchema.getExtensions()));
                    apiParam.setShow(getParamIsShow(refSchema.getExtensions()));
                }
            }

            apiParams.add(apiParam);
        });
        return apiParams;
    }

    private String getDescribe(Schema schema, String defaultDescribe) {
        if (StringUtils.isNotBlank(schema.getTitle())) {
            return schema.getTitle();
        }else if (StringUtils.isNotBlank(schema.getDescription())) {
            return schema.getDescription();
        }
        return defaultDescribe;
    }

    /**
     * 将对象模式（ObjectSchema）转换为API参数列表（List<ApiParam>）。
     *
     * @return 转换后的API参数列表，每个参数包含名称和描述。
     */
    private List<ApiParam> arraySchemaConvert(ArraySchema arraySchema) {
        Schema<?> schema = arraySchema.getItems();
        if (StringUtils.isNotBlank(schema.get$ref())) {
            Schema<?> refSchema = getRefSchema(schema);
            if (refSchema instanceof ArraySchema) {
                return arraySchemaConvert((ArraySchema) refSchema);
            }else if (refSchema instanceof ObjectSchema) {
                return objectSchemaConvert((ObjectSchema)refSchema);
            }
        } else if (schema instanceof ObjectSchema) {
            return objectSchemaConvert((ObjectSchema) schema);
        }
        return Arrays.asList(addOtherSchema(schema));
    }

    private List<ApiParam> parametersConvert(List<Parameter> parameters) {
        // 初始化API参数列表
        List<ApiParam> apiParams = new ArrayList<>();
        parameters.forEach(v1 -> {
            String type = "";
            String describe = v1.getDescription();
            if (v1.getSchema() != null) {
                type = v1.getSchema().getType();
                if (StringUtils.isBlank(describe)) {
                    describe = getDescribe(v1.getSchema(), null);
                }
            }
            ApiParam apiParam = new ApiParam();
            apiParam.setType(type);
            apiParam.setName(v1.getName());
            apiParam.setRequired(v1.getRequired() != null && v1.getRequired());
            apiParam.setShow(getParamIsShow(v1.getExtensions()));
            apiParam.setDescribe(describe);
            apiParam.setParamType(v1.getIn());
            apiParams.add(apiParam);
        });
        return apiParams;
    }

    private boolean getParamIsShow(Map<String, Object> extensions) {
        if (extensions == null) {
            return true;
        }
        Object obj = extensions.get(paramHideExtensionKey);
        return !"1".equals(String.valueOf(obj));
    }

    private boolean getParamIsAttachment(Map<String, Object> extensions) {
        if (extensions == null) {
            return false;
        }
        Object obj = extensions.get(paramAttachmentExtensionKey);
        return "1".equals(String.valueOf(obj));
    }

    /**
     * 将插件的API信息合并为一个YAML字符串。
     *
     * @param list          包含插件API信息的列表。
     * @param pluginInfoDto 插件的基本信息。
     * @return 表示插件API信息的YAML字符串。
     */
    public static String mergeYmal(List<PluginApiInfoDto> list, PluginInfoDto pluginInfoDto, String pluginUrl) {
        // 初始化OpenAPI对象并设置插件的基本信息

        OpenApiConvertApiInfo openApiConvertApiInfo = new OpenApiConvertApiInfo(new OpenAPI());
        openApiConvertApiInfo.openAPI.setInfo(new Info());
        openApiConvertApiInfo.openAPI.getInfo().setTitle(pluginInfoDto.getPluginName());
        openApiConvertApiInfo.openAPI.getInfo().setDescription(pluginInfoDto.getDescribe());
        openApiConvertApiInfo.openAPI.getInfo().setVersion("1.0.0");
        openApiConvertApiInfo.openAPI.setComponents(new Components());
        openApiConvertApiInfo.openAPI.getComponents().setSchemas(new HashMap<>());
        if(StringUtils.isNotBlank(pluginUrl)) {
            openApiConvertApiInfo.openAPI.setServers(new ArrayList<Server>() {
            });
            openApiConvertApiInfo.openAPI.getServers().add(new Server().url(pluginUrl));
        }

        // 初始化路径集合
        openApiConvertApiInfo.openAPI.setPaths(new Paths());
        // 遍历插件API信息列表，合并API定义
        if (CollectionUtils.isNotEmpty(list)) {
            for (PluginApiInfoDto dto : list) {
                // 如果元数据不为空，则解析并合并到OpenAPI定义中
                if (StringUtils.isNotBlank(dto.getMetaData())) {

                    OpenAPI currentOpenAPI = new OpenAPIV3Parser().readContents(dto.getMetaData(), null, null).getOpenAPI();

                    PathItem pathItem = currentOpenAPI.getPaths().entrySet().iterator().next().getValue();

                    Operation operation = getMethod(pathItem);
                    if(currentOpenAPI.getInfo() != null && StringUtils.isNotBlank(currentOpenAPI.getInfo().getTitle())) {
                        operation.setSummary(currentOpenAPI.getInfo().getTitle());
                    }

                    if (StringUtils.isBlank(operation.getOperationId()) && StringUtils.isNotBlank(dto.getCode())) {
                        operation.setOperationId(dto.getCode().replaceAll("-", "_"));
                    }

                    openApiConvertApiInfo.openAPI.getPaths().putAll(currentOpenAPI.getPaths());
                    if (currentOpenAPI.getComponents() != null && currentOpenAPI.getComponents().getSchemas() != null) {
                        openApiConvertApiInfo.openAPI.getComponents().getSchemas().putAll(currentOpenAPI.getComponents().getSchemas());
                    }

                    if (operation != null) {
                        if (operation.getExtensions() == null){
                            operation.setExtensions(new HashMap<>());
                        }
                        operation.getExtensions().put(pathItemIdkey, dto.getCode());
                        openApiConvertApiInfo.updateParams(operation, dto.getParams());
                        openApiConvertApiInfo.updateResponse(operation, dto.getResults());
                    }
                }
            }
        }
        // 将OpenAPI对象转换为YAML字符串并返回
        try {
            return Yaml.pretty().writeValueAsString(openApiConvertApiInfo.openAPI);
        } catch (JsonProcessingException e) {
            // 处理转换异常，抛出业务逻辑异常
            throw new BusinessLogicException("插件转yaml文件失败");
        }
    }

    public static String updateResponse(String yaml, List<ApiParam> params) {
        OpenApiConvertApiInfo openApiConvertApiInfo = OpenApiConvertApiInfo.build(yaml);
        PathItem pathItem = openApiConvertApiInfo.openAPI.getPaths().entrySet().iterator().next().getValue();
        Operation operation = getMethod(pathItem);
        openApiConvertApiInfo.updateResponse(operation, params);
        try {
            return Yaml.pretty().writeValueAsString(openApiConvertApiInfo.openAPI);
        } catch (JsonProcessingException e) {
            // 处理转换异常，抛出业务逻辑异常
            throw new BusinessLogicException("插件转yaml文件失败");
        }
    }

    private void updateParams(Operation operation, List<ApiParam> params) {
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        Map<String, ApiParam> paramsMap = params.stream().collect(Collectors.toMap(ApiParam::getName, Function.identity()));
        if (operation.getRequestBody() != null) {
            MediaType mediaType = operation.getRequestBody().getContent().entrySet().iterator().next().getValue();
            Schema<?> schema = mediaType.getSchema();
            // 如果schema是对象类型，则进行转换
            if (schema instanceof ObjectSchema) {
                ObjectSchema objectSchema = (ObjectSchema) schema;
                if (objectSchema.getProperties() != null) {
                    objectSchema.getProperties().forEach((k1, v1) -> {
                        ApiParam apiParam = paramsMap.get(k1);
                        v1.setDescription(apiParam.getDescribe());
                        if (apiParam.isShow()) {
                            v1.addExtension(paramHideExtensionKey, "0");
                        } else {
                            v1.addExtension(paramHideExtensionKey, "1");
                        }
                    });
                }
            }
            // 如果操作不包含请求体，但包含参数列表，则直接将参数添加到DTO
        }
        if (operation.getParameters() != null) {
            operation.getParameters().forEach(v1 -> {
                ApiParam apiParam = paramsMap.get(v1.getName());
                v1.setDescription(apiParam.getDescribe());
                if (apiParam.isShow()) {
                    v1.addExtension(paramHideExtensionKey, "0");
                } else {
                    v1.addExtension(paramHideExtensionKey, "1");
                }
            });
        }
    }

    private void updateResponse(Operation operation, List<ApiParam> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        // 检查操作是否有响应信息
        if (operation.getResponses() != null) {
            // 获取200响应码的内容
            Content content = operation.getResponses().get("200").getContent();
            // 尝试获取媒体类型
            MediaType mediaType = getMediaType(content);
            // 如果没有找到媒体类型，直接返回
            if (mediaType == null) {
                return;
            }
            // 获取媒体类型的Schema
            Schema<?> schema = mediaType.getSchema();
            // 判断schema是否为ObjectSchema类型
            if (schema instanceof ObjectSchema && schema.getProperties() != null) {
                ObjectSchema objectSchema = (ObjectSchema) schema;
                Schema<?> refSchema = objectSchema.getProperties().get("data");
                if (refSchema != null) {
                    refSchema = getRefSchema(refSchema);
                } else {
                    refSchema = objectSchema;
                }
                // 如果解析出的引用Schema是ObjectSchema类型，则将其转换并存储在dto中
                if (refSchema instanceof ObjectSchema) {
                    updateObjectSchema((ObjectSchema)refSchema, result, null);
                } else if (refSchema instanceof ArraySchema) {
                    // 如果不是ObjectSchema类型，则将该schema作为其他schema处理并添加到dto
                    ApiParam apiParam = result.get(0);
                    updateOtherSchema(refSchema , apiParam);
                    updateArraySchema((ArraySchema)refSchema, result.get(0).getChild());

                } else {
                    updateOtherSchema(refSchema , result.get(0));
                }
            } else if (schema instanceof ArraySchema) {
                updateArraySchema((ArraySchema)schema, result);
            } else {
                // 如果schema不是ObjectSchema类型，则直接将其作为其他schema处理并添加到dto
                updateOtherSchema(schema , result.get(0));
            }
        }
    }

    private void updateObjectSchema(ObjectSchema objectSchema, List<ApiParam> result, ApiParam apiParam) {
        // 初始化API参数列表
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<String, ApiParam> resultMap = result.stream().collect(Collectors.toMap(ApiParam::getName, Function.identity()));
        if (apiParam != null) {
            updateOtherSchema(objectSchema, apiParam);
        }
        // 遍历对象模式中的每个属性，创建并添加ApiParam实例
        if (StringUtils.isNotBlank(objectSchema.get$ref())) {
            Schema<?> schema = getRefSchema(objectSchema);
            updateProperties(schema.getProperties(), resultMap);
        } else {
            updateProperties(objectSchema.getProperties(), resultMap);
        }
    }

    private void updateOtherSchema(Schema<?> schema, ApiParam apiParam) {
        schema.setDescription(apiParam.getDescribe());
        if (apiParam.isAttachment()) {
            schema.addExtension(paramAttachmentExtensionKey, "1");
        } else {
            schema.addExtension(paramAttachmentExtensionKey, "0");
        }
    }

    private void updateProperties(Map<String, Schema> properties, Map<String, ApiParam> resultMap) {
        properties.forEach((k1, v1) -> {
            ApiParam apiParam = resultMap.get(k1);
            if (apiParam == null) {
                return;
            }
            v1.setDescription(apiParam.getDescribe());
            if (apiParam.isShow()) {
                v1.addExtension(paramHideExtensionKey, "0");
            } else {
                v1.addExtension(paramHideExtensionKey, "1");
            }
            if (apiParam.isAttachment()) {
                v1.addExtension(paramAttachmentExtensionKey, "1");
            } else {
                v1.addExtension(paramAttachmentExtensionKey, "0");
            }
            if (v1 instanceof ArraySchema) {
                updateArraySchema((ArraySchema)v1, apiParam.getChild());
            }else if (v1 instanceof ObjectSchema) {
                updateObjectSchema((ObjectSchema)v1, apiParam.getChild(), null);
            }else if (v1.getType() == null && StringUtils.isNotBlank(v1.get$ref())) {
                // 如果是引用对象，则拷贝一份components/schemas，解决引用相同对象导致描述覆盖问题
                Schema<?> refSchema = getRefSchema(v1, reuseSchemaRef);
                refSchema.setDescription(apiParam.getDescribe());
                if (refSchema instanceof ObjectSchema) {
                    updateObjectSchema((ObjectSchema)refSchema, apiParam.getChild(), apiParam);
                }
            }
        });
    }

    /**
     * 将对象模式（ObjectSchema）转换为API参数列表（List<ApiParam>）。
     *
     * @return 转换后的API参数列表，每个参数包含名称和描述。
     */
    private void updateArraySchema(ArraySchema arraySchema, List<ApiParam> result) {
        Schema<?> schema = arraySchema.getItems();
        if (StringUtils.isNotBlank(schema.get$ref())) {
            Schema<?> refSchema = getRefSchema(schema);
            if (refSchema instanceof ArraySchema) {
                updateArraySchema((ArraySchema) refSchema, result);
            }else if (refSchema instanceof ObjectSchema) {
                updateObjectSchema((ObjectSchema)refSchema, result, null);
            }
        } else if (schema instanceof ObjectSchema) {
            updateObjectSchema((ObjectSchema) schema, result, null);
        }
    }


}
