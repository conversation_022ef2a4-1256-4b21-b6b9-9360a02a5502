package com.mysoft.gptbuilder.agent.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mysoft.framework.common.user.UserContext;
import com.mysoft.framework.service.AppService;
import com.mysoft.gptbuilder.agent.interfaces.ISkillUserCollectService;
import com.mysoft.gptbuilder.agent.model.dto.SkillUserCollectRequest;
import com.mysoft.gptbuilder.agent.service.dao.SkillUserCollectDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.AssistantEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.SkillUserCollectEntity;
import com.mysoft.gptbuilder.common.service.AppServiceBase;
import com.mysoft.sdk.aop.annotations.DisableAOP;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@DisableAOP
public class SkillUserCollectService extends AppServiceBase implements ISkillUserCollectService {

    @Autowired
    private SkillUserCollectDao skillUserCollectDao;

    @Autowired
    private SkillService skillService;

    @Override
    public boolean saveSkillUserCollect(SkillUserCollectRequest skillUserCollectRequest) {
        UserContext userContext = this.getContextInfo().getUserContext();
        //先查询是否存在

        if (skillUserCollectRequest.isCollect()) {
            if (skillUserCollectDao.selectOne(new LambdaQueryWrapper<SkillUserCollectEntity>()
                    .eq(SkillUserCollectEntity::getSkillGUID, skillUserCollectRequest.getSkillGUID())
                    .eq(SkillUserCollectEntity::getUserGUID, userContext.getUserId().toString())) != null) {
                return true;
            }
            SkillUserCollectEntity skillUserCollectEntity = new SkillUserCollectEntity();
            skillUserCollectEntity.setUserGUID(userContext.getUserId().toString());
            skillUserCollectEntity.setSkillGUID(skillUserCollectRequest.getSkillGUID());
            skillUserCollectEntity.setUserName(userContext.getUserName());
            skillUserCollectEntity.setUserCode(userContext.getUserCode());
            skillUserCollectDao.insert(skillUserCollectEntity);
            skillService.refreshSkillCollectCount(skillUserCollectRequest.getSkillGUID(), 1);
            return true;
        } else {
            //删除数据  查询的参数是技能guid和用户
            LambdaQueryWrapper<SkillUserCollectEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SkillUserCollectEntity::getSkillGUID, skillUserCollectRequest.getSkillGUID());
            wrapper.eq(SkillUserCollectEntity::getUserGUID, userContext.getUserId().toString());
            if (skillUserCollectDao.delete(wrapper) > 0) {
                skillService.refreshSkillCollectCount(skillUserCollectRequest.getSkillGUID(), -1);
            }
            return true;
        }
    }

    @Override
    public List<String> getSkillCollect(List<String> skillGUIDS) {
        UserContext userContext = this.getContextInfo().getUserContext();
        LambdaQueryWrapper<SkillUserCollectEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkillUserCollectEntity::getUserGUID, userContext.getUserId().toString());
        if (!CollectionUtils.isEmpty(skillGUIDS)) {
            wrapper.in(SkillUserCollectEntity::getSkillGUID, skillGUIDS);
        }
        wrapper.orderByDesc(SkillUserCollectEntity::getCreatedTime);
        List<SkillUserCollectEntity> skillUserCollectEntities = skillUserCollectDao.selectList(wrapper);
        //判断是否为空，获取Id的集合
        List<String> skillGuids = skillUserCollectEntities.stream().map(SkillUserCollectEntity::getSkillGUID).collect(Collectors.toList());
        return skillGuids;
    }
} 