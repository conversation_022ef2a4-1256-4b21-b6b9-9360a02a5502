package com.mysoft.gptbuilder.agent.service.service.skill.process;

import com.mysoft.gptbuilder.agent.model.dto.skill.AbstractNodeConfigDto;
import com.mysoft.gptbuilder.agent.model.dto.skill.SkillNodeDto;
import com.mysoft.gptbuilder.agent.model.dto.skill.SkillOrchestrationContextDto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public abstract class AbstractTemplateProcessor {
    protected Map<String, Object> templateParams;

    abstract String getTemplateUrl();

    protected void createBaseTemData(SkillOrchestrationContextDto skillOrchestrationContextDto, SkillNodeDto node,
                                     int stepNumber, AbstractNodeConfigDto nodeConfigDto,
                                     ArrayList<String> orchestrationGUIDs) {
        this.templateParams = new HashMap<>();
        templateParams.put("skillOrchestrationContextDto", skillOrchestrationContextDto);
        templateParams.put("node", node);
        templateParams.put("stepNumber", stepNumber);
        templateParams.put("nodeConfigDto", nodeConfigDto);
        templateParams.put("orchestrationGUIDs", orchestrationGUIDs);
    }

    /**
     * 获取模板参数映射。
     *
     * @return 参数映射对象
     */
    protected Map<String, Object> getParams() {
        return templateParams;
    }

    /**
     * 更新或添加模板参数映射中的键值对。
     *
     * @param key   参数的键
     * @param value 参数的值
     */
    protected void addParam(String key, Object value) {
        templateParams.put(key, value);
    }

    protected String processTemplate() {
        return FreeMarkerTemplateProcessor.processTemplate(getTemplateUrl(), templateParams);
    }
}
