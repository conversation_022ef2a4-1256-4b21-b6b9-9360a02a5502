package com.mysoft.gptbuilder.agent.service.controller;

import com.mysoft.framework.modeling.dataapi.LoadDataApi;
import com.mysoft.framework.modeling.dto.FieldsSetting;
import com.mysoft.framework.modeling.dto.ListDataResult;
import com.mysoft.framework.modeling.dto.LoadDataParams;
import com.mysoft.framework.mvc.annotation.ActionRight;
import com.mysoft.framework.rpc.annotation.PubAction;
import com.mysoft.framework.rpc.annotation.PubService;
import com.mysoft.framework.rpc.contants.RequestPrefix;
import com.mysoft.gptbuilder.agent.interfaces.IMcpToolParameterService;
import com.mysoft.gptbuilder.agent.model.dto.mcp.ExecuteMcpToolRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

/**
 * MCP工具参数管理控制器
 * 提供MCP工具参数管理相关的REST接口
 * 主要用于解析和展示MCP工具的InputSchema参数信息
 */
@PubService(value = "/mcpToolParameter", prefix = RequestPrefix.API, businessCode = "42000801")
@Tag(name = "API-MCP工具参数管理")
public class McpToolParameterController implements LoadDataApi {

    @Autowired
    private IMcpToolParameterService mcpToolParameterService;

    @Override
    public boolean projectFilter() {
        return false;
    }

    @Override
    public FieldsSetting loadFields() {
        return mcpToolParameterService.loadFields();
    }

    @Override
    public ListDataResult loadData(LoadDataParams options) throws Exception {
        return mcpToolParameterService.loadData(options);
    }

    /**
     * 执行MCP工具
     *
     * @param request 执行MCP工具请求
     * @return 执行结果
     */
    @PubAction(value = "/executeMcpTool", method = RequestMethod.POST)
    @ActionRight(ActionName = "执行", ActionCode = "03")
    @Operation(summary = "执行MCP工具", description = "调用GPT引擎执行指定的MCP工具")
    public Object executeMcpTool(@Valid @RequestBody ExecuteMcpToolRequestDto request) {
        return mcpToolParameterService.executeMcpTool(request);
    }
}
