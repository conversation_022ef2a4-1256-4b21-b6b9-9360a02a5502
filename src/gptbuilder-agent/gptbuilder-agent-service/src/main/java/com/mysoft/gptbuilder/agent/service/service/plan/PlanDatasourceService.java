package com.mysoft.gptbuilder.agent.service.service.plan;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.framework.common.util.StringUtil;
import com.mysoft.framework.core.common.spring.SpringContextHolder;
import com.mysoft.framework.modeling.dto.*;
import com.mysoft.gptbuilder.agent.interfaces.IPlanDatasourceService;
import com.mysoft.gptbuilder.agent.model.dto.plan.*;
import com.mysoft.gptbuilder.agent.model.dto.pub.approval.RuleDto;
import com.mysoft.gptbuilder.agent.service.dao.approval.*;
import com.mysoft.gptbuilder.agent.service.dao.entity.approval.*;
import com.mysoft.gptbuilder.agent.service.service.plan.datasource.IPlanDatasourceBaseService;
import com.mysoft.gptbuilder.common.model.consts.GPTBuilderConst;
import com.mysoft.sdk.aop.annotations.DisableAOP;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@DisableAOP
public class PlanDatasourceService implements IPlanDatasourceService {

    private PlanRuleDao planRuleDao;

    private PlanDao planDao;

    private PlanParamsDao planParamsDao;

    private PlanDatasourceDao planDatasourceDao;

    private PlanDatasourceRuleDao planDatasourceRuleDao;

    private Map<Integer, IPlanDatasourceBaseService> datasourceBaseServiceMap;

    private final static String DATASOURCE_FIELD_ID_SEPARATOR = "_";

    private final static String DATASOURCE_FIELD_NAME_SEPARATOR = "-";

    private final static String DATASOURCE_FIELD_SEPARATOR = ",";

    public PlanDatasourceService(PlanDao planDao, PlanRuleDao planRuleDao, PlanParamsDao planParamsDao, PlanDatasourceDao planDatasourceDao,
                                 PlanDatasourceRuleDao planDatasourceRuleDao, List<IPlanDatasourceBaseService> datasourceBaseServiceList) {
        this.planDao = planDao;
        this.planRuleDao = planRuleDao;
        this.planParamsDao = planParamsDao;
        this.planDatasourceDao = planDatasourceDao;
        this.planDatasourceRuleDao = planDatasourceRuleDao;
        if (datasourceBaseServiceMap == null) {
            datasourceBaseServiceMap = datasourceBaseServiceList.stream()
                    .collect(Collectors.toMap(IPlanDatasourceBaseService::source, Function.identity()));
        }
    }

    @Override
    public List<PlanDatasourceDto> findDatasourceList(LoadDataParams loadDataParams) {
        Object planGUID = loadDataParams.getOptions().getFilters().stream().filter(o -> StringUtil.equalsIgnoreCase(o.getCondition().get(0).getField(), "null.planGUID")).findFirst()
                .map(filter -> filter.getCondition().get(0).getValue()).orElse(null);
        if (planGUID == null) {
            return new ArrayList<>();
        }
        // 查询方案下的所有数据源
        List<PlanDatasourceEntity> list = planDatasourceDao.selectList(Wrappers.<PlanDatasourceEntity>lambdaQuery()
                .select(PlanDatasourceEntity::getPlanDatasourceGUID, PlanDatasourceEntity::getName, PlanDatasourceEntity::getSource, PlanDatasourceEntity::getPlanGUID)
                .eq(PlanDatasourceEntity::getPlanGUID, planGUID.toString()));

        // 查询方案下的所有数据源-规则关联
        List<PlanDatasourceRuleEntity> datasourceRules = planDatasourceRuleDao.selectList(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery()
                .select(PlanDatasourceRuleEntity::getPlanRuleGUID, PlanDatasourceRuleEntity::getPlanDatasourceGUID)
                .eq(PlanDatasourceRuleEntity::getPlanGUID, planGUID.toString()));

        List<String> ruleGuids = new ArrayList<>();
        Map<String, List<String>> map = new HashMap<>();
        datasourceRules.forEach(datasourceRuleEntity -> {
            ruleGuids.add(datasourceRuleEntity.getPlanRuleGUID());
            if (map.containsKey(datasourceRuleEntity.getPlanDatasourceGUID())) {
                map.get(datasourceRuleEntity.getPlanDatasourceGUID()).add(datasourceRuleEntity.getPlanRuleGUID());
            } else {
                map.put(datasourceRuleEntity.getPlanDatasourceGUID(), new ArrayList<>(Arrays.asList(datasourceRuleEntity.getPlanRuleGUID())));
            }
        });
        Map<String, PlanRuleEntity> ruleMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ruleGuids)) {
            ruleMap = planRuleDao.selectList(Wrappers.<PlanRuleEntity>lambdaQuery()
                    .select(PlanRuleEntity::getRuleGUID, PlanRuleEntity::getRuleName, PlanRuleEntity::getPlanGUID, PlanRuleEntity::getRuleGroupGUID)
                    .in(PlanRuleEntity::getRuleGUID, ruleGuids)).stream()
                    .collect(Collectors.toMap(PlanRuleEntity::getRuleGUID, Function.identity()));
        }

        Map<String, PlanRuleEntity> finalRuleMap = ruleMap;
        return list.stream().map(entity -> {
            PlanDatasourceDto dto = new PlanDatasourceDto();
            dto.setPlanDatasourceGUID(entity.getPlanDatasourceGUID());
            dto.setName(entity.getName());
            dto.setSource(entity.getSource());
            dto.setPlanGUID(entity.getPlanGUID());
            List<String> ids = map.get(entity.getPlanDatasourceGUID());
            if (!CollectionUtil.isEmpty(ids)) {
                List<RuleDto> rules = ids.stream().map(ruleGuid -> {
                    PlanRuleEntity planRuleEntity = finalRuleMap.get(ruleGuid);
                    if (planRuleEntity == null) {
                        return null;
                    }
                    RuleDto ruleDto = new RuleDto();
                    ruleDto.setRuleGUID(planRuleEntity.getRuleGUID());
                    ruleDto.setRuleName(planRuleEntity.getRuleName());
                    ruleDto.setRuleGroupGUID(planRuleEntity.getRuleGroupGUID());
                    return ruleDto;
                }).filter(ruleDto -> ruleDto != null).collect(Collectors.toList());
                dto.setRules(rules);
            }
            return dto;
        }).collect(Collectors.toList());


    }

    @Override
    public FieldsSetting planDatasourceLoadFields() {
        FieldsSetting fieldsSetting = new FieldsSetting();
        fieldsSetting.setIdField("planDatasourceGUID");
        fieldsSetting.setFields(new ArrayList<>());
        fieldsSetting.getFields().add(new DataApiField("planGUID", null, "方案ID", FieldType.String));
        fieldsSetting.getFields().add(new DataApiField("name", null, "数据源名称", FieldType.String));
        fieldsSetting.getFields().add(new DataApiField("source", null, "数据来源", FieldType.Int));
        fieldsSetting.getFields().add(new DataApiField("sourceId", null, "数据来源ID", FieldType.String));
        fieldsSetting.getFields().add(new DataApiField("metadata", null, "元数据", FieldType.String));
        fieldsSetting.getFields().add(new DataApiField("rules", null, "规则列表", FieldType.String));
        fieldsSetting.getFields().add(new DataApiField("", null, "规则列表", FieldType.String));
        return fieldsSetting;
    }

    @Override
    public List<DataSourceSource> findDataSourceSource(int source, String workSpaceGUID, String groupId) {
        if (datasourceBaseServiceMap.containsKey(source)) {
            return datasourceBaseServiceMap.get(source).findDataSourceSource(workSpaceGUID, groupId);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DataSourceSource> findDataSourceSourceGroup(int source, String workSpaceGUID) {
        if (datasourceBaseServiceMap.containsKey(source)) {
            return datasourceBaseServiceMap.get(source).findDataSourceSourceGroup(workSpaceGUID);
        }
        return new ArrayList<>();
    }

    @Override
    public DataSourceMetadata findDataSourceSourceMetadata(int source, String sourceId, String planDatasourceGUID, String workSpaceGUID) {
        if (StringUtils.isNotBlank(planDatasourceGUID)) {
            PlanDatasourceEntity planDatasourceEntity = planDatasourceDao.selectOne(Wrappers.<PlanDatasourceEntity>lambdaQuery()
                    .eq(PlanDatasourceEntity::getPlanDatasourceGUID, planDatasourceGUID).eq(PlanDatasourceEntity::getSourceId, sourceId));
            if (planDatasourceEntity !=null && StringUtils.isNotBlank(planDatasourceEntity.getMetadata())) {
                return datasourceBaseServiceMap.get(source).formatMetadata(planDatasourceEntity.getMetadata());
            }

        }
        if (datasourceBaseServiceMap.containsKey(source)) {
            return datasourceBaseServiceMap.get(source).findDataSourceSourceMetadata(sourceId, workSpaceGUID);
        }
        return new DataSourceMetadata();
    }

    @Transactional
    @Override
    public String saveDataSource(PlanDatasourceDto planDatasourceDto) {
        if (StringUtils.isBlank(planDatasourceDto.getPlanGUID())) {
            throw new BusinessLogicException("方案ID不能为空");
        }

        PlanDatasourceEntity planDatasourceEntity = planDatasourceDao.selectOne(Wrappers.<PlanDatasourceEntity>lambdaQuery().
                eq(PlanDatasourceEntity::getPlanGUID, planDatasourceDto.getPlanGUID()).
                and(q -> q.eq(PlanDatasourceEntity::getSource, 0)));
        if (planDatasourceDto.getSource() == 0) {
            if (planDatasourceEntity != null && !planDatasourceEntity.getPlanDatasourceGUID().equals(planDatasourceDto.getPlanDatasourceGUID())) {
                throw new BusinessLogicException("流程数据源已经存在，无法重复保存");
            }
        }
        if (StringUtils.isNotBlank(planDatasourceDto.getMetadata()) && !CollectionUtils.isEmpty(planDatasourceDto.getResults())) {
            String metadata = datasourceBaseServiceMap.get(planDatasourceDto.getSource()).saveResultMetadata(planDatasourceDto.getMetadata(), planDatasourceDto.getResults());
            planDatasourceDto.setMetadata(metadata);
        }
        PlanDatasourceEntity entity = new PlanDatasourceEntity();
        BeanUtil.copyProperties(planDatasourceDto, entity);
        if (StringUtils.isBlank(entity.getPlanDatasourceGUID())) {
            entity.setPlanDatasourceGUID(UUID.randomUUID().toString());
            planDatasourceDto.setPlanDatasourceGUID(entity.getPlanDatasourceGUID());

            checkName(entity);
            planDatasourceDao.insert(entity);
        } else {
            checkName(entity);
            PlanDatasourceEntity oldEntity = planDatasourceDao.selectById(entity.getPlanDatasourceGUID());
            if (!oldEntity.getSourceId().equals(entity.getSourceId()) && entity.getSource() != oldEntity.getSource()) {
                DataSourceMetadata oldMetadata = datasourceBaseServiceMap.get(oldEntity.getSource()).formatMetadata(oldEntity.getMetadata());

                DataSourceMetadata newMetadata = datasourceBaseServiceMap.get(entity.getSource()).formatMetadata(entity.getMetadata());

                synchronizeField(oldMetadata.getResults(), newMetadata.getResults(), true);

                synchronizeDatasourceRule(entity, newMetadata.getResults());
                synchronizePlanParams(entity, newMetadata.getParams());


            } else if (oldEntity != null && !oldEntity.getMetadata().equals(entity.getMetadata()) && CollectionUtils.isNotEmpty(planDatasourceDto.getResults())) {
                synchronizeDatasourceRule(entity, planDatasourceDto.getResults());
            }

            planDatasourceDao.updateById(entity);
        }
        updatePlanUseFlow(planDatasourceDto, planDatasourceEntity);

        return entity.getPlanDatasourceGUID();
    }

    private void updatePlanUseFlow(PlanDatasourceDto planDatasourceDto, PlanDatasourceEntity planDatasourceEntity) {
        PlanEntity oldPlanEntity = planDao.selectById(planDatasourceDto.getPlanGUID());
        if (oldPlanEntity == null) {
            throw new BusinessLogicException("方案不存在");
        }
        PlanEntity planEntity = new PlanEntity();
        planEntity.setPlanGUID(planDatasourceDto.getPlanGUID());
        if (planDatasourceDto.getSource() == 0 && oldPlanEntity.getUseFlow() != 1) {
            planEntity.setUseFlow(1);
            planDao.updateById(planEntity);
            refreshPlanParamsFlow(planDatasourceDto);
        } else if (planDatasourceDto.getSource() != 0 && oldPlanEntity.getUseFlow() == 1
                && planDatasourceEntity.getPlanDatasourceGUID().equals(planDatasourceDto.getPlanDatasourceGUID())) {
            planEntity.setUseFlow(0);
            planDao.updateById(planEntity);
        }

    }

    private void refreshPlanParamsFlow(PlanDatasourceDto planDatasourceDto) {
        planParamsDao.delete(Wrappers.<PlanParamsEntity>lambdaQuery().eq(PlanParamsEntity::getPlanGUID, planDatasourceDto.getPlanGUID()));
        List<DataSourceField> fields = datasourceBaseServiceMap.get(planDatasourceDto.getSource()).formatParamsMetadata(planDatasourceDto.getMetadata());
        Map<String, PlanParamsDto> map = new HashMap<>();
        List<PlanParamsDto> result = new ArrayList<>();
        dataSourceFieldConvert(planDatasourceDto.getPlanDatasourceGUID(), planDatasourceDto.getPlanGUID(), fields, result, map);
        result.forEach(item -> planParamsDao.insert(JsonUtil.parse(JsonUtil.toString(item), PlanParamsEntity.class)));
    }

    @Transactional
    @Override
    public void deleteDataSource(String planDatasourceGUID) {
        PlanDatasourceEntity planDatasource = planDatasourceDao.selectById(planDatasourceGUID);
        if (planDatasource != null && planDatasource.getSource() == 0) {
            PlanEntity planEntity = new PlanEntity();
            planEntity.setPlanGUID(planDatasource.getPlanGUID());
            planEntity.setUseFlow(0);
            planDao.updateById(planEntity);
        }
        long count = planDatasourceRuleDao.selectCount(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery().eq(PlanDatasourceRuleEntity::getPlanDatasourceGUID, planDatasourceGUID));
        if (count > 0) {
            throw new BusinessLogicException("数据源下存在规则，无法删除");
        }
        planDatasourceRuleDao.delete(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery().eq(PlanDatasourceRuleEntity::getPlanDatasourceGUID, planDatasourceGUID));
        planDatasourceDao.deleteById(planDatasourceGUID);
        deleteDataSourceParams(planDatasource);
    }

    @Override
    public DatasourceRuleMetadata findDatasourceRuleMetadata(String planDatasourceGUID, String ruleGUID) {
        List<String> datasourceGuids = Arrays.asList(planDatasourceGUID.split(","));
        List<PlanDatasourceEntity> datasourceList = planDatasourceDao.selectList(Wrappers.<PlanDatasourceEntity>lambdaQuery().in(PlanDatasourceEntity::getPlanDatasourceGUID, datasourceGuids));
        List<DataSourceSource> dataSourceSources = new ArrayList<>();
        Map<String, List<DataSourceField>> resultMap = new HashMap<>();
        Map<String, Integer> sourceMap = new HashMap<>();
        datasourceList.forEach(item -> {
            DataSourceSource dataSourceSource = new DataSourceSource();
            dataSourceSource.setText(item.getName());
            dataSourceSource.setValue(item.getPlanDatasourceGUID());
            dataSourceSources.add(dataSourceSource);
            resultMap.put(item.getPlanDatasourceGUID(), datasourceBaseServiceMap.get(item.getSource()).formatResultMetadata(item.getMetadata()));
            sourceMap.put(item.getPlanDatasourceGUID(), item.getSource());
        });
        List<PlanDatasourceRuleEntity> rules =  planDatasourceRuleDao.selectList(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery().eq(PlanDatasourceRuleEntity::getPlanRuleGUID, ruleGUID));
        rules.forEach(item -> {
            List<DataSourceField> newList = resultMap.get(item.getPlanDatasourceGUID());
            if (CollectionUtils.isNotEmpty(newList) &&  StringUtils.isNotBlank(item.getResultMetadata())) {
                List<DataSourceField> oldList = JsonUtil.parse(item.getResultMetadata(), new TypeReference<List<DataSourceField>>() {});
                setSelected(oldList, newList);
            }
        });
        DatasourceRuleMetadata datasourceRuleMetadata = new DatasourceRuleMetadata();
        datasourceRuleMetadata.setDatasource(dataSourceSources);
        datasourceRuleMetadata.setResultMap(resultMap);
        return datasourceRuleMetadata;
    }

    private void setSelected(List<DataSourceField> oldList, List<DataSourceField> newList) {
        for (DataSourceField newItem: newList) {
            Optional<DataSourceField> oldItemOpt = oldList.stream()
            .filter(oldItem -> oldItem.getParamId().equals(newItem.getParamId()))
            .findFirst();
            oldItemOpt.ifPresent(oldItem -> {
                newItem.setSelected(oldItem.getSelected());
                if (CollectionUtils.isNotEmpty(newItem.getChildren()) && CollectionUtils.isNotEmpty(oldItem.getChildren())) {
                    setSelected(oldItem.getChildren(), newItem.getChildren());
                }
            });


        }
    }

    @Override
    public List<String> findDatasourceGUIDByRule(String ruleGUID) {
        List<PlanDatasourceRuleEntity> rules =  planDatasourceRuleDao.selectList(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery().eq(PlanDatasourceRuleEntity::getPlanRuleGUID, ruleGUID));
        if (CollectionUtils.isNotEmpty(rules)) {
            return rules.stream().map(PlanDatasourceRuleEntity::getPlanDatasourceGUID).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }



    @Override
    public List<PlanParamsDto> dataSourceParamsList(String planGUID) {
        List<PlanParamsDto> result = new ArrayList<>();
        PlanEntity entity = planDao.selectById(planGUID);
        if (entity == null) {
            return result;
        }

        LambdaQueryWrapper<PlanDatasourceEntity> wrapper = Wrappers.<PlanDatasourceEntity>lambdaQuery().eq(PlanDatasourceEntity::getPlanGUID, planGUID);
        if (entity.getUseFlow() == 1) {
            wrapper.and(q -> q.eq(PlanDatasourceEntity::getSource, 0));
        }
        List<PlanDatasourceEntity> list = planDatasourceDao.selectList(wrapper);
        Map<String, PlanParamsDto> map = new HashMap<>();
        list.forEach(item -> {
            if (StringUtils.isBlank(item.getMetadata())) {
                return;
            }
            List<DataSourceField> fields = datasourceBaseServiceMap.get(item.getSource()).formatParamsMetadata(item.getMetadata());
            dataSourceFieldConvert(item.getPlanDatasourceGUID(), entity.getPlanGUID(), fields, result, map);
        });
        return result;
    }

    private void dataSourceFieldConvert(String datasourceId, String planGUID, List<DataSourceField> fields, List<PlanParamsDto> result, Map<String, PlanParamsDto> map) {
        fields.forEach(field -> {
            String key = field.getParamId() + "++" + field.getParamType();
            PlanParamsDto planParamsDto = map.get(key);
            String id = datasourceId + DATASOURCE_FIELD_ID_SEPARATOR + field.getParamId();
            if (Objects.isNull(planParamsDto)) {
                planParamsDto = new PlanParamsDto();
                planParamsDto.setParamsId(field.getParamId());
                planParamsDto.setParamsName(field.getParamName());
                planParamsDto.setPlanGUID(planGUID);
                planParamsDto.setParamsType(1);
                planParamsDto.setMappingDatasource(id);
                map.put(key, planParamsDto);
                result.add(planParamsDto);
            } else {
                planParamsDto.setMappingDatasource(planParamsDto.getMappingDatasource() + DATASOURCE_FIELD_SEPARATOR + id);
            }
        });
    }



    @Override
    public List<OptionItem> findAllParamsFields(List<Filter> filters, Map<String, String> urlParams) {
        String planGUID = urlParams.get("oid");
        List<OptionItem> result = new ArrayList<>();
        List<PlanDatasourceEntity> list = planDatasourceDao.selectList(Wrappers.<PlanDatasourceEntity>lambdaQuery().eq(PlanDatasourceEntity::getPlanGUID, planGUID));
        list.forEach(item -> {
            if (StringUtils.isBlank(item.getMetadata())) {
                return;
            }
            List<DataSourceField> fields = datasourceBaseServiceMap.get(item.getSource()).formatParamsMetadata(item.getMetadata());
            fields.forEach(field -> {
                OptionItem optionItem = new OptionItem();
                optionItem.setText(item.getName()+DATASOURCE_FIELD_NAME_SEPARATOR+field.getParamName());
                optionItem.setValue(item.getPlanDatasourceGUID()+DATASOURCE_FIELD_ID_SEPARATOR+field.getParamId());
                result.add(optionItem);
            });
        });
        return result;
    }

    @Override
    public List<PlanParamsDto> findPlanParams(String planGUID) {
        return planParamsDao.selectList(Wrappers.<PlanParamsEntity>lambdaQuery().eq(PlanParamsEntity::getPlanGUID, planGUID))
                .stream().map(item -> {
                    PlanParamsDto planParamsDto = new PlanParamsDto();
                    BeanUtils.copyProperties(item, planParamsDto);
                    return planParamsDto;
                }).collect(Collectors.toList());
    }

    @Override
    public List<DataSourceField> formatResultMetadata(int source, String metadata) {
        return datasourceBaseServiceMap.get(source).formatResultMetadata(metadata);
    }

    @Override
    public Map<String, String> findAllRequiredParams(String planGUID) {
        Map<String, String> map = new HashMap<>();
        List<PlanDatasourceEntity> list = planDatasourceDao.selectList(Wrappers.<PlanDatasourceEntity>lambdaQuery().eq(PlanDatasourceEntity::getPlanGUID, planGUID));
        list.forEach(item -> {
            if (StringUtils.isBlank(item.getMetadata())) {
                return;
            }
            List<DataSourceField> fields = datasourceBaseServiceMap.get(item.getSource()).formatParamsMetadata(item.getMetadata());
            fields.forEach(field -> {
                if (field.getRequired() != 1) {
                    return;
                }
                String key = item.getPlanDatasourceGUID()+DATASOURCE_FIELD_ID_SEPARATOR+field.getParamId();
                String value = item.getName()+DATASOURCE_FIELD_NAME_SEPARATOR+field.getParamName();
                map.put(key, value);
            });
        });
        return map;
    }

    @Transactional
    @Override
    public DataSourceMetadata synchronizeDatasource(String planDatasourceGUID, String workSpaceGUID) {
        PlanDatasourceEntity entity = planDatasourceDao.selectById(planDatasourceGUID);
        if (entity == null) {
            throw new BusinessLogicException("数据源不存在！");
        }
        DataSourceMetadata oldMetadata = null;
        if (entity != null && StringUtils.isNotBlank(entity.getMetadata())) {
            oldMetadata = datasourceBaseServiceMap.get(entity.getSource()).formatMetadata(entity.getMetadata());
        }
        DataSourceMetadata newMetadata = datasourceBaseServiceMap.get(entity.getSource()).findDataSourceSourceMetadata(entity.getSourceId(), workSpaceGUID);
        if (StringUtils.isBlank(newMetadata.getMetadata())) {
            throw new BusinessLogicException("原始数据源不存在,无法同步！");
        }
        if (oldMetadata == null) {
            oldMetadata = new DataSourceMetadata();
        }
        synchronizeField(oldMetadata.getResults(), newMetadata.getResults(), true);

        String newMetadataString = datasourceBaseServiceMap.get(entity.getSource()).saveResultMetadata(newMetadata.getMetadata(), newMetadata.getResults());
        entity.setMetadata(newMetadataString);
        planDatasourceDao.updateById(entity);

        synchronizeDatasourceRule(entity, newMetadata.getResults());
        synchronizePlanParams(entity, newMetadata.getParams());
        return newMetadata;

    }

    public void synchronizeField(List<DataSourceField> oldList, List<DataSourceField> newList, boolean isReplaceAttachment) {
        if (CollectionUtils.isEmpty(newList) || CollectionUtils.isEmpty(oldList)) {
            return;
        }
        Map<String, DataSourceField> map = oldList.stream().collect(Collectors.toMap(DataSourceField::getParamId, item -> item));
        newList.forEach(item -> {
            DataSourceField oldField = map.get(item.getParamId());
            if (oldField == null || oldField.getParamType() == null || !oldField.getParamType().equals(item.getParamType())) {
                return;
            }
            if (isReplaceAttachment) {
                item.setAttachment(oldField.getAttachment());
            }
            item.setSelected(oldField.getSelected());
            synchronizeField(oldField.getChildren(), item.getChildren(), isReplaceAttachment);
        });
    }

    private void synchronizeDatasourceRule(PlanDatasourceEntity entity, List<DataSourceField> resultFields) {
        List<PlanDatasourceRuleEntity> list = planDatasourceRuleDao.selectList(Wrappers.<PlanDatasourceRuleEntity>lambdaQuery()
                .eq(PlanDatasourceRuleEntity::getPlanDatasourceGUID, entity.getPlanDatasourceGUID()));
        list.forEach(item -> {
            List<DataSourceField> newList = JsonUtil.parse(JsonUtil.toString(resultFields), new TypeReference<List<DataSourceField>>() {});
            String resultMetadata = item.getResultMetadata();

            List<DataSourceField> oldList = JsonUtil.parse(resultMetadata, new TypeReference<List<DataSourceField>>() {});
            synchronizeField(oldList, newList, false);
            item.setResultMetadata(JsonUtil.toString(newList));
            planDatasourceRuleDao.updateById(item);
        });
    }

    private void synchronizePlanParams(PlanDatasourceEntity entity, List<DataSourceField> paramsFields) {
        List<PlanParamsEntity> list = planParamsDao.selectList(Wrappers.<PlanParamsEntity>lambdaQuery().eq(PlanParamsEntity::getPlanGUID, entity.getPlanGUID()));
        List<String> paramIds = paramsFields.stream().map(DataSourceField::getParamId).collect(Collectors.toList());
        list.forEach(item -> {
            String mapping = item.getMappingDatasource();
            if (StringUtils.isBlank(mapping) || mapping.indexOf(entity.getPlanDatasourceGUID()) < 0) {
                return;
            }
            List<String> mappings = Arrays.asList(mapping.split(DATASOURCE_FIELD_SEPARATOR));
            List<String> newMappings = mappings.stream().filter(m -> paramIds.contains(m.split(DATASOURCE_FIELD_ID_SEPARATOR)[1])).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(newMappings)) {
                mapping = newMappings.stream().collect(Collectors.joining(DATASOURCE_FIELD_SEPARATOR));
            } else {
                mapping = "";
            }
            item.setMappingDatasource(mapping);
            planParamsDao.updateById(item);
        });
    }

    private void deleteDataSourceParams(PlanDatasourceEntity entity) {
        List<PlanParamsEntity> list = planParamsDao.selectList(Wrappers.<PlanParamsEntity>lambdaQuery().eq(PlanParamsEntity::getPlanGUID, entity.getPlanGUID()));
        list.forEach(item -> {
            String mapping = item.getMappingDatasource();
            if (StringUtils.isBlank(mapping) || mapping.indexOf(entity.getPlanDatasourceGUID()) < 0) {
                return;
            }
            List<String> mappings = Arrays.asList(mapping.split(DATASOURCE_FIELD_SEPARATOR));
            List<String> newMappings = mappings.stream().filter(m -> !m.startsWith(entity.getPlanDatasourceGUID())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(newMappings)) {
                mapping = newMappings.stream().collect(Collectors.joining(DATASOURCE_FIELD_SEPARATOR));
            } else {
                mapping = "";
            }
            item.setMappingDatasource(mapping);
            planParamsDao.updateById(item);

        });
    }

    private void checkName(PlanDatasourceEntity entity) {
        LambdaQueryWrapper<PlanDatasourceEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(PlanDatasourceEntity::getPlanDatasourceGUID , entity.getPlanDatasourceGUID());
        wrapper.and(w -> w.eq(PlanDatasourceEntity::getPlanGUID, entity.getPlanGUID()));
        wrapper.and(w -> w.eq(PlanDatasourceEntity::getName, entity.getName()));
        Long count = planDatasourceDao.selectCount(wrapper);
        if (count > 0) {
            throw new BusinessLogicException("数据源名称重复");
        }
    }


}
