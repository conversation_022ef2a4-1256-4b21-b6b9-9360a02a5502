package com.mysoft.gptbuilder.agent.service.service.eval;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mysoft.gptbuilder.agent.model.dto.eval.EvalObjectDto;
import com.mysoft.gptbuilder.agent.model.dto.eval.EvalObjectParamsDto;
import com.mysoft.gptbuilder.agent.service.dao.PromptDao;
import com.mysoft.gptbuilder.agent.service.dao.PromptParamDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.PromptEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.PromptParamEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.eval.EvalTaskEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.eval.EvalTaskRecordEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.eval.PromptEvalResultEntity;
import com.mysoft.gptbuilder.agent.service.dao.eval.PromptEvalResultDao;
import com.mysoft.gptbuilder.common.model.enums.EvalTypeEnum;
import com.mysoft.gptbuilder.excel.service.ExcelToolUtil;
import com.mysoft.gptbuilder.excel.service.model.ExcelExportData;
import com.mysoft.gptbuilder.excel.service.watermark.ExcelWatermarkService;
import com.mysoft.sdk.aop.annotations.DisableAOP;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@DisableAOP
public class PromptEvalTask implements IEvalTask {

    @Resource
    private PromptDao promptDao;
    @Resource
    private PromptParamDao promptParamDao;
    @Resource
    private PromptEvalResultDao promptEvalResultDao;

    public static final String EXCEL_HEADER_PARAMS_NAME_PREFIX = "输入参数-";

    public static final String EXCEL_HEADER_RESULT_NAME = "预期输出";

    @Override
    public int source() {
        return EvalTypeEnum.PROMPT.getValue();
    }

    public List<EvalObjectDto> getEvalObjectList(EvalObjectParamsDto evalObjectParamsDto) {
        LambdaQueryWrapper<PromptEntity> wrapper = Wrappers.<PromptEntity>lambdaQuery().select(PromptEntity::getPromptGUID, PromptEntity::getPromptName)
                .eq(PromptEntity::getSpaceGUID, evalObjectParamsDto.getSpaceGUID());
        return promptDao.selectList(wrapper).stream().map(item -> {
            EvalObjectDto dto = new EvalObjectDto();
            dto.setText(item.getPromptName());
            dto.setValue(item.getPromptGUID());
            return dto;
        }).collect(Collectors.toList());
    }

    public void deleteResult(String evalTaskGUID) {
        promptEvalResultDao.delete(new LambdaQueryWrapper<PromptEvalResultEntity>().eq(PromptEvalResultEntity::getEvalTaskGUID, evalTaskGUID));
    }

    public void downloadDataset(String evalObjectGUID, HttpServletResponse response) {
        PromptEntity entity = promptDao.selectById(evalObjectGUID);
        List<PromptParamEntity> list = promptParamDao.selectList(new LambdaQueryWrapper<PromptParamEntity>().eq(PromptParamEntity::getPromptGUID, evalObjectGUID));
        String filename = entity.getPromptName() + "-评测集模版.xlsx";

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("评测集");
        HSSFRow headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("提示词编码");
        headerRow.createCell(1).setCellValue("提示词名称");
        AtomicInteger i = new AtomicInteger(2);
        list.stream().filter(item -> item.getParamType() == 1).forEach(item -> {
            headerRow.createCell(i.get()).setCellValue(EXCEL_HEADER_PARAMS_NAME_PREFIX + item.getParamCode());
            i.addAndGet(1);
        });
        headerRow.createCell(i.get()).setCellValue(EXCEL_HEADER_RESULT_NAME);

        HSSFRow dataRow = sheet.createRow(1);
        dataRow.createCell(0).setCellValue(entity.getPromptCode());
        dataRow.createCell(1).setCellValue(entity.getPromptName());
        byte[] bytes = new ExcelWatermarkService().addWatermark(workbook);

        ExcelExportData excelExportData = new ExcelExportData(filename, bytes);
        ExcelToolUtil.exportExcel(excelExportData, response);

    }


    public Object getJobData(EvalTaskEntity entity, EvalTaskRecordEntity recordEntity) {
        PromptEntity promptEntity = promptDao.selectById(entity.getEvalObject());
        return promptEntity;
    }

}
