package com.mysoft.gptbuilder.agent.service.service.skill.process;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.gptbuilder.agent.model.dto.skill.AbstractNodeConfigDto;
import com.mysoft.gptbuilder.agent.model.dto.skill.ChatBINodeConfigDto;
import com.mysoft.gptbuilder.agent.model.dto.skill.SkillNodeDto;
import com.mysoft.gptbuilder.agent.model.dto.skill.SkillOrchestrationContextDto;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Map;

public class ChatBINodeParseStrategy extends DefaultNodeConfigParseStrategy {

    @Override
    public String getTemplateUrl() {
        return "./ChatBINode.ftl";
    }

    @Override
    public String getNodeType() {
        return "ChatBI";
    }

    /**
     * 创建ChatBI节点的编排配置
     * @param skillOrchestrationContextDto 编排上下文
     * @param node 技能节点
     * @param stepNumber 步骤编号
     * @param nodeConfigDto 节点配置
     * @param paramsMap 参数映射
     * @param orchestrationGUIDs 编排GUID列表
     * @return 编排配置字符串
     */
    @Override
    public String createOrchestration(SkillOrchestrationContextDto skillOrchestrationContextDto, SkillNodeDto node, int stepNumber, AbstractNodeConfigDto nodeConfigDto, Map<String, AbstractNodeConfigDto.ParamDto> paramsMap, ArrayList<String> orchestrationGUIDs) {
        ChatBINodeConfigDto currentNodeConfigDto = (ChatBINodeConfigDto) nodeConfigDto;
        // 初始化该节点的paramsMap
        super.createOrchestration(skillOrchestrationContextDto, node, stepNumber, currentNodeConfigDto, paramsMap, orchestrationGUIDs);

        Map<String, Object> params = super.templateParams;
        
        // 处理知识库编码
        StringBuilder knowledgeCodesBuffer = new StringBuilder();
        if (!CollectionUtils.isEmpty(currentNodeConfigDto.getKnowledges())) {
            for (int i = 0; i < currentNodeConfigDto.getKnowledges().size(); i++) {
                if (i > 0) {
                    knowledgeCodesBuffer.append(",");
                }
                knowledgeCodesBuffer.append(currentNodeConfigDto.getKnowledges().get(i));
            }
        }
        params.put("knowledgeCodes", knowledgeCodesBuffer.toString());
        
        // 查询参数
        params.put("minScore", currentNodeConfigDto.getMinScore() != null ? currentNodeConfigDto.getMinScore() : 0.5);
        params.put("topK", currentNodeConfigDto.getTopK() != null ? currentNodeConfigDto.getTopK() : 3);
        params.put("asSource", currentNodeConfigDto.getAsSource() != null ? currentNodeConfigDto.getAsSource() : true);
        params.put("isCOR", currentNodeConfigDto.getIsCOR() != null ? currentNodeConfigDto.getIsCOR() : false);
        params.put("pattern", currentNodeConfigDto.getPattern() != null ? currentNodeConfigDto.getPattern() : 0);
        
        // 获取输入参数
        if (!CollectionUtils.isEmpty(currentNodeConfigDto.getInputs())) {
            AbstractNodeConfigDto.ParamDto input = currentNodeConfigDto.getInputs().get(0);
            AbstractNodeConfigDto.ParamValueDto paramValueDto = super.parseInputParam(input);
            if (!ObjectUtils.isEmpty(paramValueDto)) {
                params.put("query", paramValueDto.getContent());
            }
        } else {
            // 默认使用System_Input
            params.put("query", "System_Input");
        }
        
        return super.processTemplate();
    }

    @Override
    public ChatBINodeConfigDto parse(SkillNodeDto node) {
        Map<Object, Object> config = node.getConfig();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return mapper.convertValue(config, ChatBINodeConfigDto.class);
        } catch (Exception e) {
            throw new RuntimeException("解析ChatBI节点配置失败", e);
        }
    }

    /**
     * ChatBI节点参数校验
     * @param node 技能节点
     */
    @Override
    public void validate(SkillNodeDto node) {
        AbstractNodeConfigDto abstractNodeConfigDto = parse(node);
        ChatBINodeConfigDto currentNodeConfigDto = (ChatBINodeConfigDto) abstractNodeConfigDto;
        
        if (ObjectUtils.isEmpty(currentNodeConfigDto)) {
            throw new BusinessLogicException("节点【" + node.getName() + "】配置参数不允许为空");
        }
        
        if (currentNodeConfigDto.getTopK() != null && currentNodeConfigDto.getTopK() > 10) {
            throw new BusinessLogicException("节点【" + node.getName() + "】最大结果数量不能大于10");
        }
        
        if (currentNodeConfigDto.getMinScore() != null && currentNodeConfigDto.getMinScore() > 1) {
            throw new BusinessLogicException("节点【" + node.getName() + "】最小匹配评分不能大于1");
        }
        
        if (CollectionUtils.isEmpty(currentNodeConfigDto.getKnowledges())) {
            throw new BusinessLogicException("节点【" + node.getName() + "】必须配置至少一个知识库");
        }
        
        super.validate(node);
    }
}
