package com.mysoft.gptbuilder.agent.service.controller;

import com.mysoft.framework.rpc.annotation.PubAction;
import com.mysoft.framework.rpc.annotation.PubService;
import com.mysoft.framework.rpc.contants.RequestPrefix;
import com.mysoft.gptbuilder.agent.interfaces.IAssistantPublishPub;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantRemoteResourceResult;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceParams;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 助手发布公开接口
 * <AUTHOR>
 */
@PubService(value = "/gpt/assistant", prefix = RequestPrefix.PUB, businessCode = "42000101")
@Tag(name = "PUB-助手发布开放接口")
public class AssistantPublishPubController {

    @Autowired
    IAssistantPublishPub assistantPublishPub;

    @Operation(summary = "获取发布助手的信息", description = "获取发布助手的信息")
    @PubAction(value = "/getToken", method = RequestMethod.POST)
    public AssistantResourceResult getToken(@RequestBody AssistantResourceParams assistantResourceParams) {
        return assistantPublishPub.getToken(assistantResourceParams);
    }

    @Operation(summary = "获取远程助手的访问信息", description = "获取远程助手的访问信息")
    @PubAction(value = "/getRemoteToken", method = RequestMethod.POST)
    public AssistantRemoteResourceResult getRemoteToken(@RequestBody AssistantResourceParams assistantResourceParams) {
        return assistantPublishPub.getRemoteToken(assistantResourceParams);
    }

    @Operation(summary = "获取发布助手的访问URL", description = "获取发布助手的访问URL")
    @PubAction(value = "/getUrl", method = RequestMethod.POST)
    public String getUrl(@RequestBody AssistantResourceParams assistantResourceParams) {
        return assistantPublishPub.getUrl(assistantResourceParams);
    }
}
