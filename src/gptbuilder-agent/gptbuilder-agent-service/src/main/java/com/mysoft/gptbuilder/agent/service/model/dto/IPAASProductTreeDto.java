package com.mysoft.gptbuilder.agent.service.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mysoft.framework.service.dto.DTO;
import lombok.Data;

import java.util.List;

@Data
public class IPAASProductTreeDto extends DTO {

    @JsonProperty("Id")
    private String id;

    @JsonProperty("Name")
    private String name;

    @JsonProperty("NodeType")
    private Integer nodeType;

    @JsonProperty("ParentId")
    private String parentId;

    @JsonProperty("Children")
    private List<IPAASProductTreeDto> children;
}
