package com.mysoft.gptbuilder.agent.service.dao.eval;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mysoft.gptbuilder.agent.model.dto.eval.EvalResultStatisticsDto;
import com.mysoft.gptbuilder.agent.service.dao.entity.eval.SkillEvalResultEntity;

import java.util.List;

public interface SkillEvalResultDao extends BaseMapper<SkillEvalResultEntity> {

    List<EvalResultStatisticsDto> selectEvalConclusionByTaskRecordGUID(String guid);
}
