package com.mysoft.gptbuilder.agent.service.service.skill.process;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.gptbuilder.agent.model.dto.skill.*;
import com.mysoft.gptbuilder.common.util.StringUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Map;

public class SelectorNodeParseStrategy extends DefaultNodeConfigParseStrategy {
    @Override
    public String getTemplateUrl() {
        return "./SelectorNode.ftl";
    }

    @Override
    public String getNodeType() {
        return "Selector";
    }

    @Override
    public String createOrchestration(SkillOrchestrationContextDto skillOrchestrationContextDto, SkillNodeDto node, int stepNumber, AbstractNodeConfigDto nodeConfigDto, Map<String, AbstractNodeConfigDto.ParamDto> paramsMap, ArrayList<String> orchestrationGUIDs) {
        SelectorNodeConfigDto currentNodeConfigDto = (SelectorNodeConfigDto)nodeConfigDto;
        //初始化该节点的paramsMap
        super.createOrchestration(skillOrchestrationContextDto, node, stepNumber, nodeConfigDto, paramsMap, orchestrationGUIDs);

        Map<String, Object> params = super.templateParams;
        //构建插件模板的入参对象


//        List<SkillOrchestrationContextDto.OrchestrationPlugin> plugins = skillOrchestrationContextDto.getPlugins();
//        if (CollectionUtils.isEmpty(plugins)) {
//            plugins = new ArrayList<>();
//        }

//        //判断 plugins 里面是否存在相同的元素
//        if (!plugins.stream().anyMatch(plugin -> plugin.getPluginId().equals(currentNodeConfigDto.getPluginId()) && plugin.getToolId().equals(currentNodeConfigDto.getToolId()))) {
//            SkillOrchestrationContextDto.OrchestrationPlugin orchestrationPlugin = new SkillOrchestrationContextDto.OrchestrationPlugin();
//            orchestrationPlugin.setPluginId(currentNodeConfigDto.getPluginId());
//            orchestrationPlugin.setToolId(currentNodeConfigDto.getToolId());
//            plugins.add(orchestrationPlugin);
//            skillOrchestrationContextDto.setPlugins(plugins);
//        }
        return super.processTemplate();
    }

    @Override
    public SelectorNodeConfigDto parse(SkillNodeDto node) {
        Map<Object, Object> config = node.getConfig();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            SelectorNodeConfigDto configDto = mapper.convertValue(config, SelectorNodeConfigDto.class);
            // 使用person对象
            return configDto;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 插件参数校验
     * @param node
     */
    @Override
    public void validate(SkillNodeDto node) {
        AbstractNodeConfigDto abstractNodeConfigDto = parse(node);
        SelectorNodeConfigDto currentNodeConfigDto = (SelectorNodeConfigDto)abstractNodeConfigDto;
        if (CollectionUtils.isEmpty(currentNodeConfigDto.getConditions())) {
            throw new BusinessLogicException("选择器节点的分支条件不能为空");
        }
        ArrayList<String> noCheckOperator = new ArrayList<String>() {{
            add("isempty");
            add("notisempty");
        }};
        for (SelectorNodeConfigDto.ConditionsDTO condition : currentNodeConfigDto.getConditions())
        {
            for(SelectorNodeConfigDto.ConditionsDTO.ExpressionsDTO expression : condition.getExpressions())
            {
                for(SelectorNodeConfigDto.ConditionsDTO.ExpressionsDTO.RulesDTO rule : expression.getRules())
                {
                    if (StringUtil.isNullOrEmpty(rule.getLeft().getValue().getContent()) || (!noCheckOperator.contains(rule.getOperator()) && StringUtil.isNullOrEmpty(rule.getRight().getValue().getContent())))
                    {
                        throw new BusinessLogicException("节点【" + node.getName() + "】的分支【" + condition.getTitle() + "】参数设置错误");
                    }
                }
            }
        }
        super.validate(node);
    }
}
