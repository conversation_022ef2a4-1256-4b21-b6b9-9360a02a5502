package com.mysoft.gptbuilder.agent.service.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.api.document.DocumentProvider;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.framework.context.info.ContextInfo;
import com.mysoft.gptbuilder.agent.interfaces.IAssistantPublishPub;
import com.mysoft.gptbuilder.agent.model.dto.ApplicationUrlResponseDto;
import com.mysoft.gptbuilder.agent.model.dto.AssistantDto;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantRemoteResourceResult;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceParams;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceResult;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceToken;
import com.mysoft.gptbuilder.agent.service.dao.ApplicationDao;
import com.mysoft.gptbuilder.agent.service.dao.ApplicationReleaseDao;
import com.mysoft.gptbuilder.agent.service.dao.ApplicationSecurityDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.ApplicationEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.ApplicationReleaseEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.ApplicationSecurityEntity;
import com.mysoft.gptbuilder.common.model.consts.GPTEngineAddress;
import com.mysoft.gptbuilder.common.model.dto.DocumentDto;
import com.mysoft.gptbuilder.common.service.AppServiceBase;
import com.mysoft.gptbuilder.common.util.JwtUtils;
import com.mysoft.sdk.aop.annotations.DisableAOP;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
@DisableAOP
public class AssistantPublishPub extends AppServiceBase implements IAssistantPublishPub {
    @Autowired
    private ApplicationDao applicationDao;

    @Autowired
    private ApplicationReleaseDao applicationReleaseDao;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private AssistantService assistantService;

    @Autowired
    private DocumentProvider documentProvider;

    @Autowired
    private ApplicationSecurityDao applicationSecurityDao;

    private static final String JSP_PATH = "/gptbuilder/assistant/index.js";

    private static final String JSP_LOADER_PATH = "/gptbuilder/assistant/loader.js";

    private static final String ASSISTANT_INDEX_HTML_PATH = "/gptbuilder/assistant/index.html";

    /**
     * 已发布场景下使用
     * @param assistantResourceParams
     * @return {@link AssistantResourceResult }
     */
    @Override
    public AssistantResourceResult getToken(AssistantResourceParams assistantResourceParams) {
        if (StringUtils.isBlank(assistantResourceParams.getPublishCode())) {
            throw new BusinessLogicException("发布助手code不能为空");
        }
        ApplicationEntity applicationEntity = applicationDao.selectOne(Wrappers.<ApplicationEntity>lambdaQuery().eq(ApplicationEntity::getApplicationCode, assistantResourceParams.getPublishCode()));
        if (applicationEntity == null) {
            throw new BusinessLogicException("发布助手code不正确");
        }

        List<ApplicationReleaseEntity> list = applicationReleaseDao.selectList(Wrappers.<ApplicationReleaseEntity>lambdaQuery().eq(ApplicationReleaseEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessLogicException("没有正确的发布渠道");
        }

        ApplicationReleaseEntity applicationReleaseEntity = list.get(0);

        ApplicationSecurityEntity applicationSecurityEntity = applicationSecurityDao.selectOne(Wrappers.<ApplicationSecurityEntity>lambdaQuery()
                .eq(ApplicationSecurityEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));

        if (applicationSecurityEntity == null) {
            throw new BusinessLogicException("应用安全配置不存在");
        }

        AssistantResourceToken assistantResourceToken = new AssistantResourceToken();
        assistantResourceToken.setHistory(applicationReleaseEntity.getShowHistory().toString());
        assistantResourceToken.setBaseUrl(getGptEngineEndpoint());
        assistantResourceToken.setJsUrl(getGptEngineEndpoint() + JSP_LOADER_PATH);
        assistantResourceToken.setAccessToken(getAccessToken(assistantResourceParams, applicationSecurityEntity.getAppSecret()));
        assistantResourceToken.setShareCode(getShareCode(assistantResourceParams.getPublishCode(), applicationReleaseEntity.getReleaseCode()));
        String json = JsonUtil.toString(assistantResourceToken);
        String encodedString = Base64.getEncoder().encodeToString(json.getBytes());
        return new AssistantResourceResult(encodedString);
    }

    @Override
    public AssistantRemoteResourceResult getRemoteToken(AssistantResourceParams assistantResourceParams) {
        if (StringUtils.isBlank(assistantResourceParams.getPublishCode())) {
            throw new BusinessLogicException("发布助手code不能为空");
        }
        ApplicationEntity applicationEntity = applicationDao.selectOne(Wrappers.<ApplicationEntity>lambdaQuery().eq(ApplicationEntity::getApplicationCode, assistantResourceParams.getPublishCode()));
        if (applicationEntity == null) {
            throw new BusinessLogicException("发布助手code不正确");
        }

        List<ApplicationReleaseEntity> list = applicationReleaseDao.selectList(Wrappers.<ApplicationReleaseEntity>lambdaQuery().eq(ApplicationReleaseEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessLogicException("没有正确的发布渠道");
        }
        ApplicationReleaseEntity releaseEntity = list.stream().filter(item -> item.getReleaseCode().equals(assistantResourceParams.getReleaseCode())).findFirst().get();
        String JsUrl = getGptEngineEndpoint() + JSP_LOADER_PATH;

        ApplicationSecurityEntity applicationSecurityEntity = applicationSecurityDao.selectOne(Wrappers.<ApplicationSecurityEntity>lambdaQuery()
                .eq(ApplicationSecurityEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));

        if (applicationSecurityEntity == null) {
            throw new BusinessLogicException("应用安全配置不存在");
        }

        AssistantResourceToken assistantResourceToken = new AssistantResourceToken();
        assistantResourceToken.setHistory(releaseEntity.getShowHistory().toString());
        assistantResourceToken.setAccessToken(getAccessToken(assistantResourceParams, applicationSecurityEntity.getAppSecret()));
        assistantResourceToken.setShareCode(getShareCode(assistantResourceParams.getPublishCode(), releaseEntity.getReleaseCode()));
        String json = JsonUtil.toString(assistantResourceToken);
        String encodedString = Base64.getEncoder().encodeToString(json.getBytes());

        AssistantDto assistantDto = assistantService.queryAssistant(applicationEntity.getAssistantGUIDs().split(",")[0]);
        //获取图标 [{"name":"图标.png","documentGuid":"18509be1-2026-41a1-bfe2-afa706396727","checked":0,"isDefault":0,"size":10859}]
        AssistantRemoteResourceResult assistantResourceResult = new AssistantRemoteResourceResult();
        if (ObjectUtils.isEmpty(assistantDto)) {
            return assistantResourceResult;
        }
        if (assistantResourceParams.getIsIcon()  && StringUtils.isNotBlank(assistantDto.getIcon())) {
            try {
                JsonUtil.parse(assistantDto.getIcon(), new TypeReference<List<DocumentDto>>() {});

                List<DocumentDto> documentDtos = JsonUtil.parse(assistantDto.getIcon(), new TypeReference<List<DocumentDto>>() {
                });
                String iconData;
                try (InputStream download = documentProvider.download(documentDtos.get(0).getDocumentGuid())) {
                    //byte[] bytes = StreamUtils.copyToByteArray(download).toString();
                    assistantResourceResult.setIcon(StreamUtils.copyToByteArray(download));
                    assistantResourceResult.setFileName(documentDtos.get(0).getName());
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } catch (Exception e) {
                log.error("获取图标失败", e);
            }
        }
        assistantResourceResult.setJsTokenUrl(JsUrl + "?token=" + encodedString);
        return assistantResourceResult;
    }

    @Override
    public String getUrl(AssistantResourceParams assistantResourceParams) {
        if (StringUtils.isBlank(assistantResourceParams.getPublishCode())) {
            throw new BusinessLogicException("发布助手code不能为空");
        }
        ApplicationEntity applicationEntity = applicationDao.selectOne(Wrappers.<ApplicationEntity>lambdaQuery().eq(ApplicationEntity::getApplicationCode, assistantResourceParams.getPublishCode()));
        if (applicationEntity == null) {
            throw new BusinessLogicException("发布助手code不正确");
        }

        List<ApplicationReleaseEntity> list = applicationReleaseDao.selectList(Wrappers.<ApplicationReleaseEntity>lambdaQuery().eq(ApplicationReleaseEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessLogicException("没有正确的发布渠道");
        }
        ApplicationReleaseEntity applicationReleaseEntity = list.get(0);

        ApplicationSecurityEntity applicationSecurityEntity = applicationSecurityDao.selectOne(Wrappers.<ApplicationSecurityEntity>lambdaQuery()
                .eq(ApplicationSecurityEntity::getApplicationGUID, applicationEntity.getApplicationGUID()));

        if (applicationSecurityEntity == null) {
            throw new BusinessLogicException("应用安全配置不存在");
        }

        // 获取必要的参数
        String shareCode = getShareCode(assistantResourceParams.getPublishCode(), applicationReleaseEntity.getReleaseCode());
        String accessToken = getAccessToken(assistantResourceParams, applicationSecurityEntity.getAppSecret());
        String history = applicationReleaseEntity.getShowHistory().toString();
        String engineEndpoint = getGptEngineEndpoint();

        // 构建完整的URL
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(engineEndpoint)
                .append(ASSISTANT_INDEX_HTML_PATH)
                .append("?share=").append(shareCode)
                .append("&history=").append(history)
                .append("&access_token=").append(accessToken);

        return urlBuilder.toString();
    }

    private String getAccessToken(AssistantResourceParams assistantResourceParams, String appSecret){
        if (assistantResourceParams.getPayload() == null) {
            throw new BusinessLogicException("payload参数不能为空");
        }
        if (StringUtils.isEmpty(assistantResourceParams.getPayload().getCustomerGuid()) && StringUtils.isEmpty(assistantResourceParams.getPayload().getTenantCode())) {
            ContextInfo contextInfo = this.getContextInfo();
            if (!ObjectUtils.isEmpty(contextInfo)) {
                assistantResourceParams.getPayload().setTenantCode(contextInfo.getTenantCode());
                if (!ObjectUtils.isEmpty(contextInfo.getUserContext())) {
                    assistantResourceParams.getPayload().setCustomerGuid(contextInfo.getUserContext().getCustomerGuid());
                }
            }
        }
        return JwtUtils.generateJwtToken(assistantResourceParams.getPayload(), appSecret, JwtUtils.DefaultExpireTime);
    }

    private String getShareCode(String applicationCode, String releaseCode) {
        ApplicationUrlResponseDto applicationUrlResponse = applicationService.gptApplicationUrlParam();
        return applicationCode + "+" + releaseCode + "+" + applicationUrlResponse.getEncryptData();
    }

}
