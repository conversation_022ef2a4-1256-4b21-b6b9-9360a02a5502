package com.mysoft.gptbuilder.agent.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.gptbuilder.agent.interfaces.IMcpToolManagerService;
import com.mysoft.gptbuilder.agent.model.dto.mcp.McpToolDto;
import com.mysoft.gptbuilder.agent.service.dao.McpServiceToolDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.McpServiceToolEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MCP工具管理业务实现类
 * 实现MCP工具查询相关的业务逻辑操作
 */
@Slf4j
@Service
public class McpToolManagerService implements IMcpToolManagerService {

    @Autowired
    private McpServiceToolDao mcpServiceToolDao;

    /**
     * 根据工具GUID查询MCP工具信息
     *
     * @param toolGUID 工具GUID
     * @return MCP工具信息，如果未找到返回null
     */
    @Override
    public McpToolDto getToolByGUID(String toolGUID) {
        if (StringUtils.isBlank(toolGUID)) {
            throw new BusinessLogicException("工具GUID不能为空");
        }

        try {
            LambdaQueryWrapper<McpServiceToolEntity> query = Wrappers.<McpServiceToolEntity>lambdaQuery();
            query.eq(McpServiceToolEntity::getToolGUID, toolGUID)
                 .eq(McpServiceToolEntity::getStatus, 1);

            McpServiceToolEntity entity = mcpServiceToolDao.selectOne(query);
            
            if (entity == null) {
                log.warn("未找到工具GUID为 {} 的MCP工具", toolGUID);
                return null;
            }

            return convertToDto(entity);
            
        } catch (Exception e) {
            log.error("根据工具GUID查询MCP工具失败: toolGUID={}, error={}", toolGUID, e.getMessage(), e);
            throw new BusinessLogicException("查询MCP工具失败: " + e.getMessage());
        }
    }

    /**
     * 根据服务GUID查询该服务下的所有工具
     *
     * @param serviceGUID 服务GUID
     * @return 工具列表
     */
    @Override
    public List<McpToolDto> getToolsByServiceGUID(String serviceGUID) {
        if (StringUtils.isBlank(serviceGUID)) {
            throw new BusinessLogicException("服务GUID不能为空");
        }

        try {
            LambdaQueryWrapper<McpServiceToolEntity> query = Wrappers.<McpServiceToolEntity>lambdaQuery();
            query.eq(McpServiceToolEntity::getServiceGUID, serviceGUID)
                 .eq(McpServiceToolEntity::getStatus, 1)
                 .orderByAsc(McpServiceToolEntity::getToolName);

            List<McpServiceToolEntity> entities = mcpServiceToolDao.selectList(query);
            
            List<McpToolDto> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(entities)) {
                for (McpServiceToolEntity entity : entities) {
                    McpToolDto dto = convertToDto(entity);
                    result.add(dto);
                }
            }

            log.info("根据服务GUID查询到 {} 个工具: serviceGUID={}", result.size(), serviceGUID);
            return result;
            
        } catch (Exception e) {
            log.error("根据服务GUID查询工具列表失败: serviceGUID={}, error={}", serviceGUID, e.getMessage(), e);
            throw new BusinessLogicException("查询工具列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有启用状态的MCP工具
     *
     * @return 工具列表
     */
    @Override
    public List<McpToolDto> getAllEnabledTools() {
        try {
            LambdaQueryWrapper<McpServiceToolEntity> query = Wrappers.<McpServiceToolEntity>lambdaQuery();
            query.eq(McpServiceToolEntity::getStatus, 1)
                 .orderByAsc(McpServiceToolEntity::getServiceGUID)
                 .orderByAsc(McpServiceToolEntity::getToolName);

            List<McpServiceToolEntity> entities = mcpServiceToolDao.selectList(query);
            
            List<McpToolDto> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(entities)) {
                for (McpServiceToolEntity entity : entities) {
                    McpToolDto dto = convertToDto(entity);
                    result.add(dto);
                }
            }

            log.info("查询到 {} 个启用状态的MCP工具", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询所有启用工具失败: error={}", e.getMessage(), e);
            throw new BusinessLogicException("查询工具列表失败: " + e.getMessage());
        }
    }

    /**
     * 将实体转换为DTO，避免自动映射的字段类型问题
     *
     * @param entity MCP工具实体
     * @return MCP工具DTO
     */
    private McpToolDto convertToDto(McpServiceToolEntity entity) {
        McpToolDto dto = new McpToolDto();

        // 基本字段映射
        dto.setToolGUID(entity.getToolGUID());
        dto.setServiceGUID(entity.getServiceGUID());
        dto.setName(entity.getToolName());
        dto.setTitle(entity.getToolTitle());
        dto.setDescription(entity.getToolDescription());
        dto.setOutputSchema(entity.getOutputSchema());
        dto.setLastTestInput(entity.getLastTestInput());
        dto.setLastTestOutput(entity.getLastTestOutput());
        dto.setStatus(entity.getStatus());

        // 处理InputSchema字段 - 将JSON字符串转换为Map
        if (StringUtils.isNotBlank(entity.getInputSchema())) {
            try {
                Map<String, Object> inputSchemaMap = JsonUtil.parse(entity.getInputSchema(),
                    new TypeReference<Map<String, Object>>() {});
                dto.setInputSchema(inputSchemaMap);
            } catch (Exception e) {
                log.warn("解析InputSchema失败: toolGUID={}, error={}", entity.getToolGUID(), e.getMessage());
                dto.setInputSchema(null);
            }
        }

        return dto;
    }
}
