package com.mysoft.gptbuilder.agent.service.controller;

import com.mysoft.framework.mvc.annotation.ActionRight;
import com.mysoft.framework.mvc.api.Controller;
import com.mysoft.framework.rpc.annotation.PubAction;
import com.mysoft.framework.rpc.annotation.PubService;
import com.mysoft.framework.rpc.contants.RequestPrefix;
import com.mysoft.gptbuilder.agent.interfaces.IMcpManagerService;
import com.mysoft.gptbuilder.agent.model.dto.mcp.GetMcpToolsRequestDto;
import com.mysoft.gptbuilder.agent.model.dto.mcp.McpServiceDto;
import com.mysoft.gptbuilder.agent.model.dto.mcp.McpToolDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

import java.util.List;

/**
 * MCP服务管理控制器
 * 提供MCP服务管理相关的REST接口
 */
@PubService(value = "/mcpManager", prefix = RequestPrefix.API, businessCode = "42000801")
@Tag(name = "API-MCP服务管理")
public class McpManagerController extends Controller {

    @Autowired
    private IMcpManagerService mcpManagerService;

    /**
     * 删除MCP服务
     *
     * @param serviceGUID MCP服务的唯一标识符
     */
    @PubAction(value = "/deleteMcpService", method = RequestMethod.POST)
    @ActionRight(ActionName = "删除", ActionCode = "03")
    @Operation(summary = "删除MCP服务", description = "根据服务GUID删除MCP服务")
    public void deleteMcpService(@RequestParam("serviceGUID") String serviceGUID) {
        mcpManagerService.deleteMcpService(serviceGUID);
    }

    /**
     * 保存MCP服务（新建或更新）
     *
     * @param mcpServiceDto MCP服务信息
     * @return 保存后的服务GUID
     */
    @PubAction(value = "/saveMcpService", method = RequestMethod.POST)
    @ActionRight(ActionName = "新建", ActionCode = "01")
    @ActionRight(ActionName = "修改", ActionCode = "02")
    @Operation(summary = "保存MCP服务", description = "保存MCP服务信息，支持新建和修改")
    public String saveMcpService(@RequestBody McpServiceDto mcpServiceDto) {
        return mcpManagerService.saveMcpService(mcpServiceDto);
    }

    /**
     * 查询MCP服务信息
     *
     * @param serviceGUID MCP服务的唯一标识符
     * @return MCP服务信息
     */
    @PubAction(value = "/getMcpServiceInfo", method = RequestMethod.POST)
    @ActionRight(ActionName = "查询", ActionCode = "00")
    @Operation(summary = "查询MCP服务信息", description = "根据服务GUID查询MCP服务详细信息")
    public McpServiceDto getMcpServiceInfo(@RequestParam("serviceGUID") String serviceGUID) {
        return mcpManagerService.getMcpServiceInfo(serviceGUID);
    }

    /**
     * 获取MCP服务的工具列表
     *
     * @param serviceGUID MCP服务的唯一标识符
     * @return MCP工具列表
     */
    @PubAction(value = "/getMcpTools", method = RequestMethod.POST)
    @ActionRight(ActionName = "查询", ActionCode = "00")
    @Operation(summary = "获取MCP工具列表", description = "根据服务GUID获取MCP服务的工具列表")
    public List<McpToolDto> getMcpTools(@RequestParam("serviceGUID") String serviceGUID) {
        return mcpManagerService.getMcpTools(serviceGUID);
    }

    /**
     * 刷新MCP服务的工具列表
     *
     * @param request 包含MCP服务GUID的请求对象
     * @return 刷新后的工具列表
     */
    @PubAction(value = "/refreshMcpTools", method = RequestMethod.POST)
    @ActionRight(ActionName = "修改", ActionCode = "02")
    @Operation(summary = "刷新MCP工具列表", description = "从GPT引擎重新获取工具列表并更新数据库")
    public List<McpToolDto> refreshMcpTools(@Valid @RequestBody GetMcpToolsRequestDto request) {
        return mcpManagerService.refreshMcpTools(request);
    }
}