package com.mysoft.gptbuilder.agent.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.framework.modeling.dto.*;
import com.mysoft.gptbuilder.agent.interfaces.IMcpToolParameterService;
import com.mysoft.gptbuilder.agent.model.dto.mcp.ExecuteMcpToolRequestDto;
import com.mysoft.gptbuilder.agent.service.dao.McpServiceToolDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.McpServiceToolEntity;
import com.mysoft.gptbuilder.common.model.consts.GPTEngineAddress;
import com.mysoft.gptbuilder.common.service.AppServiceBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP工具参数管理业务实现类
 * 实现MCP工具参数管理相关的业务逻辑操作
 * 主要用于解析和管理MCP工具的InputSchema参数信息
 */
@Slf4j
@Service
public class McpToolParameterService extends AppServiceBase implements IMcpToolParameterService {

    @Autowired
    private McpServiceToolDao mcpServiceToolDao;

    /**
     * 获取显示字段设置
     * 实现LoadDataApi接口的loadFields方法
     * 返回四个字段：工具GUID、参数名称、参数类型、参数值
     *
     * @return 字段设置信息
     */
    @Override
    public FieldsSetting loadFields() {
        FieldsSetting fields = new FieldsSetting();
        List<DataApiField> list = new ArrayList<>();

        DataApiField toolGUID = new DataApiField();
        toolGUID.setFieldName("toolGUID");
        toolGUID.setDisplayName("工具GUID");
        toolGUID.setFieldType(FieldType.Guid);
        toolGUID.setGroupName("MCP_TOOL_PARAMETER");
        list.add(toolGUID);

        DataApiField paramName = new DataApiField();
        paramName.setFieldName("paramName");
        paramName.setDisplayName("参数名称");
        paramName.setFieldType(FieldType.String);
        paramName.setGroupName("MCP_TOOL_PARAMETER");
        list.add(paramName);

        DataApiField paramType = new DataApiField();
        paramType.setFieldName("paramType");
        paramType.setDisplayName("参数类型");
        paramType.setFieldType(FieldType.String);
        paramType.setGroupName("MCP_TOOL_PARAMETER");
        list.add(paramType);

        DataApiField paramValue = new DataApiField();
        paramValue.setFieldName("paramValue");
        paramValue.setDisplayName("参数值");
        paramValue.setFieldType(FieldType.String);
        paramValue.setGroupName("MCP_TOOL_PARAMETER");
        list.add(paramValue);

        fields.setFields(list);
        fields.setIdField("toolGUID");
        return fields;
    }

    /**
     * 加载参数数据列表
     * 实现LoadDataApi接口的loadData方法
     * 从gpt_McpServiceTool表的InputSchema字段解析参数信息
     * 根据toolGUID入参查询对应的参数列表
     *
     * @param options 加载数据参数
     * @return 参数数据列表结果
     */
    @Override
    public ListDataResult loadData(LoadDataParams options) {
        ListDataResult dto = new ListDataResult();

        try {
            // 从入参中提取toolGUID
            String toolGUID = extractToolGUIDFromOptions(options);

            if (StringUtils.isBlank(toolGUID)) {
                log.warn("未提供toolGUID参数，返回空结果");
                dto.setData(Lists.newArrayList());
                dto.setTotal(0);
                return dto;
            }

            // 根据toolGUID查询对应的MCP工具
            LambdaQueryWrapper<McpServiceToolEntity> query = Wrappers.<McpServiceToolEntity>lambdaQuery();
            query.eq(McpServiceToolEntity::getToolGUID, toolGUID)
                    .eq(McpServiceToolEntity::getStatus, 1);

            McpServiceToolEntity toolEntity = mcpServiceToolDao.selectOne(query);

            List<Map<String, Object>> parameterDataList = new ArrayList<>();

            if (toolEntity != null) {
                // 解析InputSchema字段，提取参数信息
                parameterDataList = parseInputSchema(toolEntity.getInputSchema(), toolEntity.getToolGUID());
            }

            dto.setData(parameterDataList);
            dto.setTotal(parameterDataList.size());

        } catch (Exception e) {
            log.error("加载MCP工具参数数据失败: {}", e.getMessage(), e);
            dto.setData(Lists.newArrayList());
            dto.setTotal(0);
        }

        return dto;
    }

    /**
     * 从LoadDataParams中提取toolGUID参数
     *
     * @param options 加载数据参数
     * @return toolGUID值，如果未找到则返回空字符串
     */
    private String extractToolGUIDFromOptions(LoadDataParams options) {
        try {
            // 参考PluginManagerService的实现方式
            SqlCondition sqlCondition = options.getOptions().getFilters().get(0).getCondition().get(0);
            String toolGUID = "";
            if ("MCP_TOOL_PARAMETER.toolGUID".equals(sqlCondition.getField())) {
                if (ObjectUtils.isNotEmpty(sqlCondition.getValue())) {
                    toolGUID = sqlCondition.getValue().toString();
                }
            }
            return toolGUID;
        } catch (Exception e) {
            log.warn("提取toolGUID参数失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 解析InputSchema JSON字符串，提取参数信息
     *
     * @param inputSchemaJson InputSchema JSON字符串
     * @param toolGUID        工具GUID
     * @return 参数列表，每个参数包含toolGUID、paramName、paramType、paramValue四个字段
     */
    private List<Map<String, Object>> parseInputSchema(String inputSchemaJson, String toolGUID) {
        List<Map<String, Object>> parameters = new ArrayList<>();

        if (StringUtils.isBlank(inputSchemaJson)) {
            return parameters;
        }

        try {
            // 解析JSON
            Map<String, Object> schema = JsonUtil.parse(inputSchemaJson, new TypeReference<Map<String, Object>>() {
            });

            // 获取properties字段
            Object propertiesObj = schema.get("properties");
            if (propertiesObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> properties = (Map<String, Object>) propertiesObj;

                // 获取required字段列表
                List<String> requiredFields = new ArrayList<>();
                Object requiredObj = schema.get("required");
                if (requiredObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> requiredList = (List<Object>) requiredObj;
                    for (Object item : requiredList) {
                        if (item instanceof String) {
                            requiredFields.add((String) item);
                        }
                    }
                }

                // 遍历每个参数
                for (Map.Entry<String, Object> entry : properties.entrySet()) {
                    String paramName = entry.getKey();
                    Object paramDefObj = entry.getValue();

                    if (paramDefObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> paramDef = (Map<String, Object>) paramDefObj;

                        // 提取参数类型
                        String paramType = (String) paramDef.get("type");
                        if (paramType == null) {
                            paramType = "string"; // 默认类型
                        }

                        // 提取参数描述作为参数值
                        String paramValue = (String) paramDef.get("description");
                        if (paramValue == null) {
                            paramValue = ""; // 默认为空
                        }

                        // 如果是必填字段，在参数值前加上标识
                        if (requiredFields.contains(paramName)) {
                            paramValue = "[必填] " + paramValue;
                        }

                        // 创建参数记录
                        Map<String, Object> parameter = new HashMap<>();
                        parameter.put("toolGUID", toolGUID);
                        parameter.put("paramName", paramName);
                        parameter.put("paramType", paramType);
                        parameter.put("paramValue", paramValue);

                        parameters.add(parameter);
                    }
                }
            }

        } catch (Exception e) {
            log.warn("解析InputSchema失败: {}, 原始数据: {}", e.getMessage(), inputSchemaJson);
        }

        return parameters;
    }

    /**
     * 执行MCP工具
     * 调用GPT引擎的MCP工具执行接口
     *
     * @param request 执行MCP工具请求
     * @return 执行结果
     */
    @Override
    public Object executeMcpTool(ExecuteMcpToolRequestDto request) {
        try {
            // 根据toolGUID查询对应的MCP工具，获取serviceGUID
            LambdaQueryWrapper<McpServiceToolEntity> query = Wrappers.lambdaQuery();
            query.eq(McpServiceToolEntity::getToolGUID, request.getToolGUID())
                    .eq(McpServiceToolEntity::getStatus, 1);

            McpServiceToolEntity toolEntity = mcpServiceToolDao.selectOne(query);

            if (toolEntity == null) {
                log.error("未找到工具GUID为 {} 的MCP工具", request.getToolGUID());
                throw new RuntimeException("未找到指定的MCP工具");
            }

            // 构建请求体，按照GPT引擎期望的格式
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("ServiceGUID", toolEntity.getServiceGUID());

            Map<String, Object> executeRequest = new HashMap<>();
            executeRequest.put("ToolGUID", request.getToolGUID());
            executeRequest.put("Arguments", request.getArguments());
            requestBody.put("ExecuteRequest", executeRequest);

            // 使用系统标准的callEngine方法调用GPT引擎
            Map<String, Object> engineResponse = callEngine(GPTEngineAddress.EXECUTE_MCP_TOOL, requestBody, Map.class, (Cookie[]) null);

            // 检查引擎响应是否成功
            if (engineResponse == null) {
                log.error("GPT引擎返回空响应: toolGUID={}", request.getToolGUID());
                throw new RuntimeException("GPT引擎返回空响应");
            }

            Boolean success = (Boolean) engineResponse.get("success");
            if (success == null || !success) {
                String errorMessage = (String) engineResponse.get("message");
                log.error("GPT引擎执行失败: toolGUID={}, error={}", request.getToolGUID(), errorMessage);
                throw new RuntimeException("GPT引擎执行失败: " + errorMessage);
            }

            // 提取result字段并反序列化为Object，然后替换data中的result
            String resultJson = (String) engineResponse.get("result");
            if (StringUtils.isNotBlank(resultJson)) {
                try {
                    // 将JSON字符串反序列化为Object
                    Object resultObject = JsonUtil.parse(resultJson, Object.class);
                    // 替换data中的result字段
                    engineResponse.put("result", resultObject);
                } catch (Exception e) {
                    log.warn("解析result JSON失败，保持原始字符串: toolGUID={}, error={}",
                            request.getToolGUID(), e.getMessage());
                    // 如果解析失败，保持原始字符串
                }
            }

            log.info("MCP工具执行成功: toolGUID={}, serviceGUID={}",
                    request.getToolGUID(), toolEntity.getServiceGUID());

            // 返回整个data对象
            return engineResponse;

        } catch (Exception e) {
            log.error("执行MCP工具异常: toolGUID={}, error={}",
                    request.getToolGUID(), e.getMessage(), e);
            throw e;
        }
    }
}
