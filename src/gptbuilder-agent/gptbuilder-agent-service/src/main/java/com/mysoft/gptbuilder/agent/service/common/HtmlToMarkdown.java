package com.mysoft.gptbuilder.agent.service.common;

import com.vladsch.flexmark.html.renderer.ResolvedLink;
import com.vladsch.flexmark.html2md.converter.*;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.data.MutableDataHolder;
import com.vladsch.flexmark.util.data.MutableDataSet;
import org.jetbrains.annotations.NotNull;
import org.jsoup.nodes.Node;

import java.util.Base64;
import java.util.Collections;
import java.util.Set;

public class HtmlToMarkdown {

    public static boolean checkIsBase64Image(String imageUrl) {
        if (imageUrl.startsWith("data:image/")) {
            imageUrl = imageUrl.substring(imageUrl.indexOf(",") + 1); // 去掉前缀
            try {
                Base64.getDecoder().decode(imageUrl);
                return true;
            } catch (IllegalArgumentException e) {
                return false;
            }
        }
        return false;
    }

    public static String convert(String html){
        MutableDataSet options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, Collections.emptySet());
//        options.set(Parser.EXTENSIONS, Collections.singletonList(HtmlConverterTextExtension.create()));
        options.set(FlexmarkHtmlConverter.SETEXT_HEADINGS, false);

        String markdown = GptFlexmarkHtmlConverter.builder(options).build().convert(html);

        markdown = markdown.replaceAll("\\n{2,}", "\n").replaceAll("<br />", "").replaceAll("!\\[\\]\\(\\)", "");
        return markdown;
    }


    static class CustomLinkResolver implements HtmlLinkResolver {
        public CustomLinkResolver(HtmlNodeConverterContext context) {
        }

        @Override
        public ResolvedLink resolveLink(Node node, HtmlNodeConverterContext context, ResolvedLink link) {
            // convert all links from http:// to https://
            if (HtmlToMarkdown.checkIsBase64Image(link.getUrl())) {
                return link.withUrl("");
            }
            return link;
        }

        static class Factory implements HtmlLinkResolverFactory {
            @Override
            public Set<Class<?>> getAfterDependents() {
                return null;
            }

            @Override
            public Set<Class<?>> getBeforeDependents() {
                return null;
            }

            @Override
            public boolean affectsGlobalScope() {
                return false;
            }

            @Override
            public HtmlLinkResolver apply(HtmlNodeConverterContext context) {
                return new CustomLinkResolver(context);
            }
        }
    }

    static class HtmlConverterTextExtension implements GptFlexmarkHtmlConverter.HtmlConverterExtension {
        public static HtmlConverterTextExtension create() {
            return new HtmlConverterTextExtension();
        }

        @Override
        public void rendererOptions(@NotNull MutableDataHolder options) {

        }

        @Override
        public void extend(GptFlexmarkHtmlConverter.@NotNull Builder builder) {
            builder.linkResolverFactory(new CustomLinkResolver.Factory());
        }
    }

}


