package com.mysoft.gptbuilder.agent.service.controller;

import com.mysoft.framework.mvc.annotation.ActionRight;
import com.mysoft.framework.mvc.api.Controller;
import com.mysoft.framework.rpc.annotation.PubAction;
import com.mysoft.framework.rpc.annotation.PubService;
import com.mysoft.framework.rpc.contants.RequestPrefix;
import com.mysoft.gptbuilder.agent.interfaces.IMcpToolManagerService;
import com.mysoft.gptbuilder.agent.model.dto.mcp.McpToolDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * MCP工具管理控制器
 * 提供MCP工具查询相关的REST接口
 */
@PubService(value = "/mcpToolManager", prefix = RequestPrefix.API, businessCode = "42000801")
@Tag(name = "API-MCP工具管理")
public class McpToolManagerController extends Controller {

    @Autowired
    private IMcpToolManagerService mcpToolManagerService;

    /**
     * 根据工具GUID查询MCP工具信息
     *
     * @param toolGUID 工具GUID
     * @return MCP工具信息
     */
    @PubAction(value = "/getToolByGUID", method = RequestMethod.POST)
    @ActionRight(ActionName = "查询", ActionCode = "01")
    @Operation(summary = "根据工具GUID查询MCP工具", description = "根据提供的工具GUID查询对应的MCP工具详细信息")
    public McpToolDto getToolByGUID(@RequestParam("toolGUID") String toolGUID) {
        return mcpToolManagerService.getToolByGUID(toolGUID);
    }
}
