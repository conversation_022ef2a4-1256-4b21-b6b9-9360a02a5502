package com.mysoft.gptbuilder.agent.service.service.skill.process;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
/**
 * 节点配置解析策略工厂类，用于根据节点类型创建相应的配置解析策略实例。
 */
@Slf4j
public class NodeConfigParseStrategyFactory {

    // 服务注册表，使用ConcurrentHashMap以支持并发，存储节点类型与对应的解析策略
    private static final Map<String, NodeConfigParseStrategy> serviceRegistry = new ConcurrentHashMap<>();

    static {
        // 在类加载时初始化服务注册表，动态加载所有实现了NodeConfigParseStrategy接口的类
        // 并将节点类型与解析策略实例注册到服务注册表中
//        for (NodeConfigParseStrategy strategy : ServiceLoader.load(NodeConfigParseStrategy.class)) {
//            try {
//                String nodeType = strategy.getNodeType();
//                if (nodeType != null && strategy != null) {
//                    serviceRegistry.put(nodeType, strategy);
//                }
//            } catch (Exception e) {
//                log.error("Failed to load NodeConfigParseStrategy", e);
//            }
//        }
        serviceRegistry.put("Start", new SystemPluginNodeParseStrategy());
        serviceRegistry.put("PromptTemplate", new PromptTemplateNodeParseStrategy());
        serviceRegistry.put("Card", new CardNodeParseStrategy());
        serviceRegistry.put("Plugin", new PluginNodeParseStrategy());
        serviceRegistry.put("Knowledge", new KnowledgeNodeParseStrategy());
        serviceRegistry.put("DocumentAnalysis", new DocumentAnalysisNodeParseStrategy());
        serviceRegistry.put("Selector", new SelectorNodeParseStrategy());
        serviceRegistry.put("Page", new PageNodeParseStrategy());
        serviceRegistry.put("End", new EndNodeParseStrategy());
        serviceRegistry.put("ImageAnalysis", new ImageAnalysisNodeParseStrategy());
        serviceRegistry.put("FormBinding", new FormBindingNodeParseStrategy());
        serviceRegistry.put("TextTemplate", new TextTemplateNodeParseStrategy());
        serviceRegistry.put("Classification", new ClassificationNodeParseStrategy());
        serviceRegistry.put("Plan", new PlanNodeParseStrategy());
        serviceRegistry.put("ChatBI", new ChatBINodeParseStrategy());

    }

    /**
     * 根据给定的节点类型创建对应的配置解析策略实例。
     *
     * @param type 节点类型，对应解析策略的标识。
     * @return 返回与节点类型匹配的配置解析策略实例。如果找不到匹配的策略，则返回默认的解析策略实例。
     */
    public static NodeConfigParseStrategy createStrategy(String type) {
        // 使用Optional来避免返回null，增强API的健壮性
        return serviceRegistry.get(type);
    }

}

