package com.mysoft.gptbuilder.agent.service.dao.entity;
 
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mysoft.framework.mybatis.BaseEntity;
import lombok.Data;

/**
 * @description (gpt_chat)表实体类
 * <AUTHOR>
 * @date 2024-06-24 10:13:08
 */
@Data
@TableName("gpt_ChatMessageKnowledgeNodeLog")
public class ChatMessageKnowledgeNodeLogEntity extends BaseEntity {
    @TableId(value = "ChatMessageKnowledgeNodeLogGUID", type = IdType.INPUT)
    private String chatMessageKnowledgeNodeLogGUID;

    @TableField("ChatGUID")
    private String chatGUID;

    @TableField("BatchGUID")
    private String batchGUID;

    @TableField("ApplicationGUID")
    private String applicationGUID;

    @TableField("AssistantGUID")
    private String assistantGUID;

    @TableField("SkillGUID")
    private String skillGUID;

    @TableField("NodeGUID")
    private String nodeGUID;

    @TableField("KnowledgeCode")
    private String knowledgeCode;

    @TableField("KnowledgeFileSectionGUID")
    private String knowledgeFileSectionGUID;

    @TableField("Inputs")
    private String inputs;

    @TableField("Outputs")
    private String outputs;

    @TableField("CustomerId")
    private String customerId;

    @TableField("CustomerName")
    private String customerName;

    @TableField("UserGUID")
    private String userGUID;

    @TableField("UserName")
    private String userName;

    @TableField("TenantCode")
    private String tenantCode;

    @TableField("TenantName")
    private String tenantName;

    @TableField("Score")
    private double score;


}
