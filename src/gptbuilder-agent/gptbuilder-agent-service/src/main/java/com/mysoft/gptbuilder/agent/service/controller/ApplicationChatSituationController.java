package com.mysoft.gptbuilder.agent.service.controller;

import com.mysoft.framework.mvc.api.Controller;
import com.mysoft.framework.rpc.annotation.PubService;
import com.mysoft.framework.rpc.contants.RequestPrefix;
import com.mysoft.gptbuilder.agent.interfaces.IChatService;
import com.mysoft.gptbuilder.agent.interfaces.IChatUserLogService;
import com.mysoft.gptbuilder.agent.model.dto.ApplicationChatSituationRequestDto;
import com.mysoft.gptbuilder.agent.model.dto.ApplicationChatSituationResponseDto;
import com.mysoft.gptbuilder.agent.model.dto.ChatUserLogRequest;
import com.mysoft.gptbuilder.agent.model.dto.WorkSpaceDto;
import com.mysoft.gptbuilder.agent.service.service.WorkSpaceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
/**
 * 助手管理
 */
@PubService(value = "/applicationChatSituation", prefix = RequestPrefix.API, businessCode = "42000702")
@Tag(name = "API-会话用户行为日志")
public class ApplicationChatSituationController extends Controller {

    @Autowired
    IChatUserLogService chatUserLogService;

    @Autowired
    IChatService chatService;

    @Autowired
    WorkSpaceService workSpaceService;

    /**
     * 用户反馈接口
     *
     * @param chatUserLogRequest
     */
    @PostMapping(value="/feedBack")
    public boolean feedBack(@RequestBody ChatUserLogRequest chatUserLogRequest)
    {
        return chatUserLogService.feedBack(chatUserLogRequest);
    }

    /**
     * 应用情况分析接口
     *
     * @param applicationChatSituationRequestDto
     */
    @PostMapping(value="/summary")
    public ApplicationChatSituationResponseDto summary(@RequestBody ApplicationChatSituationRequestDto applicationChatSituationRequestDto)
    {
        if (applicationChatSituationRequestDto.getSpaceGUIDs() != null
                && applicationChatSituationRequestDto.getSpaceGUIDs().size() == 1
                && StringUtils.isEmpty(applicationChatSituationRequestDto.getSpaceGUIDs().get(0))) {
            applicationChatSituationRequestDto.setSpaceGUIDs(null);
        }
        return chatService.summary(applicationChatSituationRequestDto);
    }

    /**
     * 应用情况分析接口
     *
     * @param applicationChatSituationRequestDto
     */
    @PostMapping(value="/rankSummary")
    public ApplicationChatSituationResponseDto rankSummary(@RequestBody ApplicationChatSituationRequestDto applicationChatSituationRequestDto)
    {
        if (applicationChatSituationRequestDto.getSpaceGUIDs() != null
                && applicationChatSituationRequestDto.getSpaceGUIDs().size() == 1
                && StringUtils.isEmpty(applicationChatSituationRequestDto.getSpaceGUIDs().get(0))) {
            applicationChatSituationRequestDto.setSpaceGUIDs(null);
        }
        return chatService.rankSummary(applicationChatSituationRequestDto);
    }

    /**
     * 技能每日使用统计
     *
     * @param applicationChatSituationRequestDto
     */
    @PostMapping(value="/graphSummary")
    public ApplicationChatSituationResponseDto graphSummary(@RequestBody ApplicationChatSituationRequestDto applicationChatSituationRequestDto)
    {
        if (applicationChatSituationRequestDto.getSpaceGUIDs() != null
                && applicationChatSituationRequestDto.getSpaceGUIDs().size() == 1
                && StringUtils.isEmpty(applicationChatSituationRequestDto.getSpaceGUIDs().get(0))) {
            applicationChatSituationRequestDto.setSpaceGUIDs(null);
        }
        return chatService.graphSummary(applicationChatSituationRequestDto);
    }

    @PostMapping(value="/chatSpace")
    protected List<WorkSpaceDto> chatSpace() {
        return workSpaceService.chatSpace();
    }

}
