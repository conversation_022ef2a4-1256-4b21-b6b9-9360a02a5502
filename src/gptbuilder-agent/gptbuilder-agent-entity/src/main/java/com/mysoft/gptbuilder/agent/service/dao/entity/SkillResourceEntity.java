package com.mysoft.gptbuilder.agent.service.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mysoft.framework.mybatis.BaseEntity;
import lombok.Data;

/**
 * @description (gpt_SkillResource)表实体类
 * <AUTHOR>
 * @date 2025-03-6 10:07:41
 */
@Data
@TableName("gpt_SkillResource")
public class SkillResourceEntity extends BaseEntity {

    @TableId(value = "SkillResourceGUID", type = IdType.INPUT)
    private String skillResourceGUID;

    @TableField("SpaceGUID")
    private String spaceGUID;

    @TableField("SkillGUID")
    private String skillGUID;

    @TableField("ResourceGUID")
    private String resourceGUID;

    @TableField("ResourceCode")
    private String resourceCode;

    @TableField("ResourceType")
    private String resourceType;

    @TableField("NodeGUID")
    private String nodeGUID;
}
