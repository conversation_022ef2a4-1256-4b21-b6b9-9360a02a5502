package com.mysoft.gptbuilder.agent.service.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mysoft.framework.mybatis.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gpt_Supervision")
public class SupervisionEntity extends BaseEntity {

    @TableId(value="SupervisionGUID", type= IdType.INPUT)
    private String SupervisionGUID;

    @TableField(value="AssociateTaskName")
    private String AssociateTaskName;

    @TableField(value="DutyUserName")
    private String DutyUserName;

    @TableField(value="Remark")
    private String Remark;

    @TableField(value="ExpectedFinishDate")
    private Date ExpectedFinishDate;
}
