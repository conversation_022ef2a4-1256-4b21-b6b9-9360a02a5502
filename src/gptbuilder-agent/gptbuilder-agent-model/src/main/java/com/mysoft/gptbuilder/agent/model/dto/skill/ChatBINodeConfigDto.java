package com.mysoft.gptbuilder.agent.model.dto.skill;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ChatBI节点配置实体：
 * 用于数据查询与总结功能
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("ChatBI")
public class ChatBINodeConfigDto extends AbstractNodeConfigDto {

    /**
     * 知识库编码列表
     */
    @SerializedName("knowledges")
    private List<String> knowledges;
}
