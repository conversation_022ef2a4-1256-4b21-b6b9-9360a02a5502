package com.mysoft.gptbuilder.agent.model.dto.skill;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ChatBI节点配置实体：
 * 用于数据查询与总结功能
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("ChatBI")
public class ChatBINodeConfigDto extends AbstractNodeConfigDto {

    /**
     * 知识库编码列表
     */
    @SerializedName("knowledges")
    private List<String> knowledges;

    /**
     * 最小匹配评分
     */
    @SerializedName("minScore")
    private Double minScore = 0.5;

    /**
     * 最大结果数量
     */
    @SerializedName("topK")
    private Integer topK = 3;

    /**
     * 是否作为来源
     */
    @SerializedName("asSource")
    private Boolean asSource = true;

    /**
     * 是否使用COR模式
     */
    @SerializedName("isCOR")
    private Boolean isCOR = false;

    /**
     * 查询模式
     */
    @SerializedName("pattern")
    private Integer pattern = 0;
}
