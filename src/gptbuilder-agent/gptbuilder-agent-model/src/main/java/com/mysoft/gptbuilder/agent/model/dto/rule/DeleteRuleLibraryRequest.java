package com.mysoft.gptbuilder.agent.model.dto.rule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
public class DeleteRuleLibraryRequest {

    @Schema(description = "规则库GUID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "规则库GUID不能为空")
    private String ruleLibraryGUID;

}
