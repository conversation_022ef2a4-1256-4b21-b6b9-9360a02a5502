package com.mysoft.gptbuilder.agent.model.dto.skill;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class AssistantChatDto {

    /**
     * input
     */
    @SerializedName("input")
    private String input;
    /**
     * chatGUID
     */
    @SerializedName("chatGUID")
    private String chatGUID;
    /**
     * assistanGUID
     */
    @SerializedName("assistanGUID")
    private String assistanGUID;
    /**
     * skillGUID
     */
    @SerializedName("skillGUID")
    private String skillGUID;

    @SerializedName("pluginGUID")
    private String pluginGUID;

    @SerializedName("tool")
    private String tool;

    /**
     * arguments
     */
    @SerializedName("arguments")
    private List<ArgumentsDTO> arguments;
    /**
     * next
     */
    @SerializedName("next")
    private Boolean next;

    /**
     * ArgumentsDTO
     */
    @NoArgsConstructor
    @Data
    public static class ArgumentsDTO {
        /**
         * key
         */
        @SerializedName("key")
        private String key;
        /**
         * value
         */
        @SerializedName("value")
        private String value;
    }
}
