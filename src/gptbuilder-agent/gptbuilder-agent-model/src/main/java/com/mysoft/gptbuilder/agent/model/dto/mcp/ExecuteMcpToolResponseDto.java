package com.mysoft.gptbuilder.agent.model.dto.mcp;

import com.mysoft.framework.service.dto.DTO;
import lombok.Data;

import java.util.Map;

/**
 * 执行MCP工具响应DTO
 */
@Data
public class ExecuteMcpToolResponseDto extends DTO {

    /**
     * 执行是否成功
     */
    private Boolean success;

    /**
     * 错误消息（如果执行失败）
     */
    private String errorMessage;

    /**
     * 执行结果数据
     */
    private Map<String, Object> data;

    /**
     * 执行耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 工具GUID
     */
    private String toolGUID;

    /**
     * 服务GUID
     */
    private String serviceGUID;
}
