package com.mysoft.gptbuilder.agent.model.dto.mcp;

import com.mysoft.framework.service.dto.DTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 执行MCP工具请求DTO
 */
@Data
public class ExecuteMcpToolRequestDto extends DTO {

    /**
     * 工具GUID
     */
    @NotBlank(message = "工具GUID不能为空")
    private String toolGUID;

    /**
     * 工具参数
     */
    private Map<String, Object> arguments;
}
