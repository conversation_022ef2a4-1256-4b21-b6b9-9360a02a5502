package com.mysoft.gptbuilder.agent.model.dto.pub.approval;

import com.mysoft.framework.service.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 检查方案规则分组关系
 * <AUTHOR>
 * @date 2025/05/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "规则分组对象")
public class RuleGroupRelationshipDTO extends DTO {

    @Schema(description = "父规则组GUID")
    private String parentGUID;

    @Schema(description = "规则组GUID")
    private String groupGUID;

    @Schema(description = "规则组名称")
    private String groupName;

    @Schema(description = "排序")
    private int sort;

}
