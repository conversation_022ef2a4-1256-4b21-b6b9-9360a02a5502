package com.mysoft.gptbuilder.agent.model.dto;

import com.google.gson.annotations.SerializedName;
import com.mysoft.gptbuilder.agent.model.dto.skill.AbstractNodeConfigDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class ParamMappingRequestDto {
    public List<ParamDto> source;
    public List<ParamDto> target;

    @NoArgsConstructor
    @Data
    public static class ParamDto {
        /**
         * 参数编码
         */
        @SerializedName("code")
        private String code;
        /**
         * 参数类型
         */
        @SerializedName("type")
        private String type;
        /**
         * 参数名称
         */
        @SerializedName("name")
        private String name;
        /**
         * 描述
         */
        @SerializedName("description")
        private String description;
    }
}
