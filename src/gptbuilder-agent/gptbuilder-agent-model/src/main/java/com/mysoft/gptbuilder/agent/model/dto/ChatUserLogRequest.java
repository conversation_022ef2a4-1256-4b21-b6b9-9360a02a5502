package com.mysoft.gptbuilder.agent.model.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class ChatUserLogRequest {

    @SerializedName("chatUserLogGUID")
    private String chatUserLogGUID;

    @SerializedName("assistantGUID")
    private String assistantGUID;

    @SerializedName("skillGUID")
    private String skillGUID;

    @SerializedName("chatGUID")
    private String chatGUID;

    @SerializedName("batchGUID")
    private String batchGUID;

    @SerializedName("feedBack")
    private Integer feedBack;

    @SerializedName("feedBackContent")
    private String feedBackContent;
}
