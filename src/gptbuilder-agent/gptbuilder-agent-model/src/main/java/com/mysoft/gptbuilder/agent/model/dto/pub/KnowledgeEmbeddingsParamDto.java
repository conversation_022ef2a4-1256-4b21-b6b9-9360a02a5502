package com.mysoft.gptbuilder.agent.model.dto.pub;

import com.mysoft.framework.service.dto.DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/30 14:04
 */
@Data
public class KnowledgeEmbeddingsParamDto extends DTO {

    @Schema(description = "模型实例编码", required = true)
    @NotBlank(message = "模型实例编码")
    private String modelInstanceCode;

    @Schema(description = "文本内容", required = true)
    @NotEmpty(message = "文本内容必填")
    private List<String> input;
}
