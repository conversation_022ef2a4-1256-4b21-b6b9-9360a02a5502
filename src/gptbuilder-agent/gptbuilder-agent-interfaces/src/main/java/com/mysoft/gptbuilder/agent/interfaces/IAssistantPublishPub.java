package com.mysoft.gptbuilder.agent.interfaces;

import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantRemoteResourceResult;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceParams;
import com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceResult;

/**
 * 助手发布公开接口
 * <AUTHOR>
 */
public interface IAssistantPublishPub {

    /**
     * 获取发布助手的信息
     * {
     *   "shareCode": "发布渠道分享码",
     *   "baseUrl": "引擎所属地址的baseUrl（https://域名+端口/ ,如：https://gptengine.apaas.mypaas.com）"
     *   "jsUrl": "https://gptengine.apaas.mypaas.com/gptbuilder/assistant/index.js",
     *   "accessToken": "用户Token"
     * }
     * <AUTHOR>
     * @param  assistantResourceParams
     * @return  com.mysoft.gptbuilder.agent.model.dto.pub.AssistantResourceResult
     */
    AssistantResourceResult getToken(AssistantResourceParams assistantResourceParams);

    AssistantRemoteResourceResult getRemoteToken(AssistantResourceParams assistantResourceParams);

    /**
     * 获取发布助手的访问URL
     * 返回格式：{engineEndpoint}/gptengine/gptbuilder/assistant/index.html?share=xxx&history=0&access_token=xxx
     * <AUTHOR>
     * @param  assistantResourceParams
     * @return  java.lang.String 完整的访问URL
     */
    String getUrl(AssistantResourceParams assistantResourceParams);
}
