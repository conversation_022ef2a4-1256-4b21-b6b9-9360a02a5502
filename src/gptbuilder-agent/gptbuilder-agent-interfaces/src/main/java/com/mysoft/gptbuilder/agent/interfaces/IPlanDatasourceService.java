package com.mysoft.gptbuilder.agent.interfaces;

import com.mysoft.framework.modeling.dto.FieldsSetting;
import com.mysoft.framework.modeling.dto.Filter;
import com.mysoft.framework.modeling.dto.LoadDataParams;
import com.mysoft.framework.modeling.dto.OptionItem;
import com.mysoft.gptbuilder.agent.model.dto.plan.*;

import java.util.List;
import java.util.Map;

public interface IPlanDatasourceService {

    List<PlanDatasourceDto> findDatasourceList(LoadDataParams loadDataParams);

    FieldsSetting planDatasourceLoadFields();

    List<DataSourceSource> findDataSourceSource(int source, String workSpaceGUID, String groupId);

    List<DataSourceSource> findDataSourceSourceGroup(int source, String workSpaceGUID);

    DataSourceMetadata findDataSourceSourceMetadata(int source, String sourceId, String planDatasourceGUID, String workSpaceGUID);

    String saveDataSource(PlanDatasourceDto planDatasourceDto);

    void deleteDataSource(String planDatasourceGUID);

    DatasourceRuleMetadata findDatasourceRuleMetadata(String planDatasourceGUID, String ruleGUID);

    List<String> findDatasourceGUIDByRule(String ruleGUID);

    List<PlanParamsDto> dataSourceParamsList(String planGUID);

    List<OptionItem> findAllParamsFields(List<Filter> filters, Map<String, String> urlParams);

    List<PlanParamsDto> findPlanParams(String planGUID);

    List<DataSourceField> formatResultMetadata(int source, String metadata);

    Map<String, String> findAllRequiredParams(String planGUID);

    DataSourceMetadata synchronizeDatasource(String planDatasourceGUID, String workSpaceGUID);

}
