package com.mysoft.gptbuilder.agent.interfaces;

import com.mysoft.gptbuilder.agent.model.dto.mcp.McpToolDto;

import java.util.List;

/**
 * MCP工具管理接口
 * 提供MCP工具查询相关的业务逻辑操作
 */
public interface IMcpToolManagerService {

    /**
     * 根据工具GUID查询MCP工具信息
     * @param toolGUID 工具GUID
     * @return MCP工具信息，如果未找到返回null
     */
    McpToolDto getToolByGUID(String toolGUID);

    /**
     * 根据服务GUID查询该服务下的所有工具
     * @param serviceGUID 服务GUID
     * @return 工具列表
     */
    List<McpToolDto> getToolsByServiceGUID(String serviceGUID);

    /**
     * 查询所有启用状态的MCP工具
     * @return 工具列表
     */
    List<McpToolDto> getAllEnabledTools();
}
