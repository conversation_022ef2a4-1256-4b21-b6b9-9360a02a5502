package com.mysoft.gptbuilder.agent.interfaces;

import com.mysoft.framework.modeling.dto.FieldsSetting;
import com.mysoft.framework.modeling.dto.ListDataResult;
import com.mysoft.framework.modeling.dto.LoadDataParams;
import com.mysoft.gptbuilder.agent.model.dto.mcp.ExecuteMcpToolRequestDto;

/**
 * MCP工具参数管理接口
 * 提供MCP工具参数管理相关的业务逻辑操作
 * 主要用于解析和管理MCP工具的InputSchema参数信息
 */
public interface IMcpToolParameterService {

    /**
     * 获取显示字段设置
     * 返回参数名称、参数类型、参数值三个字段的定义
     *
     * @return 字段设置信息
     */
    FieldsSetting loadFields();

    /**
     * 加载参数数据列表
     * 从gpt_McpServiceTool表的InputSchema字段解析参数信息
     *
     * @param options 加载数据参数
     * @return 参数数据列表结果
     */
    ListDataResult loadData(LoadDataParams options);

    /**
     * 执行MCP工具
     * 调用GPT引擎的MCP工具执行接口
     *
     * @param request
     * @return 执行结果
     */
    Object executeMcpTool(ExecuteMcpToolRequestDto request);
}
