package com.mysoft.gptbuilder.agent.imports.dto;

import lombok.Data;

import java.util.*;

@Data
public class ImportContext {
    private String taskId;

    private String zipFileName;

    private String documentGuid;

    private String zipFileUrl;

    private String workSpaceGUID;

    /**
     * 下面是上传zip文件里面的可上传对象的属性
     */
    private Map<String, List<ImportMetaDataContext>> workspaceMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> applicationMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> assistantMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> skillMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> promptMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> systemPromptTemplateMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> knowledgeMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> pluginMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> planMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> modelInstanceMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> documentMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> ruleLibraryMetaData = new HashMap<>();

    private Map<String, List<ImportMetaDataContext>> labelMetaData = new HashMap<>();

    /**
     * 下面是需要导入数据的 GUID 集合
     */
    private Set<String> applicationGUID = new HashSet<>();

    private Set<String> assistantGUID = new HashSet<>();

    private Set<String> skillGUID = new HashSet<>();

    private Set<String> promptGUID = new HashSet<>();

    private Set<String> systemPromptTemplateGUID = new HashSet<>();

    private Set<String> knowledgeGUID = new HashSet<>();

    private Set<String> knowledgeCode = new HashSet<>();

    private Set<String> pluginGUID = new HashSet<>();

    private Set<String> planGUID = new HashSet<>();

    private Set<String> modelInstanceGUID = new HashSet<>();

    private Set<String> modelInstanceCode = new HashSet<>();

    private Set<String> documentGUIDs = new HashSet<>();

    private Set<String> ruleLibraryGUIDs = new HashSet<>();

    private Set<String> labelGUIDs = new HashSet<>();


    /**
     * API/UI
     * API:针对接口的导入导出，导出是只传空间的GUID，在查询其他表的时候会把自动把关联的数据添加到集合中，如查询助手的时候 会默认把助手关联的技能添加到技能的集合中
     * UI：针对页面的导入导出，所有的数据都需要用户自己传，传啥就导出啥
     */
    private String type;

    @Data
    public static class ImportMetaDataContext {
        private String path;
        private String fileName;
        private String tableName;
        private String content;

        //下面是导入数据任务实体所需的参数
        private String objectGUID;
        private String objectCode;
        private String objectName;
        private String workspaceGUID;
        /**
         * 冲突检查原因
         */
        private String errorReason;
        /**
         * 是否允许导入
         */
        private boolean isAllowImport = true;
    }
}
