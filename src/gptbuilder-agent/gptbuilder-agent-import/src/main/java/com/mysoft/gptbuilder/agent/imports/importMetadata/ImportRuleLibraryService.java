package com.mysoft.gptbuilder.agent.imports.importMetadata;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.common.util.JsonUtil;
import com.mysoft.gptbuilder.agent.imports.dto.GPTImportConst;
import com.mysoft.gptbuilder.agent.imports.dto.ImportContext;
import com.mysoft.gptbuilder.agent.service.dao.LabelDao;
import com.mysoft.gptbuilder.agent.service.dao.RuleDao;
import com.mysoft.gptbuilder.agent.service.dao.RuleGroupDao;
import com.mysoft.gptbuilder.agent.service.dao.RuleLabelDao;
import com.mysoft.gptbuilder.agent.service.dao.RuleLibraryDao;
import com.mysoft.gptbuilder.agent.service.dao.entity.label.RuleLabelEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.rule.RuleEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.rule.RuleGroupEntity;
import com.mysoft.gptbuilder.agent.service.dao.entity.rule.RuleLibraryEntity;
import com.mysoft.gptbuilder.common.model.consts.GPTBuilderConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ImportRuleLibraryService extends ImportHandlerBase {

    private RuleLibraryDao ruleLibraryDao;

    private RuleGroupDao ruleGroupDao;

    private RuleDao ruleDao;

    private RuleLabelDao ruleLabelDao;

    public ImportRuleLibraryService(RuleLibraryDao ruleLibraryDao, RuleGroupDao ruleGroupDao, RuleDao ruleDao,
                                    RuleLabelDao ruleLabelDao) {
        this.ruleLibraryDao = ruleLibraryDao;
        this.ruleGroupDao = ruleGroupDao;
        this.ruleDao = ruleDao;
        this.ruleLabelDao = ruleLabelDao;
    }

    @Override
    public String folderName() {
        return "ruleLibrary";
    }

    @Override
    public int importOrder() {
        return ImportOrderEnum.RuleLibrary.getOrder();
    }

    @Override
    public void validate(ImportContext importContext) {
        Map<String, List<ImportContext.ImportMetaDataContext>> ruleLibraryMetaData = importContext.getRuleLibraryMetaData();
        if (CollectionUtils.isEmpty(ruleLibraryMetaData)) {
            return;
        }
        ruleLibraryMetaData.keySet().forEach(key -> {
            ruleLibraryMetaData.get(key).forEach(importMetaDataContext -> {
                validateHandleData(importMetaDataContext, importContext);
            });
        });
    }

    private void validateHandleData(ImportContext.ImportMetaDataContext importMetaDataContext, ImportContext importContext) {
        if (!"gpt_RuleLibrary.json".equalsIgnoreCase(importMetaDataContext.getFileName())) {
            return;
        }
        RuleLibraryEntity entity = JsonUtil.parse(importMetaDataContext.getContent(), RuleLibraryEntity.class);
        //校验
        List<RuleLibraryEntity> existEntity = ruleLibraryDao.selectList(Wrappers.<RuleLibraryEntity>lambdaQuery()
                .and(wrapper -> {
                    if (StringUtils.isNotBlank(entity.getRuleLibraryGUID())) {
                        wrapper.eq(RuleLibraryEntity::getRuleLibraryGUID, entity.getRuleLibraryGUID());
                    }

                    if (StringUtils.isNotBlank(entity.getLibraryCode())) {
                        wrapper.or().eq(RuleLibraryEntity::getLibraryCode, entity.getLibraryCode());
                    }

                    if (StringUtils.isNotBlank(entity.getLibraryName())) {
                        wrapper.or().eq(RuleLibraryEntity::getLibraryName, entity.getLibraryName());
                    }
                }));

        super.validateHandleData(importMetaDataContext, importContext, ImportCheckBaseDto.coverByRuleLibrary(entity), ImportCheckBaseDto.coverByRuleLibraryList(existEntity));
    }

    @Override
    public void handlerImport(ImportContext importContext) {
        //处理数据
        Map<String, List<ImportContext.ImportMetaDataContext>> metaData = importContext.getRuleLibraryMetaData();
        if (CollectionUtils.isEmpty(metaData)) {
            return;
        }
        // 过滤出符合条件的数据
        Map<String, List<ImportContext.ImportMetaDataContext>> filteredMetaData = metaData.entrySet().stream()
                .filter(entry -> (GPTBuilderConst.EXPORT_TYPE_UI.equals(importContext.getType()) && importContext.getRuleLibraryGUIDs().contains(entry.getKey())) || GPTImportConst.EXPORT_TYPE_API.equals(importContext.getType()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        // 获取所有的值
        List<ImportContext.ImportMetaDataContext> allValues = new ArrayList<>();
        filteredMetaData.values().forEach(allValues::addAll);
        // 基于FileName分组
        Map<String, List<ImportContext.ImportMetaDataContext>> groupedData = allValues.stream()
                .collect(Collectors.groupingBy(ImportContext.ImportMetaDataContext::getFileName));
        // 处理分组后的数据
        groupedData.forEach((fileName, contexts) -> {
            handleData(contexts, importContext, fileName);
        });
    }

    private void handleData(List<ImportContext.ImportMetaDataContext> importMetaDataContexts, ImportContext importContext, String fileName) {
        //循环分组 对应不同的表
        switch (fileName.toLowerCase()) {
            case "gpt_rulelibrary.json":
                saveRuleLibrary(importMetaDataContexts, importContext);
                break;
            case "gpt_rulegroup.json":
                saveRuleGroup(importMetaDataContexts);
                break;
            case "gpt_rule.json":
                saveRule(importMetaDataContexts);
                break;
            case "gpt_rulelabel.json":
                saveRuleLabel(importMetaDataContexts, importContext);
                break;
        }
    }

    private void saveRuleLibrary(List<ImportContext.ImportMetaDataContext> importMetaDataContexts, ImportContext importContext) {
        ArrayList<RuleLibraryEntity> entities = new ArrayList<>();
        importMetaDataContexts.forEach(importMetaDataContext -> {
            RuleLibraryEntity entity = JsonUtil.parse(importMetaDataContext.getContent(), RuleLibraryEntity.class);
            entities.add(entity);
        });

        //校验
        LambdaQueryWrapper<RuleLibraryEntity> wrapper = Wrappers.<RuleLibraryEntity>lambdaQuery()
                .in(RuleLibraryEntity::getRuleLibraryGUID, entities.stream().map(RuleLibraryEntity::getRuleLibraryGUID).collect(Collectors.toList()))
                .or()
                .in(RuleLibraryEntity::getLibraryCode, entities.stream().map(RuleLibraryEntity::getLibraryCode).collect(Collectors.toList()));

        List<RuleLibraryEntity> entitiesExists = ruleLibraryDao.selectList(wrapper);
        List<String> GUIDsExists = entitiesExists.stream().map(RuleLibraryEntity::getRuleLibraryGUID).collect(Collectors.toList());

        super.importCheckBatch(importMetaDataContexts, importContext, ImportCheckBaseDto.coverByRuleLibraryList(entities), ImportCheckBaseDto.coverByRuleLibraryList(entitiesExists));

        entities.forEach(entity -> {
            clearAuditFieldsData(entity);
            if (!CollectionUtils.isEmpty(GUIDsExists) && GUIDsExists.contains(entity.getRuleLibraryGUID())) {
                ruleLibraryDao.updateById(entity);
            } else {
                entity.setSpaceGUID(importContext.getWorkSpaceGUID());
                ruleLibraryDao.insert(entity);
            }
        });
    }

    private void saveRuleGroup(List<ImportContext.ImportMetaDataContext> importMetaDataContexts) {
        ArrayList<RuleGroupEntity> entitiesList = new ArrayList<>();
        importMetaDataContexts.forEach(importMetaDataContext -> {
            List<RuleGroupEntity> entities = JsonUtil.parse(importMetaDataContext.getContent(), new TypeReference<List<RuleGroupEntity>>() {
            });
            entitiesList.addAll(entities);
        });

        if (CollectionUtils.isEmpty(entitiesList)) {
            return;
        }

        List<String> GUIDs = entitiesList.stream().map(RuleGroupEntity::getRuleLibraryGUID).collect(Collectors.toList());
        ruleGroupDao.delete(new LambdaQueryWrapper<RuleGroupEntity>().in(RuleGroupEntity::getRuleLibraryGUID, GUIDs));

        entitiesList.forEach(this::clearAuditFieldsData);

        ruleGroupDao.insertBatch(entitiesList);
    }

    private void saveRule(List<ImportContext.ImportMetaDataContext> importMetaDataContexts) {
        ArrayList<RuleEntity> entitiesList = new ArrayList<>();
        importMetaDataContexts.forEach(importMetaDataContext -> {
            List<RuleEntity> entities = JsonUtil.parse(importMetaDataContext.getContent(), new TypeReference<List<RuleEntity>>() {
            });
            entitiesList.addAll(entities);
        });

        if (CollectionUtils.isEmpty(entitiesList)) {
            return;
        }
        List<String> GUIDs = entitiesList.stream().map(RuleEntity::getRuleLibraryGUID).collect(Collectors.toList());
        ruleDao.delete(new LambdaQueryWrapper<RuleEntity>().in(RuleEntity::getRuleLibraryGUID, GUIDs));
        entitiesList.forEach(this::clearAuditFieldsData);
        ruleDao.insertBatch(entitiesList);
    }

    private void saveRuleLabel(List<ImportContext.ImportMetaDataContext> importMetaDataContexts, ImportContext importContext) {
        ArrayList<RuleLabelEntity> entitiesList = new ArrayList<>();
        importMetaDataContexts.forEach(importMetaDataContext -> {
            List<RuleLabelEntity> entities = JsonUtil.parse(importMetaDataContext.getContent(), new TypeReference<List<RuleLabelEntity>>() {
            });
            entitiesList.addAll(entities);
        });

        if (CollectionUtils.isEmpty(entitiesList)) {
            return;
        }
        List<String> GUIDs = entitiesList.stream().map(RuleLabelEntity::getRuleLibraryGUID).collect(Collectors.toList());
        ruleLabelDao.delete(new LambdaQueryWrapper<RuleLabelEntity>().in(RuleLabelEntity::getRuleLibraryGUID, GUIDs));
        entitiesList.forEach(this::clearAuditFieldsData);
        ruleLabelDao.insertBatch(entitiesList);

        // 添加关联的标签导入
        Collection<String> labelGUIDs = entitiesList.stream()
                .map(RuleLabelEntity::getLabelGUID)
                .collect(Collectors.toSet());

        importContext.getLabelGUIDs().addAll(labelGUIDs);
    }

}
