package com.mysoft.gptbuilder.common.model.consts;

/**
 * <AUTHOR>
 * @description gptbuilder 全局常量
 * @date 2024/5/13 15:59
 */

public class GPTBuilderConst {

    /**
     * GPT开发平台系统代码
     */
    public static final String GPT_BUILDER_SYSTEM_CODE = "4200";

    /**
     * 默认空间GUID
     */
    public static final String DEFAULT_SPACE_GUID = "08dca713-3d6e-fbce-835e-6d2d2154296c";

    /**
     * 默认助手GUID
     */
    public static final String DEFAULT_ASSISTANT_GUID = "7a3f4310-125c-11ef-83d7-00155d822d63";

    /**
     * 智能检查助手GUID
     */
    public static final String DEFAULT_PLAN_ASSISTANT_GUID = "01bb58c9-8138-4f70-be1e-e5ee3d2ac669";

    /**
     * 智能检查应用GUID
     */
    public static final String DEFAULT_PLAN_APPLICATION_GUID = "08dd0d2e-f1b7-4ae9-8d79-0a96175787f6";

    /**
     * 智能检查技能GUID
     */
    public static final String DEFAULT_PLAN_SKILL_GUID = "23142564-1cf8-4f64-ab98-e49d33a156df";

    /**
     * 测评默认技能GUID
     */
    public static final String DEFAULT_EVAL_PLAN_SKILL_GUID = "b199b3b2-ea9c-41a5-9298-4dd77c7c289f";

    /**
     * 评判语义默认技能GUID
     */
    public static final String DEFAULT_SEMANTIC_EVAL_SKILL_GUID = "6aede3c1-a38e-4d0e-84e0-24efaa604997";

    /**
     * 测评默认技能GUID
     */
    public static final String DEFAULT_EVAL_PROMPT_SKILL_GUID = "7ae7118b-41d6-46a4-8809-5e8d88225d57";

    /**
     * 集团级参数默认的ScopeId值。说明：该值为从平台开发处问得，尚不了解为何是硬编码的。建议为配置值，或由平台提供无参数的方法。
     */
    public static final String PARAMS_SCOPE_ID_JT = "11B11DB4-E907-4F1F-8835-B9DAAB6E1F23";

    public static final String EXPORT_DATA_FILE_SUFFIX_JSON = ".json";

    public static final String EXPORT_ROOT_PATH = "tmp_export";

    public static final String EXPORT_DEFAULT_ZIP_FILE_NAME = "导出.zip";
    public static final String EXPORT_DEFAULT_ZIP_FILE_TYPE = ".zip";

    public static final String EXPORT_TYPE_API = "API";

    public static final String EXPORT_TYPE_UI = "UI";

    public static final String APAAS_URL_PLACEHOLDER = "{gptbuilder.apaas.url}";
}
