package com.mysoft.gptbuilder.common.model.excel.dto;

import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @description 数据行
 * <AUTHOR>
 * @date 2022/12/19
 */
@Data
public class DataRow implements Serializable {

    /**
     *数据行字段值映射
     */
    private Map<String, Object> dataMap;

    public DataRow() {
        this.dataMap = Maps.newHashMap();
    }

    /**
     * 添加数据字段-值映射
     * @param columnName 列名称
     * @param value 列值
     */
    public void put(String columnName, Object value) {
        this.dataMap.put(columnName, value);
    }
}
