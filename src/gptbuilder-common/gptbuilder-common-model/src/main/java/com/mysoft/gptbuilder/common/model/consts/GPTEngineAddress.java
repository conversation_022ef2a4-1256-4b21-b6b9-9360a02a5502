package com.mysoft.gptbuilder.common.model.consts;

public class GPTEngineAddress {
    public static final String GPT_ENGINE_CONTENT_PATH = "/GptEngine";

    public static final String RUN_TEST_PATH = "/ModelInstance/TestConnection";

    public static final String STREAMING_CHAT_COMPLETION = "/Agent/StreamingChatCompletion";
    //保存文档向量化数据
    public static final String SAVE_EMBEDDING_FILE = "/Knowledge/SaveEmbeddingFile";
    //删除知识库文件
    public static final String DELETE_KNOWLEDGEFILEINFO = "/Knowledge/DeleteEmbeddingInfoByFileGUID";
    //删除知识库
    public final static String DELETE_KNOWLEDGEINFO = "/Knowledge/DeleteKnowledge";
    //刷新向量
    public static final String REFRESH_KNOWLEDGE_FILE_SECTION = "/Knowledge/RefreshKnowledgeFileSection";
    //刷新整个向量库
    public static final String REFRESH_KNOWLEDGE_TOTAL= "/Knowledge/RefreshKnowledgeTotal";
    //刷新片段向量ALL
    public static final String SAVE_KNOWLEDGE_FILE_SECTION_AND_QUESTION = "/Knowledge/SaveKnowledgeFileSectionAndQuestion";
    //删除片段向量ALL
    public static final String DEL_KNOWLEDGE_FILE_SECTION = "/Knowledge/DelKnowledgeFileSection";
    //搜索向量
    public static final String SEARCH_KNOWLEDGE = "/Knowledge/SearchKnowledge";
    //搜索向量
    public static final String AGENT_GET_CHAT_MESSAGE_LIST = "/Agent/GetChatMessageList";
    //搜索向量
    public static final String AGENT_CREATE_CHAT = "/Agent/CreateChat";
    //搜索向量
    public static final String AGENT_CHAT_COMPLETION_ASYNC = "/Agent/ChatCompletion";
    //创建提示问
    public final static String QUESTION_GENERATE = "/Knowledge/QuestionGenerate";
    //参数对比
    public static final String PARAM_MAPPING = "/Parameter/ParamMapping";
    //搜索向量
    public static final String KNOWLEDGE_EXCUTE_TEST = "/Knowledge/KnowledgeExcuteTest";
    //创建向量库
    public static final String KNOWLEDGE_CREATE_COLLECTION = "/Knowledge/CreateCollection";
    //创建向量库
    public static final String KNOWLEDGE_FILE_SECTION_DISABLE = "/Knowledge/KnowledgeFileSectionDisable";
    //批量禁用向量库
    public static final String KNOWLEDGE_FILE_SECTION_DISABLE_ALL = "/Knowledge/KnowledgeFileSectionDisableALL";
    //创建向量库
    public static final String SAVE_KNOWLEDGE_FILE_SECTION_QUESTION = "/Knowledge/SaveKnowledgeFileSectionQuestion";
    //创建向量库
    public static final String DELETE_KNOWLEDGE_FILE_SECTION_QUESTION = "/Knowledge/DeleteKnowledgeFileSectionQuestion";
    // 文本向量化
    public static final String KNOWLEDGE_TEXTEMBEDDINGS = "/Knowledge/TextEmbeddings";

    //mip插件接口调用
    public static final String PLUGIN_MIP = "/Plugin/MysoftIPassApi";
    //mip插件接口调用
    public static final String PLUGIN_YKFINIT = "/Plugin/YkfInit";
    //知识库评测任务执行
    public static final String KNOWLEDGE_EVALUATING_TASK_EXCUTE = "/Knowledge/KnowledgeEvaluatingTaskExcute";
    //保存单条向量数据
    public static final String SAVE_EMBEDDING_CONTENT = "/Knowledge/SaveEmbeddingContent";
    //删除单条向量数据
    public static final String DELETE_EMBEDDING_CONTENT = "/Knowledge/DeleteEmbeddingContent";
    //刷新问题状态
    public static final String REFRESH_QUESTION_DISABLE_STATUS = "/Knowledge/RefreshQuestionDisableStatus";

    //执行审批方案
    public static final String AGENT_EXECUTE_PLAN = "/Approval/ExecutePlan";
    //PDF转图片
    public final static String CONVERT_PDF_TO_IMG = "/Knowledge/ConvertDocumentToImg";

    //执行审批方案
    public static final String AGENT_EVAL_PLAN = "/Eval/ExecutePlan";

    //更新敏感词的缓存
    public static final String UPDATE_SENSITIVE_WORDS_CACHE = "/ModelInstance/UpdateSensitiveWordsCache";

    //读取OpenApi内容
    public static final String GET_FUNCTIONs_METADATA = "/Plugin/GetFunctionsMetadata";

    //通过SK转换YAML文件
    public static final String GET_APIINFO_BYMETADATA = "/Plugin/GetApiInfoByMetadata";

    //测试milvus连接
    public static final String AGENT_MILVUS_CONNECT_TEST = "/Knowledge/TestEmbeddingDatabase";

    //获取MCP工具列表
    public static final String GET_MCP_TOOLS = "/Mcp/GetMcpTools";

    //执行MCP工具
    public static final String EXECUTE_MCP_TOOL = "/Mcp/ExecuteMcpTool";
}
