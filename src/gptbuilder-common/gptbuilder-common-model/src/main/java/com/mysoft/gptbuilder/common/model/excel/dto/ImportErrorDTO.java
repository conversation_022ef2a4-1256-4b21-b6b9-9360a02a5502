package com.mysoft.gptbuilder.common.model.excel.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.mysoft.framework.service.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 导入错误信息DTO
 * @date: 2022/12/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class ImportErrorDTO extends DTO {
    /**
     * 错误标题
     */
    @JsonProperty("Title")
    private String title;

    /**
     * 原因
     */
    @JsonProperty("Reason")
    private String reason;

    /**
     * 主键值(因为主键也可能报错)
     */
    @JsonProperty("MainGuidValue")
    private String mainGuidValue;

    /**
     * 排序号
     */
    @JsonProperty("Sequence")
    private Integer sequence;

    /**
     * 初始化
     *
     * @param title    错误标题
     * @param reason   原因
     * @param sequence 排序号
     */
    public ImportErrorDTO(String title, String reason, int sequence) {
        this.title = title;
        this.reason = reason;
        this.sequence = sequence;
    }

    public ImportErrorDTO(String title, String reason) {
        this.title = title;
        this.reason = reason;
    }
}