package com.mysoft.gptbuilder.common.model.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum RuleLibraryReadonlyEnum {

    True(1, "规则库只读，只可通过API管理"),
    False(0, "页面可编辑");

    private final int value;
    private final String name;

    RuleLibraryReadonlyEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static RuleLibraryReadonlyEnum valueOf(Integer value) {
        if (value == null) return null;
        return Arrays.stream(RuleLibraryReadonlyEnum.values()).filter(e -> e.getValue() == value).findFirst().orElse(null);
    }
}
