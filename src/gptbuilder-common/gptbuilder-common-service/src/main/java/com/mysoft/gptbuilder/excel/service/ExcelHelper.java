package com.mysoft.gptbuilder.excel.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mysoft.framework.common.exception.BusinessLogicException;
import com.mysoft.framework.common.util.ReflectUtil;
import com.mysoft.framework.modeling.function.multilingual.LanguageHelper;
import com.mysoft.framework.mybatis.BaseEntity;
import com.mysoft.framework.service.dto.DTO;
import com.mysoft.gptbuilder.common.model.consts.CommonLangRes;
import com.mysoft.gptbuilder.common.model.excel.annotation.FormatStr;
import com.mysoft.gptbuilder.common.model.excel.dto.*;
import com.mysoft.gptbuilder.common.util.*;
import com.mysoft.gptbuilder.common.util.DateUtil;
import com.mysoft.gptbuilder.excel.service.model.*;
import com.mysoft.gptbuilder.excel.service.watermark.WatermarkExtension;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.ExtensionMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 导出帮助类
 * @date: 2022-12-05
 */
@ExtensionMethod({StringExtension.class, WatermarkExtension.class})
@Data
public class ExcelHelper {

    /**
     * 26进制(单元格字母)
     */
    private static final int TWENTY = 26;

    /**
     * 自定义样式dic，提升性能
     */
    private final Map<String, CellStyle> cellStyleDic = Maps.newHashMap();

    /**
     * 自定义颜色行 key:列名；value:对应列下自定义颜色行
     */
    @Setter
    private Map<String, List<ExcelCellColorDTO>> cellColorListDic = Maps.newHashMap();

    /**
     * 是否启用加密
     */
    private boolean hasPassword = false;

    /**
     * 是否启用多语言
     */
    private boolean supportMultilingual = true;

    /**
     * 创建excel单元格
     */
    private Workbook workbook;

    public Workbook getWorkbook() {
        if (this.workbook == null) {
            this.workbook = new HSSFWorkbook();
        }
        return workbook;
    }

    /**
     * 错误提示标题字段
     */
    @Getter
    @Setter
    private String errorTitleField;

    /**
     * 错误提示主键
     */
    @Getter
    private String errorMainField;

    /**
     * 错误提醒
     */
    @Getter
    private List<ImportErrorDTO> errorList;

    /**
     * xml模板实体
     */
    @Getter
    private Config xmlConfig;

    /**
     * T 类型的属性
     */
    private List<Field> fieldList;

    /**
     * 当期行
     */
    private int curRowIndex;

    @Setter
    private String formatErrorMsg;

    public String getFormatErrorMsg() {
        return StringUtils.isNotBlank(formatErrorMsg) ? formatErrorMsg : ExcelLangRes.PriceMng_BtmMng();
    }

    /**
     * 锁定样式颜色（灰色）
     */
    private java.awt.Color lockColor;

    public java.awt.Color getLockColor() {
        if (this.lockColor == null) {
            this.lockColor = new java.awt.Color(217, 217, 217);
        }
        return lockColor;
    }

    /**
     * 数据边框颜色(灰色)
     */
    private java.awt.Color dataBorderColor;

    public java.awt.Color getDataBorderColor() {
        if (this.dataBorderColor == null) {
            this.dataBorderColor = new java.awt.Color(128, 128, 128);
        }
        return dataBorderColor;
    }

    /**
     * 白色
     */
    private java.awt.Color whiteColor;

    public java.awt.Color getWhiteColor() {
        if (this.whiteColor == null) {
            this.whiteColor = new java.awt.Color(255, 255, 255);
        }
        return whiteColor;
    }

    /**
     * 黑色
     */
    private java.awt.Color black;

    public java.awt.Color getBlack() {
        if (this.black == null) {
            this.black = java.awt.Color.BLACK;
        }
        return black;
    }


    /**
     * 灰色
     */
    private java.awt.Color gray;

    public java.awt.Color getGray() {
        if (this.gray == null) {
            return java.awt.Color.GRAY;
        }
        return this.gray;
    }

    /**
     * 记录每个工作表的数据行(从0开始)的起始行索引
     * key为工作表(sheet)在工作薄(workbook)的索引位置,从0开始.超过最大将忽略.
     * value的数据行在工作表中的起始行索引
     */
    @Getter
    private Map<Integer, Integer> _dataRowStartPosition = Maps.newHashMap();

    /**
     * 出Excel文件的行分组参数信息
     * key为工作表(sheet)在工作薄(workbook)的索引位置,从0开始.超过最大将忽略.
     * value为所有的分组行信息参数
     */
    private Map<Integer, List<ExcelRowGroupIndex>> groupInfo;

    /**
     * 初始化颜色
     */
    public void initColor() {
        if (this.getWorkbook() instanceof HSSFWorkbook) {
            List<java.awt.Color> colors = new ArrayList<>();
            colors.add(new java.awt.Color(253, 233, 217));
            colors.add(new java.awt.Color(0, 0, 0));
            colors.add(new java.awt.Color(255, 0, 0));
            colors.add(new java.awt.Color(128, 128, 128));
            colors.add(new java.awt.Color(217, 217, 217));
            colors.add(new java.awt.Color(255, 255, 255));
            colors.add(new java.awt.Color(0xFFD4B7));
            colors.add(new java.awt.Color(0xE96821));
            colors.add(new java.awt.Color(0x9C4514));
            colors.add(new java.awt.Color(0xB3C7E4));
            colors.add(new java.awt.Color(0xFACAB1));
            colors.add(new java.awt.Color(0xFFE5A3));
            colors.add(new java.awt.Color(0xBCD7EC));
            colors.add(new java.awt.Color(0xF4B084));
            colors.add(this.getGray());
            HSSFPalette palette = ((HSSFWorkbook) this.getWorkbook()).getCustomPalette();

            short FIRST_COLOR_INDEX = 0x8;
            short MAX_COLOR_INDEX = 0x40;

            colors = colors.stream().distinct().collect(Collectors.toList());
            for (int i = 0; i < colors.size(); i++) {
                if ((FIRST_COLOR_INDEX + i) > MAX_COLOR_INDEX) {
                    break;
                }
                // index的取值范围 0x8 - 0x40
                palette.setColorAtIndex((short) (FIRST_COLOR_INDEX + i), (byte) colors.get(i).getRed(), (byte) colors.get(i).getGreen(), (byte) colors.get(i).getBlue());
            }
        }
    }

    /**
     * 构建者，添加参数信息
     */
    public static class Builder {
        /**
         * 模板文件路径
         */
        private String builderMapXmlPath;
        /**
         * excel文件类型 {@link  ExcelTypeEnum}，默认是2003版本
         */
        private ExcelTypeEnum excelTypeEnum = ExcelTypeEnum.excelVersionOne;
        /**
         * 是否加密文件
         */
        private boolean builderHasPassword = false;

        /**
         * 是否支撑多语言
         */
        private boolean builderSupportMultilingual = true;

        /**
         * 错误提示标题字段
         */
        private String builderErrorTitleField;

        /**
         * 行分组配置
         */
        private Map<Integer, List<ExcelRowGroupIndex>> groupInfo;

        /*
        构造含还是
         */
        public Builder() {

        }

        /**
         * 设置Path地址
         *
         * @param mapXmlPath 路径地址
         * @return 返回当前
         */
        public Builder setMapXmlPath(String mapXmlPath) {
            this.builderMapXmlPath = mapXmlPath;
            return this;
        }

        /**
         * 设置导出类型，2003版本excel,2007版excel
         *
         * @param excelType 导出excel版本
         * @return 返回当前
         */
        public Builder setExcelType(int excelType) {
            this.excelTypeEnum = ExcelTypeEnum.parse(excelType);
            return this;
        }

        /**
         * @param hasPassword 是否加密
         * @return 返回当前
         */
        public Builder setHasPassword(boolean hasPassword) {
            this.builderHasPassword = hasPassword;
            return this;
        }

        /**
         * @param isSupportMultilingual 是否支撑多语言
         * @return 返回当前
         */
        public Builder setSupportMultilingual(boolean isSupportMultilingual) {
            this.builderSupportMultilingual = isSupportMultilingual;
            return this;
        }

        /**
         * @param errorTitleField 错误提示标题字段
         * @return 返回当前
         */
        public Builder setErrorTitleField(String errorTitleField) {
            this.builderErrorTitleField = errorTitleField;
            return this;
        }

        /**
         * 设置行分组配置
         *
         * @param groupInfo 行分组配置
         * @return 返回当前
         */
        public Builder setGroupInfo(Map<Integer, List<ExcelRowGroupIndex>> groupInfo) {
            this.groupInfo = groupInfo;
            return this;
        }

        public ExcelHelper build() {
            ExcelHelper excelHelper = new ExcelHelper();
            // 根据excel类型创建工作簿对象
            excelHelper.workbook = excelTypeEnum == ExcelTypeEnum.excelVersionOne ? new HSSFWorkbook() : new XSSFWorkbook();
            excelHelper.hasPassword = builderHasPassword;
            excelHelper.supportMultilingual = builderSupportMultilingual;
            excelHelper.errorTitleField = builderErrorTitleField;
            excelHelper.errorList = Lists.newArrayList();
            excelHelper.deserializeFromXml(builderMapXmlPath);
            excelHelper.groupInfo = groupInfo;
            return excelHelper;
        }
    }

    /**
     * 反序列化
     */
    private void deserializeFromXml(String pXmlPath) {
        try {
            Config config = ResourceUtil.readResource(pXmlPath, Config.class);
            if (this.supportMultilingual) {
                config.deserializeColumns(config.getEntity().getColumns());
                if (config.getClientDescription() != null && StringUtils.isNotEmpty(config.getClientDescription().getText())) {
                    String desc = config.getClientDescription().getText().tryTranslate();
                    config.getClientDescription().setText(StringUtils.replace(desc, "\\n", "\n"));
                }
            }
            config.setMaxRowRange(config.deserializeIndex(config.getEntity().getColumns()));
            config.descrializeColumnRange(config.getEntity().getColumns(), config.getMaxRowRange());
            this.xmlConfig = config;
        } catch (Throwable ex) {
            throw new BusinessLogicException(ex.getMessage());
        }
    }


    /**
     * 将list导出excel
     *
     * @param dataList 数据
     * @return byte[]
     */
    public <T> byte[] exportToExcel(List<T> dataList, Class<T> clazz) {
        return exportToExcel(dataList, 1, clazz);
    }

    /**
     * @param dataList
     * @param xmlPath
     * @param clazz
     */
    public <T> void exportToExcels(List<T> dataList, String xmlPath, Class<T> clazz) {
        try {
            Config config = ResourceUtil.readResource(xmlPath, Config.class);
            if (config.getSheetName().contains("L0011_")) {
                config.setSheetName(LanguageHelper.getString(config.getSheetName()));
            }
            if (this.supportMultilingual) {
                config.deserializeColumns(config.getEntity().getColumns());
                if (config.getClientDescription() != null && StringUtils.isNotEmpty(config.getClientDescription().getText())) {
                    String desc = config.getClientDescription().getText().tryTranslate();
                    config.getClientDescription().setText(StringUtils.replace(desc, "\\n", "\n"));
                }
            }
            config.setMaxRowRange(config.deserializeIndex(config.getEntity().getColumns()));
            config.descrializeColumnRange(config.getEntity().getColumns(), config.getMaxRowRange());
            this.xmlConfig = config;
            setHSSFWorkbook(dataList, config, 1, clazz);
        } catch (Throwable ex) {
            throw new BusinessLogicException(ex.getMessage());
        }

    }

    /**
     * 将list导出excel
     *
     * @param dataList   数据
     * @param columIndex 列开始的位置
     * @return byte[]
     */
    public <T> byte[] exportToExcel(List<T> dataList, int columIndex, Class<T> clazz) {
        setHSSFWorkbook(dataList, this.xmlConfig, columIndex, clazz);
        setRowGroups();
        return getWorkbookBytes();
    }

    /**
     * 获取多个sheet的excel文档
     *
     * @return byte类型
     */
    public byte[] getWorkbookBytes() {
        try {
            return this.getWorkbook().addWatermark();
        } catch (Throwable ex) {
            throw new BusinessLogicException(ex.getMessage());
        }
    }

    /**
     * 导出dto到excel
     *
     * @param datas  继承自{@link DTO}
     * @param config excel字段规则配置
     */
    public void exportDtoToExcels(List<? extends DTO> datas, Config config) {
        Assert.notNull(config, "Config is null");
        Assert.isTrue(CollectionUtils.isNotEmpty(datas), "data list is empty");
        MysoftDataColumn[] configColumns = config.getEntity().getColumns();
        // 创建sheet
        Sheet sheet = this.workbook.createSheet(config.getSheetName());
        // 设置自定义颜色
        initColor();
        int rowIndex = 0;
        // 设置Excel列头
        setExelCol(0, rowIndex++, sheet, configColumns);
        // 获取类型所有属性字段
        DTO first = datas.get(0);
        List<Field> fields = ReflectUtil.getAllNoStaticDeclaredFields(first.getClass());
        // 填充数据
        for (DTO dto : datas) {
            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;
            for (MysoftDataColumn column : configColumns) {
                Cell cell = dataRow.createCell(colIndex++);
                // 根据excel列配置获取dto对应属性值
                Object cellValue = getDataValue(dto, dto.getValues(), fields, column);
                if (cellValue == null || StringUtils.isBlank(cellValue.toString())) {
                    cell.setBlank();
                } else {
                    cell.setCellValue(cellValue.toString());
                }
            }
        }
    }

    /**
     * 导出entity数据到excel
     *
     * @param entities 继承自{@link BaseEntity}
     * @param config   xml配置
     */
    public void exportEntryToExcels(List<? extends BaseEntity> entities, Config config) {
        Assert.notNull(config, "Config is null");
        Assert.isTrue(CollectionUtils.isNotEmpty(entities), "data list is empty");
        MysoftDataColumn[] configColumns = config.getEntity().getColumns();
        // 创建sheet
        Sheet sheet = workbook.createSheet(config.getSheetName());
        // 设置自定义颜色
        initColor();
        int rowIndex = 0;
        // 设置Excel列头
        setExelCol(0, rowIndex++, sheet, configColumns);
        // 获取类型所有属性字段
        BaseEntity first = entities.get(0);
        List<Field> fields = ReflectUtil.getAllNoStaticDeclaredFields(first.getClass());

        for (BaseEntity entity : entities) {
            Row dataRow = sheet.createRow(rowIndex++);
            int colIndex = 0;
            for (MysoftDataColumn column : configColumns) {
                Cell cell = dataRow.createCell(colIndex++);
                // 根据excel列配置获取entity对应属性值
                Object cellValue = getDataValue(entity, entity.getAttributes(), fields, column);
                if (cellValue == null || StringUtils.isBlank(cellValue.toString())) {
                    cell.setBlank();
                } else {
                    cell.setCellValue(cellValue.toString());
                }
            }
        }
    }

    /**
     * 将excel中的所有数据导入到List中
     *
     * @param paramsDto 相关参数
     * @return 返回的List
     */
    public <T extends ImportBaseDTO> List<T> excelToAllList(ExcelToDataTableParamDTO paramsDto, Class<T> clazz) {
        DataTable dt = excelToDataTable(paramsDto);
        int startRowIndex = 1;
        if (paramsDto.getIsExcelRowIndex()) {
            // 数据行在Excel中的起始坐标，从1开始
            startRowIndex = getHeadRowIndex() + 2;
        }
        EntityConvertHelper<T> entityConvertHelper = new EntityConvertHelper<>(this.getErrorTitleField(), this.getErrorMainField(), this.xmlConfig.getEntity().getEndColumns(), startRowIndex, clazz);
        entityConvertHelper.convertToList(dt, false);
        this.errorList.addAll(entityConvertHelper.getErrorList());
        return entityConvertHelper.getResultList();
    }

    /**
     * 将excel中的有效数据导入到List中(排除数据类型转换错误的数据，例如：字符串转decimal、字符串转guid等)
     *
     * @param paramsDto
     * @return
     */
    public <T extends ImportBaseDTO> List<T> excelToListExcludeError(ExcelToDataTableParamDTO paramsDto, Class<T> clazz) {
        DataTable dt = excelToDataTable(paramsDto);
        int startRowIndex = 1;
        if (paramsDto.getIsExcelRowIndex()) {
            // 数据行在Excel中的起始坐标，从1开始
            startRowIndex = getHeadRowIndex() + 2;
        }
        EntityConvertHelper<T> entityConvertHelper = new EntityConvertHelper<>(this.errorTitleField, this.errorMainField, this.xmlConfig.getEntity().getEndColumns(), startRowIndex, clazz);
        entityConvertHelper.convertToList(dt, true);
        this.errorList.addAll(entityConvertHelper.getErrorList());
        return entityConvertHelper.getResultList();
    }

    /**
     * 设置Excel工作薄行分组信息
     */
    private void setRowGroups() {
        if (MapUtils.isEmpty(this.groupInfo)) {
            return;
        }
        int count = workbook.getNumberOfSheets();
        for (Integer idx : this.groupInfo.keySet()) {
            if (idx < 0 || idx >= count) {
                continue;
            }
            if (!_dataRowStartPosition.containsKey(idx)) {
                continue;
            }
            Sheet sheet = workbook.getSheetAt(idx);
            List<ExcelRowGroupIndex> group = this.groupInfo.get(idx);
            Integer dataRowStart = this._dataRowStartPosition.get(idx);
            for (ExcelRowGroupIndex item : group) {
                int from = item.getFrom() + dataRowStart;
                int to = item.getTo() + dataRowStart;
                sheet.groupRow(from, to);
            }
        }
    }

    /**
     * 获取标题行行坐标
     *
     * @return headRowIndex
     */
    private int getHeadRowIndex() {
        int headerRowIndex = 0;
        // 有备注信息，则列头行坐标下移一行
        if (xmlConfig.getClientDescription() != null) {
            headerRowIndex++;
        }
        // 有描述行信息，则列头行坐标下移一行
        if (xmlConfig.getHasDesc()) {
            headerRowIndex++;
        }
        return headerRowIndex;

    }

    /**
     * 将excel中的数据导入到DataTable中
     *
     * @param paramsDto 相关参数
     * @return 返回的DataTable
     */
    private DataTable excelToDataTable(ExcelToDataTableParamDTO paramsDto) {
        DataTable data = new DataTable();
        // 获取导入的workbook
        Workbook workbook = getImportWorkBook(paramsDto.getFileStream(), paramsDto.getFileName());
        // 获取导入的sheet
        Sheet sheet = getImportSheet(paramsDto.getSheetName(), workbook);
        if (sheet == null) {
            return data;
        }
        // 获取头部行（列名称行）
        int headerRowIndex = getHeadRowIndex();
        //解决添加批注后，需要隔一行
        if (sheet.getSheetName().equals(CommonLangRes.special_SheetName001())) {
            headerRowIndex += 1;
        }
        Row headRow = getHeadRow(sheet, headerRowIndex);
        // 存在动态列
        if (CollectionUtils.isNotEmpty(paramsDto.getDynamicColList())) {
            // 动态列，但不参与校验和逻辑，则此类列必须全部在EXCEL存在，或者全部不存在；
            // 例如价格方案导入（价格生效日期，价格失效日期）,都存在或者都不存在，否则如果只移除其中一列也是模板格式不正确
            List<String> dyColIgnoreList = paramsDto.getDynamicColList().stream().filter(MysoftDataColumn::isIgnore).map(MysoftDataColumn::getExcelKey).collect(Collectors.toList());
            // 获取所有列
            List<Cell> headRowCells = getRowCells(headRow);
            List<Cell> cellExistsKey = headRowCells.stream().filter(cell -> dyColIgnoreList.contains(getCellValue(cell))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cellExistsKey) && cellExistsKey.size() != dyColIgnoreList.size()) {
                throw new BusinessLogicException(getFormatErrorMsg());
            }

            // EXCEL模板-动态列插入集合
            for (MysoftDataColumn mysoftDataColumn : paramsDto.getDynamicColList()) {
                if (mysoftDataColumn.isIgnore()) {
                    // 动态列，不参与校验和逻辑，EXCEL中有则插入，否则不插入
                    headRowCells.stream().filter(t -> StringUtils.equals(getCellValue(t), mysoftDataColumn.getExcelKey())).findFirst().ifPresent(item -> this.getXmlConfig().insertColumnsBefore(Lists.newArrayList(mysoftDataColumn), paramsDto.getNextColumn()));
                } else {// 动态列，必须参于校验和逻辑，直接插入
                    this.getXmlConfig().insertColumnsBefore(Arrays.asList(mysoftDataColumn), paramsDto.getNextColumn());
                }
            }
        }

        if (paramsDto.getIsFirstRowColumn()) {
            // 设置列的名称
            setDtColumns(paramsDto.getColumnStartIndex(), headRow, data);
        }

        int startRow = headerRowIndex + 1;
        // 如果有合并列，需要+1
        if (hasMergeColumn()) {
            startRow += 1;
        }
        // 获取导入的数据
        return getImportData(paramsDto.getColumnStartIndex(), sheet, startRow, data);
    }

    /**
     * 获取导入的数据
     *
     * @param columnIndex
     * @param sheet
     * @param startRow
     * @param data
     * @return
     */
    private DataTable getImportData(int columnIndex, Sheet sheet, int startRow, DataTable data) {
        // 总列
        int cellCount = getXmlConfig().getEntity().getEndColumns().length;
        // 最后一列的标号
        int rowCount = sheet.getLastRowNum();
        // 循环每行数据
        for (int i = startRow; i <= rowCount; ++i) {
            Row row = sheet.getRow(i);
            if (row == null) {
                // 没有数据的行默认是null　　　　　　　
                continue;
            }
            DataRow dataRow = new DataRow();
            int dtColumnIndex = 0;
            for (int j = columnIndex; j < (cellCount + columnIndex); ++j) {
                String cellValue = "";
                Cell thisCell = row.getCell(j);
                // 获取对应的列名称
                String columnName = data.getColumns().get(dtColumnIndex++).getColumnName();

                if (Objects.isNull(thisCell)) {
                    // 当前单元格为空，值为空串
                    dataRow.put(columnName, cellValue);
                    continue;
                }
                cellValue = getCellValue(thisCell);
                // 行数据添加当前列值
                dataRow.put(columnName, cellValue);
            }
            if (dataRow.getDataMap().values().stream().anyMatch(a -> !Objects.equals(a.toString(), ""))) {
                data.getRows().add(dataRow);
            }
        }
        return data;
    }

    private String getCellValue(Cell cell) {
        // 单元格不为空，则按单元格类型取值
        if (cell.getCellType() == CellType.FORMULA && cell.getCachedFormulaResultType() == CellType.NUMERIC) {
            // 公式类型单元格，且公式结果为数字值类型
            return NumberToTextConverter.toText(cell.getNumericCellValue());
        }

        if (cell.getCellType() == CellType.NUMERIC) {
            // 数字类型单元格，可能是日期类型，先判断是否为日期类型
            if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                // 日期类型
                try {
                    return DateUtil.dateToString(cell.getDateCellValue(), DateUtil.DATE_FORMAT_YYYY_MM_DD2);
                } catch (Exception e) {
                    /*excel中有数字会被认为是日期*/
                    // 异常日期类型(e.g:1234567890)
                    return String.valueOf(cell.getNumericCellValue());
                }
            }
            // 数字类型
            return NumberToTextConverter.toText(cell.getNumericCellValue());
        }

        // 其他类型，取单元格字符串值
        return cell.toString();
    }

    /**
     * 设置列的名称
     *
     * @param columIndex
     * @param headRow
     * @param data
     */
    private void setDtColumns(int columIndex, Row headRow, DataTable data) {
        MysoftDataColumn[] columns = getXmlConfig().getEntity().getEndColumns();
        // 复杂表头存在列名称一样的问题，单独处理
        // 合并列
        if (hasMergeColumn()) {
            validateColumns(columIndex, headRow);
            for (MysoftDataColumn col : columns) {
                DataColumn column = new DataColumn(col.getDataKey());
                data.getColumns().add(column);
            }
            return;
        }

        // 总列
        int cellCount = columns.length;
        for (int i = columIndex; i < (cellCount + columIndex); ++i) {
            Cell cell = headRow.getCell(i);
            if (cell == null) {
                throw new BusinessLogicException(getFormatErrorMsg());
            }
            String cellValue = getCellValue(cell);

            if (StringUtils.isBlank(cellValue)) {
                break;
            }

            // 获取列对应字段
            String dataKey = getDataKey(columns, cellValue);

            // 标准表头EXCEL的列名重复校验
            if (data.containColumn(dataKey)) {
                throw new BusinessLogicException(getFormatErrorMsg());
            }
            DataColumn column = new DataColumn(i, dataKey);
            data.getColumns().add(column);
        }

        if (CollectionUtils.isEmpty(data.getColumns())) {
            throw new BusinessLogicException(getFormatErrorMsg());
        }
    }

    /**
     * 获取excel对应的数据库的字段
     *
     * @param xmlColumns
     * @param excelKey
     * @return
     */
    private String getDataKey(MysoftDataColumn[] xmlColumns, String excelKey) {
        String dataKey = null;
        for (MysoftDataColumn col : xmlColumns) {
            // 合并列
            if (ArrayUtils.isNotEmpty(col.getColumns())) {
                dataKey = getDataKey(col.getColumns(), excelKey);
            } else {
                MysoftDataColumn column = Arrays.stream(xmlColumns).filter(t -> StringUtils.equals(t.getExcelKey(), excelKey)).findFirst().orElse(null);
                dataKey = Objects.nonNull(column) ? column.getDataKey() : null;
                break;
            }
        }
        if (dataKey == null) {
            throw new BusinessLogicException(getFormatErrorMsg());
        }
        return dataKey;
    }

    /**
     * 校验表列
     *
     * @param columIndex
     * @param headRow
     */
    private void validateColumns(int columIndex, Row headRow) {
        MysoftDataColumn[] columns = getXmlConfig().getEntity().getColumns();
        int current = columIndex;
        for (MysoftDataColumn column : columns) {
            current = validateColumn(current, headRow, column);
        }
    }

    /**
     * 校验表列
     *
     * @param columIndex
     * @param headRow
     * @param column
     * @return
     */
    private int validateColumn(int columIndex, Row headRow, MysoftDataColumn column) {
        Cell cell = headRow.getCell(columIndex);
        if (cell == null) {
            throw new BusinessLogicException(getFormatErrorMsg());
        }
        String cellValue = getCellValue(cell);
        if (StringUtils.isBlank(cellValue)) {
            return validateColumn(++columIndex, headRow, column);
        }
        if (!StringUtils.equals(column.getExcelKey(), cellValue)) {
            throw new BusinessLogicException(getFormatErrorMsg());
        }
        return ++columIndex;
    }

    /**
     * 是否有合并列
     *
     * @return 有合并列，返回true，否则返回false
     */
    private boolean hasMergeColumn() {
        return Arrays.stream(getXmlConfig().getEntity().getColumns()).anyMatch(col -> ArrayUtils.isNotEmpty(col.getColumns()));
    }

    /**
     * 获取一行中所有单元格
     *
     * @param row
     * @return
     */
    private List<Cell> getRowCells(Row row) {
        Iterator<Cell> cellIterator = row.cellIterator();
        List<Cell> cells = Lists.newArrayList();
        while (cellIterator.hasNext()) {
            cells.add(cellIterator.next());
        }
        return cells;
    }

    /**
     * 获取头部行（列名称行）
     *
     * @param sheet
     * @param rowIndex
     * @return
     */
    private Row getHeadRow(Sheet sheet, int rowIndex) {
        Row headRow = sheet.getRow(rowIndex);
        if (headRow == null) {
            throw new BusinessLogicException(getFormatErrorMsg());
        }
        return headRow;
    }

    /**
     * 获取导入的sheet
     *
     * @param sheetName
     * @param workbook
     * @return
     */
    private Sheet getImportSheet(String sheetName, Workbook workbook) {
        Sheet sheet;
        if (sheetName != null) {
            sheet = workbook.getSheet(sheetName);
            // 如果没有找到指定的sheetName对应的sheet，则尝试获取第一个sheet
            if (sheet == null) {
                sheet = workbook.getSheetAt(0);
            }
        } else {
            sheet = workbook.getSheetAt(0);
        }
        return sheet;
    }

    /**
     * 获取导入的workbook
     *
     * @param fileStream 文件输入流
     * @param fileName   文件名称
     * @return
     */
    private Workbook getImportWorkBook(InputStream fileStream, String fileName) {
        Workbook workbook = null;
        try {
            if (StringUtils.endsWithIgnoreCase(fileName, ExcelToolUtil.FILE_EXTENSION_XLSX) || StringUtils.endsWithIgnoreCase(fileName, ExcelToolUtil.FILE_EXTENSION_XLSM)) {
                // 2007版本
                workbook = new XSSFWorkbook(fileStream);
            } else if (StringUtils.endsWithIgnoreCase(fileName, ExcelToolUtil.FILE_EXTENSION_XLS)) {
                // 2003版本
                workbook = new HSSFWorkbook(fileStream);
            }
        } catch (Exception e) {
            /*上传numbers编辑的文档时，给出友好错误提示*/
            throw new BusinessLogicException(ExcelLangRes.ExcelHelper_ExelGsTip());
        }
        return workbook;
    }


    /**
     * 获取属性值或扩展属性值
     *
     * @param object         dto或entity对象
     * @param customFieldMap dto或entity对象包含的扩展属性映射
     * @param fields         dto或entity对象属性列表
     * @param column         excel列配置
     * @return 属性值
     */
    private Object getDataValue(Object object, Map<String, Object> customFieldMap, List<Field> fields, MysoftDataColumn column) {
        Assert.notNull(column, "column is null");
        Assert.notNull(object, "dto is null");
        if (StringUtils.isBlank(column.getDataKey())) {
            return column.getFixedValue();
        }
        // 匹配到对应的属性字段，并获取数据
        Field matchField = fields.stream().filter(field -> Objects.equals(field.getName(), column.getDataKey())).findFirst().orElse(null);
        if (Objects.nonNull(matchField)) {
            return ReflectUtil.getField(matchField, object);
        }
        // 匹配不到属性字段，则获取扩展数据
        return customFieldMap.get(column.getDataKey());
    }

    /**
     * 导出形成excel文档
     *
     * @param datas      数据
     * @param config     配置
     * @param columIndex 行列
     */
    private <T> void setHSSFWorkbook(List<T> datas, Config config, int columIndex, Class<T> clazz) {
        MysoftDataColumn[] columns = config.getEntity().getColumns();
        // 创建sheet
        Sheet sheet = this.getWorkbook().createSheet(StringUtil.isNullOrEmpty(config.getSheetName()) ? "sheet1" : config.getSheetName());
        // 通过反射，获取公有属性，包含父类
        this.fieldList = ReflectUtil.getAllNoStaticDeclaredFields(clazz);
        // 设定excel的计算公式，并且返回冻结列index
        int freezeColumnsIndex = setExcelFormula(datas.size(), columIndex, columns);
        // 从0开始
        this.curRowIndex = 0;
        // 对首行设置备注，版本号信息
        if (config.getClientDescription() != null) {
            setDescription(config, sheet, columIndex);
        }
        // 设置自定义颜色
        initColor();
        int headerRowIndex = config.getHeaderRowIndex() == null ? this.curRowIndex : config.getHeaderRowIndex();

        if (config.getHasDesc()) {
            // 设置Excel描述行
            setExcelDesc(columIndex, headerRowIndex - 1, sheet, columns);
            this.curRowIndex = this.curRowIndex + 1;
        }
        //解决添加批注后，需要隔一行
        if (sheet.getSheetName().equals(CommonLangRes.special_SheetName001())) {
            headerRowIndex += 1;
        }
        // 设置excel列头
        setExelCol(columIndex, headerRowIndex, sheet, columns);
        this.curRowIndex = this.curRowIndex + config.getMaxRowRange();

        // 记录当前工作表的数据行起始索引
        int idx = workbook.getSheetIndex(sheet);
        _dataRowStartPosition.put(idx, curRowIndex);

        // 绑定数据
        bindExcelData(datas, columIndex, sheet, config);
        // 启用公式
        sheet.setForceFormulaRecalculation(true);
        // 是否启用密码保护视图
        if (this.hasPassword) {
            if (!StringUtil.isNullOrEmpty(config.getPassword())) {
                sheet.protectSheet(config.getPassword());
            } else {
                sheet.protectSheet("");
            }
        }
        // 冻结行，列
        int rowRange = config.getMaxRowRange() + 1;
        //解决添加批注后，需要隔一行
        if (sheet.getSheetName().equals(CommonLangRes.special_SheetName001())) {
            rowRange += 1;
        }
        // 如果描述行为空，则只有标题才冻结(用于无备注只有标题行顶头的纯输出不用导入的EXCEL模板，例如批量二维码生成)
        if (config.getClientDescription() == null) {
            rowRange = config.getMaxRowRange();
        }
        if (config.getHasDesc()) {
            rowRange++;
        }
        sheet.createFreezePane(freezeColumnsIndex + columIndex, rowRange, freezeColumnsIndex + columIndex, rowRange);
    }

    /**
     * 绑定excel的数据
     *
     * @param datas      数据
     * @param columIndex 列索引
     * @param sheet      单元格
     * @param config     配置信息
     */
    @SuppressWarnings({"AlibabaCollectionInitShouldAssignCapacity", "AlibabaMethodTooLong"})
    private <T> void bindExcelData(List<T> datas, int columIndex, Sheet sheet, Config config) {
        // 数据锁定单元格的样式
        CellStyle lockCellstyle = getLockedStyle();
        // 正常数据单元格的样式
        CellStyle whiteStyle = getWhiteColorStyle();
        // 格式化的样式
        Map<String, CellStyle> dicFormatStyles = new HashMap<>();

        DataFormat dataformat = this.getWorkbook().createDataFormat();
        MysoftDataColumn[] columns = this.xmlConfig.getEntity().getEndColumns();

        Map<String, Field> dataKeyFieldMap = Maps.newHashMap();
        for (MysoftDataColumn column : columns) {
            dataKeyFieldMap.put(column.getDataKey(), getObjColumn(column.getDataKey()));
        }

        for (T data : datas) {
            int cellIndex = columIndex;
            Row dataRow = sheet.createRow(this.curRowIndex);
            for (MysoftDataColumn item : columns) {
                String dataKey = item.getDataKey();

                Object value = null;
                Field field = dataKeyFieldMap.getOrDefault(dataKey, null);
                if (field == null) {
                    if (ImportBaseDTO.class.isAssignableFrom(data.getClass())) {
                        Map<String, Object> cstFields = ((ImportBaseDTO) data).getCstFields();
                        if (!(StringUtil.isNullOrWhiteSpace(dataKey) && cstFields.containsKey(dataKey))) {
                            value = cstFields.get(dataKey);
                        }
                    } else if (DTO.class.isAssignableFrom(data.getClass())) {
                        value = ((DTO) data).getValues().get(dataKey);
                    }
                } else {
                    value = getBindValue(config, data, field);
                }

                Cell cell = dataRow.createCell(cellIndex);
                if (value == null) {
                    cell.setBlank();
                } else {
                    if (ArrayUtils.isNotEmpty(item.getItems())) {
                        final String finalValue = value.toString();
                        ColumnItem ctem = Arrays.stream(item.getItems()).filter(it -> Objects.equals(it.getValue(), finalValue)).findFirst().orElse(null);
                        if (ctem != null) {
                            value = ctem.getText();
                        }
                    }
                    cell.setCellValue(value.toString());
                }
                if (!StringUtil.isNullOrEmpty(item.getFormula())) {
                    cell.setCellFormula(String.format(item.getFormula(), this.curRowIndex + 1));
                }
                //不管锁定，还是不锁定，都需要给单元格设置格式
                cell.setCellStyle(getFormatCellStyle(lockCellstyle,dicFormatStyles,dataformat,item));
                // 设置自定义颜色
                if (!StringUtil.isNullOrEmpty(item.getFontColor())) {
                    CellStyle color = getColorFontCellStyle(item.getFontColor(), item.isLock());
                    // 列属性有颜色设置
                    // 当前列存在自定义颜色，列字段存在
                    // noinspection ConstantConditions
                    if (this.cellColorListDic.containsKey(dataKey)) {
                        // 自定义字典中对应数据行集合，且需要设置颜色，设置为特殊颜色
                        int index = datas.indexOf(data);
                        // noinspection ConstantConditions
                        if (this.cellColorListDic.get(dataKey).stream().anyMatch(it -> it.getDataRowIndex() == index && it.getShowColor())) {
                            cell.setCellStyle(color);
                        }
                    } else {
                        // 没有设置字典，默认整列自定义颜色
                        cell.setCellStyle(color);
                    }
                }
                cellIndex++;
            }
            this.curRowIndex = this.curRowIndex + 1;
        }
    }

    /**
     * 设置单元格格式，不管有没有被锁定的列，都需要设置
     * @param style
     * @param dicFormatStyles
     * @param dataformat
     * @param dataColumn
     * @return
     */
    private CellStyle getFormatCellStyle(CellStyle style,Map<String, CellStyle> dicFormatStyles,DataFormat dataformat,MysoftDataColumn dataColumn) {
        // 如果锁定设置为灰色
        if (dataColumn.isLock()){
            style = getLockedStyle();
        }else{
            style = getWhiteColorStyle();
        }
        // 是否存在格式
        if (!StringUtil.isNullOrEmpty(dataColumn.getFormat())) {
            String intrnationalFormat = InternationalizationUtil.getInternationalFormat(dataColumn.getFormat());
            if (dicFormatStyles.containsKey(intrnationalFormat)) {
                style = dicFormatStyles.get(intrnationalFormat);
            } else {
                style.setDataFormat(dataformat.getFormat(intrnationalFormat));                
                dicFormatStyles.put(intrnationalFormat, style);
            }
        }
        return style;
    }

    /**
     * 获取绑定的值
     *
     * @param config 配置
     * @param data   数据
     * @param field  字段
     * @return 获取绑定值数据
     */
    private Object getBindValue(Config config, Object data, Field field) {
        try {
            Object value = ReflectUtil.getField(field, data);
            if (config.isJumpEmpty() && value != null && StringUtil.isNullOrEmpty(value.toString())) {
                return null;
            } else {
                if (value != null && !StringUtil.isNullOrEmpty(value.toString())) {
                    FormatStr formatStr = field.getAnnotation(FormatStr.class);

                    if (formatStr != null && Objects.equals(field.getType(), Date.class)) {
                        // 是否启动国际化
                        if (InternationalizationUtil.isInternationalEnable()) {
                            value = InternationalHelper.dateFormat(DateExtension.convertToUserTimezone((Date) value));
                        } else {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formatStr.value());
                            value = simpleDateFormat.format(DateExtension.convertToUserTimezone((Date) value));
                        }
                    }

                }
            }
            return value;
        } catch (Throwable ex) {
            throw new BusinessLogicException(ex.getMessage());
        }
    }

    /**
     * 获取对象的字段
     *
     * @param fieldName 字段名称
     * @return Field
     */
    private Field getObjColumn(String fieldName) {
        // field首字母转大写，java规范属性定义首字母小写，与xml配置中不一致导致查找不到
        return fieldList.stream().filter(it -> StringUtils.equals(it.getName(), fieldName)
                || StringUtils.equals(it.getName().firstCharToUppercase(), fieldName)).findFirst().orElse(null);
    }

    /**
     * 设置excel的列头
     *
     * @param pColumIndex 列标识
     * @param rowIndex    行标识
     * @param sheet       单元格标识
     * @param columns     行数据集合
     */
    private void setExelCol(int pColumIndex, int rowIndex, Sheet sheet, MysoftDataColumn[] columns) {
        int columIndex = pColumIndex;
        Row headerRow = sheet.getRow(rowIndex) == null ? sheet.createRow(rowIndex) : sheet.getRow(rowIndex);
        headerRow.setHeight((short) (19.5 * 20));
        Drawing drawing = sheet.createDrawingPatriarch();
        // 非必填头部样式
        CellStyle headNoRequiredCellstyle = getExelHeadNoRequiredStyle();

        Short i = 0;
        for (MysoftDataColumn item : columns) {
            // 只能在末级列上绑定下拉数据源
            if (ArrayUtils.isEmpty(item.getColumns()) && ArrayUtils.isNotEmpty(item.getItems())) {
                addSourceData(sheet, item.getItems(), this.curRowIndex + this.xmlConfig.getMaxRowRange(), columIndex);
            }
            // 必填列头样式动态获取
            CellStyle headCellstyle = getExelHeadStyle(Integer.parseInt(item.getHeadBgc() != null ? item.getHeadBgc() : "F4B084", 16));

            String displayName = item.getExcelKey();
            // 如果该属性指定了DisplayName，则输出
            if (!StringUtil.isNullOrEmpty(displayName)) {
                Cell rowHead = headerRow.createCell(columIndex);
                if (!item.isRequired()) {
                    rowHead.setCellStyle(headNoRequiredCellstyle);
                } else {
                    rowHead.setCellStyle(headCellstyle);
                }
                if (!StringUtil.isNullOrEmpty(item.getComment())) {
                    Comment comment2 = drawing.createCellComment(new HSSFClientAnchor(0, 0, 0, 0, i, rowIndex, i, rowIndex));
                    comment2.setAuthor(CommonLangRes.adminName());
                    comment2.setString(new HSSFRichTextString(item.getComment()));
                    rowHead.setCellComment(comment2);
                }

                rowHead.setCellValue(displayName);
                sheet.setColumnWidth(columIndex, (int) ((item.getWidth() + 0.72) * 256));
                sheet.setColumnHidden(columIndex, !item.isDisplay());
                // 设置列格式
                if (!StringUtil.isNullOrEmpty(item.getFormat())) {
                    String intrnationalFormat = InternationalizationUtil.getInternationalFormat(item.getFormat());
                    CellStyle style = this.getWorkbook().createCellStyle();
                    DataFormat format = this.getWorkbook().createDataFormat();
                    style.setDataFormat(format.getFormat(intrnationalFormat));
                    sheet.setDefaultColumnStyle(columIndex, style);
                }
                if (ArrayUtils.isNotEmpty(item.getColumns())) {
                    setExelCol(columIndex, rowIndex + 1, sheet, item.getColumns());
                }
                if (item.getRowRange() != 1 || item.getColumnRange() != 1) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(this.curRowIndex, this.curRowIndex + item.getRowRange() - 1, columIndex, columIndex + item.getColumnRange() - 1);
                    sheet.addMergedRegion(cellRangeAddress);

                    CellStyle style = rowHead.getCellStyle();
                    CellBorderStyle cellBorderStyle = getCellBorderStyle(this.getBlack());
                    DrawerExtension.setBorderColor(style, this.getWorkbook(), cellBorderStyle);

                    for (int rIndex = cellRangeAddress.getFirstRow(); rIndex <= cellRangeAddress.getLastRow(); rIndex++) {
                        Row row = CellUtil.getRow(rIndex, sheet);
                        if (row == null) {
                            break;
                        }
                        for (int colIndex = cellRangeAddress.getFirstColumn(); colIndex <= cellRangeAddress.getLastColumn(); colIndex++) {
                            Cell singleCell = CellUtil.getCell(row, colIndex);
                            if (singleCell != null) {
                                singleCell.setCellStyle(style);
                            } else {
                                break;
                            }
                        }
                    }
                }
            }
            columIndex += item.getColumnRange();
            i++;
        }
    }

    /**
     * 添加验证数据
     *
     * @param sheet       单元格数据
     * @param items       列集合数
     * @param rowIndex    行标识
     * @param columnIndex 列索引
     */
    private void addSourceData(Sheet sheet, ColumnItem[] items, int rowIndex, int columnIndex) {
        String sheetName = "_constraintSheet_";
        Sheet constraintSheet = this.getWorkbook().getSheet(sheetName) == null ? this.getWorkbook().createSheet(sheetName) : this.getWorkbook().getSheet(sheetName);

        String lastValue = "";
        int begIndex = 0;
        for (ColumnItem item : items) {
            Row row = constraintSheet.getRow(begIndex) == null ? constraintSheet.createRow(begIndex) : constraintSheet.getRow(begIndex);
            row.createCell(columnIndex).setCellValue(StringExtension.tryTranslate(item.getText()));
            begIndex++;
            lastValue = StringExtension.tryTranslate(item.getText());
        }
        // 如果只有一个可选值的话，则增加一个相同的选项，不然在Excel中会有问题，可能是NPOI的BUG
        if (items.length == 1) {
            Row row = constraintSheet.getRow(begIndex) == null ? constraintSheet.createRow(begIndex) : constraintSheet.getRow(begIndex);
            row.createCell(columnIndex).setCellValue(lastValue);
            begIndex++;
        }
        HSSFName range = (HSSFName) this.getWorkbook().createName();
        range.setRefersToFormula(StringUtil.formatMessage("{0}!${1}$1:${1}${2}", sheetName, getRefersColumn(columnIndex), begIndex));

        String rangeName = "dicRange" + columnIndex;
        range.setNameName(rangeName);

        CellRangeAddressList cellRegions = new CellRangeAddressList(rowIndex, 65535, columnIndex, columnIndex);

        DVConstraint constraint = DVConstraint.createFormulaListConstraint(rangeName);
        this.getWorkbook().setSheetHidden(this.getWorkbook().getSheetIndex(constraintSheet), true);
        HSSFDataValidation validation = new HSSFDataValidation(cellRegions, constraint);
        validation.setShowErrorBox(true);

        sheet.addValidationData(validation);
        sheet.setForceFormulaRecalculation(true);
    }

    /**
     * 设置Excel描述行
     *
     * @param pColumIndex 列标识
     * @param rowIndex    行标识
     * @param sheet       单元格处理
     * @param columns     列集合数据
     */
    private void setExcelDesc(int pColumIndex, int rowIndex, Sheet sheet, MysoftDataColumn[] columns) {
        int columIndex = pColumIndex;
        Row headerRow = sheet.getRow(rowIndex) == null ? sheet.createRow(rowIndex) : sheet.getRow(rowIndex);
        headerRow.setHeight((short) (50 * 20));

        // 非必填头部样式
        CellStyle headNoRequiredCellstyle = getExelDescStyle();
        Font descFontStyle = getFontStyle("宋体", this.getGray(), 10, false);//$忽略多语言$
        Font tempFontStyle = getFontStyle("宋体", this.getGray(), 10, true);//$忽略多语言$

        for (MysoftDataColumn column : columns) {
            Cell descCell = headerRow.createCell(columIndex);
            descCell.setCellStyle(headNoRequiredCellstyle);

            // 如果该属性指定了DescKey，则输出描述信息
            if (!StringUtil.isNullOrEmpty(column.getDescKey())) {
                descCell.setCellValue(getDescRichTextString(column.getDescKey(), column.getTempKey(), descFontStyle, tempFontStyle));
            }
            columIndex += column.getColumnRange();
        }

    }

    /**
     * 设定excel的计算公式
     *
     * @param rowCount   行的数量（数据行）
     * @param columIndex 填充数据列的开始位置
     * @param columns    列集合
     * @return 返回冻结列index
     */
    private int setExcelFormula(int rowCount, int columIndex, MysoftDataColumn[] columns) {
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, rowCount + 2, 0, (int) (Arrays.stream(columns).count() + 2));

        int freezeColumnsIndex = 0;
        for (int i = 0; i < columns.length; i++) {
            columns[i].setNumTo26Sys(numTo26Sys(i + columIndex + 1));
            if (columns[i].isFreeze()) {
                freezeColumnsIndex = i + 1;
            }
        }
        for (MysoftDataColumn item : Arrays.stream(columns).filter(it -> !StringUtil.isNullOrEmpty(it.getFormula())).collect(Collectors.toList())) {
            String str = item.getFormula();
            for (MysoftDataColumn ctem : columns) {
                str = str.replace("{" + ctem.getDataKey() + "}", ctem.getNumTo26Sys() + "{0}");
            }
            item.setFormula(str);
        }
        return freezeColumnsIndex;
    }

    /**
     * 对首行设置备注、版本号
     *
     * @param config     配置文件
     * @param sheet      单元格
     * @param columIndex 列标识
     */
    private void setDescription(Config config, Sheet sheet, int columIndex) {
        // 设置备注
        CellStyle remarkStyle = getCellStyle(getFontStyle("宋体", null, 11, false), null);//$忽略多语言$
        remarkStyle.setWrapText(true);
        Row remarkRow = sheet.createRow(this.curRowIndex++);
        remarkRow.setHeight((short) (config.getClientDescription().getHeight() * 20));
        Cell remarkCell = remarkRow.createCell(columIndex);
        remarkCell.setCellValue(config.getClientDescription().getText());
        remarkCell.setCellStyle(remarkStyle);
        int colend = config.getClientDescription().getColspan() + columIndex;
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, columIndex, colend);
        if (cellRangeAddress.getNumberOfCells() < 2) {
            return;
        }
        sheet.addMergedRegion(cellRangeAddress);
    }

    /**
     * 获取单元格样式
     *
     * @param font                     单元格字体
     * @param fillForegroundColorShort 图案的颜色
     * @return CellStyle
     */
    private CellStyle getCellStyle(Font font, java.awt.Color fillForegroundColorShort) {
        CellStyle cellStyle = this.getWorkbook().createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.TOP);
        if (fillForegroundColorShort != null) {
            DrawerExtension.fillForegroundColorFixed(cellStyle, this.getWorkbook(), fillForegroundColorShort);
        }
        if (font != null) {
            cellStyle.setFont(font);
        } else {
            String fontfamily = "宋体";//$忽略多语言$
            cellStyle.setFont(getFontStyle(fontfamily, null, 11, false));
        }
        return cellStyle;
    }

    /**
     * 获取字体样式
     *
     * @param fontFamily 字体名
     * @param fontcolor  字体颜色
     * @param fontsize   字体大小
     * @param isBold     是否粗体,默认为否
     * @return Font
     */
    private Font getFontStyle(String fontFamily, java.awt.Color fontcolor, int fontsize, boolean isBold) {
        Font font1 = this.getWorkbook().createFont();
        if (!StringUtil.isNullOrEmpty(fontFamily)) {
            font1.setFontName(fontFamily);
        }
        if (fontcolor != null) {
            Color color = DrawerExtension.createColor(this.getWorkbook(), fontcolor);
            if (this.getWorkbook() instanceof XSSFWorkbook) {
                ((XSSFFont) font1).setColor((XSSFColor) color);
            } else if (this.getWorkbook() instanceof HSSFWorkbook) {
                font1.setColor(((HSSFColor) color).getIndex());
            }
        }
        if (isBold) {
            font1.setBold(true);
        }
        font1.setFontHeightInPoints((short) fontsize);
        return font1;
    }

    /**
     * 获取头部样式
     *
     * @param headBgc 列头背景色号
     * @return CellStyle
     */
    private CellStyle getExelHeadStyle(int headBgc) {
        // 颜色通过16进制进行转换
        java.awt.Color bgc = new java.awt.Color(headBgc);
        // 必填头部样式
        CellStyle headCellstyle = getCellStyle();
        CellBorderStyle borderStyle = getCellBorderStyle(this.getBlack());
        DrawerExtension.setBorderColor(headCellstyle, this.getWorkbook(), borderStyle);
        DrawerExtension.fillForegroundColorFixed(headCellstyle, this.getWorkbook(), bgc);
        headCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return headCellstyle;
    }

    /**
     * 获取头部描述信息行样式
     *
     * @return CellStyle
     */
    private CellStyle getExelDescStyle() {
        // 颜色通过16进制进行转换
        java.awt.Color bgc = new java.awt.Color(Integer.parseInt("FFFFFF", 16));
        // 必填头部样式
        Font headRequiredFont = getFontStyle("宋体", this.getBlack(), 11, false);//$忽略多语言$
        headRequiredFont.setBold(true);
        CellStyle headCellstyle = getCellStyle(headRequiredFont, null);
        headCellstyle.setAlignment(HorizontalAlignment.LEFT);
        headCellstyle.setVerticalAlignment(VerticalAlignment.CENTER);

        CellBorderStyle borderStyle = new CellBorderStyle();
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setLeftBorderColor(this.getBlack());
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setRightBorderColor(this.getBlack());
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setTopBorderColor(this.getBlack());
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBottomBorderColor(this.getBlack());

        DrawerExtension.setBorderColor(headCellstyle, this.getWorkbook(), borderStyle);
        DrawerExtension.fillForegroundColorFixed(headCellstyle, this.getWorkbook(), bgc);
        headCellstyle.setWrapText(true);
        return headCellstyle;
    }

    /**
     * 获取非必填的头部样式
     *
     * @return CellStyle
     */
    private CellStyle getExelHeadNoRequiredStyle() {
        CellStyle headNoRequiredCellstyle = getCellStyle();
        CellBorderStyle borderStyle = getCellBorderStyle(this.getBlack());
        DrawerExtension.setBorderColor(headNoRequiredCellstyle, this.getWorkbook(), borderStyle);
        DrawerExtension.fillForegroundColorFixed(headNoRequiredCellstyle, this.getWorkbook(), this.getLockColor());
        headNoRequiredCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return headNoRequiredCellstyle;
    }

    private CellStyle getCellStyle() {
        Font headRequiredFont = getFontStyle("宋体", this.getBlack(), 11, false);//$忽略多语言$
        headRequiredFont.setBold(true);
        CellStyle headNoRequiredCellstyle = getCellStyle(headRequiredFont, null);
        headNoRequiredCellstyle.setAlignment(HorizontalAlignment.CENTER);
        headNoRequiredCellstyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return headNoRequiredCellstyle;
    }


    /**
     * 获取描述信息单元格富文本
     *
     * @param descString    描述信息
     * @param tempString    示例数据信息
     * @param descFontStyle 描述信息样式
     * @param tempFontStyle 示例数据信息样式
     * @return HSSFRichTextString
     */
    private HSSFRichTextString getDescRichTextString(String descString, String tempString, Font descFontStyle, Font tempFontStyle) {
        if (StringUtil.isNullOrEmpty(descString)) {
            return new HSSFRichTextString("");
        }
        String descText = descString;
        if (!StringUtil.isNullOrEmpty(tempString)) {
            descText = descText + '\n' + tempString;
        }
        HSSFRichTextString cellText = new HSSFRichTextString(descText);
        cellText.applyFont(0, descString.length(), descFontStyle);
        if (!StringUtil.isNullOrEmpty(tempString)) {
            cellText.applyFont(descText.length() - tempString.length(), descText.length(), tempFontStyle);
        }
        return cellText;
    }

    /**
     * 数据单元边框样式
     *
     * @return CellStyle
     */
    private CellStyle getLockedStyle() {
        CellStyle lockCellstyle = getCellStyle(null, this.getLockColor());
        CellBorderStyle cellBorderStyle = getCellBorderStyle(this.getDataBorderColor());
        DrawerExtension.setBorderColor(lockCellstyle, this.getWorkbook(), cellBorderStyle);
        DrawerExtension.fillForegroundColorFixed(lockCellstyle, this.getWorkbook(), this.getLockColor());
        lockCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return lockCellstyle;
    }


    /**
     * 数据边框，白色样式
     *
     * @return CellStyle
     */
    private CellStyle getWhiteColorStyle() {
        CellStyle dataCellstyle = getCellStyle(null, this.getWhiteColor());
        CellBorderStyle cellBorderStyle = getCellBorderStyle(this.getDataBorderColor());
        DrawerExtension.setBorderColor(dataCellstyle, this.getWorkbook(), cellBorderStyle);
        DrawerExtension.fillForegroundColorFixed(dataCellstyle, this.getWorkbook(), this.getWhiteColor());
        dataCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        dataCellstyle.setLocked(false);
        return dataCellstyle;
    }

    /**
     * 获取字典列
     *
     * @return 字符串
     */
    private String getRefersColumn(int columnIndex) {
        int firstChart = columnIndex / TWENTY;
        int lastChart = columnIndex % TWENTY;
        if (firstChart > 0) {
            return StringUtil.formatMessage("{0}{1}", (char) ('A' + firstChart - 1), (char) ('A' + lastChart));
        } else {
            return String.valueOf((char) ('A' + lastChart));
        }
    }

    /**
     * 锁定/正常单元格，设置字体自定义颜色
     *
     * @param color    传入颜色
     * @param isLocked 是否锁定
     * @return CellStyle
     */
    private CellStyle getColorFontCellStyle(String color, boolean isLocked) {
        String key = color + isLocked;
        if (this.cellStyleDic.containsKey(key)) {
            return this.cellStyleDic.get(key);
        }
        // 传入颜色
        java.awt.Color bgc = new java.awt.Color(Integer.parseInt(color, 16));
        // 设置对应颜色的字体
        Font iFont = getFontStyle("宋体", bgc, 11, false);//$忽略多语言$

        CellStyle colorCellstyle = getCellStyle(iFont, this.getLockColor());

        CellBorderStyle cellBorderStyle = getCellBorderStyle(this.getDataBorderColor());
        DrawerExtension.setBorderColor(colorCellstyle, this.getWorkbook(), cellBorderStyle);
        if (isLocked) {
            DrawerExtension.fillForegroundColorFixed(colorCellstyle, this.getWorkbook(), this.getLockColor());
            colorCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        } else {
            DrawerExtension.fillForegroundColorFixed(colorCellstyle, this.getWorkbook(), this.getWhiteColor());
            colorCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            colorCellstyle.setLocked(false);
        }
        DrawerExtension.fillForegroundColorFixed(colorCellstyle, this.getWorkbook(), this.getLockColor());
        colorCellstyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        this.cellStyleDic.replace(key, colorCellstyle);
        return colorCellstyle;
    }

    /**
     * 获取单元格标识信息
     *
     * @return CellBorderStyle
     */
    public CellBorderStyle getCellBorderStyle(java.awt.Color borderColor) {
        CellBorderStyle borderStyle = new CellBorderStyle();
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setLeftBorderColor(borderColor);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setRightBorderColor(borderColor);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setTopBorderColor(borderColor);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBottomBorderColor(borderColor);
        return borderStyle;
    }

    /**
     * 设置列Items
     *
     * @param dataKey     数据标识
     * @param columnItems 选项列表
     */
    public void setColumnItems(String dataKey, ColumnItem[] columnItems) {
        MysoftDataColumn[] dataColumns = this.xmlConfig.getEntity().getColumns();
        Arrays.stream(dataColumns).filter(c -> Objects.equals(c.getDataKey(), dataKey)).forEach(col -> col.setItems(columnItems));
    }

    /**
     * 设置列Items
     *
     * @param columnItems Key: 数据标识 Values: 选项列表
     */
    public void setColumnItems(Map<String, ColumnItem[]> columnItems) {
        MysoftDataColumn[] dataColumns = this.xmlConfig.getEntity().getColumns();
        Set<String> setColKeys = columnItems.keySet();

        final Map<String, MysoftDataColumn> setColMap = Arrays.stream(dataColumns).filter(c -> setColKeys.contains(c.getDataKey())).collect(Collectors.toMap(MysoftDataColumn::getDataKey, v -> v));
        columnItems.forEach((dataKey, items) -> setColMap.get(dataKey).setItems(items));
    }

    /**
     * 将数字列转换为这母表示的列，26位字母从A-Z。27表示为“AA”
     * 此方法翻译自.net平台的NPOI RangeAddress.NumTo26Sys()，此方法从1开始。
     * java平台poi未提示同名接口方法，CellReference.convertNumToColString()从0开始，不等效。
     *
     * @param _num 列索引
     * @return
     */
    private String numTo26Sys(int _num) {
        StringBuilder str = new StringBuilder();
        do {
            --_num;
            int num = 65 + _num % 26;
            _num /= 26;
            str.insert(0, (char) num);
        }
        while (_num > 0);
        return str.toString();
    }

}
