package com.mysoft.gptbuilder.excel.service.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.Data;

import java.io.Serializable;

/**
 * 描述类
 * <AUTHOR>
 * @date: 2022-12-06 10:20
 */
@Data
public class ClientDescription implements Serializable {

    @JacksonXmlProperty(localName = "colspan")
    public int colspan;

    @JacksonXmlProperty(localName = "height")
    public float height;

    @JacksonXmlText
    public String text;
}
