package com.mysoft.gptbuilder.subscriber;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mysoft.framework.event.annotation.Subscriber;
import com.mysoft.framework.event.api.EventSubscriber;
import com.mysoft.framework.event.enums.EventResultEnum;
import com.mysoft.framework.event.info.EventArgument;
import com.mysoft.framework.event.info.Result;
import com.mysoft.framework.event.info.param.ParamChangeMessage;
import com.mysoft.gptbuilder.common.service.MyParamsCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
@Subscriber(name = "业务参数变更-缓存失效")
public class ResetCacheWhenParamEventSubscriber extends EventSubscriber<ParamChangeMessage,Void> {

    @Autowired
    MyParamsCacheService myParamsCacheService;

    @Override
    public Result<Void> subscribe(ParamChangeMessage message) {
        myParamsCacheService.checkDeleteContentReviewBaidu(message.getParamCode());
        return new Result<>(EventResultEnum.SUCCESS);
    }

    @Override
    public TypeReference<EventArgument<ParamChangeMessage, Void>> getMessageType() {
        return new TypeReference<EventArgument<ParamChangeMessage, Void>>() {
        };
    }
}
