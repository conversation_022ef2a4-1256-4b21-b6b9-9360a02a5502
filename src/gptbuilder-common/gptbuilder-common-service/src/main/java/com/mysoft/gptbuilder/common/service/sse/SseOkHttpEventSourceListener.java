package com.mysoft.gptbuilder.common.service.sse;

import com.mysoft.gptbuilder.common.model.sse.SseOkHttpParam;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.UUID;

/**
 *  流式响应监听器
 */
@Slf4j
public class SseOkHttpEventSourceListener<T> extends EventSourceListener {

    private SseOkHttpParam sseOkHttpPara;

    public SseOkHttpEventSourceListener(SseOkHttpParam sseOkHttpPara) {
        super();
        this.sseOkHttpPara = sseOkHttpPara;
    }

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        super.onOpen(eventSource, response);
        log.info("#### 建立sse连接 onOpen {}", sseOkHttpPara.getId());
        sseOkHttpPara.setStreamResults(new ArrayList<>());
    }

    @Override
    public void onEvent(EventSource eventSource, @Nullable String id, @Nullable String type, String data) {
        super.onEvent(eventSource, id, type, data);
        log.info("###### sse连接响应 {} ID={} TYPE={} DATA= {}", sseOkHttpPara.getId(), id, type, data);
        if (!StringUtils.isEmpty(data) ) {
            try {
                SseEmitter.SseEventBuilder sseEventBuilder = SseEmitter.event()
                        .id(id) // You can give nay string as id
                        .name(type)
                        .data(data);
                SseEmitter sseEmitter = sseOkHttpPara.getSseEmitter();
                sseEmitter.send(sseEventBuilder);
            } catch (Exception e) {
                log.warn("sse连接响应 转发IO异常 {}", sseOkHttpPara.getId(), e);
            }

        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        super.onClosed(eventSource);
        sseOkHttpPara.getSseEmitter().complete();
        sseOkHttpPara.getCountDownLatch().countDown();
        log.info("关闭sse连接 onClosed {}", sseOkHttpPara.getId());
    }

    @Override
    public void onFailure(EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
        super.onFailure(eventSource, t, response);
        int responseCode = response.code();
        String responseMessage = t == null ? "null" : t.getMessage();
        String throwableName = t == null ? "null" : t.getClass().getName();
        sseOkHttpPara.getCountDownLatch().countDown();
        log.warn("sse连接异常 onFailure {} {} {} {}", sseOkHttpPara.getId(), responseCode, responseMessage, throwableName, t);
    }
}
